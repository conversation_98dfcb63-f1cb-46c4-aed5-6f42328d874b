# WordPress Plugin: Mind Qtrl | Community Media Player - Improved AI Development Prompts

This document provides a set of expert-level, modular prompts for guiding an AI assistant in developing the "Mind Qtrl | Community Media Player" WordPress plugin. Each prompt is structured for clarity, specificity, and best practices, referencing the hybrid integration approach, .augment-guidelines, and Vidstack/WordPress standards.

**Plugin Name:** MQCMP (Mind Qtrl | Community Media Player)
**Primary Goal:** Replace default YouTube and Vimeo players within Fluent Community activities and other frontend portal areas with a Vidstack player, styled according to Fluent Community's theme and ensuring only one video plays at a time.

---

## Phase 1: Basic Plugin Setup & Vidstack Integration

**Prompt 1: Create Basic Plugin Structure**
Role: Expert WordPress plugin developer.
Goal: Generate a modular, standards-compliant file/folder structure for a new plugin named 'MQCMP'.
Requirements:
- Create `mqcmp.php` (main plugin file with full WP header).
- Add `includes/` for PHP classes, `assets/js/` and `assets/css/` for scripts/styles.
- Add `readme.txt` with standard WP structure.
- Ensure all files are ready for internationalization and future expansion.
Output: List of files/folders and sample headers for each.

**Prompt 2: Enqueue Vidstack Player CDN and Basic CSS/JS**
Role: WP developer with Vidstack integration expertise.
Goal: Enqueue Vidstack Player (latest CDN) and plugin assets only on the frontend.
Requirements:
- Use `wp_enqueue_script`/`wp_enqueue_style` for Vidstack and plugin assets.
- Ensure scripts are loaded as modules if required by Vidstack.
- Register `mqcmp-main.js` (depends on Vidstack) and `mqcmp-main.css`.
- Load only when plugin is enabled (see admin setting in later prompt).
- Follow WP best practices for asset versioning and dependencies.
Output: PHP code for asset registration/enqueueing.

**Prompt 3: Create Basic Vidstack Custom Element Definition (JavaScript)**
Role: JS developer specializing in custom elements and Vidstack.
Goal: Define `<mqcmp-vidstack-player>` as a custom element wrapping Vidstack.
Requirements:
- On connect, initialize `<media-player>` with `src` and `poster` attributes.
- Use Vidstack Default Layout and outlet.
- Log creation and initialization events.
- Follow progressive enhancement: ensure fallback for non-JS users.
- Use Shadow DOM for style isolation.
Output: JS code for the custom element, with JSDoc comments.

---

## Phase 2: Fluent Community Integration & Player Replacement

**Prompt 4: Identify YouTube/Vimeo iFrames in Fluent Community Activities (JS)**
Role: JS developer with Fluent Community knowledge.
Goal: Write functions to detect YouTube/Vimeo iframes in Fluent Community activity DOM.
Requirements:
- Use selectors specific to Fluent Community (e.g., `.fcom-activity-item ... iframe`).
- Log found elements and their `src`.
- Modularize detection for future extension.
- Use MutationObserver for dynamic content.
Output: JS functions with comments, ready for integration.

**Prompt 5: PHP - Get Fluent Community CSS Variables**
Role: WP backend developer.
Goal: Expose Fluent Community CSS variables (light/dark) to JS.
Requirements:
- Retrieve variables (e.g., `--fcom-primary-color`) from Fluent Community.
- Make available via `wp_localize_script` or inline `<style>`.
- Support both light and dark mode (detect via body class or event).
- Document variable names and structure.
Output: PHP function/class with inline docs.

**Prompt 6: JS - Apply Fluent Community Styles to Vidstack Player**
Role: JS developer with theming expertise.
Goal: Map Fluent Community CSS variables to Vidstack theme variables in the custom element.
Requirements:
- On player init, apply mapped CSS variables.
- Support dynamic light/dark mode switching (MutationObserver or event-driven).
- Use progressive enhancement: fallback to default if variables missing.
- Reference Vidstack theming docs.
Output: JS code for style application and event handling.

**Prompt 7: JS - Replace YouTube/Vimeo iFrames with MQCMP Player**
Role: JS developer.
Goal: Replace detected iframes with `<mqcmp-vidstack-player>`.
Requirements:
- For each iframe, extract video URL, create custom element, set `src`/`poster`.
- Replace in DOM, preserving accessibility.
- Use MutationObserver for dynamic content.
- Call on DOM ready and on new content.
- Modularize for future generalization.
Output: JS function with error handling and comments.

---

## Phase 3: Thumbnail Fetching & Advanced Player Control

**Prompt 8: PHP - Fetch YouTube HD Thumbnail URL**
Role: WP backend developer.
Goal: Provide a function to get the best available YouTube thumbnail.
Requirements:
- Accept video ID or URL, extract ID.
- Check for `maxresdefault.jpg`, fallback as needed.
- Use `get_headers()` for existence check.
- Return best available URL.
- Add error handling and PHPDoc.
Output: PHP function/class.

**Prompt 9: PHP - Fetch Vimeo HD Thumbnail URL**
Role: WP backend developer.
Goal: Provide a function to get the best available Vimeo thumbnail.
Requirements:
- Accept video ID or URL, extract ID.
- Use Vimeo oEmbed API, extract/modify `thumbnail_url` for best size.
- Return best available URL.
- Add error handling and PHPDoc.
Output: PHP function/class.

**Prompt 10: AJAX Endpoint for Thumbnails and Frontend Integration**
Role: Full-stack WP developer.
Goal: AJAX endpoint for thumbnail fetching, JS integration.
Requirements:
- Register AJAX actions for logged-in/out users.
- Accept `video_url` and `video_type`, call correct function.
- Return JSON with thumbnail URL.
- In JS, after replacing iframe, fetch and set poster via AJAX.
- Update custom element to reflect poster.
- Add error handling and comments.
Output: PHP AJAX handler and JS integration code.

**Prompt 11: JS - Ensure Only One Player Plays at a Time**
Role: JS developer with Vidstack event knowledge.
Goal: Only one Vidstack player plays at a time.
Requirements:
- Track currently playing instance.
- On `play` event, pause any other playing instance.
- Use event-driven architecture (custom event bus if needed).
- Add error handling and comments.
Output: JS logic for single-player enforcement.

---

## Phase 4: Generalization, Admin Settings & Refinements

**Prompt 12: Generalize Player Replacement**
Role: JS developer.
Goal: Expand replacement logic to all YouTube/Vimeo iframes, `<video>`, and `<audio>` elements.
Requirements:
- Carefully select elements to avoid unintended replacements.
- For `<video>`/`<audio>`, extract relevant attributes.
- Use custom elements for each type if needed.
- Add setting to control replacement aggressiveness (see admin prompt).
- Modularize and document.
Output: JS code for generalized replacement.

**Prompt 13: Create Basic Admin Settings Page**
Role: WP backend developer.
Goal: Add admin settings page for MQCMP.
Requirements:
- Add submenu or top-level menu.
- Checkbox to enable/disable player replacement.
- Optional custom CSS field.
- Use WP Settings API for saving/retrieving.
- All plugin logic must respect enable/disable setting.
- Add inline docs and security checks.
Output: PHP code for settings page and logic.

**Prompt 14: Refine Light/Dark Mode CSS Variable Application**
Role: JS/CSS developer.
Goal: Ensure seamless Fluent Community style inheritance for Vidstack.
Requirements:
- Inherit all relevant variables (border radius, font, etc.).
- Test and respond to dynamic mode switching (event or MutationObserver).
- Ensure all Vidstack UI states (idle, hover, active) use correct colors.
- Add comments and fallback logic.
Output: JS/CSS for robust style application.

---

## Phase 5: Testing, Final Touches & Documentation

**Prompt 15: Comprehensive Testing Plan**
Role: QA/test engineer.
Goal: Define a thorough testing plan for MQCMP.
Requirements:
- Test all video sources, Fluent Community areas, generic media tags.
- Test all player features, styling, admin settings, browser compatibility, responsiveness, and plugin/theme conflicts.
- Document test cases and expected results.
Output: Written test plan/checklist.

**Prompt 16: Inline Code Documentation and `readme.txt` Update**
Role: Developer/documentation specialist.
Goal: Ensure all code is well-documented and `readme.txt` is complete.
Requirements:
- Add PHPDoc/JSDoc to all code.
- Follow WP coding standards.
- Update `readme.txt` with all required sections and clear feature list.
Output: Code/documentation updates and sample readme sections.

**Prompt 17: Final Review and Packaging**
Role: Release engineer.
Goal: Prepare plugin for release.
Requirements:
- Check for errors/warnings, verify all features, ensure correct file structure.
- List files/folders for ZIP packaging.
- Add basic internationalization (wrap strings in `__()`/`_e()` with `mqcmp` domain).
Output: Release checklist and packaging instructions.

---

This improved set of prompts ensures modular, robust, and maintainable development of the MQCMP plugin, following the hybrid integration approach, WordPress/Vidstack best practices, and advanced prompt structuring for AI code agents.
