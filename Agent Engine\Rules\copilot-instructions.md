# Fluent Community Integration
Mindstate.live/Mlive = Fcom/Fluent Community, same thing
Also use Mlive_Hybrid_Community_integration.md
## Core Architecture
Backend: WP/PHP REST API
- No Vue in admin
- No build tools
- Native WP admin

Frontend: Vue 3 Portal
- Element Plus UI
- Portal API only
- State management

## Integration Matrix

### WP Backend ✅
API:
- REST endpoints
- Auth middleware
- API versioning
- Controllers

Data:
- Models only
- Cache layers
- No direct SQL
- Use relations

Hooks:
- Actions/filters
- Event system
- Error handling
- Priorities

Security:
- Capabilities
- Validation
- CSRF/Nonce
- Sanitize

### Portal Frontend ✅
UI:
- Element Plus
- Portal layouts
- Portal widgets
- Slots

State:
- Vuex store
- Mutations
- Modules
- Persist

Routes:
- Vue Router
- Event bus
- Guards
- Auth

## Prohibited ❌

### Never Use
Templates:
- `get_template_part`
- `locate_template`
- Theme system
- `wp_head/footer`

Data:
- Direct `$wpdb`
- `WP_Query`
- Meta functions
- Raw queries

JS:
- Global scope
- Direct DOM
- jQuery
- Build tools

Security:
- Auth bypass
- Direct DB
- Raw SQL
- Local tokens

## Required ✓

### Must Use
API:
- Portal API
- REST patterns
- Error handling
- Caching

State:
- Vuex only
- Mutations
- Async handlers
- Persistence

Auth:
- Token check
- Role verify
- Route guards
- Capabilities

Performance:
- Lazy load
- Component cache
- API cache
- State clean

## Best Practices

### Code
- Error handle
- Standards

### Security
- Validate input
- Escape output
- CSRF tokens
- Role checks

### Speed
- Cache data
- Optimize
- Lazy load
- Monitor

### Maintain
- Version
- Document
- Log errors

## Remember:
- When prompting with the word "browse" use playwright
- When using Playwright, if the url contains "tribe-access" we need to login first:
    1. Fill in the username with "Amy" in the input field with id "username"
    2. Fill in the password with "$84(Q?.5VF1K<URi" in the input field with id "password"
    3. Click the login button on the button with id "user_registration_ajax_login_submit"
    4. Wait for the splash page to load
- After the splash page loads, the url will change to https://mindstate.live/feed and now we are on Mindstate.live's main feed page
- When prompting with the word "think" use "sequential thinking"

# Mind Qtrl Mindstate.live/fcom Community Integration Plan

This document outlines a comprehensive development plan for creating high-performance, feature-rich WP plugins that integrates with Fluent Community.

## Admin Settings Page

The plugin will include a modern AJAX-powered admin settings page that follows the Mind Qtrl branding and menu structure.

## Fluent Community Hybrid Integration Approach (Recommended)

Based on our analysis of the different integration approaches, we recommend a hybrid approach that combines the strengths of multiple methods for the most robust and maintainable integration with Fluent Community.

#### Recommended Hybrid Integration Method Implementation Strategy:

1. **Use Custom Elements as the core building blocks**
   - Encapsulate functionality in reusable, self-contained components
   - Leverage Shadow DOM for style isolation
   - Create a declarative API for easy configuration

2. **Implement DOM Manipulation with MutationObserver for integration points**
   - Detect when and where to inject components
   - Handle SPA navigation gracefully
   - Ensure components are properly mounted and unmounted

3. **Adopt an Event-Driven architecture for communication**
   - Create a custom event bus for loose coupling
   - Define clear interfaces for component communication
   - Handle Fluent Community events and dispatch custom events

4. **Apply Progressive Enhancement principles**
   - Ensure basic functionality works without JavaScript
   - Add advanced features progressively based on browser support
   - Maintain accessibility throughout

Example implementation of the hybrid approach:

```javascript
// 1. Define Custom Elements
class MQMediaGrid extends HTMLElement {
    // Custom element implementation
}
customElements.define('mq-media-grid', MQMediaGrid);

// 2. Set up MutationObserver for integration
const observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
        if (mutation.type === 'childList') {
            // Check for integration points
            checkForIntegrationPoints();
        }
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

// 3. Create Event Bus
const eventBus = new EventBus();

// 4. Integration function with progressive enhancement
function checkForIntegrationPoints() {
    // Check for profile pages
    const profileHolder = document.querySelector('.fcom_profile_holder');
    if (profileHolder && !profileHolder.hasAttribute('data-mq-enhanced')) {
        // Mark as enhanced to prevent duplicate processing
        profileHolder.setAttribute('data-mq-enhanced', 'true');

        // Get username
        const username = getCurrentUsername();

        // Create and inject the media tab
        injectMediaTab(profileHolder, username);

        // Dispatch event
        eventBus.emit('profile:enhanced', { profileHolder, username });
    }
}

// Helper functions
function injectMediaTab(profileHolder, username) {
    // Implementation details...
}

function getCurrentUsername() {
    // Implementation details...
}

// Initialize on DOM ready with progressive enhancement
document.addEventListener('DOMContentLoaded', () => {
    // Check for existing integration points
    checkForIntegrationPoints();

    // Set up event listeners for Fluent Community events
    setupFluentCommunityEventListeners();
});

function setupFluentCommunityEventListeners() {
    // Listen for navigation events
    document.addEventListener('fcom:route:changed', (e) => {
        // Handle route changes
        setTimeout(checkForIntegrationPoints, 100);
    });

    // Listen for other relevant events
    // ...
}
```

### Integration with Fluent Community's Event System

Fluent Community has its own event system based on Vue's event handling and Vuex state management. Our integration approaches need to work alongside this system without interference.

#### How Event-Driven Architecture Works with Fluent Community:

Fluent Community emits several DOM events during navigation and UI updates that we can leverage:

```javascript
// Examples of Fluent Community events we can intercept
document.addEventListener('fcom:route:changed', (e) => {
  // Route change event with route information
  console.log('Route changed:', e.detail.route);
});

document.addEventListener('fcom:feed:updated', (e) => {
  // Feed update event with new feed data
  console.log('Feed updated:', e.detail);
});

document.addEventListener('fcom:user:loaded', (e) => {
  // User data loaded event
  console.log('User loaded:', e.detail.user);
});
```

Our event bus acts as a bridge between Fluent Community's events and our custom components:

```javascript
// Bridge pattern implementation
function setupEventBridge() {
  // Listen for Fluent Community events
  document.addEventListener('fcom:route:changed', (e) => {
    // Translate to our event system
    eventBus.emit('route:changed', e.detail);

    // Check if we're on a profile page
    if (e.detail.route.name.startsWith('user_profile')) {
      eventBus.emit('profile:loaded', {
        username: e.detail.route.params.username,
        route: e.detail.route
      });
    }
  });

  document.addEventListener('fcom:feed:updated', (e) => {
    // Notify our components about feed updates
    eventBus.emit('media:refresh', {
      source: 'feed_update',
      data: e.detail
    });
  });
}
```

This approach works because we're not trying to replace Fluent Community's event system but rather complement it with our own layer that's specifically designed for our media grid functionality.

#### How Progressive Enhancement Works with Fluent Community:

Progressive enhancement ensures our functionality works across different environments and browser capabilities:

```javascript
// Feature detection for Fluent Community integration
function initializeWithProgressiveEnhancement() {
  // Check if Fluent Community's core is available
  const fluentCommunityAvailable =
    typeof window.FluentCommunityApp !== 'undefined' ||
    typeof window.FluentCommunityUtil !== 'undefined';

  // Check for modern browser features
  const supportsCustomElements = 'customElements' in window;
  const supportsIntersectionObserver = 'IntersectionObserver' in window;

  if (fluentCommunityAvailable) {
    // Full integration with Fluent Community
    setupFluentCommunityIntegration();
  } else {
    // Standalone functionality
    setupStandaloneFunctionality();
  }

  // Apply enhancements based on browser capabilities
  if (supportsCustomElements) {
    registerCustomElements();
  } else {
    useVanillaComponents();
  }

  if (supportsIntersectionObserver) {
    setupInfiniteScroll();
  } else {
    setupPaginationButtons();
  }
}
```

This ensures that our media grid can function in various environments:
- In modern browsers with Fluent Community: Full interactive experience
- In modern browsers without Fluent Community: Standalone functionality
- In older browsers: Basic media viewing capability


## Step-by-Step Development Plan

### Phase 1: Backend API Development

#### Implement Caching and Performance Optimization

```
Develop a caching system to improve performance. This should:
1. Cache API responses for a configurable time period
2. Use object caching for frequent database queries
3. Add proper cache invalidation
```

### Phase 2: Frontend Development


#### Implement Lazy Loading and Infinite Scroll

```
Create an efficient lazy loading and infinite scroll system if applicable. This should:
1. Only load images that are visible in the viewport
2. Use Intersection Observer API for better performance
3. Include smooth loading animations and transitions
4. Handle error states gracefully
```

### Phase 3: Advanced Features

#### Optimize for Performance

```
Optimize for maximum performance. This should:
1. Implement code splitting and lazy component loading
2. Minimize DOM operations and reflows
3. Use requestAnimationFrame for smooth animations
4. Optimize asset loading and delivery
5. Add performance monitoring and reporting
```

### Phase 4: Testing and Refinement

#### Refine User Experience

```
Refine the user experience of the media grid based on testing and feedback. This should:
1. Improve loading states and animations
2. Enhance error handling and user feedback
3. Optimize touch interactions for mobile devices
4. Improve accessibility features
5. Add user preference settings
```

#### Documentation and Deployment

```
Create comprehensive documentation for the media grid and prepare for deployment. This should:
1. Include developer documentation for API endpoints and components
2. Add user documentation for features and settings
3. Create deployment scripts and procedures
4. Implement version control and migration strategies
5. Add monitoring and analytics
```