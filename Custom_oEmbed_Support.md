# Adding Custom oEmbed Providers to Fluent Community Portal Feed

This guide explains how to add support for additional oEmbed providers to the Fluent Community portal feed system. The implementations cover:

- Rumble.com videos
- Facebook videos and reels
- Instagram videos and reels

## How oEmbed Works in Fluent Community

The Fluent Community plugin uses WordPress's core oEmbed functionality with some additional handling for specific providers. When a user pastes a URL in the feed composer, the system:

1. Checks if the URL matches a registered oEmbed provider pattern
2. Sends a request to the provider's oEmbed endpoint
3. Processes the response and stores the data in the feed's metadata
4. Displays the embedded content in the feed

The main components involved are:

- `RemoteUrlParser` class - Handles fetching and processing oEmbed data
- `FeedsController::getOembed()` method - API endpoint for fetching oEmbed data
- `FeedsHelper::processFeedMetaData()` method - Processes metadata including oEmbed content

## Implementation for Rumble.com Videos

### Step 1: Register Rumble as an oEmbed Provider

Add the following code to your plugin or theme's functions.php:

```php
/**
 * Register Rumble.com as an oEmbed provider
 */
function fc_register_rumble_oembed_provider() {
    wp_oembed_add_provider( 
        '#https?://(?:www\.)?rumble\.com/.*#i', 
        'https://rumble.com/api/Media/oembed.json', 
        true 
    );
}
add_action( 'init', 'fc_register_rumble_oembed_provider' );
```

### Step 2: Handle Thumbnail and Provider Information

Some oEmbed providers may not return all needed information, so we can enhance the data:

```php
/**
 * Enhance Rumble oEmbed response data
 */
function fc_enhance_rumble_oembed_data( $data, $url ) {
    // Only process Rumble URLs
    if ( strpos( $url, 'rumble.com' ) !== false && is_object( $data ) ) {
        // Ensure provider is set correctly
        $data->provider_name = 'Rumble';
        
        // If thumbnail URL doesn't exist, try to extract it
        if ( empty( $data->thumbnail_url ) && !empty( $data->html ) ) {
            // Extract thumbnail from iframe src if available
            preg_match( '/src="([^"]+)"/', $data->html, $matches );
            if ( !empty( $matches[1] ) ) {
                $embed_url = $matches[1];
                // Rumble often includes thumbnail info in the URL
                if ( strpos( $embed_url, 'thumbnail=' ) !== false ) {
                    preg_match( '/thumbnail=([^&]+)/', $embed_url, $thumb_matches );
                    if ( !empty( $thumb_matches[1] ) ) {
                        $data->thumbnail_url = urldecode( $thumb_matches[1] );
                    }
                }
            }
        }
    }
    
    return $data;
}
add_filter( 'oembed_result_data', 'fc_enhance_rumble_oembed_data', 10, 2 );
```

## Implementation for Facebook Videos and Reels

Facebook requires an App ID and App Secret for oEmbed API access due to their Graph API restrictions.

### Step 1: Create a Facebook Developer App

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app (Business type)
3. Add the "oEmbed" product to your app
4. Copy your App ID and App Secret

### Step 2: Register Facebook as an oEmbed Provider

```php
/**
 * Register Facebook as an oEmbed provider
 */
function fc_register_facebook_oembed_provider() {
    // Replace with your actual App ID and Secret
    $app_id = 'YOUR_APP_ID';
    $app_secret = 'YOUR_APP_SECRET';
    
    // Register for Facebook videos
    wp_oembed_add_provider( 
        '#https?://(www\.)?facebook\.com/.*/videos/.*#i', 
        'https://graph.facebook.com/v18.0/oembed_video?access_token=' . $app_id . '|' . $app_secret, 
        true 
    );
    
    // Register for Facebook posts (including reels)
    wp_oembed_add_provider( 
        '#https?://(www\.)?facebook\.com/.*/posts/.*#i', 
        'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $app_id . '|' . $app_secret, 
        true 
    );
    
    // Register for Facebook reels
    wp_oembed_add_provider( 
        '#https?://(www\.)?facebook\.com/reel/.*#i', 
        'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $app_id . '|' . $app_secret, 
        true 
    );
}
add_action( 'init', 'fc_register_facebook_oembed_provider' );
```

### Step 3: Process Facebook oEmbed Response

```php
/**
 * Enhance Facebook oEmbed response data
 */
function fc_enhance_facebook_oembed_data( $data, $url ) {
    // Only process Facebook URLs
    if ( strpos( $url, 'facebook.com' ) !== false && is_object( $data ) ) {
        // Set content type based on URL
        if ( strpos( $url, '/videos/' ) !== false ) {
            $data->content_type = 'video';
        } elseif ( strpos( $url, '/reel/' ) !== false ) {
            $data->content_type = 'reel';
        }
        
        // Ensure provider name is set correctly
        $data->provider_name = 'Facebook';
    }
    
    return $data;
}
add_filter( 'oembed_result_data', 'fc_enhance_facebook_oembed_data', 10, 2 );
```

## Implementation for Instagram Videos and Reels

Instagram integration requires a Facebook Developer App similar to Facebook integration.

### Step 1: Use the same Facebook Developer App

Use the same app you created for Facebook integration.

### Step 2: Register Instagram as an oEmbed Provider

```php
/**
 * Register Instagram as an oEmbed provider
 */
function fc_register_instagram_oembed_provider() {
    // Replace with your actual App ID and Secret
    $app_id = 'YOUR_APP_ID';
    $app_secret = 'YOUR_APP_SECRET';
    
    // Register for Instagram posts (including videos)
    wp_oembed_add_provider( 
        '#https?://(www\.)?instagram\.com/p/.*#i', 
        'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $app_id . '|' . $app_secret, 
        true 
    );
    
    // Register for Instagram reels
    wp_oembed_add_provider( 
        '#https?://(www\.)?instagram\.com/reel/.*#i', 
        'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $app_id . '|' . $app_secret, 
        true 
    );
}
add_action( 'init', 'fc_register_instagram_oembed_provider' );
```

### Step 3: Process Instagram oEmbed Response

```php
/**
 * Enhance Instagram oEmbed response data
 */
function fc_enhance_instagram_oembed_data( $data, $url ) {
    // Only process Instagram URLs
    if ( strpos( $url, 'instagram.com' ) !== false && is_object( $data ) ) {
        // Set content type based on URL
        if ( strpos( $url, '/reel/' ) !== false ) {
            $data->content_type = 'reel';
        } elseif ( strpos( $url, '/p/' ) !== false ) {
            // Check if it's a video based on the thumbnail or title
            if ( !empty( $data->thumbnail_url ) && strpos( $data->title, 'video' ) !== false ) {
                $data->content_type = 'video';
            }
        }
        
        // Ensure provider name is set correctly
        $data->provider_name = 'Instagram';
    }
    
    return $data;
}
add_filter( 'oembed_result_data', 'fc_enhance_instagram_oembed_data', 10, 2 );
```

## Creating a Complete Plugin

You can combine all these implementations into a single plugin. Here's a complete plugin structure:

```php
<?php
/**
 * Plugin Name: Fluent Community Custom oEmbed Support
 * Description: Adds oEmbed support for Rumble, Facebook videos/reels, and Instagram videos/reels
 * Version: 1.0.0
 * Author: Your Name
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) exit;

class Fluent_Community_Custom_oEmbed {
    
    // Facebook/Instagram App credentials
    private $fb_app_id = 'YOUR_APP_ID';
    private $fb_app_secret = 'YOUR_APP_SECRET';
    
    public function __construct() {
        // Register oEmbed providers
        add_action( 'init', array( $this, 'register_oembed_providers' ) );
        
        // Enhance oEmbed data
        add_filter( 'oembed_result_data', array( $this, 'enhance_oembed_data' ), 10, 2 );
        add_filter( 'oembed_result', array( $this, 'filter_oembed_html' ), 10, 3 );
        
        // Apply custom media processing to Fluent Community
        add_filter( 'fluent_community/media_provider_urls', array( $this, 'add_custom_provider_urls' ) );
    }
    
    /**
     * Register custom oEmbed providers
     */
    public function register_oembed_providers() {
        // Rumble
        wp_oembed_add_provider( '#https?://(?:www\.)?rumble\.com/.*#i', 'https://rumble.com/api/Media/oembed.json', true );
        
        // Facebook videos
        wp_oembed_add_provider( 
            '#https?://(www\.)?facebook\.com/.*/videos/.*#i', 
            'https://graph.facebook.com/v18.0/oembed_video?access_token=' . $this->fb_app_id . '|' . $this->fb_app_secret, 
            true 
        );
        
        // Facebook posts and reels
        wp_oembed_add_provider( 
            '#https?://(www\.)?facebook\.com/.*/posts/.*#i', 
            'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $this->fb_app_id . '|' . $this->fb_app_secret, 
            true 
        );
        
        wp_oembed_add_provider( 
            '#https?://(www\.)?facebook\.com/reel/.*#i', 
            'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $this->fb_app_id . '|' . $this->fb_app_secret, 
            true 
        );
        
        // Instagram
        wp_oembed_add_provider( 
            '#https?://(www\.)?instagram\.com/p/.*#i', 
            'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $this->fb_app_id . '|' . $this->fb_app_secret, 
            true 
        );
        
        wp_oembed_add_provider( 
            '#https?://(www\.)?instagram\.com/reel/.*#i', 
            'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $this->fb_app_id . '|' . $this->fb_app_secret, 
            true 
        );
    }
    
    /**
     * Enhance oEmbed response data
     */
    public function enhance_oembed_data( $data, $url ) {
        if ( !is_object( $data ) ) {
            return $data;
        }
        
        // Process Rumble
        if ( strpos( $url, 'rumble.com' ) !== false ) {
            $data->provider_name = 'Rumble';
            $data->content_type = 'video';
        }
        
        // Process Facebook
        elseif ( strpos( $url, 'facebook.com' ) !== false ) {
            $data->provider_name = 'Facebook';
            
            if ( strpos( $url, '/videos/' ) !== false ) {
                $data->content_type = 'video';
            } elseif ( strpos( $url, '/reel/' ) !== false ) {
                $data->content_type = 'reel';
            }
        }
        
        // Process Instagram
        elseif ( strpos( $url, 'instagram.com' ) !== false ) {
            $data->provider_name = 'Instagram';
            
            if ( strpos( $url, '/reel/' ) !== false ) {
                $data->content_type = 'reel';
            } elseif ( strpos( $url, '/p/' ) !== false && 
                      (strpos( $data->title, 'video' ) !== false || strpos( $data->html, 'video' ) !== false) ) {
                $data->content_type = 'video';
            }
        }
        
        return $data;
    }
    
    /**
     * Filter oEmbed HTML to ensure it will display properly
     */
    public function filter_oembed_html( $html, $url, $args ) {
        // Make sure videos are responsive
        if ( strpos( $html, '<iframe' ) !== false ) {
            // Add wrapper for responsive embeds if not already present
            if ( strpos( $html, 'class="wp-embed-responsive"' ) === false ) {
                $html = '<div class="wp-embed-responsive">' . $html . '</div>';
            }
            
            // Add loading="lazy" for better performance
            if ( strpos( $html, 'loading=' ) === false ) {
                $html = str_replace( '<iframe', '<iframe loading="lazy"', $html );
            }
        }
        
        return $html;
    }
    
    /**
     * Add custom provider patterns to Fluent Community media provider URLs
     */
    public function add_custom_provider_urls( $provider_urls ) {
        // Add Rumble
        $provider_urls['rumble'] = 'rumble\.com';
        
        // These may already be supported, but ensure they're included
        $provider_urls['facebook_video'] = 'facebook\.com\/.*\/videos\/';
        $provider_urls['facebook_reel'] = 'facebook\.com\/reel\/';
        $provider_urls['instagram_video'] = 'instagram\.com\/p\/';
        $provider_urls['instagram_reel'] = 'instagram\.com\/reel\/';
        
        return $provider_urls;
    }
}

// Initialize the plugin
new Fluent_Community_Custom_oEmbed();
```

## Integration with Fluent Community Code

The plugin above will work with the standard oEmbed system in WordPress. For deeper integration with Fluent Community's feed system, you should also:

1. Make sure the RemoteUrlParser class can handle these new providers
2. Ensure the feed transformation processes the media correctly

## Testing Your Implementation

To test that your implementation works:

1. Install and activate the plugin
2. Try pasting URLs from the supported providers into the feed composer
3. Verify that the embedded content displays correctly in the feed
4. Test different types of content (videos, reels, etc.)

## Security Considerations

When implementing oEmbed support:

1. Always sanitize URLs before processing
2. Use WordPress's built-in oEmbed functions when possible
3. Store API credentials securely
4. Apply appropriate content filters to embedded HTML

## Troubleshooting

If embeds aren't working:

1. Check browser console for JavaScript errors
2. Verify that the oEmbed provider is registered correctly
3. Make sure your Facebook/Instagram App credentials are valid
4. Inspect the raw oEmbed response for any issues
5. Check if the site's Content Security Policy blocks embedded frames
