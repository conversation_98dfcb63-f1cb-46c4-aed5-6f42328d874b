/**
 * Admin CSS for Mind Qtrl | UI Template
 * This file implements the Modern dark Mind Qtrl UI template for the admin interface.
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.0
 */

/* Modern dark Mind Qtrl UI template */

.toplevel_page_mind-qtrl-community-options #wpwrap {
    background: #1B1B1E !important;
}

.mqco-logo {
    width: 100px;
    height: 100px;
    margin-bottom: 20px;
}

.mqco-admin-wrap {
    background: #1e1e2d;
    color: #ffffff;
    padding: 30px;
    border-radius: 12px;
    margin: 20px 20px 20px 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

/* Tab Navigation */
.mqco-admin-tabs {
    margin-bottom: 30px;
}

input[type="text"], input[type="password"], input[type="date"], input[type="datetime"], input[type="datetime-local"], input[type="email"], input[type="month"], input[type="number"], input[type="search"], input[type="tel"], input[type="time"], input[type="url"], input[type="week"] {
    background: #1e1e2d;
    border: 1px solid #333344;
    color: #ffffff;
}

.mqco-tabs-nav {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0 auto;
    border-bottom: 2px solid #333344;
    justify-content: flex-start;
    gap: 10px;
}

.mqco-tabs-nav li {
    padding: 12px 24px;
    cursor: pointer;
    background: #2a2a3c;
    border-radius: 8px 8px 0 0;
    border: 2px solid transparent;
    border-bottom: none;
    color: #8770FF;
    transition: all 0.3s ease;
    font-weight: 500;
    margin-bottom: 0px;
}

.mqco-tabs-nav li:hover {
    background: #333344;
    transform: translateY(-2px);
}

.mqco-tabs-nav li.mqco-tab-active {
    background: #8770FF;
    color: #ffffff;
    border-color: #8770FF;
    box-shadow: 0 4px 8px rgba(135, 112, 255, 0.3);
}

/* Tab Content */
.mqco-tab-content {
    display: none;
    padding: 30px 0;
}

.mqco-tab-content.mqco-tab-active {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* Debug Log Styling */
.mqco-debug-log-container {
    background: #2a2a3c;
    padding: 24px;
    border-radius: 8px;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2);
}

.mqco-log-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
}

.mqco-log-controls .button {
    margin-right: 0;
    background: #8770FF;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mqco-log-controls .button:hover {
    background: #7560EE;
    border-color: #7560EE;
}

.mqco-log-viewer {
    background: #1e1e2d;
    border: 1px solid #333344;
    border-radius: 4px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.mqco-log-content {
    font-family: monospace;
    white-space: pre-wrap;
    margin: 0;
    color: #e0e0e0;
    font-size: 13px;
    line-height: 1.5;
}

.mqco-admin-wrap h1 {
    color: #8770FF;
    font-size: 24px;
    margin-bottom: 20px;
    max-width: 335px;
}

.mqco-admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 10px;
}

.mqco-title-container {
    display: flex;
    align-items: center;
}

.mqco-debug-log-container h2 {
    color: #8770FF;
}

.mqco-logo img {
    max-height: 100px;
}

.mqco-version {
    color: #8770FF;
    font-size: 14px;
    font-style: italic;
}

.mqco-admin-content {
    display: flex;
    gap: 30px;
}

.mqco-admin-sidebar {
    flex: 0 0 300px;
}

.mqco-admin-main {
    flex: 1;
    background: #2a2a3c;
    padding: 20px;
    border-radius: 6px;
}

.mqco-sidebar-section {
    background: #2a2a3c;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 6px;
}

.mqco-sidebar-section h3 {
    color: #8770FF;
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #333344;
}

.mqco-sidebar-section a {
    color: #8770FF;
    text-decoration: none;
}

.mqco-sidebar-section a:hover {
    text-decoration: underline;
}

/* Form styling */
.mqco-settings-form h2 {
    color: #8770FF;
    font-size: 18px;
    margin: 30px 0 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333344;
}

.mqco-settings-form .form-table {
    background: #2a2a3c;
    border-collapse: collapse;
}

.mqco-settings-form .form-table th {
    color: #ffffff;
    font-weight: 500;
    padding: 15px;
}

.mqco-settings-form .form-table td {
    padding: 15px;
}

.mqco-settings-form input[type="text"],
.mqco-settings-form select {
    background: #1e1e2d;
    border: 1px solid #333344;
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    width: 100%;
    max-width: 300px;
}

.mqco-settings-form input[type="checkbox"] {
    border: 1px solid #333344;
    background: #1e1e2d;
}

.mqco-settings-form .description {
    color: #aaaabc;
    font-style: italic;
    margin-top: 5px;
}

/* Color picker */
.wp-picker-container .wp-color-result.button {
    background: #1e1e2d;
    border-color: #333344;
}

.wp-picker-container .wp-color-result-text {
    background: #2a2a3c;
    color: #ffffff;
}

/* Submit button */
.mqco-settings-form .button-primary {
    background: #8770FF;
    border-color: #6550dd;
    color: #ffffff;
    padding: 8px 20px;
    height: auto;
    font-size: 14px;
    transition: all 0.3s ease;
}

.mqco-settings-form .button-primary:hover,
.mqco-settings-form .button-primary:focus {
    background: #6550dd;
    border-color: #5440cc;
}

.mqco-admin-footer {
    margin-top: 30px;
    text-align: center;
    color: #aaaabc;
    font-style: italic;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .mqco-admin-content {
        flex-direction: column;
    }
    
    .mqco-admin-sidebar {
        flex: 0 0 100%;
    }
}


/* Welcome text editor styles */
.mqco-welcome-text-editor {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 1px solid #333344;
    border-radius: 6px;
    background: #1e1e2d;
    color: #ffffff;
}

/* Sidebar position selector styles */
.mqco-sidebar-position-selector {
    display: flex;
    gap: 15px;
    margin: 20px 0;
}

.mqco-sidebar-position-selector label {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Number input container styles */
.mqco-number-input-container {
    display: flex;
    align-items: center;
    max-width: 150px;
}

/* Number input styles */
.mqco-number-input {
    width: 60px;
    text-align: center;
    border: 1px solid #7e8993;
    border-radius: 4px;
    padding: 0 8px;
    height: 30px;
    margin: 0 5px;
}

/* Plus/minus button styles */
.mqco-number-input-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: #8770FF;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    transition: background-color 0.3s ease;
}

.mqco-number-input-btn:hover {
    background: #6550dd;
}

.mqco-number-input-btn:focus {
    outline: none;
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px #8770FF;
}