<?php

class Mind_QTRL_Community_Feed_Bot_Admin {
    private $plugin_name;
    private $version;
    private $settings;
    private $space_manager;

    /**
     * Initialize the class and set its properties.
     */
    public function __construct($plugin_name, $version, $settings) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;
        
        // Load dependencies
        require_once MQCFB_PLUGIN_DIR . 'admin/class-mind-qtrl-community-feed-bot-settings.php';
        require_once MQCFB_PLUGIN_DIR . 'includes/class-space-manager.php';
        require_once MQCFB_PLUGIN_DIR . 'includes/class-feed-handler.php';
        
        $this->settings = new Mind_QTRL_Community_Feed_Bot_Settings();
        $this->space_manager = new MQCFB_Space_Manager();

        // Add AJAX handlers
        add_action('wp_ajax_mqcfb_get_feed', array($this, 'get_feed'));
        add_action('wp_ajax_mqcfb_save_feed', array($this, 'save_feed'));
        add_action('wp_ajax_mqcfb_get_spaces', array($this, 'get_spaces'));
        add_action('wp_ajax_mqcfb_get_space_topics', array($this, 'get_space_topics'));
        add_action('wp_ajax_mqcfb_get_authors', array($this, 'get_authors'));
    }

    /**
     * Register the stylesheets for the admin area.
     */
    public function enqueue_styles() {
        $screen = get_current_screen();
        if ($screen && strpos($screen->id, $this->plugin_name) !== false) {
            wp_enqueue_style(
                $this->plugin_name,
                plugin_dir_url(__FILE__) . 'css/admin.css',
                array(),
                $this->version
            );
            
            // Add the UI enhancements CSS
            wp_enqueue_style(
                $this->plugin_name . '-ui-enhancements',
                plugin_dir_url(__FILE__) . 'css/ui-enhancements.css',
                array($this->plugin_name),
                $this->version
            );
        }
    }

    /**
     * Register the JavaScript for the admin area.
     */
    public function enqueue_scripts() {
        $screen = get_current_screen();
        if ($screen && strpos($screen->id, $this->plugin_name) !== false) {
            wp_enqueue_script(
                $this->plugin_name,
                plugin_dir_url(__FILE__) . 'js/mind-qtrl-community-feed-bot-admin.js',
                array('jquery'),
                $this->version,
                false
            );
            
            // Add localization for the admin script
            wp_localize_script($this->plugin_name, 'mqcfb_admin', array(
                'nonce' => wp_create_nonce('mqcfb-admin-nonce'),
                'ajax_url' => admin_url('admin-ajax.php'),
                'fluent_community_url' => admin_url('admin.php?page=fluent-community'),
                'strings' => array(
                    'confirm_delete' => __('Are you sure you want to delete this feed?', 'mind-qtrl-community-feed-bot'),
                    'error_loading' => __('Error loading data', 'mind-qtrl-community-feed-bot'),
                    'error_saving' => __('Error saving data', 'mind-qtrl-community-feed-bot'),
                    'success_saved' => __('Settings saved successfully', 'mind-qtrl-community-feed-bot')
                )
            ));

            wp_localize_script($this->plugin_name, 'mqcfb_admin_vars', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('mqcfb_admin_nonce'),
                'add_feed_text' => __('Add New Feed', 'mind-qtrl-community-feed-bot'),
                'edit_feed_text' => __('Edit Feed', 'mind-qtrl-community-feed-bot'),
                'select_topics_text' => __('Select topics', 'mind-qtrl-community-feed-bot'),
                'feed_load_error' => __('Could not load feed data', 'mind-qtrl-community-feed-bot'),
                'ajax_error' => __('Server error occurred', 'mind-qtrl-community-feed-bot')
            ));
        }
    }

    /**
     * Add the options page to the admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Community Feed Bot', 'mind-qtrl-community-feed-bot'),
            __('Feed Bot', 'mind-qtrl-community-feed-bot'),
            'manage_options',
            $this->plugin_name,
            array($this, 'display_admin_page'),
            'dashicons-rss',
            30
        );
    }

    /**
     * Display the admin page content
     */
    public function display_admin_page() {
        // Ensure we're using the correct get_feeds() method with proper column names
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-mind-qtrl-community-feed-bot-settings.php';
        $settings = new Mind_QTRL_Community_Feed_Bot_Settings();
        
        // Include the admin display template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/admin-display.php';
    }

    /**
     * Register the REST API routes
     */
    public function register_rest_routes() {
        register_rest_route('mqcfb/v1', '/spaces', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_spaces_endpoint'),
            'permission_callback' => function () {
                return current_user_can('manage_options');
            }
        ));

        register_rest_route('mqcfb/v1', '/topics/(?P<space_id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_topics_endpoint'),
            'permission_callback' => function () {
                return current_user_can('manage_options');
            }
        ));
    }

    /**
     * REST API endpoint to get spaces
     */
    public function get_spaces_endpoint() {
        $spaces = $this->space_manager->get_spaces();
        return rest_ensure_response($spaces);
    }

    /**
     * REST API endpoint to get topics for a space
     */
    public function get_topics_endpoint($request) {
        $space_id = $request['space_id'];
        $topics = $this->space_manager->get_space_topics($space_id);
        return rest_ensure_response($topics);
    }

    /**
     * AJAX handler for getting Fluent Community spaces
     */
    public function ajax_get_spaces() {
        // Check permissions and nonce
        if (!current_user_can('manage_options') || 
            !check_ajax_referer('mqcfb-admin-nonce', 'nonce', false)) {
            wp_send_json_error('Unauthorized');
        }
        
        $spaces = $this->space_manager->get_spaces();
        wp_send_json_success($spaces);
    }

    /**
     * AJAX handler for getting topics for a space
     */
    public function ajax_get_topics() {
        // Check permissions and nonce
        if (!current_user_can('manage_options') || 
            !check_ajax_referer('mqcfb-admin-nonce', 'nonce', false)) {
            wp_send_json_error('Unauthorized');
        }
        
        $space_id = isset($_POST['space_id']) ? intval($_POST['space_id']) : 0;
        if (!$space_id) {
            wp_send_json_error('Invalid space ID');
        }
        
        $topics = $this->space_manager->get_space_topics($space_id);
        wp_send_json_success($topics);
    }

    /**
     * AJAX handler for getting community users
     */
    public function ajax_get_users() {
        // Check permissions and nonce
        if (!current_user_can('manage_options') || 
            !check_ajax_referer('mqcfb-admin-nonce', 'nonce', false)) {
            wp_send_json_error('Unauthorized');
        }
        
        $users = $this->space_manager->get_community_users();
        wp_send_json_success($users);
    }

    /**
     * AJAX handler to get a single feed's data
     */
    public function get_feed() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'mind-qtrl-community-feed-bot')]);
        }
        
        if (!isset($_GET['feed_id'])) {
            wp_send_json_error(['message' => __('Missing feed ID', 'mind-qtrl-community-feed-bot')]);
        }
        
        $feed_id = intval($_GET['feed_id']);
        $feed = $this->settings->get_feed($feed_id);
        
        if (!$feed) {
            wp_send_json_error(['message' => __('Feed not found', 'mind-qtrl-community-feed-bot')]);
        }
        
        wp_send_json_success($feed);
    }

    /**
     * AJAX handler to save a feed
     */
    public function save_feed() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'mind-qtrl-community-feed-bot')]);
        }
        
        // Parse form data
        parse_str($_POST['feed_data'], $feed_data);
        
        $feed = [
            'id' => isset($feed_data['feed_id']) ? intval($feed_data['feed_id']) : 0,
            'name' => sanitize_text_field($feed_data['feed_name']),
            'feed_url' => esc_url_raw($feed_data['feed_url']),
            'space_id' => intval($feed_data['space_id']),
            'author_id' => intval($feed_data['author_id']),
            'max_items' => intval($feed_data['max_items']),
            'image_handling' => sanitize_text_field($feed_data['image_handling']),
            'status' => sanitize_text_field($feed_data['feed_status']),
            'auto_post' => isset($feed_data['auto_post']) ? 'yes' : 'no',
            'selected_topics' => isset($feed_data['topic_ids']) ? array_map('intval', (array)$feed_data['topic_ids']) : []
        ];
        
        $result = $this->settings->save_feed($feed);
        
        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        } else {
            wp_send_json_success([
                'message' => $feed['id'] ? __('Feed updated successfully', 'mind-qtrl-community-feed-bot') : __('Feed created successfully', 'mind-qtrl-community-feed-bot'),
                'feed_id' => $result
            ]);
        }
    }

    /**
     * AJAX handler to get Fluent Community spaces
     */
    public function get_spaces() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'mind-qtrl-community-feed-bot')]);
        }
        
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Models\Space')) {
            wp_send_json_error(['message' => __('Fluent Community plugin is not active', 'mind-qtrl-community-feed-bot')]);
        }
        
        try {
            $spaces = \FluentCommunity\App\Models\Space::select(['id', 'title', 'slug'])
                ->where('status', 'published')
                ->orderBy('title', 'ASC')
                ->get();
                
            wp_send_json_success($spaces);
        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * AJAX handler to get space topics
     */
    public function get_space_topics() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'mind-qtrl-community-feed-bot')]);
        }
        
        if (!isset($_GET['space_id'])) {
            wp_send_json_error(['message' => __('Missing space ID', 'mind-qtrl-community-feed-bot')]);
        }
        
        $space_id = intval($_GET['space_id']);
        
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Models\Term')) {
            wp_send_json_error(['message' => __('Fluent Community plugin is not active', 'mind-qtrl-community-feed-bot')]);
        }
        
        try {
            // Fetch topics for the specified space
            $topics = \FluentCommunity\App\Functions\Utility::getTopicsBySpaceId($space_id);
            
            if (!$topics || !is_array($topics)) {
                $topics = [];
            }
            
            wp_send_json_success($topics);
        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * AJAX handler to get WordPress authors
     */
    public function get_authors() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'mind-qtrl-community-feed-bot')]);
        }
        
        $authors = get_users([
            'role__in' => ['administrator', 'editor', 'author'],
            'orderby' => 'display_name',
            'order' => 'ASC',
            'fields' => ['ID', 'display_name']
        ]);
        
        wp_send_json_success($authors);
    }
}