<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class for admin settings and database operations
 */
class Mind_QTRL_Community_Feed_Bot_Settings {
    private $feeds_table;
    private $queue_table;

    public function __construct() {
        global $wpdb;
        $this->feeds_table = $wpdb->prefix . 'mqcfb_feeds';
        $this->queue_table = $wpdb->prefix . 'mqcfb_article_queue';
        
        // Ensure tables have necessary columns
        $this->maybe_add_missing_columns();
    }

    /**
     * Add missing columns to database tables if needed
     */
    private function maybe_add_missing_columns() {
        global $wpdb;
        
        // Check if auto_post column exists
        $auto_post_column = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'auto_post'",
            DB_NAME,
            $this->feeds_table
        ));
        
        // Add auto_post column if it doesn't exist
        if (empty($auto_post_column)) {
            $wpdb->query("ALTER TABLE {$this->feeds_table} ADD COLUMN auto_post tinyint(1) DEFAULT 0 AFTER status");
        }
        
        // Check if connection_status column exists
        $connection_status_column = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'connection_status'",
            DB_NAME,
            $this->feeds_table
        ));
        
        // Add connection_status column if it doesn't exist
        if (empty($connection_status_column)) {
            $wpdb->query("ALTER TABLE {$this->feeds_table} ADD COLUMN connection_status varchar(50) DEFAULT 'unknown' AFTER auto_post");
        }
        
        // Check if selected_topics column exists in feeds table
        $selected_topics_column = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'selected_topics'",
            DB_NAME,
            $this->feeds_table
        ));
        
        // Add selected_topics column if it doesn't exist in feeds table
        if (empty($selected_topics_column)) {
            $wpdb->query("ALTER TABLE {$this->feeds_table} ADD COLUMN selected_topics text DEFAULT NULL");
        }

        // Check if item_guid column exists in the queue table
        $item_guid_column = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'item_guid'",
            DB_NAME,
            $this->queue_table
         ));
        
        // Add item_guid column if it doesn't exist in queue table
        if (empty($item_guid_column)) {
            $wpdb->query("ALTER TABLE {$this->queue_table} ADD COLUMN item_guid varchar(255) DEFAULT NULL AFTER feed_id");
        }
        
        // Check if selected_topics column exists in queue table
        $queue_topics_column = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'selected_topics'",
            DB_NAME,
            $this->queue_table
        ));
        
        // Add selected_topics column if it doesn't exist in queue table
        if (empty($queue_topics_column)) {
            $wpdb->query("ALTER TABLE {$this->queue_table} ADD COLUMN selected_topics text DEFAULT NULL");
        }
    }

    /**
     * Save a feed to the database
     * 
     * @param array $feed_data Feed data
     * @return int|WP_Error Feed ID or error
     */
    public function save_feed($feed_data) {
        global $wpdb;
        
        // Add better validation for required fields
        if (empty($feed_data['space_id'])) {
            return new WP_Error('missing_space', __('Space ID is required', 'mind-qtrl-community-feed-bot'));
        }
        
        if (empty($feed_data['feed_url'])) {
            return new WP_Error('missing_url', __('Feed URL is required', 'mind-qtrl-community-feed-bot'));
        }
        
        // Validate the URL format
        if (!filter_var($feed_data['feed_url'], FILTER_VALIDATE_URL)) {
            return new WP_Error('invalid_url', __('Please enter a valid URL', 'mind-qtrl-community-feed-bot'));
        }

        // Verify feed is accessible and valid
        try {
            $feed = fetch_feed($feed_data['feed_url']);
            if (is_wp_error($feed)) {
                return new WP_Error('invalid_feed', __('Could not connect to feed: ', 'mind-qtrl-community-feed-bot') . $feed->get_error_message());
            }
        } catch (Exception $e) {
            // Log but don't block - some valid feeds might still cause errors on preview
            error_log('Feed validation warning: ' . $e->getMessage());
        }
        
        // Prepare data with proper validation and sanitation
        $data = array(
            'name' => isset($feed_data['name']) ? sanitize_text_field($feed_data['name']) : 
                      (isset($feed_data['feed_name']) ? sanitize_text_field($feed_data['feed_name']) : ''),
            'feed_url' => esc_url_raw($feed_data['feed_url']),
            'space_id' => absint($feed_data['space_id']),
            'status' => isset($feed_data['status']) ? sanitize_text_field($feed_data['status']) : 
                       (isset($feed_data['feed_status']) ? sanitize_text_field($feed_data['feed_status']) : 'active')
        );
        
        // Handle auto_post setting
        $data['auto_post'] = isset($feed_data['auto_post']) && 
                             ($feed_data['auto_post'] === true || 
                              $feed_data['auto_post'] === 'true' || 
                              $feed_data['auto_post'] === '1' || 
                              $feed_data['auto_post'] === 1) ? 1 : 0;
        
        // Process selected topics - handle multiple format possibilities
        if (isset($feed_data['selected_topics'])) {
            if (is_array($feed_data['selected_topics'])) {
                $data['selected_topics'] = serialize(array_map('absint', $feed_data['selected_topics']));
            } elseif (is_string($feed_data['selected_topics']) && !empty($feed_data['selected_topics'])) {
                // For JSON string case - frontend might send JSON
                $topics = json_decode($feed_data['selected_topics'], true);
                if (is_array($topics)) {
                    $data['selected_topics'] = serialize(array_map('absint', $topics));
                } else {
                    $data['selected_topics'] = $feed_data['selected_topics']; // Already serialized
                }
            }
        }
        
        // Handle author ID
        if (isset($feed_data['author_id'])) {
            $data['author_id'] = absint($feed_data['author_id']);
        }
        
        // Handle max items
        if (isset($feed_data['max_items'])) {
            $data['max_items'] = absint($feed_data['max_items']);
            if ($data['max_items'] < 1) {
                $data['max_items'] = 10; // Default to 10 if invalid
            }
        }
        
        // Handle image handling
        if (isset($feed_data['image_handling'])) {
            $valid_image_handling = array('embed', 'featured', 'none');
            $data['image_handling'] = in_array($feed_data['image_handling'], $valid_image_handling) ? 
                $feed_data['image_handling'] : 'embed';
        }
        
        // Update existing feed or create new one
        if (!empty($feed_data['id'])) {
            $feed_id = absint($feed_data['id']);
            
            // Check if feed exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->feeds_table} WHERE id = %d",
                $feed_id
            ));
            
            if ($exists) {
                // Update existing feed
                $result = $wpdb->update(
                    $this->feeds_table,
                    $data,
                    array('id' => $feed_id),
                    null,
                    array('%d')
                );
                
                if ($result !== false) {
                    return $feed_id;
                } else {
                    return new WP_Error('update_failed', __('Failed to update feed', 'mind-qtrl-community-feed-bot'));
                }
            }
        }
        
        // Create new feed
        $data['created_at'] = current_time('mysql');
        $data['updated_at'] = current_time('mysql');
        
        $result = $wpdb->insert($this->feeds_table, $data);
        
        if ($result) {
            return $wpdb->insert_id;
        } else {
            return new WP_Error('insert_failed', __('Failed to create feed', 'mind-qtrl-community-feed-bot'));
        }
    }

    /**
     * Get all feeds
     * 
     * @return array Feeds
     */
    public function get_feeds() {
        global $wpdb;
        
        $feeds = $wpdb->get_results(
            "SELECT * FROM {$this->feeds_table} ORDER BY name ASC",
            ARRAY_A
        );
        
        if (!$feeds) {
            return array();
        }
        
        // Process feeds to ensure proper data format
        foreach ($feeds as &$feed) {
            // Unserialize selected topics
            if (!empty($feed['selected_topics'])) {
                $feed['selected_topics'] = maybe_unserialize($feed['selected_topics']);
            } else {
                $feed['selected_topics'] = array();
            }
            
            // Ensure auto_post is boolean for JS
            $feed['auto_post'] = !empty($feed['auto_post']);
        }
        
        return $feeds;
    }

    /**
     * Get a specific feed
     * 
     * @param int $feed_id Feed ID
     * @return array|false Feed data or false if not found
     */
    public function get_feed($feed_id) {
        global $wpdb;
        
        $feed = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$this->feeds_table} WHERE id = %d",
                $feed_id
            ),
            ARRAY_A
        );
        
        if (!$feed) {
            return false;
        }
        
        // Unserialize selected topics
        if (!empty($feed['selected_topics'])) {
            $feed['selected_topics'] = maybe_unserialize($feed['selected_topics']);
        } else {
            $feed['selected_topics'] = array();
        }
        
        // Ensure auto_post is boolean for JS
        $feed['auto_post'] = !empty($feed['auto_post']);
        
        return $feed;
    }

    /**
     * Delete a feed
     * 
     * @param int $feed_id Feed ID
     * @return bool Success or failure
     */
    public function delete_feed($feed_id) {
        global $wpdb;
        
        // Delete feed
        $result = $wpdb->delete(
            $this->feeds_table,
            array('id' => $feed_id),
            array('%d')
        );
        
        if ($result) {
            // Also delete any queued articles for this feed
            $wpdb->delete(
                $this->queue_table,
                array('feed_id' => $feed_id),
                array('%d')
            );
            
            return true;
        }
        
        return false;
    }

    /**
     * Update feed connection status
     * 
     * @param int $feed_id Feed ID
     * @param string $status Connection status
     * @return bool Success or failure
     */
    public function update_feed_connection_status($feed_id, $status) {
        global $wpdb;
        
        $valid_statuses = array('connected', 'disconnected', 'unknown');
        if (!in_array($status, $valid_statuses)) {
            $status = 'unknown';
        }
        
        $result = $wpdb->update(
            $this->feeds_table,
            array('connection_status' => $status),
            array('id' => $feed_id),
            array('%s'),
            array('%d')
        );
        
        return $result !== false;
    }

    /**
     * Get articles from the queue
     * 
     * @param array $args Query arguments
     * @return array Articles
     */
    public function get_queued_articles($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'status' => 'pending',
            'feed_id' => 0,
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        // Build query
        $query = "SELECT q.*, f.name as feed_name, f.space_id 
                 FROM {$this->queue_table} q 
                 LEFT JOIN {$this->feeds_table} f ON q.feed_id = f.id 
                 WHERE 1=1";
        
        $prepare = array();
        
        // Add status filter
        if (!empty($args['status'])) {
            $query .= " AND q.status = %s";
            $prepare[] = $args['status'];
        }
        
        // Add feed filter
        if (!empty($args['feed_id'])) {
            $query .= " AND q.feed_id = %d";
            $prepare[] = $args['feed_id'];
        }
        
        // Add order
        $query .= " ORDER BY q." . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);
        
        // Add limit
        $query .= " LIMIT %d OFFSET %d";
        $prepare[] = $args['limit'];
        $prepare[] = $args['offset'];
        
        // Prepare and execute query
        if (!empty($prepare)) {
            $query = $wpdb->prepare($query, $prepare);
        }
        
        $articles = $wpdb->get_results($query, ARRAY_A);
        
        if (!$articles) {
            return array();
        }
        
        // Process articles
        foreach ($articles as &$article) {
            // Unserialize selected topics
            if (!empty($article['selected_topics'])) {
                $article['selected_topics'] = maybe_unserialize($article['selected_topics']);
            } else {
                $article['selected_topics'] = array();
            }
        }
        
        return $articles;
    }
    
    /**
     * Get article count by status
     * 
     * @param string $status Article status
     * @return int Count
     */
    public function get_article_count($status = '') {
        global $wpdb;
        
        $query = "SELECT COUNT(*) FROM {$this->queue_table}";
        $prepare = array();
        
        if (!empty($status)) {
            $query .= " WHERE status = %s";
            $prepare[] = $status;
        }
        
        if (!empty($prepare)) {
            $query = $wpdb->prepare($query, $prepare);
        }
        
        return $wpdb->get_var($query);
    }
    
    /**
     * Update article status
     * 
     * @param int $article_id Article ID
     * @param string $status New status
     * @return bool Success or failure
     */
    public function update_article_status($article_id, $status) {
        global $wpdb;
        
        $valid_statuses = array('pending', 'posted', 'failed', 'skipped');
        if (!in_array($status, $valid_statuses)) {
            return false;
        }
        
        $data = array('status' => $status);
        
        // Add posted time if status is 'posted'
        if ($status === 'posted') {
            $data['posted_time'] = current_time('mysql');
        }
        
        $result = $wpdb->update(
            $this->queue_table,
            $data,
            array('id' => $article_id),
            null,
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Delete article from queue
     * 
     * @param int $article_id Article ID
     * @return bool Success or failure
     */
    public function delete_article($article_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->queue_table,
            array('id' => $article_id),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get plugin settings
     * 
     * @return array Plugin settings
     */
    public function get_plugin_settings() {
        $defaults = array(
            'schedule_frequency' => 'hourly',
            'max_items_per_feed' => 10,
            'default_post_format' => 'full',
            'default_image_handling' => 'embed',
            'remove_data_on_uninstall' => false,
            'auto_post' => false,
            'auto_post_delay' => 0,
            'debug_mode' => false
        );
        
        $settings = get_option('mqcfb_settings', array());
        
        return wp_parse_args($settings, $defaults);
    }
    
    /**
     * Save plugin settings
     * 
     * @param array $settings Settings to save
     * @return bool Success or failure
     */
    public function save_plugin_settings($settings) {
        // Validate settings
        $validated = array();
        
        // Validate schedule frequency
        $valid_frequencies = array('five_minutes', 'ten_minutes', 'fifteen_minutes', 'thirty_minutes', 'hourly', 'twice_daily', 'daily');
        $validated['schedule_frequency'] = in_array($settings['schedule_frequency'], $valid_frequencies) ? 
            $settings['schedule_frequency'] : 'hourly';
        
        // Validate max items per feed
        $validated['max_items_per_feed'] = absint($settings['max_items_per_feed']);
        if ($validated['max_items_per_feed'] < 1) {
            $validated['max_items_per_feed'] = 10;
        }
        
        // Validate post format
        $valid_formats = array('full', 'excerpt', 'title_only');
        $validated['default_post_format'] = in_array($settings['default_post_format'], $valid_formats) ? 
            $settings['default_post_format'] : 'full';
        
        // Validate image handling
        $valid_image_handling = array('embed', 'featured', 'none');
        $validated['default_image_handling'] = in_array($settings['default_image_handling'], $valid_image_handling) ? 
            $settings['default_image_handling'] : 'embed';
        
        // Validate boolean settings
        $validated['remove_data_on_uninstall'] = !empty($settings['remove_data_on_uninstall']);
        $validated['auto_post'] = !empty($settings['auto_post']);
        $validated['debug_mode'] = !empty($settings['debug_mode']);
        
        // Validate auto post delay
        $validated['auto_post_delay'] = absint($settings['auto_post_delay']);
        
        // Save settings
        return update_option('mqcfb_settings', $validated);
    }
}