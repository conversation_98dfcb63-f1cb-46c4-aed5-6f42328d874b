/* Main Admin Styles for Mind QTRL Community Feed Bot */

/* General <PERSON><PERSON> Styles */
.mqcfb-admin {
    max-width: 1200px;
    margin: 20px auto;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.mqcfb-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.mqcfb-logo {
    width: 50px;
    height: auto;
    margin-right: 15px;
}

/* Stats Bar */
.mqcfb-stats {
    display: flex;
    justify-content: center;
    background: #fff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mqcfb-stat-item {
    text-align: center;
    padding: 0 20px;
    border-right: 1px solid #eee;
    min-width: 120px;
}

.mqcfb-stat-item:last-child {
    border-right: none;
}

.mqcfb-stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.mqcfb-stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Tabs */
.mqcfb-tabs {
    display: flex;
    background: #fff;
    border-radius: 4px 4px 0 0;
    overflow: hidden;
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mqcfb-tab {
    padding: 15px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #555;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.mqcfb-tab:hover {
    background-color: #f9f9f9;
    color: #0073aa;
}

.mqcfb-tab.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
    background-color: #f9f9f9;
}

/* Content Areas */
.mqcfb-content {
    display: none;
    background: #fff;
    padding: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.mqcfb-content.active {
    display: block;
}

/* Section Headers */
.mqcfb-feeds-header,
.mqcfb-preview-header,
.mqcfb-queue-header,
.mqcfb-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* Tables */
.mqcfb-table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 10px;
}

.mqcfb-table th {
    text-align: left;
    padding: 12px;
    background-color: #f5f5f5;
    font-weight: 600;
}

.mqcfb-table td {
    padding: 12px;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
}

/* Feed Rows */
.mqcfb-feed-row {
    transition: background-color 0.2s ease;
}

.mqcfb-clickable-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mqcfb-clickable-row:hover {
    background-color: #f9f9f9;
}

/* Feed Status Indicators */
.mqcfb-connection-indicator {
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle;
}

.mqcfb-status-light {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.mqcfb-status-light.connected {
    background-color: #46b450;
}

.mqcfb-status-light.disconnected {
    background-color: #dc3232;
}

.mqcfb-status-light.unknown {
    background-color: #ffb900;
}

.mqcfb-feed-header {
    display: inline-block;
    vertical-align: middle;
}

.mqcfb-feed-title {
    font-weight: 500;
    margin-right: 8px;
}

.mqcfb-status-badge {
    display: inline-block;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: 500;
}

.mqcfb-status-badge.active {
    background-color: #e7f7ed;
    color: #2a8f37;
}

.mqcfb-status-badge.paused {
    background-color: #f8f4e3;
    color: #b7a03f;
}

/* Feed URL */
.mqcfb-feed-url-link {
    color: #0073aa;
    text-decoration: none;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

.mqcfb-feed-url-link:hover {
    color: #00a0d2;
    text-decoration: underline;
}

/* Topic Count */
.topic-count {
    display: inline-block;
    padding: 3px 8px;
    background-color: #e7f5fa;
    color: #0073aa;
    border-radius: 3px;
    font-size: 12px;
}

.no-topics {
    color: #888;
    font-style: italic;
    font-size: 12px;
}

/* Action Buttons */
.mqcfb-icon-button {
    background: none;
    border: none;
    color: #72777c;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.mqcfb-icon-button:hover {
    color: #0073aa;
    background-color: #f0f0f0;
}

.mqcfb-delete-feed:hover {
    color: #dc3232;
}

.mqcfb-action-cell {
    text-align: right;
}

.mqcfb-action-cell .mqcfb-icon-button {
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

.mqcfb-clickable-row:hover .mqcfb-action-cell .mqcfb-icon-button {
    opacity: 1;
}

/* Preview Grid */
.mqcfb-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.mqcfb-preview-card {
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mqcfb-preview-card:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.mqcfb-preview-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    background-color: #f5f5f5;
}

.mqcfb-preview-content {
    padding: 15px;
}

.mqcfb-preview-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 10px 0;
    line-height: 1.4;
}

.mqcfb-preview-excerpt {
    font-size: 14px;
    color: #555;
    margin: 0 0 15px 0;
    line-height: 1.5;
    max-height: 63px;
    overflow: hidden;
}

.mqcfb-preview-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #777;
}

.mqcfb-preview-feed {
    font-weight: 500;
}

.mqcfb-preview-date {
    font-style: italic;
}

.mqcfb-preview-actions {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
}

/* Settings Form */
.mqcfb-settings-form {
    max-width: 800px;
}

.mqcfb-settings-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.mqcfb-settings-section:last-child {
    border-bottom: none;
}

.mqcfb-setting-field {
    margin-bottom: 15px;
}

.mqcfb-setting-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.mqcfb-setting-field input[type="text"],
.mqcfb-setting-field input[type="number"],
.mqcfb-setting-field input[type="url"],
.mqcfb-setting-field select {
    width: 100%;
    max-width: 400px;
}

.mqcfb-setting-field .description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.mqcfb-settings-actions {
    margin-top: 20px;
}

.mqcfb-settings-message {
    display: inline-block;
    margin-left: 15px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 14px;
}

.mqcfb-settings-message.success {
    background-color: #e7f7ed;
    color: #2a8f37;
}

.mqcfb-settings-message.error {
    background-color: #fbeaea;
    color: #dc3232;
}

/* Modals */
.mqcfb-modal {
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.mqcfb-modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    width: 80%;
    max-width: 600px;
    animation: mqcfbFadeIn 0.3s;
}

.mqcfb-modal-large {
    max-width: 800px;
}

.mqcfb-modal h2 {
    padding: 15px 20px;
    margin: 0;
    border-bottom: 1px solid #e5e5e5;
}

.mqcfb-modal-close {
    position: absolute;
    top: 10px;
    right: 20px;
    color: #999;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.mqcfb-modal-close:hover {
    color: #333;
}

#mqcfb-feed-form {
    padding: 20px;
}

.mqcfb-form-field {
    margin-bottom: 20px;
}

.mqcfb-form-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
}

.mqcfb-form-field input[type="text"],
.mqcfb-form-field input[type="url"],
.mqcfb-form-field input[type="number"],
.mqcfb-form-field select {
    width: 100%;
    padding: 8px;
}

.mqcfb-form-actions {
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid #e5e5e5;
    text-align: right;
}

.mqcfb-form-message {
    display: inline-block;
    margin-left: 15px;
}

.mqcfb-form-message.error {
    color: #d63638;
}

.mqcfb-form-message.success {
    color: #00a32a;
}

/* Form loading state */
.mqcfb-form.loading {
    position: relative;
}

.mqcfb-form.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 10;
}

.mqcfb-form.loading::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border-radius: 50%;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #0073aa;
    animation: mqcfbSpin 1s linear infinite;
    z-index: 11;
}

@keyframes mqcfbFadeIn {
    from {opacity: 0; transform: translateY(-20px);}
    to {opacity: 1; transform: translateY(0);}
}

@keyframes mqcfbSpin {
    to {transform: rotate(360deg);}
}

/* Modal open body style */
body.modal-open {
    overflow: hidden;
}

/* Article Modal */
#mqcfb-article-content {
    max-height: 500px;
    overflow-y: auto;
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.mqcfb-modal-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.mqcfb-modal-actions button {
    margin-left: 10px;
}

/* Responsive Adjustments */
@media screen and (max-width: 782px)
{

/* Support for select2 if used */
.select2-container {
    width: 100% !important;
}
}