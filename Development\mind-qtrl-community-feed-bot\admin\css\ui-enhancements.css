/* UI Enhancements for Mind QTRL Community Feed Bot */

/* Enhanced Feed Row Styling */
.mqcfb-feed-row {
    position: relative;
    transition: all 0.2s ease;
}

.mqcfb-feed-row:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Improved Status Indicators */
.mqcfb-status-light {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.mqcfb-status-light.connected {
    box-shadow: 0 0 5px rgba(70, 180, 80, 0.5);
}

.mqcfb-status-light.disconnected {
    box-shadow: 0 0 5px rgba(220, 50, 50, 0.5);
}

.mqcfb-status-light.unknown {
    box-shadow: 0 0 5px rgba(255, 185, 0, 0.5);
}

/* Enhanced Status Badges */
.mqcfb-status-badge {
    transition: all 0.2s ease;
    font-weight: 600;
}

.mqcfb-status-badge.active {
    background: linear-gradient(135deg, #e7f7ed, #d1f0d9);
}

.mqcfb-status-badge.paused {
    background: linear-gradient(135deg, #f8f4e3, #f0e9c9);
}

/* Improved Preview Cards */
.mqcfb-preview-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mqcfb-preview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mqcfb-preview-card:hover::before {
    opacity: 1;
}

.mqcfb-preview-image {
    transition: all 0.3s ease;
}

.mqcfb-preview-card:hover .mqcfb-preview-image {
    transform: scale(1.05);
}

/* Enhanced Form Fields */
.mqcfb-form-field input[type="text"],
.mqcfb-form-field input[type="url"],
.mqcfb-form-field input[type="number"],
.mqcfb-form-field select {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    transition: all 0.2s ease;
}

.mqcfb-form-field input[type="text"]:focus,
.mqcfb-form-field input[type="url"]:focus,
.mqcfb-form-field input[type="number"]:focus,
.mqcfb-form-field select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

/* Enhanced Buttons */
.mqcfb-icon-button {
    position: relative;
    overflow: hidden;
}

.mqcfb-icon-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(0, 115, 170, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.mqcfb-icon-button:hover::after {
    width: 200%;
    height: 200%;
}

/* Enhanced Tabs */
.mqcfb-tab {
    position: relative;
    overflow: hidden;
}

.mqcfb-tab::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #0073aa;
    transform: translateY(3px);
    transition: transform 0.3s ease;
}

.mqcfb-tab:hover::after {
    transform: translateY(0);
}

.mqcfb-tab.active::after {
    transform: translateY(0);
}

/* Enhanced Modal Animations */
@keyframes modalScale {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.mqcfb-modal-content {
    animation: modalScale 0.3s ease;
}

/* Enhanced Loading Animation */
.mqcfb-loading::after {
    content: '...';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0% { content: '.'; }
    33% { content: '..'; }
    66% { content: '...'; }
    100% { content: '.'; }
}

/* Enhanced Queue Table */
.mqcfb-queue-table tr {
    transition: all 0.2s ease;
}

.mqcfb-queue-table tr:hover {
    background-color: #f5f9fc;
}

/* Enhanced Settings Form */
.mqcfb-settings-section {
    transition: all 0.3s ease;
    border-radius: 4px;
    padding: 20px;
}

.mqcfb-settings-section:hover {
    background-color: #f9f9f9;
}

/* Enhanced Checkbox Styling */
.mqcfb-setting-field input[type="checkbox"] {
    position: relative;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    vertical-align: middle;
}

/* Responsive Enhancements */
@media screen and (max-width: 782px) {
    .mqcfb-preview-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .mqcfb-tabs {
        flex-wrap: wrap;
    }
    
    .mqcfb-tab {
        flex: 1 1 auto;
        text-align: center;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .admin-color-light .mqcfb-status-badge.active {
        background: linear-gradient(135deg, #1e3a29, #2a5a3d);
        color: #a0d9b4;
    }
    
    .admin-color-light .mqcfb-status-badge.paused {
        background: linear-gradient(135deg, #3a3520, #5a522d);
        color: #d9c9a0;
    }
}