/**
 * Mind QTRL Community Feed Bot Admin JavaScript
 *
 * Handles all admin UI interactions including:
 * - Tab switching
 * - Modal dialogs
 * - AJAX requests for feed management
 * - Feed previews and article queue management
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize tabs
        initTabs();
        
        // Initialize feed management
        initFeedManagement();
        
        // Initialize feed preview
        initFeedPreview();
        
        // Initialize article queue
        initArticleQueue();
        
        // Initialize settings
        initSettings();
        
        // Initialize modals
        initModals();
    });

    /**
     * Initialize tab switching
     */
    function initTabs() {
        $('.mqcfb-tab').on('click', function() {
            // Remove active class from all tabs and content
            $('.mqcfb-tab').removeClass('active');
            $('.mqcfb-content').removeClass('active');
            
            // Add active class to clicked tab and corresponding content
            $(this).addClass('active');
            $('#' + $(this).data('tab') + '-content').addClass('active');
            
            // Store active tab in localStorage
            localStorage.setItem('mqcfb_active_tab', $(this).data('tab'));
        });
        
        // Check if there's a stored active tab
        const activeTab = localStorage.getItem('mqcfb_active_tab');
        if (activeTab) {
            $('.mqcfb-tab[data-tab="' + activeTab + '"]').trigger('click');
        }
    }

    /**
     * Initialize feed management
     */
    function initFeedManagement() {
        // Add new feed button
        $('.add-new-feed').on('click', function(e) {
            e.preventDefault();
            
            // Reset form
            $('#mqcfb-feed-form')[0].reset();
            $('#feed_id').val('');
            $('#mqcfb-modal-title').text('Add New Feed');
            
            // Load spaces
            loadSpaces();
            
            // Load authors
            loadAuthors();
            
            // Show modal
            $('#mqcfb-feed-modal').fadeIn(300);
        });
        
        // Edit feed
        $('.mqcfb-clickable-row').on('click', function() {
            const feedId = $(this).data('feed-id');
            editFeed(feedId);
        });
        
        // Delete feed
        $('.mqcfb-delete-feed').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const feedId = $(this).data('id');
            if (confirm(mqcfb_admin.strings.confirm_delete)) {
                deleteFeed(feedId);
            }
        });
        
        // Refresh feed
        $('.mqcfb-refresh-feed').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const feedId = $(this).data('id');
            refreshFeed(feedId);
        });
        
        // Space selection change
        $('#space_id').on('change', function() {
            const spaceId = $(this).val();
            if (spaceId) {
                loadTopics(spaceId);
            } else {
                $('#topic_ids').empty();
            }
        });
        
        // Feed form submission
        $('#mqcfb-feed-form').on('submit', function(e) {
            e.preventDefault();
            saveFeed();
        });
    }

    /**
     * Initialize feed preview
     */
    function initFeedPreview() {
        // Load previews on tab click
        $('.mqcfb-tab[data-tab="preview"]').on('click', function() {
            loadPreviews();
        });
        
        // Filter change
        $('#preview-feed-filter').on('change', function() {
            loadPreviews($(this).val());
        });
        
        // Refresh button
        $('.mqcfb-refresh-preview').on('click', function() {
            loadPreviews($('#preview-feed-filter').val());
        });
    }

    /**
     * Initialize article queue
     */
    function initArticleQueue() {
        // Load queue on tab click
        $('.mqcfb-tab[data-tab="queue"]').on('click', function() {
            loadQueue();
        });
        
        // Filter change
        $('#queue-status-filter, #queue-feed-filter').on('change', function() {
            loadQueue();
        });
        
        // Refresh button
        $('.mqcfb-refresh-queue').on('click', function() {
            loadQueue();
        });
        
        // Article actions (delegated)
        $('.mqcfb-queue-table').on('click', '.mqcfb-view-article', function(e) {
            e.preventDefault();
            const articleId = $(this).data('id');
            viewArticle(articleId);
        });
        
        $('.mqcfb-queue-table').on('click', '.mqcfb-post-article-queue', function(e) {
            e.preventDefault();
            const articleId = $(this).data('id');
            postArticle(articleId);
        });
        
        $('.mqcfb-queue-table').on('click', '.mqcfb-delete-article', function(e) {
            e.preventDefault();
            const articleId = $(this).data('id');
            if (confirm('Are you sure you want to delete this article?')) {
                deleteArticle(articleId);
            }
        });
    }

    /**
     * Initialize settings
     */
    function initSettings() {
        // Load settings
        $('.mqcfb-tab[data-tab="settings"]').on('click', function() {
            loadSettings();
        });
        
        // Auto post checkbox toggle
        $('#auto_post').on('change', function() {
            if ($(this).is(':checked')) {
                $('.auto-post-delay-field').slideDown();
            } else {
                $('.auto-post-delay-field').slideUp();
            }
        });
        
        // Settings form submission
        $('#mqcfb-settings-form').on('submit', function(e) {
            e.preventDefault();
            saveSettings();
        });
    }

    /**
     * Initialize modals
     */
    function initModals() {
        // Close modal
        $('.mqcfb-modal-close, .mqcfb-modal-cancel').on('click', function() {
            $('.mqcfb-modal').fadeOut(300);
        });
        
        // Close modal when clicking outside
        $('.mqcfb-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).fadeOut(300);
            }
        });
        
        // Post article from modal
        $('.mqcfb-post-article').on('click', function() {
            const articleId = $(this).data('id');
            postArticle(articleId);
        });
    }

    /**
     * Load spaces via AJAX
     */
    function loadSpaces() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_spaces',
                nonce: mqcfb_admin.nonce
            },
            beforeSend: function() {
                $('#space_id').html('<option value="">Loading spaces...</option>');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);

(function($) {
    'use strict';
    
    // Modal handling
    const feedModal = {
        init: function() {
            // Cache DOM elements
            this.$modal = $('#mqcfb-feed-modal');
            this.$form = $('#mqcfb-feed-form');
            this.$closeBtn = this.$modal.find('.mqcfb-modal-close');
            this.$cancelBtn = this.$modal.find('.mqcfb-modal-cancel');
            this.$title = $('#mqcfb-modal-title');
            this.$formMessage = $('.mqcfb-form-message');
            
            // Add event listeners
            this.$closeBtn.on('click', this.close.bind(this));
            this.$cancelBtn.on('click', this.close.bind(this));
            this.$form.on('submit', this.handleSubmit.bind(this));
            
            // Close when clicking outside the modal content
            $(window).on('click', (e) => {
                if (e.target === this.$modal[0]) {
                    this.close();
                }
            });
            
            // Add new feed button
            $('.add-new-feed').on('click', (e) => {
                e.preventDefault();
                this.open();
            });
        },
        
        open: function(feedId = null) {
            // Reset form first
            this.resetForm();
            
            if (feedId) {
                // Loading existing feed
                this.$title.text(mqcfb_admin_vars.edit_feed_text);
                this.loadFeedData(feedId);
            } else {
                // Adding new feed
                this.$title.text(mqcfb_admin_vars.add_feed_text);
                this.loadFormDependencies();
            }
            
            this.$modal.fadeIn(300);
            $('body').addClass('modal-open');
        },
        
        close: function() {
            this.$modal.fadeOut(200);
            $('body').removeClass('modal-open');
        },
        
        resetForm: function() {
            this.$form[0].reset();
            $('#feed_id').val('');
            this.$formMessage.text('').removeClass('error success');
            
            // Clear select options for dynamic dropdowns
            $('#topic_ids').html('');
        },
        
        loadFeedData: function(feedId) {
            this.showFormLoading();
            
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_feed',
                    feed_id: feedId,
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.populateForm(response.data);
                    } else {
                        this.showFormError(response.data.message || mqcfb_admin_vars.feed_load_error);
                    }
                },
                error: () => {
                    this.showFormError(mqcfb_admin_vars.ajax_error);
                },
                complete: () => {
                    this.hideFormLoading();
                }
            });
        },
        
        loadFormDependencies: function() {
            // Load spaces
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_spaces',
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        const $spaceSelect = $('#space_id');
                        $spaceSelect.find('option:not(:first)').remove();
                        
                        $.each(response.data, (i, space) => {
                            $spaceSelect.append(
                                $('<option>').val(space.id).text(space.title)
                            );
                        });
                    }
                }
            });
            
            // Load authors
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_authors',
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        const $authorSelect = $('#author_id');
                        $authorSelect.find('option:not(:first)').remove();
                        
                        $.each(response.data, (i, author) => {
                            $authorSelect.append(
                                $('<option>').val(author.ID).text(author.display_name)
                            );
                        });
                    }
                }
            });
        },
        
        populateForm: function(feed) {
            // Set basic form values
            $('#feed_id').val(feed.id);
            $('#feed_name').val(feed.name);
            $('#feed_url').val(feed.feed_url);
            $('#max_items').val(feed.max_items || 10);
            $('#image_handling').val(feed.image_handling || 'embed');
            $('#feed_status').val(feed.status || 'active');
            $('#auto_post').prop('checked', feed.auto_post === 'yes');
            
            // Load related data
            this.loadFormDependencies();
            
            // Set space after spaces are loaded
            if (feed.space_id) {
                const spaceCheckInterval = setInterval(() => {
                    const $spaceOption = $('#space_id option[value="' + feed.space_id + '"]');
                    if ($spaceOption.length) {
                        $('#space_id').val(feed.space_id).trigger('change');
                        clearInterval(spaceCheckInterval);
                        
                        // Now load topics for this space
                        this.loadTopicsForSpace(feed.space_id, feed.selected_topics);
                    }
                }, 100);
            }
            
            // Set author after authors are loaded
            if (feed.author_id) {
                const authorCheckInterval = setInterval(() => {
                    const $authorOption = $('#author_id option[value="' + feed.author_id + '"]');
                    if ($authorOption.length) {
                        $('#author_id').val(feed.author_id);
                        clearInterval(authorCheckInterval);
                    }
                }, 100);
            }
        },
        
        loadTopicsForSpace: function(spaceId, selectedTopics = []) {
            if (!spaceId) {
                $('#topic_ids').html('');
                return;
            }
            
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_space_topics',
                    space_id: spaceId,
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        const $topicSelect = $('#topic_ids');
                        $topicSelect.html('');
                        
                        $.each(response.data, (i, topic) => {
                            const $option = $('<option>').val(topic.id).text(topic.name);
                            
                            if (selectedTopics && selectedTopics.includes(parseInt(topic.id))) {
                                $option.prop('selected', true);
                            }
                            
                            $topicSelect.append($option);
                        });
                        
                        // Initialize or refresh select2 if used
                        if ($.fn.select2 && $topicSelect.data('select2')) {
                            $topicSelect.select2('destroy').select2();
                        } else if ($.fn.select2) {
                            $topicSelect.select2({
                                placeholder: mqcfb_admin_vars.select_topics_text
                            });
                        }
                    }
                }
            });
        },
        
        handleSubmit: function(e) {
            e.preventDefault();
            
            this.showFormLoading();
            this.$formMessage.text('').removeClass('error success');
            
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'mqcfb_save_feed',
                    feed_data: this.$form.serialize(),
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showFormSuccess(response.data.message);
                        
                        // Reload page after short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        this.showFormError(response.data.message);
                    }
                },
                error: () => {
                    this.showFormError(mqcfb_admin_vars.ajax_error);
                },
                complete: () => {
                    this.hideFormLoading();
                }
            });
        },
        
        showFormLoading: function() {
            this.$form.find('button').prop('disabled', true);
            this.$form.addClass('loading');
        },
        
        hideFormLoading: function() {
            this.$form.find('button').prop('disabled', false);
            this.$form.removeClass('loading');
        },
        
        showFormError: function(message) {
            this.$formMessage.text(message).addClass('error').removeClass('success');
        },
        
        showFormSuccess: function(message) {
            this.$formMessage.text(message).addClass('success').removeClass('error');
        }
    };

    // Handle clickable rows
    function initClickableRows() {
        $('.mqcfb-clickable-row').on('click', function(e) {
            // Don't trigger if clicking on buttons, links or form elements
            if ($(e.target).is('a, button, input, select, .dashicons') || 
                $(e.target).parents('a, button, .mqcfb-action-cell').length) {
                return;
            }
            
            const feedId = $(this).data('feed-id');
            if (feedId) {
                feedModal.open(feedId);
            }
        });
        
        // Make sure space change loads appropriate topics
        $('#space_id').on('change', function() {
            const spaceId = $(this).val();
            feedModal.loadTopicsForSpace(spaceId);
        });
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize the feed modal
        feedModal.init();
        
        // Initialize clickable rows
        initClickableRows();
        
        // Add select2 to multiple select elements if available
        if ($.fn.select2) {
            $('#topic_ids').select2({
                placeholder: mqcfb_admin_vars.select_topics_text
            });
        }
    });
    
})(jQuery);

(function($) {
    'use strict';
    
    // Modal handling
    const feedModal = {
        init: function() {
            // Cache DOM elements
            this.$modal = $('#mqcfb-feed-modal');
            this.$form = $('#mqcfb-feed-form');
            this.$closeBtn = this.$modal.find('.mqcfb-modal-close');
            this.$cancelBtn = this.$modal.find('.mqcfb-modal-cancel');
            this.$title = $('#mqcfb-modal-title');
            this.$formMessage = $('.mqcfb-form-message');
            
            // Add event listeners
            this.$closeBtn.on('click', this.close.bind(this));
            this.$cancelBtn.on('click', this.close.bind(this));
            this.$form.on('submit', this.handleSubmit.bind(this));
            
            // Close when clicking outside the modal content
            $(window).on('click', (e) => {
                if (e.target === this.$modal[0]) {
                    this.close();
                }
            });
            
            // Add new feed button
            $('.add-new-feed').on('click', (e) => {
                e.preventDefault();
                this.open();
            });
        },
        
        open: function(feedId = null) {
            // Reset form first
            this.resetForm();
            
            if (feedId) {
                // Loading existing feed
                this.$title.text(mqcfb_admin_vars.edit_feed_text);
                this.loadFeedData(feedId);
            } else {
                // Adding new feed
                this.$title.text(mqcfb_admin_vars.add_feed_text);
                this.loadFormDependencies();
            }
            
            this.$modal.fadeIn(300);
            $('body').addClass('modal-open');
        },
        
        close: function() {
            this.$modal.fadeOut(200);
            $('body').removeClass('modal-open');
        },
        
        resetForm: function() {
            this.$form[0].reset();
            $('#feed_id').val('');
            this.$formMessage.text('').removeClass('error success');
            
            // Clear select options for dynamic dropdowns
            $('#topic_ids').html('');
        },
        
        loadFeedData: function(feedId) {
            this.showFormLoading();
            
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_feed',
                    feed_id: feedId,
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.populateForm(response.data);
                    } else {
                        this.showFormError(response.data.message || mqcfb_admin_vars.feed_load_error);
                    }
                },
                error: () => {
                    this.showFormError(mqcfb_admin_vars.ajax_error);
                },
                complete: () => {
                    this.hideFormLoading();
                }
            });
        },
        
        loadFormDependencies: function() {
            // Load spaces
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_spaces',
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        const $spaceSelect = $('#space_id');
                        $spaceSelect.find('option:not(:first)').remove();
                        
                        $.each(response.data, (i, space) => {
                            $spaceSelect.append(
                                $('<option>').val(space.id).text(space.title)
                            );
                        });
                    }
                }
            });
            
            // Load authors
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_authors',
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        const $authorSelect = $('#author_id');
                        $authorSelect.find('option:not(:first)').remove();
                        
                        $.each(response.data, (i, author) => {
                            $authorSelect.append(
                                $('<option>').val(author.ID).text(author.display_name)
                            );
                        });
                    }
                }
            });
        },
        
        populateForm: function(feed) {
            // Set basic form values
            $('#feed_id').val(feed.id);
            $('#feed_name').val(feed.name);
            $('#feed_url').val(feed.feed_url);
            $('#max_items').val(feed.max_items || 10);
            $('#image_handling').val(feed.image_handling || 'embed');
            $('#feed_status').val(feed.status || 'active');
            $('#auto_post').prop('checked', feed.auto_post === 'yes');
            
            // Load related data
            this.loadFormDependencies();
            
            // Set space after spaces are loaded
            if (feed.space_id) {
                const spaceCheckInterval = setInterval(() => {
                    const $spaceOption = $('#space_id option[value="' + feed.space_id + '"]');
                    if ($spaceOption.length) {
                        $('#space_id').val(feed.space_id).trigger('change');
                        clearInterval(spaceCheckInterval);
                        
                        // Now load topics for this space
                        this.loadTopicsForSpace(feed.space_id, feed.selected_topics);
                    }
                }, 100);
            }
            
            // Set author after authors are loaded
            if (feed.author_id) {
                const authorCheckInterval = setInterval(() => {
                    const $authorOption = $('#author_id option[value="' + feed.author_id + '"]');
                    if ($authorOption.length) {
                        $('#author_id').val(feed.author_id);
                        clearInterval(authorCheckInterval);
                    }
                }, 100);
            }
        },
        
        loadTopicsForSpace: function(spaceId, selectedTopics = []) {
            if (!spaceId) {
                $('#topic_ids').html('');
                return;
            }
            
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'GET',
                data: {
                    action: 'mqcfb_get_space_topics',
                    space_id: spaceId,
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        const $topicSelect = $('#topic_ids');
                        $topicSelect.html('');
                        
                        $.each(response.data, (i, topic) => {
                            const $option = $('<option>').val(topic.id).text(topic.name);
                            
                            if (selectedTopics && selectedTopics.includes(parseInt(topic.id))) {
                                $option.prop('selected', true);
                            }
                            
                            $topicSelect.append($option);
                        });
                        
                        // Initialize or refresh select2 if used
                        if ($.fn.select2 && $topicSelect.data('select2')) {
                            $topicSelect.select2('destroy').select2();
                        } else if ($.fn.select2) {
                            $topicSelect.select2({
                                placeholder: mqcfb_admin_vars.select_topics_text
                            });
                        }
                    }
                }
            });
        },
        
        handleSubmit: function(e) {
            e.preventDefault();
            
            this.showFormLoading();
            this.$formMessage.text('').removeClass('error success');
            
            $.ajax({
                url: mqcfb_admin_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'mqcfb_save_feed',
                    feed_data: this.$form.serialize(),
                    nonce: mqcfb_admin_vars.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showFormSuccess(response.data.message);
                        
                        // Reload page after short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        this.showFormError(response.data.message);
                    }
                },
                error: () => {
                    this.showFormError(mqcfb_admin_vars.ajax_error);
                },
                complete: () => {
                    this.hideFormLoading();
                }
            });
        },
        
        showFormLoading: function() {
            this.$form.find('button').prop('disabled', true);
            this.$form.addClass('loading');
        },
        
        hideFormLoading: function() {
            this.$form.find('button').prop('disabled', false);
            this.$form.removeClass('loading');
        },
        
        showFormError: function(message) {
            this.$formMessage.text(message).addClass('error').removeClass('success');
        },
        
        showFormSuccess: function(message) {
            this.$formMessage.text(message).addClass('success').removeClass('error');
        }
    };

    // Handle clickable rows
    function initClickableRows() {
        $('.mqcfb-clickable-row').on('click', function(e) {
            // Don't trigger if clicking on buttons, links or form elements
            if ($(e.target).is('a, button, input, select, .dashicons') || 
                $(e.target).parents('a, button, .mqcfb-action-cell').length) {
                return;
            }
            
            const feedId = $(this).data('feed-id');
            if (feedId) {
                feedModal.open(feedId);
            }
        });
        
        // Make sure space change loads appropriate topics
        $('#space_id').on('change', function() {
            const spaceId = $(this).val();
            feedModal.loadTopicsForSpace(spaceId);
        });
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize the feed modal
        feedModal.init();
        
        // Initialize clickable rows
        initClickableRows();
        
        // Add select2 to multiple select elements if available
        if ($.fn.select2) {
            $('#topic_ids').select2({
                placeholder: mqcfb_admin_vars.select_topics_text
            });
        }
    });
    
})(jQuery);
) {
                if (response.success && response.data) {
                    let options = '<option value="">Select a Space</option>';
                    $.each(response.data, function(index, space) {
                        options += '<option value="' + space.id + '">' + space.name + '</option>';
                    });
                    $('#space_id').html(options);
                } else {
                    $('#space_id').html('<option value="">No spaces found</option>');
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                $('#space_id').html('<option value="">Error loading spaces</option>');
            }
        });
    }

    /**
     * Load topics for a space via AJAX
     */
    function loadTopics(spaceId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_topics',
                nonce: mqcfb_admin.nonce,
                space_id: spaceId
            },
            beforeSend: function() {
                $('#topic_ids').html('<option value="">Loading topics...</option>');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    let options = '';
                    $.each(response.data, function(index, topic) {
                        options += '<option value="' + topic.id + '">' + topic.name + '</option>';
                    });
                    $('#topic_ids').html(options);
                } else {
                    $('#topic_ids').html('<option value="">No topics found</option>');
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                $('#topic_ids').html('<option value="">Error loading topics</option>');
            }
        });
    }

    /**
     * Load authors via AJAX
     */
    function loadAuthors() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_users',
                nonce: mqcfb_admin.nonce
            },
            beforeSend: function() {
                $('#author_id').html('<option value="">Loading authors...</option>');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    let options = '<option value="">Select an Author</option>';
                    $.each(response.data, function(index, user) {
                        options += '<option value="' + user.id + '">' + user.name + '</option>';
                    });
                    $('#author_id').html(options);
                } else {
                    $('#author_id').html('<option value="">No authors found</option>');
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                $('#author_id').html('<option value="">Error loading authors</option>');
            }
        });
    }

    /**
     * Edit feed
     */
    function editFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('#mqcfb-modal-title').text('Loading Feed...');
                $('#mqcfb-feed-modal').fadeIn(300);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    const feed = response.data;
                    
                    // Set form values
                    $('#feed_id').val(feed.id);
                    $('#feed_name').val(feed.name);
                    $('#feed_url').val(feed.feed_url);
                    $('#feed_status').val(feed.status);
                    $('#auto_post').prop('checked', feed.auto_post);
                    $('#max_items').val(feed.max_items || 10);
                    $('#image_handling').val(feed.image_handling || 'embed');
                    
                    // Load spaces and set selected
                    loadSpaces();
                    setTimeout(function() {
                        $('#space_id').val(feed.space_id);
                        loadTopics(feed.space_id);
                        
                        // Set selected topics after topics are loaded
                        setTimeout(function() {
                            if (feed.selected_topics && feed.selected_topics.length) {
                                $.each(feed.selected_topics, function(index, topicId) {
                                    $('#topic_ids option[value="' + topicId + '"]').prop('selected', true);
                                });
                            }
                        }, 500);
                    }, 500);
                    
                    // Load authors and set selected
                    loadAuthors();
                    setTimeout(function() {
                        $('#author_id').val(feed.author_id);
                    }, 500);
                    
                    $('#mqcfb-modal-title').text('Edit Feed');
                } else {
                    alert(mqcfb_admin.strings.error_loading);
                    $('#mqcfb-feed-modal').fadeOut(300);
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                alert(mqcfb_admin.strings.error_loading);
                $('#mqcfb-feed-modal').fadeOut(300);
            }
        });
    }

    /**
     * Save feed
     */
    function saveFeed() {
        const formData = $('#mqcfb-feed-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_feed',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-form-message').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-form-message').html('Feed saved successfully!');
                    setTimeout(function() {
                        $('#mqcfb-feed-modal').fadeOut(300);
                        window.location.reload();
                    }, 1000);
                } else {
                    $('.mqcfb-form-message').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                $('#topic_ids').html('<option value="">Error loading topics</option>');
            }
        });
    }

    /**
     * Load authors via AJAX
     */
    function loadAuthors() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_users',
                nonce: mqcfb_admin.nonce
            },
            beforeSend: function() {
                $('#author_id').html('<option value="">Loading authors...</option>');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    let options = '<option value="">Select an Author</option>';
                    $.each(response.data, function(index, user) {
                        options += '<option value="' + user.id + '">' + user.name + '</option>';
                    });
                    $('#author_id').html(options);
                } else {
                    $('#author_id').html('<option value="">No authors found</option>');
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                $('#author_id').html('<option value="">Error loading authors</option>');
            }
        });
    }

    /**
     * Edit feed
     */
    function editFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('#mqcfb-modal-title').text('Loading Feed...');
                $('#mqcfb-feed-modal').fadeIn(300);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    const feed = response.data;
                    
                    // Set form values
                    $('#feed_id').val(feed.id);
                    $('#feed_name').val(feed.name);
                    $('#feed_url').val(feed.feed_url);
                    $('#feed_status').val(feed.status);
                    $('#auto_post').prop('checked', feed.auto_post);
                    $('#max_items').val(feed.max_items || 10);
                    $('#image_handling').val(feed.image_handling || 'embed');
                    
                    // Load spaces and set selected
                    loadSpaces();
                    setTimeout(function() {
                        $('#space_id').val(feed.space_id);
                        loadTopics(feed.space_id);
                        
                        // Set selected topics after topics are loaded
                        setTimeout(function() {
                            if (feed.selected_topics && feed.selected_topics.length) {
                                $.each(feed.selected_topics, function(index, topicId) {
                                    $('#topic_ids option[value="' + topicId + '"]').prop('selected', true);
                                });
                            }
                        }, 500);
                    }, 500);
                    
                    // Load authors and set selected
                    loadAuthors();
                    setTimeout(function() {
                        $('#author_id').val(feed.author_id);
                    }, 500);
                    
                    $('#mqcfb-modal-title').text('Edit Feed');
                } else {
                    alert(mqcfb_admin.strings.error_loading);
                    $('#mqcfb-feed-modal').fadeOut(300);
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                $('#author_id').html('<option value="">Error loading authors</option>');
            }
        });
    }

    /**
     * Edit feed
     */
    function editFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('#mqcfb-modal-title').text('Loading Feed...');
                $('#mqcfb-feed-modal').fadeIn(300);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    const feed = response.data;
                    
                    // Set form values
                    $('#feed_id').val(feed.id);
                    $('#feed_name').val(feed.name);
                    $('#feed_url').val(feed.feed_url);
                    $('#feed_status').val(feed.status);
                    $('#auto_post').prop('checked', feed.auto_post);
                    $('#max_items').val(feed.max_items || 10);
                    $('#image_handling').val(feed.image_handling || 'embed');
                    
                    // Load spaces and set selected
                    loadSpaces();
                    setTimeout(function() {
                        $('#space_id').val(feed.space_id);
                        loadTopics(feed.space_id);
                        
                        // Set selected topics after topics are loaded
                        setTimeout(function() {
                            if (feed.selected_topics && feed.selected_topics.length) {
                                $.each(feed.selected_topics, function(index, topicId) {
                                    $('#topic_ids option[value="' + topicId + '"]').prop('selected', true);
                                });
                            }
                        }, 500);
                    }, 500);
                    
                    // Load authors and set selected
                    loadAuthors();
                    setTimeout(function() {
                        $('#author_id').val(feed.author_id);
                    }, 500);
                    
                    $('#mqcfb-modal-title').text('Edit Feed');
                } else {
                    alert(mqcfb_admin.strings.error_loading);
                    $('#mqcfb-feed-modal').fadeOut(300);
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    // Close modal if open
                    $('#mqcfb-article-modal').fadeOut(300);
                    
                    // Reload queue
                    loadQueue();
                    
                    // Show success message
                    alert('Article posted successfully!');
                } else {
                    $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                    $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-article').text('Post to Community').prop('disabled', false);
                $('.mqcfb-post-article-queue[data-id="' + articleId + '"]').text('Post').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Delete article
     */
    function deleteArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
() {
                alert(mqcfb_admin.strings.error_loading);
                $('#mqcfb-feed-modal').fadeOut(300);
            }
        });
    }

    /**
     * Save feed
     */
    function saveFeed() {
        const formData = $('#mqcfb-feed-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_feed',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-form-message').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-form-message').html('Feed saved successfully!');
                    setTimeout(function() {
                        $('#mqcfb-feed-modal').fadeOut(300);
                        window.location.reload();
                    }, 1000);
                } else {
                    $('.mqcfb-form-message').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-form-message').html(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Delete feed
     */
    function deleteFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_delete_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                alert(mqcfb_admin.strings.error_saving);
            }
        });
    }

    /**
     * Refresh feed
     */
    function refreshFeed(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_refresh_feed',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').addClass('dashicons-update-spin');
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                
                if (response.success) {
                    // Update last fetch time
                    if (response.data && response.data.last_fetch) {
                        $('.mqcfb-feed-row[data-feed-id="' + feedId + '"] .feed-last-fetch-text').text(response.data.last_fetch);
                    }
                    
                    // Update connection status
                    if (response.data && response.data.connection_status) {
                        const statusClass = response.data.connection_status === 'connected' ? 'connected' : 
                                          (response.data.connection_status === 'disconnected' ? 'disconnected' : 'unknown');
                        
                        $('.mqcfb-connection-indicator[data-feed-id="' + feedId + '"] .mqcfb-status-light')
                            .removeClass('connected disconnected unknown')
                            .addClass(statusClass);
                    }
                } else {
                    alert(response.data || 'Error refreshing feed');
                }
            },
            error: function() {
                $('.mqcfb-refresh-feed[data-id="' + feedId + '"] .dashicons').removeClass('dashicons-update-spin');
                alert('Error refreshing feed');
            }
        });
    }

    /**
     * Load feed previews
     */
    function loadPreviews(feedId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_previews',
                nonce: mqcfb_admin.nonce,
                feed_id: feedId || ''
            },
            beforeSend: function() {
                $('.mqcfb-preview-grid').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">No preview items found</div>');
                        return;
                    }
                    
                    $.each(response.data, function(index, item) {
                        const card = $('<div class="mqcfb-preview-card"></div>');
                        
                        // Add image if available
                        if (item.image) {
                            card.append('<img src="' + item.image + '" class="mqcfb-preview-image" alt="' + item.title + '">');
                        } else {
                            card.append('<div class="mqcfb-preview-image-placeholder"></div>');
                        }
                        
                        // Add content
                        const content = $('<div class="mqcfb-preview-content"></div>');
                        content.append('<h3 class="mqcfb-preview-title">' + item.title + '</h3>');
                        content.append('<div class="mqcfb-preview-excerpt">' + item.excerpt + '</div>');
                        
                        // Add meta
                        const meta = $('<div class="mqcfb-preview-meta"></div>');
                        meta.append('<span class="mqcfb-preview-feed">' + item.feed_name + '</span>');
                        meta.append('<span class="mqcfb-preview-date">' + item.date + '</span>');
                        content.append(meta);
                        
                        // Add actions
                        const actions = $('<div class="mqcfb-preview-actions"></div>');
                        actions.append('<a href="' + item.url + '" target="_blank" class="button">View Source</a>');
                        actions.append('<button class="button button-primary mqcfb-post-preview" data-id="' + item.id + '">Post to Community</button>');
                        content.append(actions);
                        
                        card.append(content);
                        $('.mqcfb-preview-grid').append(card);
                    });
                    
                    // Add post preview handler
                    $('.mqcfb-post-preview').on('click', function() {
                        const itemId = $(this).data('id');
                        postPreviewItem(itemId);
                    });
                } else {
                    $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-preview-grid').html('<div class="mqcfb-no-previews">Error loading previews</div>');
            }
        });
    }

    /**
     * Post preview item to community
     */
    function postPreviewItem(itemId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_preview',
                nonce: mqcfb_admin.nonce,
                item_id: itemId
            },
            beforeSend: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posting...').prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success) {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Posted!').addClass('button-disabled');
                } else {
                    $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                    alert(response.data || 'Error posting to community');
                }
            },
            error: function() {
                $('.mqcfb-post-preview[data-id="' + itemId + '"]').text('Post to Community').prop('disabled', false);
                alert('Error posting to community');
            }
        });
    }

    /**
     * Load article queue
     */
    function loadQueue() {
        const status = $('#queue-status-filter').val() || 'pending';
        const feedId = $('#queue-feed-filter').val() || '';
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_queue',
                nonce: mqcfb_admin.nonce,
                status: status,
                feed_id: feedId
            },
            beforeSend: function() {
                $('.mqcfb-queue-table tbody').empty();
                $('.mqcfb-loading').show();
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                $('.mqcfb-loading').hide();
                
                if (response.success && response.data) {
                    if (response.data.length === 0) {
                        $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">No articles found</td></tr>');
                        return;
                    }
                    
                    $.each(response.data, function(index, article) {
                        const row = $('<tr></tr>');
                        
                        // Title
                        row.append('<td><strong>' + article.article_title + '</strong></td>');
                        
                        // Feed
                        row.append('<td>' + article.feed_name + '</td>');
                        
                        // Date
                        row.append('<td>' + article.created_at + '</td>');
                        
                        // Status
                        const statusClass = article.status === 'posted' ? 'success' : 
                                         (article.status === 'failed' ? 'error' : 
                                         (article.status === 'skipped' ? 'warning' : ''));
                        row.append('<td><span class="mqcfb-status-badge ' + statusClass + '">' + article.status + '</span></td>');
                        
                        // Actions
                        const actions = $('<td></td>');
                        actions.append('<button class="button mqcfb-view-article" data-id="' + article.id + '">View</button> ');
                        
                        if (article.status !== 'posted') {
                            actions.append('<button class="button button-primary mqcfb-post-article-queue" data-id="' + article.id + '">Post</button> ');
                        }
                        
                        actions.append('<button class="button mqcfb-delete-article" data-id="' + article.id + '">Delete</button>');
                        row.append(actions);
                        
                        $('.mqcfb-queue-table tbody').append(row);
                    });
                } else {
                    $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
                }
            },
            error: function() {
                $('.mqcfb-loading').hide();
                $('.mqcfb-queue-table tbody').html('<tr><td colspan="5">Error loading articles</td></tr>');
            }
        });
    }

    /**
     * View article
     */
    function viewArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('#mqcfb-article-title').text('Loading Article...');
                $('#mqcfb-article-content').html('<div class="mqcfb-loading">Loading article content...</div>');
                $('#mqcfb-article-modal').fadeIn(300);
                $('.mqcfb-post-article').data('id', articleId);
            },
            success: function(response) {
                if (response.success) {
                    // Reload queue
                    loadQueue();
                } else {
                    alert(response.data || 'Error deleting article');
                }
            },
            error: function() {
                alert('Error deleting article');
            }
        });
    }

    /**
     * Load settings
     */
    function loadSettings() {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_get_settings',
                nonce: mqcfb_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    const settings = response.data;
                    
                    // Set form values
                    $('#schedule_frequency').val(settings.schedule_frequency);
                    $('#max_items_per_feed').val(settings.max_items_per_feed);
                    $('#default_post_format').val(settings.default_post_format);
                    $('#default_image_handling').val(settings.default_image_handling);
                    $('#remove_data_on_uninstall').prop('checked', settings.remove_data_on_uninstall);
                    $('#auto_post').prop('checked', settings.auto_post);
                    $('#auto_post_delay').val(settings.auto_post_delay);
                    $('#debug_mode').prop('checked', settings.debug_mode);
                    
                    // Show/hide auto post delay field
                    if (settings.auto_post) {
                        $('.auto-post-delay-field').show();
                    } else {
                        $('.auto-post-delay-field').hide();
                    }
                }
            }
        });
    }

    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#mqcfb-settings-form').serialize();
        
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_save_settings',
                nonce: mqcfb_admin.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('.mqcfb-settings-message').removeClass('success error').html('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    $('.mqcfb-settings-message').addClass('success').html(mqcfb_admin.strings.success_saved);
                    
                    // Reload page after a delay to refresh schedule info
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    $('.mqcfb-settings-message').addClass('error').html(response.data || mqcfb_admin.strings.error_saving);
                }
            },
            error: function() {
                $('.mqcfb-settings-message').addClass('error').html(mqcfb_admin.strings.error_saving);
            }
        });
    }
})(jQuery);
) {
                if (response.success && response.data) {
                    $('#mqcfb-article-title').text(response.data.article_title);
                    $('#mqcfb-article-content').html(response.data.article_content);
                } else {
                    $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
                }
            },
            error: function() {
                $('#mqcfb-article-content').html('<div class="mqcfb-error">Error loading article content</div>');
            }
        });
    }

    /**
     * Post article to community
     */
    function postArticle(articleId) {
        $.ajax({
            url: mqcfb_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcfb_post_article',
                nonce: mqcfb_admin.nonce,
                article_id: articleId
            },
            beforeSend: function() {
                $('.mqcfb-post-article').text('Posting...').prop('disabled', true);