<?php
if (!defined('ABSPATH')) exit;

// Get feeds with proper error handling
$feeds = method_exists($this->settings, 'get_feeds') ? $this->settings->get_feeds() : array();
?>

<div class="wrap mqcfb-admin mqcfb-admin-wrap" id="mqcfb-admin-app">
    <div class="mqcfb-header">
        <img src="<?php echo esc_url(plugin_dir_url(dirname(dirname(__FILE__))) . 'admin/images/m_only_live_trns_150px.png'); ?>" alt="Mind Qtrl" class="mqcfb-logo">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    </div>
    
    <!-- Version information and quick stats - now centered -->
    <div class="mqcfb-stats">
        <div class="mqcfb-stat-item">
            <span class="mqcfb-stat-label"><?php _e('Version', 'mind-qtrl-community-feed-bot'); ?></span>
            <span class="mqcfb-stat-value"><?php echo MQCFB_VERSION; ?></span>
        </div>
        <div class="mqcfb-stat-item">
            <span class="mqcfb-stat-label"><?php _e('Active Feeds', 'mind-qtrl-community-feed-bot'); ?></span>
            <span class="mqcfb-stat-value feed-count">
                <?php 
                $active_feeds = 0;
                if (is_array($feeds)) {
                    foreach ($feeds as $feed) {
                        if (isset($feed['status']) && $feed['status'] === 'active') {
                            $active_feeds++;
                        }
                    }
                }
                echo $active_feeds; 
                ?>
            </span>
        </div>
        <div class="mqcfb-stat-item next-run">
            <span class="mqcfb-stat-label"><?php _e('Next Run', 'mind-qtrl-community-feed-bot'); ?></span>
            <span class="mqcfb-stat-value">
                <?php 
                $next_run = wp_next_scheduled('mqcfb_process_feeds');
                echo $next_run ? human_time_diff(time(), $next_run) : __('Not scheduled', 'mind-qtrl-community-feed-bot');
                ?>
            </span>
        </div>
    </div>
    
    <div class="mqcfb-tabs">
        <div class="mqcfb-tab active" data-tab="feeds"><?php _e('RSS Feeds', 'mind-qtrl-community-feed-bot'); ?></div>
        <div class="mqcfb-tab" data-tab="preview"><?php _e('Feed Previews', 'mind-qtrl-community-feed-bot'); ?></div>
        <div class="mqcfb-tab" data-tab="queue"><?php _e('Article Queue', 'mind-qtrl-community-feed-bot'); ?></div>
        <div class="mqcfb-tab" data-tab="settings"><?php _e('Settings', 'mind-qtrl-community-feed-bot'); ?></div>
    </div>
    
    <!-- Feeds tab -->
    <div class="mqcfb-content active" id="feeds-content">
        <div class="mqcfb-feeds-header">
            <h2><?php _e('Manage RSS Feeds', 'mind-qtrl-community-feed-bot'); ?></h2>
            <a href="#" class="button button-primary add-new-feed"><?php _e('Add New Feed', 'mind-qtrl-community-feed-bot'); ?></a>
        </div>
        
        <div class="mqcfb-feeds-list">
            <table class="wp-list-table widefat fixed striped mqcfb-table">
                <thead>
                    <tr>
                        <th><?php _e('Feed Name', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th><?php _e('URL', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th><?php _e('Topics', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th><?php _e('Last Fetch', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th width="50"><?php _e('', 'mind-qtrl-community-feed-bot'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($feeds)) : ?>
                        <?php foreach ($feeds as $feed) : ?>
                            <tr class="mqcfb-feed-row mqcfb-clickable-row" data-feed-id="<?php echo esc_attr($feed['id']); ?>">
                                <td class="mqcfb-feed-name-cell">
                                    <!-- Fixed position for status indicator -->
                                    <div class="mqcfb-connection-indicator" data-feed-id="<?php echo esc_attr($feed['id']); ?>">
                                        <?php 
                                        $connection_status = !empty($feed['connection_status']) ? $feed['connection_status'] : 'unknown';
                                        $status_class = $connection_status == 'connected' ? 'connected' : 
                                                      ($connection_status == 'disconnected' ? 'disconnected' : 'unknown');
                                        ?>
                                        <span class="mqcfb-status-light <?php echo esc_attr($status_class); ?>" 
                                              title="<?php echo $status_class == 'connected' ? 'Feed is working correctly' : 'Feed has connection issues'; ?>">
                                        </span>
                                    </div>
                                    
                                    <div class="mqcfb-feed-header">
                                        <span class="mqcfb-feed-title"><?php echo esc_html($feed['name']); ?></span>
                                        <!-- Add the feed status as a small badge next to the name -->
                                        <span class="mqcfb-status-badge <?php echo esc_attr($feed['status']); ?>">
                                            <?php echo esc_html(ucfirst($feed['status'])); ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="feed-url">
                                    <a href="<?php echo esc_url($feed['feed_url']); ?>" target="_blank" title="<?php echo esc_attr($feed['feed_url']); ?>" class="mqcfb-feed-url-link" onclick="event.stopPropagation();">
                                        <?php echo esc_url(wp_trim_words($feed['feed_url'], 8, '...')); ?>
                                    </a>
                                </td>
                                <td class="feed-topics">
                                    <?php if (!empty($feed['selected_topics'])) : ?>
                                        <span class="topic-count"><?php echo count($feed['selected_topics']); ?> topics</span>
                                    <?php else : ?>
                                        <span class="no-topics">No topics</span>
                                    <?php endif; ?>
                                </td>
                                <td class="feed-last-fetch">
                                    <span class="feed-last-fetch-text">
                                        <?php 
                                        if (!empty($feed['last_fetch'])) {
                                            echo esc_html(human_time_diff(strtotime($feed['last_fetch']), current_time('timestamp'))) . ' ago';
                                        } else {
                                            _e('Never', 'mind-qtrl-community-feed-bot');
                                        }
                                        ?>
                                    </span>
                                    
                                    <!-- Improved refresh button styling and positioning -->
                                    <button class="mqcfb-icon-button mqcfb-refresh-feed" data-id="<?php echo esc_attr($feed['id']); ?>" title="<?php _e('Refresh Feed', 'mind-qtrl-community-feed-bot'); ?>" onclick="event.stopPropagation();">
                                        <span class="dashicons dashicons-update"></span>
                                    </button>
                                </td>
                                <td class="mqcfb-action-cell">
                                    <!-- Keep delete button but hidden until row hover -->
                                    <button class="mqcfb-icon-button mqcfb-delete-feed" data-id="<?php echo esc_attr($feed['id']); ?>" title="<?php _e('Delete Feed', 'mind-qtrl-community-feed-bot'); ?>" onclick="event.stopPropagation();">
                                        <span class="dashicons dashicons-no-alt"></span>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="5"><?php _e('No feeds found. Add your first RSS feed to get started.', 'mind-qtrl-community-feed-bot'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Preview tab -->
    <div class="mqcfb-content" id="preview-content">
        <div class="mqcfb-preview-header">
            <h2><?php _e('Feed Previews', 'mind-qtrl-community-feed-bot'); ?></h2>
            <div class="mqcfb-preview-filters">
                <select id="preview-feed-filter">
                    <option value=""><?php _e('All Feeds', 'mind-qtrl-community-feed-bot'); ?></option>
                    <?php foreach ($feeds as $feed) : ?>
                        <option value="<?php echo esc_attr($feed['id']); ?>"><?php echo esc_html($feed['name']); ?></option>
                    <?php endforeach; ?>
                </select>
                <button class="button mqcfb-refresh-preview"><?php _e('Refresh', 'mind-qtrl-community-feed-bot'); ?></button>
            </div>
        </div>
        
        <div class="mqcfb-preview-container">
            <div class="mqcfb-loading"><?php _e('Loading previews...', 'mind-qtrl-community-feed-bot'); ?></div>
            <div class="mqcfb-preview-grid"></div>
        </div>
    </div>

    <!-- Queue tab -->
    <div class="mqcfb-content" id="queue-content">
        <div class="mqcfb-queue-header">
            <h2><?php _e('Article Queue', 'mind-qtrl-community-feed-bot'); ?></h2>
            <div class="mqcfb-queue-filters">
                <select id="queue-status-filter">
                    <option value="pending"><?php _e('Pending', 'mind-qtrl-community-feed-bot'); ?></option>
                    <option value="posted"><?php _e('Posted', 'mind-qtrl-community-feed-bot'); ?></option>
                    <option value="failed"><?php _e('Failed', 'mind-qtrl-community-feed-bot'); ?></option>
                    <option value="skipped"><?php _e('Skipped', 'mind-qtrl-community-feed-bot'); ?></option>
                </select>
                <select id="queue-feed-filter">
                    <option value=""><?php _e('All Feeds', 'mind-qtrl-community-feed-bot'); ?></option>
                    <?php foreach ($feeds as $feed) : ?>
                        <option value="<?php echo esc_attr($feed['id']); ?>"><?php echo esc_html($feed['name']); ?></option>
                    <?php endforeach; ?>
                </select>
                <button class="button mqcfb-refresh-queue"><?php _e('Refresh', 'mind-qtrl-community-feed-bot'); ?></button>
            </div>
        </div>
        
        <div class="mqcfb-queue-list">
            <div class="mqcfb-loading"><?php _e('Loading queue...', 'mind-qtrl-community-feed-bot'); ?></div>
            <table class="wp-list-table widefat fixed striped mqcfb-table mqcfb-queue-table">
                <thead>
                    <tr>
                        <th><?php _e('Title', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th><?php _e('Feed', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th><?php _e('Date', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th><?php _e('Status', 'mind-qtrl-community-feed-bot'); ?></th>
                        <th><?php _e('Actions', 'mind-qtrl-community-feed-bot'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Queue items will be loaded here via AJAX -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Settings tab -->
    <div class="mqcfb-content" id="settings-content">
        <div class="mqcfb-settings-header">
            <h2><?php _e('Plugin Settings', 'mind-qtrl-community-feed-bot'); ?></h2>
        </div>
        
        <form id="mqcfb-settings-form" class="mqcfb-settings-form">
            <div class="mqcfb-settings-section">
                <h3><?php _e('Feed Settings', 'mind-qtrl-community-feed-bot'); ?></h3>
                
                <div class="mqcfb-setting-field">
                    <label for="schedule_frequency"><?php _e('Schedule Frequency', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="schedule_frequency" name="schedule_frequency">
                        <option value="five_minutes"><?php _e('Every 5 minutes', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="ten_minutes"><?php _e('Every 10 minutes', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="fifteen_minutes"><?php _e('Every 15 minutes', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="thirty_minutes"><?php _e('Every 30 minutes', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="hourly"><?php _e('Hourly', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="twice_daily"><?php _e('Twice Daily', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="daily"><?php _e('Daily', 'mind-qtrl-community-feed-bot'); ?></option>
                    </select>
                </div>
                
                <div class="mqcfb-setting-field">
                    <label for="max_items_per_feed"><?php _e('Max Items Per Feed', 'mind-qtrl-community-feed-bot'); ?></label>
                    <input type="number" id="max_items_per_feed" name="max_items_per_feed" min="1" max="50" value="10">
                    <p class="description"><?php _e('Maximum number of items to fetch from each feed', 'mind-qtrl-community-feed-bot'); ?></p>
                </div>
            </div>
            
            <div class="mqcfb-settings-section">
                <h3><?php _e('Content Settings', 'mind-qtrl-community-feed-bot'); ?></h3>
                
                <div class="mqcfb-setting-field">
                    <label for="default_post_format"><?php _e('Default Post Format', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="default_post_format" name="default_post_format">
                        <option value="full"><?php _e('Full Content', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="excerpt"><?php _e('Excerpt Only', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="title_only"><?php _e('Title Only', 'mind-qtrl-community-feed-bot'); ?></option>
                    </select>
                </div>
                
                <div class="mqcfb-setting-field">
                    <label for="default_image_handling"><?php _e('Default Image Handling', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="default_image_handling" name="default_image_handling">
                        <option value="embed"><?php _e('Embed in Content', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="featured"><?php _e('Use as Featured Image', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="none"><?php _e('No Images', 'mind-qtrl-community-feed-bot'); ?></option>
                    </select>
                    <p class="description"><?php _e('How to handle images from feed items', 'mind-qtrl-community-feed-bot'); ?></p>
                </div>
            </div>
            
            <div class="mqcfb-settings-section">
                <h3><?php _e('Auto-Posting Settings', 'mind-qtrl-community-feed-bot'); ?></h3>
                
                <div class="mqcfb-setting-field">
                    <label for="auto_post">
                        <input type="checkbox" id="auto_post" name="auto_post">
                        <?php _e('Enable Auto-Posting', 'mind-qtrl-community-feed-bot'); ?>
                    </label>
                    <p class="description"><?php _e('Automatically post new feed items to Fluent Community', 'mind-qtrl-community-feed-bot'); ?></p>
                </div>
                
                <div class="mqcfb-setting-field auto-post-delay-field" style="display: none;">
                    <label for="auto_post_delay"><?php _e('Auto-Post Delay (minutes)', 'mind-qtrl-community-feed-bot'); ?></label>
                    <input type="number" id="auto_post_delay" name="auto_post_delay" min="0" value="0">
                    <p class="description"><?php _e('Delay in minutes before auto-posting (0 for immediate)', 'mind-qtrl-community-feed-bot'); ?></p>
                </div>
            </div>
            
            <div class="mqcfb-settings-section">
                <h3><?php _e('Plugin Options', 'mind-qtrl-community-feed-bot'); ?></h3>
                
                <div class="mqcfb-setting-field">
                    <label for="remove_data_on_uninstall">
                        <input type="checkbox" id="remove_data_on_uninstall" name="remove_data_on_uninstall">
                        <?php _e('Remove Data on Uninstall', 'mind-qtrl-community-feed-bot'); ?>
                    </label>
                    <p class="description"><?php _e('Delete all plugin data when uninstalling', 'mind-qtrl-community-feed-bot'); ?></p>
                </div>
                
                <div class="mqcfb-setting-field">
                    <label for="debug_mode">
                        <input type="checkbox" id="debug_mode" name="debug_mode">
                        <?php _e('Debug Mode', 'mind-qtrl-community-feed-bot'); ?>
                    </label>
                    <p class="description"><?php _e('Enable debug logging', 'mind-qtrl-community-feed-bot'); ?></p>
                </div>
            </div>
            
            <div class="mqcfb-settings-actions">
                <button type="submit" class="button button-primary"><?php _e('Save Settings', 'mind-qtrl-community-feed-bot'); ?></button>
                <span class="mqcfb-settings-message"></span>
            </div>
        </form>
    </div>
    
    <!-- Feed Edit Modal -->
    <div id="mqcfb-feed-modal" class="mqcfb-modal">
        <div class="mqcfb-modal-content">
            <span class="mqcfb-modal-close">&times;</span>
            <h2 id="mqcfb-modal-title"><?php _e('Add New Feed', 'mind-qtrl-community-feed-bot'); ?></h2>
            
            <form id="mqcfb-feed-form">
                <input type="hidden" id="feed_id" name="feed_id" value="">
                
                <div class="mqcfb-form-field">
                    <label for="feed_name"><?php _e('Feed Name', 'mind-qtrl-community-feed-bot'); ?></label>
                    <input type="text" id="feed_name" name="feed_name" required>
                </div>
                
                <div class="mqcfb-form-field">
                    <label for="feed_url"><?php _e('Feed URL', 'mind-qtrl-community-feed-bot'); ?></label>
                    <input type="url" id="feed_url" name="feed_url" required>
                </div>
                
                <div class="mqcfb-form-field">
                    <label for="space_id"><?php _e('Fluent Community Space', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="space_id" name="space_id" required>
                        <option value=""><?php _e('Select a Space', 'mind-qtrl-community-feed-bot'); ?></option>
                        <!-- Spaces will be loaded via AJAX -->
                    </select>
                </div>
                
                <div class="mqcfb-form-field">
                    <label for="topic_ids"><?php _e('Topics', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="topic_ids" name="topic_ids[]" multiple>
                        <!-- Topics will be loaded via AJAX based on selected space -->
                    </select>
                    <p class="description"><?php _e('Optional: Select topics to assign to posts', 'mind-qtrl-community-feed-bot'); ?></p>
                </div>
                
                <div class="mqcfb-form-field">
                    <label for="author_id"><?php _e('Post Author', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="author_id" name="author_id">
                        <option value=""><?php _e('Select an Author', 'mind-qtrl-community-feed-bot'); ?></option>
                        <!-- Authors will be loaded via AJAX -->
                    </select>
                </div>
                
                <div class="mqcfb-form-field">
                    <label for="max_items"><?php _e('Max Items', 'mind-qtrl-community-feed-bot'); ?></label>
                    <input type="number" id="max_items" name="max_items" min="1" max="50" value="10">
                </div>
                
                <div class="mqcfb-form-field">
                    <label for="image_handling"><?php _e('Image Handling', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="image_handling" name="image_handling">
                        <option value="embed"><?php _e('Embed in Content', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="featured"><?php _e('Use as Featured Image', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="none"><?php _e('No Images', 'mind-qtrl-community-feed-bot'); ?></option>
                    </select>
                </div>
                
                <div class="mqcfb-form-field">
                    <label for="feed_status"><?php _e('Status', 'mind-qtrl-community-feed-bot'); ?></label>
                    <select id="feed_status" name="feed_status">
                        <option value="active"><?php _e('Active', 'mind-qtrl-community-feed-bot'); ?></option>
                        <option value="paused"><?php _e('Paused', 'mind-qtrl-community-feed-bot'); ?></option>
                    </select>
                </div>
                
                <div class="mqcfb-form-field">
                    <label>
                        <input type="checkbox" id="auto_post" name="auto_post">
                        <?php _e('Auto-Post New Items', 'mind-qtrl-community-feed-bot'); ?>
                    </label>
                </div>
                
                <div class="mqcfb-form-actions">
                    <button type="submit" class="button button-primary"><?php _e('Save Feed', 'mind-qtrl-community-feed-bot'); ?></button>
                    <button type="button" class="button mqcfb-modal-cancel"><?php _e('Cancel', 'mind-qtrl-community-feed-bot'); ?></button>
                    <span class="mqcfb-form-message"></span>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Article Preview Modal -->
    <div id="mqcfb-article-modal" class="mqcfb-modal">
        <div class="mqcfb-modal-content mqcfb-modal-large">
            <span class="mqcfb-modal-close">&times;</span>
            <h2 id="mqcfb-article-title"><?php _e('Article Preview', 'mind-qtrl-community-feed-bot'); ?></h2>
            
            <div id="mqcfb-article-content">
                <!-- Article content will be loaded here -->
            </div>
            
            <div class="mqcfb-modal-actions">
                <button type="button" class="button button-primary mqcfb-post-article"><?php _e('Post to Community', 'mind-qtrl-community-feed-bot'); ?></button>
                <button type="button" class="button mqcfb-modal-cancel"><?php _e('Close', 'mind-qtrl-community-feed-bot'); ?></button>
            </div>
        </div>
    </div>
</div>