<?php

/**
 * <PERSON><PERSON><PERSON> Handler for the Mind QTRL Community Feed Bot
 *
 * @since      1.0.0
 * @package    Mind_QTRL_Community_Feed_Bot
 * @subpackage Mind_QTRL_Community_Feed_Bot/includes
 */

class MQCFB_Ajax_Handler {

    /**
     * Initialize the class and set its properties.
     */
    public function __construct() {
        $this->register_ajax_hooks();
    }

    /**
     * Register AJAX hooks
     */
    private function register_ajax_hooks() {
        // Admin AJAX actions
        add_action('wp_ajax_mqcfb_save_feed', array($this, 'handle_save_feed'));
        add_action('wp_ajax_mqcfb_delete_feed', array($this, 'handle_delete_feed'));
        add_action('wp_ajax_mqcfb_test_feed', array($this, 'handle_test_feed'));
        add_action('wp_ajax_mqcfb_post_item', array($this, 'handle_post_item'));
        add_action('wp_ajax_mqcfb_run_schedule', array($this, 'handle_run_schedule'));
    }

    /**
     * Handle save feed AJAX request
     */
    public function handle_save_feed() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'mind-qtrl-community-feed-bot')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action', 'mind-qtrl-community-feed-bot')));
        }

        // Get settings class
        if (class_exists('Mind_QTRL_Community_Feed_Bot_Settings')) {
            $settings = new Mind_QTRL_Community_Feed_Bot_Settings();
            
            // Process feed data
            $feed_data = array();
            
            // Required fields
            if (empty($_POST['feed_url'])) {
                wp_send_json_error(array('message' => __('Feed URL is required', 'mind-qtrl-community-feed-bot')));
            }
            
            $feed_data['feed_url'] = esc_url_raw($_POST['feed_url']);
            $feed_data['title'] = sanitize_text_field($_POST['feed_title']);
            $feed_data['status'] = sanitize_text_field($_POST['status']);
            $feed_data['max_items'] = intval($_POST['max_items']);
            $feed_data['space_id'] = intval($_POST['space_id']);
            $feed_data['author_id'] = intval($_POST['author_id']);
            $feed_data['image_handling'] = sanitize_text_field($_POST['image_handling']);
            
            // Optional topic IDs
            if (!empty($_POST['topic_ids']) && is_array($_POST['topic_ids'])) {
                $feed_data['topic_ids'] = array_map('intval', $_POST['topic_ids']);
            } else {
                $feed_data['topic_ids'] = array();
            }
            
            // Save feed
            $feed_id = isset($_POST['feed_id']) ? intval($_POST['feed_id']) : 0;
            $result = $settings->save_feed($feed_data, $feed_id);
            
            if ($result) {
                wp_send_json_success(array(
                    'message' => __('Feed saved successfully', 'mind-qtrl-community-feed-bot'),
                    'feed_id' => $result
                ));
            } else {
                wp_send_json_error(array('message' => __('Failed to save feed', 'mind-qtrl-community-feed-bot')));
            }
        } else {
            wp_send_json_error(array('message' => __('Settings class not found', 'mind-qtrl-community-feed-bot')));
        }
    }

    /**
     * Handle delete feed AJAX request
     */
    public function handle_delete_feed() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'mind-qtrl-community-feed-bot')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action', 'mind-qtrl-community-feed-bot')));
        }

        // Get feed ID
        $feed_id = isset($_POST['feed_id']) ? intval($_POST['feed_id']) : 0;
        if (!$feed_id) {
            wp_send_json_error(array('message' => __('Invalid feed ID', 'mind-qtrl-community-feed-bot')));
        }

        // Get settings class
        if (class_exists('Mind_QTRL_Community_Feed_Bot_Settings')) {
            $settings = new Mind_QTRL_Community_Feed_Bot_Settings();
            $result = $settings->delete_feed($feed_id);
            
            if ($result) {
                wp_send_json_success(array('message' => __('Feed deleted successfully', 'mind-qtrl-community-feed-bot')));
            } else {
                wp_send_json_error(array('message' => __('Failed to delete feed', 'mind-qtrl-community-feed-bot')));
            }
        } else {
            wp_send_json_error(array('message' => __('Settings class not found', 'mind-qtrl-community-feed-bot')));
        }
    }

    /**
     * Handle test feed AJAX request
     */
    public function handle_test_feed() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'mind-qtrl-community-feed-bot')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action', 'mind-qtrl-community-feed-bot')));
        }

        // Get feed URL
        $feed_url = isset($_POST['feed_url']) ? esc_url_raw($_POST['feed_url']) : '';
        if (!$feed_url) {
            wp_send_json_error(array('message' => __('Invalid feed URL', 'mind-qtrl-community-feed-bot')));
        }

        // Get feed handler
        if (class_exists('MQCFB_Feed_Handler')) {
            $feed_handler = new MQCFB_Feed_Handler();
            $feed_data = $feed_handler->get_feed($feed_url, 5);
            
            if ($feed_data && !empty($feed_data['items'])) {
                wp_send_json_success(array(
                    'message' => __('Feed test successful', 'mind-qtrl-community-feed-bot'),
                    'feed_data' => $feed_data
                ));
            } else {
                wp_send_json_error(array('message' => __('Failed to fetch feed data', 'mind-qtrl-community-feed-bot')));
            }
        } else {
            wp_send_json_error(array('message' => __('Feed handler class not found', 'mind-qtrl-community-feed-bot')));
        }
    }

    /**
     * Handle post item AJAX request
     */
    public function handle_post_item() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'mind-qtrl-community-feed-bot')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action', 'mind-qtrl-community-feed-bot')));
        }

        // Get item ID
        $item_id = isset($_POST['item_id']) ? intval($_POST['item_id']) : 0;
        if (!$item_id) {
            wp_send_json_error(array('message' => __('Invalid item ID', 'mind-qtrl-community-feed-bot')));
        }

        // Get space manager and post item
        if (class_exists('MQCFB_Space_Manager')) {
            global $wpdb;
            
            // Get item data
            $item = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}mqcfb_article_queue WHERE id = %d",
                $item_id
            ));
            
            if (!$item) {
                wp_send_json_error(array('message' => __('Item not found', 'mind-qtrl-community-feed-bot')));
            }
            
            // Get feed data
            $feed = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}mqcfb_feeds WHERE id = %d",
                $item->feed_id
            ));
            
            if (!$feed) {
                wp_send_json_error(array('message' => __('Feed not found', 'mind-qtrl-community-feed-bot')));
            }
            
            // Prepare article data
            $article = array(
                'title' => $item->article_title,
                'url' => $item->article_url,
                'date' => $item->article_date,
                'content' => $item->article_content,
                'excerpt' => $item->article_excerpt,
                'image' => $item->article_image
            );
            
            // Get topic IDs
            $topic_ids = array();
            if (!empty($feed->topic_ids)) {
                $topic_ids = maybe_unserialize($feed->topic_ids);
            }
            
            // Post to community
            $space_manager = new MQCFB_Space_Manager();
            $activity_id = $space_manager->post_to_community(
                $article,
                $feed->space_id,
                $topic_ids,
                $feed->author_id,
                $feed->image_handling
            );
            
            if ($activity_id) {
                // Update item status
                $wpdb->update(
                    $wpdb->prefix . 'mqcfb_article_queue',
                    array(
                        'status' => 'posted',
                        'posted_time' => current_time('mysql'),
                        'post_id' => $activity_id
                    ),
                    array('id' => $item_id),
                    array('%s', '%s', '%d'),
                    array('%d')
                );
                
                wp_send_json_success(array(
                    'message' => __('Item posted successfully', 'mind-qtrl-community-feed-bot'),
                    'activity_id' => $activity_id
                ));
            } else {
                wp_send_json_error(array('message' => __('Failed to post item', 'mind-qtrl-community-feed-bot')));
            }
        } else {
            wp_send_json_error(array('message' => __('Space manager class not found', 'mind-qtrl-community-feed-bot')));
        }
    }

    /**
     * Handle run schedule AJAX request
     */
    public function handle_run_schedule() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqcfb_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'mind-qtrl-community-feed-bot')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action', 'mind-qtrl-community-feed-bot')));
        }

        // Get scheduler class
        if (class_exists('MQCFB_Scheduler')) {
            $scheduler = new MQCFB_Scheduler();
            $results = $scheduler->run_scheduled_tasks();
            
            wp_send_json_success(array(
                'message' => __('Schedule executed successfully', 'mind-qtrl-community-feed-bot'),
                'results' => $results
            ));
        } else {
            wp_send_json_error(array('message' => __('Scheduler class not found', 'mind-qtrl-community-feed-bot')));
        }
    }
}
