<?php

class MQCFB_Feed_Handler {
    private $image_detector;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize the image detector if available
        if (class_exists('MQCFB_Image_Detector')) {
            $this->image_detector = new MQCFB_Image_Detector();
        }
    }
    
    /**
     * Get feed data from URL
     *
     * @param string $feed_url The feed URL
     * @param int $max_items Maximum number of items to fetch
     * @return array|false Feed data or false on failure
     */
    public function get_feed($feed_url, $max_items = 10) {
        if (empty($feed_url)) {
            return false;
        }
        
        // Sanitize and validate URL
        $feed_url = esc_url_raw($feed_url);
        if (!filter_var($feed_url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        try {
            // Use WordPress's built-in feed fetcher
            if (function_exists('fetch_feed')) {
                $feed = fetch_feed($feed_url);
                
                if (is_wp_error($feed)) {
                    error_log('MQCFB Error fetching feed: ' . $feed->get_error_message());
                    return false;
                }
                
                // Set the number of items to fetch
                $max_items = $feed->get_item_quantity($max_items);
                $feed_items = $feed->get_items(0, $max_items);
                
                if (empty($feed_items)) {
                    return false;
                }
                
                $feed_data = array(
                    'title' => $feed->get_title(),
                    'description' => $feed->get_description(),
                    'link' => $feed->get_permalink(),
                    'items' => array()
                );
                
                foreach ($feed_items as $item) {
                    $item_data = $this->process_feed_item($item);
                    if ($item_data) {
                        $feed_data['items'][] = $item_data;
                    }
                }
                
                return $feed_data;
            } else {
                // Fallback to SimplePie if available
                if (class_exists('SimplePie')) {
                    $feed = new SimplePie();
                    $feed->set_feed_url($feed_url);
                    $feed->enable_cache(false);
                    $feed->init();
                    
                    if ($feed->error()) {
                        error_log('MQCFB SimplePie Error: ' . $feed->error());
                        return false;
                    }
                    
                    $feed_data = array(
                        'title' => $feed->get_title(),
                        'description' => $feed->get_description(),
                        'link' => $feed->get_permalink(),
                        'items' => array()
                    );
                    
                    $feed_items = $feed->get_items(0, $max_items);
                    
                    foreach ($feed_items as $item) {
                        $item_data = $this->process_feed_item($item);
                        if ($item_data) {
                            $feed_data['items'][] = $item_data;
                        }
                    }
                    
                    return $feed_data;
                } else {
                    error_log('MQCFB Error: No feed parser available');
                    return false;
                }
            }
        } catch (Exception $e) {
            error_log('MQCFB Error: ' . $e->getMessage());
            return false;
        }
    }
        
    /**
     * Process a feed item
     *
     * @param object $item The feed item
     * @return array|false Processed item data or false on failure
     */
    private function process_feed_item($item) {
        try {
            // Get basic item data
            $title = $item->get_title();
            $link = $item->get_permalink();
            $date = $item->get_date('Y-m-d H:i:s');
            $content = $item->get_content();
            $excerpt = $item->get_description();
            
            // Try to get the image from the content
            $image = $this->get_image_from_item($item, $content);
            
            // Clean up content if needed
            $content = $this->clean_content($content);
            
            return array(
                'title' => $title,
                'url' => $link,
                'date' => $date,
                'content' => $content,
                'excerpt' => $excerpt,
                'image' => $image
            );
        } catch (Exception $e) {
            error_log('MQCFB Error processing feed item: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get image from feed item
     *
     * @param object $item The feed item
     * @param string $content The item content
     * @return string|false Image URL or false if not found
     */
    private function get_image_from_item($item, $content) {
        // Try to get enclosure image first
        $enclosure = $item->get_enclosure();
        if ($enclosure && $enclosure->get_link() && in_array($enclosure->get_type(), array('image/jpeg', 'image/jpg', 'image/png', 'image/gif'))) {
            return $enclosure->get_link();
        }
        
        // Try to get media:content image
        $media_content = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'content');
        if ($media_content && isset($media_content[0]['attribs']['']['url'])) {
            return $media_content[0]['attribs']['']['url'];
        }
        
        // Try to get media:thumbnail image
        $media_thumbnail = $item->get_item_tags(SIMPLEPIE_NAMESPACE_MEDIARSS, 'thumbnail');
        if ($media_thumbnail && isset($media_thumbnail[0]['attribs']['']['url'])) {
            return $media_thumbnail[0]['attribs']['']['url'];
        }
        
        // Try to extract image from content using image detector
        if ($this->image_detector && !empty($content)) {
            $image_url = $this->image_detector->extract_image_from_html($content);
            if ($image_url) {
                return $image_url;
            }
        }
        
        // Try to extract image from content using simple regex
        if (!empty($content)) {
            preg_match('/<img[^>]+src=["\']([^"\'>]+)["\'][^>]*>/i', $content, $matches);
            if (!empty($matches[1])) {
                return $matches[1];
            }
        }
        
        return false;
    }
    
    /**
     * Clean up content
     *
     * @param string $content The content to clean
     * @return string Cleaned content
     */
    private function clean_content($content) {
        // Remove unwanted tags
        $content = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $content);
        $content = preg_replace('/<style\b[^>]*>(.*?)<\/style>/is', '', $content);
        
        // Remove inline styles and classes
        $content = preg_replace('/\s+style=["\'][^"\'>]*["\']/', '', $content);
        $content = preg_replace('/\s+class=["\'][^"\'>]*["\']/', '', $content);
        
        // Remove empty paragraphs
        $content = preg_replace('/<p[^>]*>\s*<\/p>/i', '', $content);
        
        return $content;
    }
    
    /**
     * Get full article content using Readability
     *
     * @param string $url The article URL
     * @return string|false Article content or false on failure
     */
    public function get_full_article($url) {
        if (empty($url)) {
            return false;
        }
        
        try {
            // Get the article HTML
            $html = $this->fetch_url_content($url);
            if (!$html) {
                return false;
            }
            
            // Use Readability if available
            if (class_exists('Readability')) {
                $readability = new Readability($html, $url);
                $result = $readability->parse();
                
                if ($result) {
                    return $result->getContent();
                }
            }
            
            // Fallback to simple HTML DOM if available
            if (function_exists('str_get_html')) {
                $dom = str_get_html($html);
                if ($dom) {
                    // Try to find the main content
                    $content_selectors = array(
                        'article',
                        '.post-content',
                        '.entry-content',
                        '.content',
                        '#content',
                        '.article-content'
                    );
                    
                    foreach ($content_selectors as $selector) {
                        $content = $dom->find($selector, 0);
                        if ($content) {
                            return $content->innertext();
                        }
                    }
                }
            }
            
            // If all else fails, return the raw HTML
            return $html;
        } catch (Exception $e) {
            error_log('MQCFB Error getting full article: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Fetch URL content
     *
     * @param string $url The URL to fetch
     * @return string|false The content or false on failure
     */
    private function fetch_url_content($url) {
        // Use WordPress HTTP API
        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ));
        
        if (is_wp_error($response)) {
            error_log('MQCFB Error fetching URL: ' . $response->get_error_message());
            return false;
        }
        
        $code = wp_remote_retrieve_response_code($response);
        if ($code !== 200) {
            error_log('MQCFB Error: URL returned status code ' . $code);
            return false;
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Save article to queue
     *
     * @param array $article Article data
     * @param int $feed_id Feed ID
     * @return int|false Article ID or false on failure
     */
    public function save_article_to_queue($article, $feed_id) {
        global $wpdb;
        
        if (empty($article) || empty($feed_id)) {
            return false;
        }
        
        // Check if article already exists in queue
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}mqcfb_article_queue WHERE feed_id = %d AND article_url = %s",
            $feed_id,
            $article['url']
        ));
        
        if ($existing) {
            return $existing;
        }
        
        // Insert article into queue
        $result = $wpdb->insert(
            $wpdb->prefix . 'mqcfb_article_queue',
            array(
                'feed_id' => $feed_id,
                'article_title' => $article['title'],
                'article_url' => $article['url'],
                'article_date' => $article['date'],
                'article_content' => $article['content'],
                'article_excerpt' => $article['excerpt'],
                'article_image' => $article['image'],
                'status' => 'pending',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Process feeds
     *
     * @param bool $auto_post Whether to automatically post articles
     * @return array Processing results
     */
    public function process_feeds($auto_post = false) {
        global $wpdb;
        
        $results = array(
            'processed' => 0,
            'added' => 0,
            'posted' => 0,
            'errors' => 0
        );
        
        // Get active feeds
        $feeds = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}mqcfb_feeds WHERE status = 'active'"
        );
        
        if (empty($feeds)) {
            return $results;
        }
        
        foreach ($feeds as $feed) {
            $results['processed']++;
            
            // Get feed data
            $feed_data = $this->get_feed($feed->feed_url, $feed->max_items);
            
            if (!$feed_data || empty($feed_data['items'])) {
                $results['errors']++;
                continue;
            }
            
            // Update last fetch time
            $wpdb->update(
                $wpdb->prefix . 'mqcfb_feeds',
                array('last_fetch' => current_time('mysql')),
                array('id' => $feed->id),
                array('%s'),
                array('%d')
            );
            
            // Process feed items
            foreach ($feed_data['items'] as $item) {
                // Save article to queue
                $article_id = $this->save_article_to_queue($item, $feed->id);
                
                if ($article_id) {
                    $results['added']++;
                    
                    // Auto post if enabled
                    if ($auto_post && !empty($feed->space_id)) {
                        // Get space manager
                        if (class_exists('MQCFB_Space_Manager')) {
                            $space_manager = new MQCFB_Space_Manager();
                            
                            // Get topic IDs
                            $topic_ids = array();
                            if (!empty($feed->topic_ids)) {
                                $topic_ids = maybe_unserialize($feed->topic_ids);
                            }
                            
                            // Post to community
                            $activity_id = $space_manager->post_to_community(
                                $item,
                                $feed->space_id,
                                $topic_ids,
                                $feed->author_id,
                                $feed->image_handling
                            );
                            
                            if ($activity_id) {
                                // Update article status
                                $wpdb->update(
                                    $wpdb->prefix . 'mqcfb_article_queue',
                                    array(
                                        'status' => 'posted',
                                        'posted_time' => current_time('mysql'),
                                        'post_id' => $activity_id
                                    ),
                                    array('id' => $article_id),
                                    array('%s', '%s', '%d'),
                                    array('%d')
                                );
                                
                                $results['posted']++;
                            }
                        }
                    }
                }
            }
        }
        
        return $results;
    }
}
