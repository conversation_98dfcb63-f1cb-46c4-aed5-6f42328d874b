<?php

class MQCFB_Image_Detector {
    
    /**
     * Extract the main image from HTML content
     *
     * @param string $html The HTML content
     * @return string|false Image URL or false if not found
     */
    public function extract_image_from_html($html) {
        if (empty($html)) {
            return false;
        }
        
        // Try to use Simple HTML DOM if available
        if (function_exists('str_get_html')) {
            return $this->extract_with_simple_html_dom($html);
        }
        
        // Fallback to regex-based extraction
        return $this->extract_with_regex($html);
    }
    
    /**
     * Extract image using Simple HTML DOM
     *
     * @param string $html The HTML content
     * @return string|false Image URL or false if not found
     */
    private function extract_with_simple_html_dom($html) {
        $dom = str_get_html($html);
        if (!$dom) {
            return false;
        }
        
        // Try to find the main image using various selectors
        $image_selectors = array(
            '.wp-post-image',
            '.featured-image img',
            '.post-thumbnail img',
            'article img:first-child',
            '.entry-content img:first-child',
            'img.size-full',
            'img.size-large',
            'img'
        );
        
        foreach ($image_selectors as $selector) {
            $img = $dom->find($selector, 0);
            if ($img && isset($img->src) && !empty($img->src)) {
                return $img->src;
            }
        }
        
        // Try to find any image
        $images = $dom->find('img');
        if ($images && count($images) > 0) {
            // Filter out small images and icons
            foreach ($images as $img) {
                // Skip if no src attribute
                if (!isset($img->src) || empty($img->src)) {
                    continue;
                }
                
                // Skip if it's likely an icon or small image
                if (strpos($img->src, 'icon') !== false || strpos($img->src, 'logo') !== false) {
                    continue;
                }
                
                // Check dimensions if available
                $width = isset($img->width) ? (int)$img->width : 0;
                $height = isset($img->height) ? (int)$img->height : 0;
                
                // Skip small images
                if (($width > 0 && $width < 100) || ($height > 0 && $height < 100)) {
                    continue;
                }
                
                return $img->src;
            }
            
            // If we couldn't find a suitable image, return the first one
            if (isset($images[0]->src) && !empty($images[0]->src)) {
                return $images[0]->src;
            }
        }
        
        return false;
    }
    
    /**
     * Extract image using regex
     *
     * @param string $html The HTML content
     * @return string|false Image URL or false if not found
     */
    private function extract_with_regex($html) {
        // Try to find the featured image
        $patterns = array(
            '/<img[^>]+class=["\']([^"\']*)wp-post-image[^"\']?["\'][^>]+src=["\']([^"\'>]+)["\'][^>]*>/i',
            '/<img[^>]+src=["\']([^"\'>]+)["\'][^>]*class=["\']([^"\']*)wp-post-image[^"\']?["\'][^>]*>/i',
            '/<img[^>]+class=["\']([^"\']*)featured-image[^"\']?["\'][^>]+src=["\']([^"\'>]+)["\'][^>]*>/i',
            '/<img[^>]+src=["\']([^"\'>]+)["\'][^>]*class=["\']([^"\']*)featured-image[^"\']?["\'][^>]*>/i',
            '/<img[^>]+src=["\']([^"\'>]+)["\'][^>]*>/i'
        );
        
        foreach ($patterns as $pattern) {
            preg_match($pattern, $html, $matches);
            if (!empty($matches)) {
                // The image URL will be in the last captured group
                return end($matches);
            }
        }
        
        return false;
    }
    
    /**
     * Check if an image URL is valid
     *
     * @param string $url The image URL
     * @return bool Whether the URL is valid
     */
    public function is_valid_image_url($url) {
        if (empty($url)) {
            return false;
        }
        
        // Check if it's a valid URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Check if it has an image extension
        $ext = strtolower(pathinfo($url, PATHINFO_EXTENSION));
        $valid_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp');
        
        if (in_array($ext, $valid_extensions)) {
            return true;
        }
        
        // If no extension, try to check the content type
        $headers = get_headers($url, 1);
        if (isset($headers['Content-Type'])) {
            $content_type = is_array($headers['Content-Type']) ? 
                $headers['Content-Type'][0] : $headers['Content-Type'];
            
            return strpos($content_type, 'image/') === 0;
        }
        
        return false;
    }
    
    /**
     * Get image dimensions
     *
     * @param string $url The image URL
     * @return array|false Array with width and height or false on failure
     */
    public function get_image_dimensions($url) {
        if (!$this->is_valid_image_url($url)) {
            return false;
        }
        
        // Try to get image size
        $size = @getimagesize($url);
        if ($size && isset($size[0]) && isset($size[1])) {
            return array(
                'width' => $size[0],
                'height' => $size[1]
            );
        }
        
        return false;
    }
}