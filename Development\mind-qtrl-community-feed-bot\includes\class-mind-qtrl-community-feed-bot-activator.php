<?php

class Mind_QTRL_Community_Feed_Bot_Activator {

    /**
     * Short Description. (use period)
     *
     * Long Description.
     *
     * @since    0.0.1
     */
    public static function activate() {
        global $wpdb;
        
        // Set up database tables
        self::setup_database_tables();
        
        // Set default options
        self::set_default_options();
        
        // Schedule cron events
        if (!wp_next_scheduled('mqcfb_process_feeds')) {
            wp_schedule_event(time(), 'hourly', 'mqcfb_process_feeds');
        }
    }
    
    /**
     * Set up the database tables needed by the plugin
     */
    private static function setup_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Create feeds table
        $table_name = $wpdb->prefix . 'mqcfb_feeds';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            feed_name varchar(255) NOT NULL,
            feed_url varchar(255) NOT NULL,
            space_id int(11) NOT NULL,
            topic_ids text,
            author_id int(11) DEFAULT NULL,
            status varchar(20) DEFAULT 'active',
            last_fetch datetime DEFAULT NULL,
            fetch_frequency varchar(20) DEFAULT 'hourly',
            post_format varchar(20) DEFAULT 'full',
            image_handling varchar(20) DEFAULT 'embed',
            max_items int(11) DEFAULT 10,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY feed_url (feed_url(191)),
            KEY space_id (space_id),
            KEY status (status)
        ) $charset_collate;";
        
        // Create article queue table
        $table_name_queue = $wpdb->prefix . 'mqcfb_article_queue';
        
        $sql_queue = "CREATE TABLE $table_name_queue (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            feed_id mediumint(9) NOT NULL,
            article_title text NOT NULL,
            article_url varchar(255) NOT NULL,
            article_date datetime DEFAULT NULL,
            article_content longtext,
            article_excerpt text,
            article_image varchar(255),
            status varchar(20) DEFAULT 'pending',
            scheduled_time datetime DEFAULT NULL,
            posted_time datetime DEFAULT NULL,
            post_id int(11) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY feed_id (feed_id),
            KEY article_url (article_url(191)),
            KEY status (status)
        ) $charset_collate;";
        
        // Use dbDelta to create/update tables
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($sql_queue);
        
        // Store the database version
        update_option('mqcfb_db_version', '1.0');
    }
    
    /**
     * Set default plugin options
     */
    private static function set_default_options() {
        // Default settings
        $default_settings = array(
            'schedule_frequency' => 'hourly',
            'max_items_per_feed' => 10,
            'default_post_format' => 'full',
            'default_image_handling' => 'embed',
            'remove_data_on_uninstall' => false,
            'auto_post' => false,
            'auto_post_delay' => 0,
            'debug_mode' => false
        );
        
        // Only set options if they don't already exist
        if (!get_option('mqcfb_settings')) {
            update_option('mqcfb_settings', $default_settings);
        }
        
        // Set the option for removing data on uninstall
        if (!get_option('mqcfb_remove_data_on_uninstall')) {
            update_option('mqcfb_remove_data_on_uninstall', false);
        }
    }
}