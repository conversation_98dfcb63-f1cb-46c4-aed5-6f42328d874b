













































































































































}    }        return $wpdb->get_results("SELECT * FROM {$wpdb->prefix}mqcfb_feeds ORDER BY title ASC");        // Change 'name' to 'title' in the ORDER BY clause                global $wpdb;    public function get_feeds() {     */     * @return array Feeds     *     * Get all feeds    /**    }        return isset($this->settings[$key]) ? $this->settings[$key] : $default;                }            $this->init();        if (!isset($this->settings)) {    public function get_setting($key, $default = null) {     */     * @return mixed Setting value     * @param mixed $default Default value if setting not found     * @param string $key Setting key     *     * Get a specific setting    /**        }        return $validated;                }            $validated['auto_post_delay'] = 0;        if ($validated['auto_post_delay'] < 0) {        $validated['auto_post_delay'] = intval($settings['auto_post_delay']);        // Validate auto post delay                $validated['debug_mode'] = !empty($settings['debug_mode']);        $validated['auto_post'] = !empty($settings['auto_post']);        $validated['remove_data_on_uninstall'] = !empty($settings['remove_data_on_uninstall']);        // Validate boolean settings                    $settings['default_image_handling'] : $default_settings['default_image_handling'];        $validated['default_image_handling'] = in_array($settings['default_image_handling'], $valid_image_handling) ?         $valid_image_handling = array('embed', 'featured', 'none');        // Validate image handling                    $settings['default_post_format'] : $default_settings['default_post_format'];        $validated['default_post_format'] = in_array($settings['default_post_format'], $valid_formats) ?         $valid_formats = array('full', 'excerpt', 'title_only');        // Validate post format                }            $validated['max_items_per_feed'] = $default_settings['max_items_per_feed'];        if ($validated['max_items_per_feed'] < 1 || $validated['max_items_per_feed'] > 50) {        $validated['max_items_per_feed'] = intval($settings['max_items_per_feed']);        // Validate max items per feed                    $settings['schedule_frequency'] : $default_settings['schedule_frequency'];        $validated['schedule_frequency'] = in_array($settings['schedule_frequency'], $valid_frequencies) ?         $valid_frequencies = array('five_minutes', 'ten_minutes', 'fifteen_minutes', 'thirty_minutes', 'hourly', 'twice_daily', 'daily');        // Validate schedule frequency                $validated = array();        $default_settings = $this->get_default_settings();    private function validate_settings($settings) {     */     * @return array Validated settings     * @param array $settings Settings to validate     *     * Validate settings    /**        }        return $result;                }            $this->settings = $validated_settings;        if ($result) {        // Update instance settings                $result = update_option('mqcfb_settings', $validated_settings);        // Update settings                $validated_settings = $this->validate_settings($new_settings);        // Validate settings    public function update_plugin_settings($new_settings) {     */     * @return bool Whether the settings were updated     * @param array $new_settings New settings     *     * Update plugin settings    /**        }        );            'debug_mode' => false            'auto_post_delay' => 0,            'auto_post' => false,            'remove_data_on_uninstall' => false,            'default_image_handling' => 'embed',            'default_post_format' => 'full',            'max_items_per_feed' => 10,            'schedule_frequency' => 'hourly',        return array(    public function get_default_settings() {     */     * @return array Default settings     *     * Get default settings    /**        }        return $settings;                }            }                $settings[$key] = $value;            if (!isset($settings[$key])) {        foreach ($default_settings as $key => $value) {        // Ensure all default settings exist                $settings = get_option('mqcfb_settings', $default_settings);        $default_settings = $this->get_default_settings();    public function get_plugin_settings() {     */     * @return array Plugin settings     *     * Get plugin settings    /**        }        $this->settings = $this->get_plugin_settings();    public function init() {     */     * Initialize the settings    /**        private $settings;class MQCFB_Settings {<?php