<?php

class Mind_QTRL_Community_Feed_Bot {
    private $version;
    private $plugin_name;
    private $settings;
    private $feed_handler;
    private $scheduler;
    private $image_detector;
    private $space_manager;
    private $loader;
    private $admin;

    /**
     * Define the core functionality of the plugin.
     *
     * Load dependencies, set locale, and register admin hooks.
     *
     * @since    0.0.1
     */
    public function __construct() {
        if (defined('MQCFB_VERSION')) {
            $this->version = MQCFB_VERSION;
        } else {
            $this->version = '0.0.1';
        }
        $this->plugin_name = 'mind-qtrl-community-feed-bot';

        $this->load_dependencies();
        $this->set_locale();
        $this->define_admin_hooks();
        $this->define_cron_hooks();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * @since    0.0.1
     * @access   private
     */
    private function load_dependencies() {
        // Check if required files exist before including them
        $required_files = [
            'includes/class-mind-qtrl-community-feed-bot-loader.php',
            'includes/class-space-manager.php',
            'includes/class-feed-handler.php',
            'includes/class-image-detector.php',
            'includes/class-scheduler.php',
            'includes/class-settings.php',
            'includes/class-ajax-handler.php',
            'admin/class-mind-qtrl-community-feed-bot-admin.php',
            'admin/class-mind-qtrl-community-feed-bot-settings.php'
        ];
        
        foreach ($required_files as $file) {
            $file_path = MQCFB_PLUGIN_DIR . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            } else {
                error_log('MQCFB Error: Required file not found: ' . $file_path);
            }
        }
        
        // Load external libraries
        $this->load_libraries();
        
        // Initialize components safely
        $this->loader = class_exists('Mind_QTRL_Community_Feed_Bot_Loader') ? 
            new Mind_QTRL_Community_Feed_Bot_Loader() : null;
            
        $this->space_manager = class_exists('MQCFB_Space_Manager') ?
            new MQCFB_Space_Manager() : null;
        
        if (class_exists('MQCFB_Settings')) {
            $this->settings = new MQCFB_Settings();
        }
        
        if (class_exists('MQCFB_Feed_Handler')) {
            $this->feed_handler = new MQCFB_Feed_Handler();
        }
        
        if (class_exists('MQCFB_Image_Detector')) {
            $this->image_detector = new MQCFB_Image_Detector();
        }
        
        if (class_exists('MQCFB_Scheduler')) {
            $this->scheduler = new MQCFB_Scheduler();
        }
            
        if ($this->loader === null) {
            error_log('MQCFB Error: Critical component "loader" could not be initialized');
        }
        
        // Load required files
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-feed-handler.php';
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-ajax-handler.php';
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-scheduler.php';
    }
    
    /**
     * Load external libraries required by the plugin
     * 
     * @since    0.0.1
     * @access   private
     */
    private function load_libraries() {
        // Load SimplePie if not already loaded by WordPress
        if (!class_exists('SimplePie')) {
            if (file_exists(MQCFB_PLUGIN_DIR . 'lib/simplepie/autoloader.php')) {
                require_once MQCFB_PLUGIN_DIR . 'lib/simplepie/autoloader.php';
            } else if (!function_exists('fetch_feed')) {
                // Try to use WordPress's built-in feed functionality
                require_once(ABSPATH . WPINC . '/feed.php');
            }
        }
        
        // Load Simple HTML DOM if not already loaded
        if (!class_exists('simple_html_dom')) {
            if (file_exists(MQCFB_PLUGIN_DIR . 'lib/simple_html_dom.php')) {
                require_once MQCFB_PLUGIN_DIR . 'lib/simple_html_dom.php';
            }
        }
        
        // Load Readability
        if (!class_exists('Readability')) {
            if (file_exists(MQCFB_PLUGIN_DIR . 'lib/readability/Readability.php')) {
                require_once MQCFB_PLUGIN_DIR . 'lib/readability/Readability.php';
            }
        }
    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * Uses the Mind_QTRL_Community_Feed_Bot_i18n class in order to set the domain and to register the hook
     * with WordPress.
     *
     * @since    0.0.1
     * @access   private
     */
    private function set_locale() {
        // Load the plugin text domain for translation
        load_plugin_textdomain(
            'mind-qtrl-community-feed-bot',
            false,
            dirname(dirname(plugin_basename(__FILE__))) . '/languages/'
        );
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    0.0.1
     * @access   private
     */
    private function define_admin_hooks() {
        if (!isset($this->loader) || !class_exists('Mind_QTRL_Community_Feed_Bot_Admin')) {
            return;
        }
        
        $plugin_admin = new Mind_QTRL_Community_Feed_Bot_Admin($this->get_plugin_name(), $this->get_version());
        $this->admin = $plugin_admin; // Store reference to admin object for cron hooks

        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');
        $this->loader->add_action('admin_menu', $plugin_admin, 'add_admin_menu');
        
        // REST API endpoints
        $this->loader->add_action('rest_api_init', $plugin_admin, 'register_rest_routes');
        
        // AJAX handlers
        $this->loader->add_action('wp_ajax_mqcfb_get_spaces', $plugin_admin, 'ajax_get_spaces');
        $this->loader->add_action('wp_ajax_mqcfb_get_space_topics', $plugin_admin, 'ajax_get_space_topics');
        $this->loader->add_action('wp_ajax_mqcfb_get_feed', $plugin_admin, 'ajax_get_feed');
        $this->loader->add_action('wp_ajax_mqcfb_save_feed', $plugin_admin, 'ajax_save_feed');
        $this->loader->add_action('wp_ajax_mqcfb_delete_feed', $plugin_admin, 'ajax_delete_feed');
        $this->loader->add_action('wp_ajax_mqcfb_check_feed_connection', $plugin_admin, 'ajax_check_feed_connection');
        $this->loader->add_action('wp_ajax_mqcfb_get_space_name', $plugin_admin, 'ajax_get_space_name');
        $this->loader->add_action('wp_ajax_mqcfb_get_community_users', $plugin_admin, 'ajax_get_community_users');
        
        // Add AJAX handlers for settings
        $this->loader->add_action('wp_ajax_mqcfb_get_settings', $plugin_admin, 'ajax_get_settings');
        $this->loader->add_action('wp_ajax_mqcfb_save_settings', $plugin_admin, 'ajax_save_settings');
        
        // Add AJAX handler for article preview
        $this->loader->add_action('wp_ajax_mqcfb_preview_article', $plugin_admin, 'ajax_preview_article');
        
        // Add AJAX handler for posting to community
        $this->loader->add_action('wp_ajax_mqcfb_post_to_community', $plugin_admin, 'ajax_post_to_community');
        
        // Cron hooks for feed processing
        $this->loader->add_action('mqcfb_process_feeds', $plugin_admin, 'process_feeds');
    }

    /**
     * Register all of the hooks related to cron tasks
     *
     * @since    0.0.1
     * @access   private
     */
    private function define_cron_hooks() {
        // Prevent fatal errors on deactivation by checking if admin class is loaded
        if (isset($this->loader) && isset($this->admin)) {
            // Add cron schedule for processing feeds
            add_filter('cron_schedules', array($this, 'add_cron_intervals'));
            
            // Schedule the feed processing event if it's not already scheduled
            if (!wp_next_scheduled('mqcfb_process_feeds')) {
                wp_schedule_event(time(), 'hourly', 'mqcfb_process_feeds');
            }
            
            // Register the action hook for processing feeds
            add_action('mqcfb_process_feeds', array($this->admin, 'process_feeds'));
        }
    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    0.0.1
     */
    public function run() {
        // Initialize settings
        if (isset($this->settings) && method_exists($this->settings, 'init')) {
            $this->settings->init();
        }

        // Register custom cron schedules
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));

        // Schedule cron jobs
        $this->schedule_events();
        
        // Execute registered hooks
        if (isset($this->loader)) {
            $this->loader->run();
        }
    }

    /**
     * Add custom cron schedules
     *
     * @param array $schedules Current cron schedules
     * @return array Modified cron schedules
     */
    public function add_cron_schedules($schedules) {
        $schedules['five_minutes'] = array(
            'interval' => 300, // 5 minutes in seconds
            'display'  => __('Every 5 Minutes', 'mind-qtrl-community-feed-bot'),
        );
        
        $schedules['ten_minutes'] = array(
            'interval' => 600, // 10 minutes in seconds
            'display'  => __('Every 10 Minutes', 'mind-qtrl-community-feed-bot'),
        );
        
        $schedules['fifteen_minutes'] = array(
            'interval' => 900, // 15 minutes in seconds
            'display'  => __('Every 15 Minutes', 'mind-qtrl-community-feed-bot'),
        );
        
        $schedules['thirty_minutes'] = array(
            'interval' => 1800, // 30 minutes in seconds
            'display'  => __('Every 30 Minutes', 'mind-qtrl-community-feed-bot'),
        );
        
        return $schedules;
    }

    /**
     * Add custom cron intervals
     *
     * @since    0.0.1
     * @param    array $schedules Existing cron schedules
     * @return   array Updated cron schedules
     */
    public function add_cron_intervals($schedules) {
        $schedules['five_minutes'] = array(
            'interval' => 5 * 60,
            'display'  => __('Every Five Minutes', 'mind-qtrl-community-feed-bot')
        );
        
        $schedules['ten_minutes'] = array(
            'interval' => 10 * 60,
            'display'  => __('Every Ten Minutes', 'mind-qtrl-community-feed-bot')
        );
        
        $schedules['fifteen_minutes'] = array(
            'interval' => 15 * 60,
            'display'  => __('Every Fifteen Minutes', 'mind-qtrl-community-feed-bot')
        );
        
        $schedules['thirty_minutes'] = array(
            'interval' => 30 * 60,
            'display'  => __('Every Thirty Minutes', 'mind-qtrl-community-feed-bot')
        );
        
        return $schedules;
    }

    /**
     * Schedule cron events based on settings
     */
    private function schedule_events() {
        // Check settings exists to avoid errors
        if (!isset($this->settings) || !method_exists($this->settings, 'get_plugin_settings')) {
            return;
        }
        
        try {
            $settings = $this->settings->get_plugin_settings();
            $schedule_frequency = isset($settings['schedule_frequency']) ? $settings['schedule_frequency'] : 'hourly';
            
            // Ensure the frequency is valid with strict validation
            $valid_frequencies = array('five_minutes', 'ten_minutes', 'fifteen_minutes', 'thirty_minutes', 'hourly', 'twice_daily', 'daily');
            if (!in_array($schedule_frequency, $valid_frequencies, true)) {
                error_log('MQCFB Notice: Invalid schedule frequency "' . $schedule_frequency . '". Defaulting to hourly.');
                $schedule_frequency = 'hourly'; // Default to hourly if invalid
            }
            
            // Clear any existing scheduled events with clear_hooks in case we have multiple events
            wp_clear_scheduled_hook('mqcfb_process_feeds');
            
            // Schedule the event with the selected frequency
            if (!wp_next_scheduled('mqcfb_process_feeds')) {
                $result = wp_schedule_event(time(), $schedule_frequency, 'mqcfb_process_feeds');
                
                if ($result === false) {
                    error_log('MQCFB Error: Failed to schedule cron event');
                }
            }
        } catch (Exception $e) {
            error_log('MQCFB Error scheduling events: ' . $e->getMessage());
        }
    }

    /**
     * Retrieve the name of the plugin.
     *
     * @since     0.0.1
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     0.0.1
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }
}