<?php

/**
 * Scheduler for the Mind QTRL Community Feed Bot
 *
 * @since      1.0.0
 * @package    Mind_QTRL_Community_Feed_Bot
 * @subpackage Mind_QTRL_Community_Feed_Bot/includes
 */

class MQCFB_Scheduler {

    /**
     * Initialize the class and set its properties.
     */
    public function __construct() {
        $this->register_hooks();
    }

    /**
     * Register hooks for scheduled events
     */
    public function register_hooks() {
        // Register cron schedule
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));
        
        // Register cron hooks
        add_action('mqcfb_scheduled_fetch', array($this, 'scheduled_fetch_feeds'));
        add_action('mqcfb_scheduled_post', array($this, 'scheduled_post_items'));
        
        // Register activation hook (must be done in main plugin file)
        // The following is for reference:
        // register_activation_hook(__FILE__, array($this, 'activate_scheduler'));
        // register_deactivation_hook(__FILE__, array($this, 'deactivate_scheduler'));
    }

    /**
     * Add custom cron schedules
     */
    public function add_cron_schedules($schedules) {
        $schedules['fifteen_minutes'] = array(
            'interval' => 15 * MINUTE_IN_SECONDS,
            'display'  => __('Every Fifteen Minutes', 'mind-qtrl-community-feed-bot')
        );
        
        $schedules['thirty_minutes'] = array(
            'interval' => 30 * MINUTE_IN_SECONDS,
            'display'  => __('Every Thirty Minutes', 'mind-qtrl-community-feed-bot')
        );
        
        return $schedules;
    }

    /**
     * Activate scheduler
     */
    public function activate_scheduler() {
        // Schedule fetch event (if not already scheduled)
        if (!wp_next_scheduled('mqcfb_scheduled_fetch')) {
            wp_schedule_event(time(), 'hourly', 'mqcfb_scheduled_fetch');
        }
        
        // Schedule post event (if not already scheduled)
        if (!wp_next_scheduled('mqcfb_scheduled_post')) {
            wp_schedule_event(time(), 'thirty_minutes', 'mqcfb_scheduled_post');
        }
    }

    /**
     * Deactivate scheduler
     */
    public function deactivate_scheduler() {
        // Unschedule fetch event
        $timestamp = wp_next_scheduled('mqcfb_scheduled_fetch');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'mqcfb_scheduled_fetch');
        }
        
        // Unschedule post event
        $timestamp = wp_next_scheduled('mqcfb_scheduled_post');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'mqcfb_scheduled_post');
        }
    }

    /**
     * Scheduled fetch feeds
     */
    public function scheduled_fetch_feeds() {
        if (class_exists('MQCFB_Feed_Handler')) {
            $feed_handler = new MQCFB_Feed_Handler();
            $results = $feed_handler->process_feeds(false);
            
            // Log results
            error_log('MQCFB Scheduled Fetch: ' . json_encode($results));
            
            // Update last run time
            update_option('mqcfb_last_fetch', current_time('mysql'));
            update_option('mqcfb_last_fetch_results', $results);
            
            return $results;
        }
        
        return false;
    }

    /**
     * Scheduled post items
     */
    public function scheduled_post_items() {
        if (class_exists('MQCFB_Space_Manager')) {
            global $wpdb;
            $space_manager = new MQCFB_Space_Manager();
            
            // Get settings
            $settings = get_option('mqcfb_settings', array());
            $max_posts = isset($settings['max_posts_per_run']) ? intval($settings['max_posts_per_run']) : 5;
            
            // Get pending articles
            $pending_items = $wpdb->get_results($wpdb->prepare(
                "SELECT aq.*, f.space_id, f.topic_ids, f.author_id, f.image_handling 
                FROM {$wpdb->prefix}mqcfb_article_queue aq 
                JOIN {$wpdb->prefix}mqcfb_feeds f ON aq.feed_id = f.id 
                WHERE aq.status = 'pending' AND f.status = 'active' 
                ORDER BY aq.article_date DESC 
                LIMIT %d",
                $max_posts
            ));
            
            $results = array(
                'processed' => 0,
                'posted' => 0,
                'errors' => 0
            );
            
            if (!empty($pending_items)) {
                foreach ($pending_items as $item) {
                    $results['processed']++;
                    
                    // Prepare article data
                    $article = array(
                        'title' => $item->article_title,
                        'url' => $item->article_url,
                        'date' => $item->article_date,
                        'content' => $item->article_content,
                        'excerpt' => $item->article_excerpt,
                        'image' => $item->article_image
                    );
                    
                    // Get topic IDs
                    $topic_ids = array();
                    if (!empty($item->topic_ids)) {
                        $topic_ids = maybe_unserialize($item->topic_ids);
                    }
                    
                    // Post to community
                    $activity_id = $space_manager->post_to_community(
                        $article,
                        $item->space_id,
                        $topic_ids,
                        $item->author_id,
                        $item->image_handling
                    );
                    
                    if ($activity_id) {
                        // Update article status
                        $wpdb->update(
                            $wpdb->prefix . 'mqcfb_article_queue',
                            array(
                                'status' => 'posted',
                                'posted_time' => current_time('mysql'),
                                'post_id' => $activity_id
                            ),
                            array('id' => $item->id),
                            array('%s', '%s', '%d'),
                            array('%d')
                        );
                        
                        $results['posted']++;
                    } else {
                        $results['errors']++;
                    }
                }
            }
            
            // Log results
            error_log('MQCFB Scheduled Post: ' . json_encode($results));
            
            // Update last run time
            update_option('mqcfb_last_post', current_time('mysql'));
            update_option('mqcfb_last_post_results', $results);
            
            return $results;
        }
        
        return false;
    }

    /**
     * Run all scheduled tasks manually
     */
    public function run_scheduled_tasks() {
        $fetch_results = $this->scheduled_fetch_feeds();
        $post_results = $this->scheduled_post_items();
        
        return array(
            'fetch' => $fetch_results,
            'post' => $post_results
        );
    }
}
