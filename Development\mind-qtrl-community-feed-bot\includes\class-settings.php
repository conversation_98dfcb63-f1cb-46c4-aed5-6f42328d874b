<?php

class MQCFB_Settings {
    private $settings;
    
    /**
     * Initialize the settings
     */
    public function init() {
        $this->settings = $this->get_plugin_settings();
    }
    
    /**
     * Get plugin settings
     *
     * @return array Plugin settings
     */
    public function get_plugin_settings() {
        $default_settings = $this->get_default_settings();
        $settings = get_option('mqcfb_settings', $default_settings);
        
        // Ensure all default settings exist
        foreach ($default_settings as $key => $value) {
            if (!isset($settings[$key])) {
                $settings[$key] = $value;
            }
        }
        
        return $settings;
    }
    
    /**
     * Get default settings
     *
     * @return array Default settings
     */
    public function get_default_settings() {
        return array(
            'schedule_frequency' => 'hourly',
            'max_items_per_feed' => 10,
            'default_post_format' => 'full',
            'default_image_handling' => 'embed',
            'remove_data_on_uninstall' => false,
            'auto_post' => false,
            'auto_post_delay' => 0,
            'debug_mode' => false
        );
    }
    
    /**
     * Update plugin settings
     *
     * @param array $new_settings New settings
     * @return bool Whether the settings were updated
     */
    public function update_plugin_settings($new_settings) {
        // Validate settings
        $validated_settings = $this->validate_settings($new_settings);
        
        // Update settings
        $result = update_option('mqcfb_settings', $validated_settings);
        
        // Update instance settings
        if ($result) {
            $this->settings = $validated_settings;
        }
        
        return $result;
    }
    
    /**
     * Validate settings
     *
     * @param array $settings Settings to validate
     * @return array Validated settings
     */
    private function validate_settings($settings) {
        $default_settings = $this->get_default_settings();
        $validated = array();
        
        // Validate schedule frequency
        $valid_frequencies = array('five_minutes', 'ten_minutes', 'fifteen_minutes', 'thirty_minutes', 'hourly', 'twice_daily', 'daily');
        $validated['schedule_frequency'] = in_array($settings['schedule_frequency'], $valid_frequencies) ? 
            $settings['schedule_frequency'] : $default_settings['schedule_frequency'];
        
        // Validate max items per feed
        $validated['max_items_per_feed'] = intval($settings['max_items_per_feed']);
        if ($validated['max_items_per_feed'] < 1 || $validated['max_items_per_feed'] > 50) {
            $validated['max_items_per_feed'] = $default_settings['max_items_per_feed'];
        }
        
        // Validate post format
        $valid_formats = array('full', 'excerpt', 'title_only');
        $validated['default_post_format'] = in_array($settings['default_post_format'], $valid_formats) ? 
            $settings['default_post_format'] : $default_settings['default_post_format'];
        
        // Validate image handling
        $valid_image_handling = array('embed', 'featured', 'none');
        $validated['default_image_handling'] = in_array($settings['default_image_handling'], $valid_image_handling) ? 
            $settings['default_image_handling'] : $default_settings['default_image_handling'];
        
        // Validate boolean settings
        $validated['remove_data_on_uninstall'] = !empty($settings['remove_data_on_uninstall']);
        $validated['auto_post'] = !empty($settings['auto_post']);
        $validated['debug_mode'] = !empty($settings['debug_mode']);
        
        // Validate auto post delay
        $validated['auto_post_delay'] = intval($settings['auto_post_delay']);
        if ($validated['auto_post_delay'] < 0) {
            $validated['auto_post_delay'] = 0;
        }
        
        return $validated;
    }
    
    /**
     * Get a specific setting
     *
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function get_setting($key, $default = null) {
        if (!isset($this->settings)) {
            $this->init();
        }
        
        return isset($this->settings[$key]) ? $this->settings[$key] : $default;
    }
}