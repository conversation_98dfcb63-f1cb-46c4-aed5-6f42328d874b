<?php

class MQCFB_Space_Manager {
    
    /**
     * Get all available Fluent Community spaces
     *
     * @return array Array of spaces with id and name
     */
    public function get_spaces() {
        $spaces = array();
        
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Models\Space')) {
            return $spaces;
        }
        
        try {
            // Get spaces from Fluent Community
            $space_model = new \FluentCommunity\App\Models\Space();
            $all_spaces = $space_model->select(['id', 'title'])
                ->where('status', 'published')
                ->orderBy('title', 'ASC')
                ->get();
                
            if ($all_spaces) {
                foreach ($all_spaces as $space) {
                    $spaces[] = array(
                        'id' => $space->id,
                        'name' => $space->title
                    );
                }
            }
        } catch (Exception $e) {
            error_log('MQCFB Error getting spaces: ' . $e->getMessage());
        }
        
        return $spaces;
    }
    
    /**
     * Get a specific space by ID
     *
     * @param int $space_id The space ID
     * @return object|false Space object or false if not found
     */
    public function get_space($space_id) {
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Models\Space')) {
            return false;
        }
        
        try {
            // Get space from Fluent Community
            $space_model = new \FluentCommunity\App\Models\Space();
            $space = $space_model->find($space_id);
            
            return $space;
        } catch (Exception $e) {
            error_log('MQCFB Error getting space: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get topics for a specific space
     *
     * @param int $space_id The space ID
     * @return array Array of topics with id and name
     */
    public function get_space_topics($space_id) {
        $topics = array();
        
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Models\Topic')) {
            return $topics;
        }
        
        try {
            // Get topics from Fluent Community
            $topic_model = new \FluentCommunity\App\Models\Topic();
            $all_topics = $topic_model->select(['id', 'title'])
                ->where('space_id', $space_id)
                ->where('status', 'published')
                ->orderBy('title', 'ASC')
                ->get();
                
            if ($all_topics) {
                foreach ($all_topics as $topic) {
                    $topics[] = array(
                        'id' => $topic->id,
                        'name' => $topic->title
                    );
                }
            }
        } catch (Exception $e) {
            error_log('MQCFB Error getting topics: ' . $e->getMessage());
        }
        
        return $topics;
    }
    
    /**
     * Get space name by ID
     *
     * @param int $space_id The space ID
     * @return string Space name or empty string if not found
     */
    public function get_space_name($space_id) {
        $space = $this->get_space($space_id);
        
        if ($space && isset($space->title)) {
            return $space->title;
        }
        
        return '';
    }
    
    /**
     * Get all community users who can post
     *
     * @return array Array of users with id and name
     */
    public function get_community_users() {
        $users = array();
        
        // Check if WordPress user functions are available
        if (!function_exists('get_users')) {
            return $users;
        }
        
        try {
            // Get users who can publish posts
            $wp_users = get_users(array(
                'capability' => 'publish_posts',
                'fields' => array('ID', 'display_name')
            ));
            
            if ($wp_users) {
                foreach ($wp_users as $user) {
                    $users[] = array(
                        'id' => $user->ID,
                        'name' => $user->display_name
                    );
                }
            }
        } catch (Exception $e) {
            error_log('MQCFB Error getting users: ' . $e->getMessage());
        }
        
        return $users;
    }
    
    /**
     * Post an article to a Fluent Community space as an activity
     *
     * @param array $article Article data
     * @param int $space_id Space ID
     * @param array $topic_ids Topic IDs
     * @param int $author_id Author ID
     * @param string $image_handling How to handle images ('embed' or 'featured')
     * @return int|false Activity ID or false on failure
     */
    public function post_to_community($article, $space_id, $topic_ids = array(), $author_id = null, $image_handling = 'embed') {
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Models\Activity')) {
            return false;
        }
        
        try {
            // Prepare content based on image handling preference
            $content = $article['content'];
            $media_ids = array();
            
            // Handle featured image if needed
            if ($image_handling == 'featured' && !empty($article['image'])) {
                // Get image and upload to media library
                $image_url = $article['image'];
                $upload_dir = wp_upload_dir();
                $image_data = file_get_contents($image_url);
                
                if ($image_data) {
                    $filename = basename($image_url);
                    $unique_filename = wp_unique_filename($upload_dir['path'], $filename);
                    $filepath = $upload_dir['path'] . '/' . $unique_filename;
                    
                    file_put_contents($filepath, $image_data);
                    
                    $wp_filetype = wp_check_filetype($filename, null);
                    $attachment = array(
                        'post_mime_type' => $wp_filetype['type'],
                        'post_title' => sanitize_file_name($filename),
                        'post_content' => '',
                        'post_status' => 'inherit'
                    );
                    
                    $attach_id = wp_insert_attachment($attachment, $filepath);
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                    $attach_data = wp_generate_attachment_metadata($attach_id, $filepath);
                    wp_update_attachment_metadata($attach_id, $attach_data);
                    
                    $media_ids[] = $attach_id;
                }
            } elseif ($image_handling == 'embed' && !empty($article['image'])) {
                // Embed image in content
                $image_html = '<img src="' . esc_url($article['image']) . '" alt="' . esc_attr($article['title']) . '" class="mqcfb-article-image" />';
                $content = $image_html . $content;
            }
            
            // Set author ID if not provided
            if (!$author_id) {
                $author_id = get_current_user_id();
            }
            
            // Create activity data
            $activity_data = array(
                'title' => $article['title'],
                'content' => $content,
                'space_id' => $space_id,
                'topic_ids' => $topic_ids,
                'user_id' => $author_id,
                'status' => 'published',
                'source_url' => $article['url']
            );
            
            // Add media if available
            if (!empty($media_ids)) {
                $activity_data['media_ids'] = $media_ids;
            }
            
            // Create activity
            $activity_model = new \FluentCommunity\App\Models\Activity();
            $activity = $activity_model->create($activity_data);
            
            if ($activity && isset($activity->id)) {
                return $activity->id;
            }
        } catch (Exception $e) {
            error_log('MQCFB Error posting to community: ' . $e->getMessage());
        }
        
        return false;
    }
}