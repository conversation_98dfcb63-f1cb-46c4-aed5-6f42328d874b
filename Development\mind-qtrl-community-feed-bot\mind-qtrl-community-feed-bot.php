<?php
/**
 * The plugin bootstrap file
 *
 * @link              https://mindstate.live
 * @since             0.0.1
 * @package           Mind_QTRL_Community_Feed_Bot
 *
 * @wordpress-plugin
 * Plugin Name:       Mind QTRL | Community Feed Bot
 * Plugin URI:        https://mindstate.live
 * Description:       RSS feed bot for sharing and scheduling articles as Fluent Community activity posts
 * Version:           0.0.1
 * Author:            Mind QTRL
 * Author URI:        https://mindstate.live
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       mind-qtrl-community-feed-bot
 * Requires plugins:  fluent-community
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Currently plugin version.
 */
define('MQCFB_VERSION', '0.0.1');

/**
 * Plugin directory path - ensuring consistent path format across environments
 */
define('MQCFB_PLUGIN_DIR', plugin_dir_path(__FILE__));

/**
 * Plugin URL - ensuring consistent URL format across environments
 */
define('MQCFB_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * The code that runs during plugin activation.
 */
function activate_mind_qtrl_community_feed_bot() {
    try {
        require_once MQCFB_PLUGIN_DIR . 'includes/class-mind-qtrl-community-feed-bot-activator.php';
        Mind_QTRL_Community_Feed_Bot_Activator::activate();
    } catch (Exception $e) {
        error_log('Error activating Mind QTRL Community Feed Bot: ' . $e->getMessage());
        exit($e->getMessage());
    }
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_mind_qtrl_community_feed_bot() {
    try {
        require_once MQCFB_PLUGIN_DIR . 'includes/class-mind-qtrl-community-feed-bot-deactivator.php';
        Mind_QTRL_Community_Feed_Bot_Deactivator::deactivate();
    } catch (Exception $e) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Error deactivating Mind QTRL Community Feed Bot: ' . $e->getMessage());
        }
    }
}

register_activation_hook(__FILE__, 'activate_mind_qtrl_community_feed_bot');
register_deactivation_hook(__FILE__, 'deactivate_mind_qtrl_community_feed_bot');

/**
 * Plugin uninstall function to clean up database tables and options
 */
function uninstall_mind_qtrl_community_feed_bot() {
    global $wpdb;
    
    // Clean up scheduled events
    wp_clear_scheduled_hook('mqcfb_process_feeds');
    
    // Only remove data if we're allowed to
    $remove_data = get_option('mqcfb_remove_data_on_uninstall', false);
    if ($remove_data) {
        // Remove plugin tables
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}mqcfb_feeds");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}mqcfb_article_queue");
        
        // Remove plugin options
        delete_option('mqcfb_settings');
        delete_option('mqcfb_db_version');
        delete_option('mqcfb_remove_data_on_uninstall');
    }
}

register_uninstall_hook(__FILE__, 'uninstall_mind_qtrl_community_feed_bot');

/**
 * Check for plugin requirements
 * 
 * @return bool Whether requirements are met
 */
function mqcfb_check_requirements() {
    $requirements_met = true;
    $missing_plugins = array();
    $missing_php_extensions = array();
    
    // Check for required PHP extensions
    $required_extensions = array('simplexml', 'dom', 'mbstring');
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $missing_php_extensions[] = $ext;
            $requirements_met = false;
        }
    }
    
    // Check for PHP version
    if (version_compare(PHP_VERSION, '7.4', '<')) {
        $requirements_met = false;
        add_action('admin_notices', function() {
            ?>
            <div class="notice notice-error">
                <p><strong>Mind QTRL Community Feed Bot:</strong> This plugin requires PHP 7.4 or higher. You are using PHP <?php echo PHP_VERSION; ?>.</p>
            </div>
            <?php
        });
    }
    
    // Check for required plugins
    if (!function_exists('is_plugin_active')) {
        require_once ABSPATH . 'wp-admin/includes/plugin.php';
    }
    
    // Check for FluentCommunity
    if (!class_exists('\FluentCommunity\App\App') && !is_plugin_active('fluent-community/fluent-community.php')) {
        $missing_plugins[] = 'Fluent Community';
        $requirements_met = false;
    }
    
    // If PHP extensions are missing
    if (!empty($missing_php_extensions)) {
        add_action('admin_notices', function() use ($missing_php_extensions) {
            ?>
            <div class="notice notice-error">
                <p>
                    <strong>Mind QTRL Community Feed Bot:</strong> 
                    <?php _e('The following PHP extensions are required but missing: ', 'mind-qtrl-community-feed-bot'); ?>
                    <strong><?php echo implode(', ', $missing_php_extensions); ?></strong>
                </p>
                <p><?php _e('Please contact your hosting provider to enable these extensions.', 'mind-qtrl-community-feed-bot'); ?></p>
            </div>
            <?php
        });
    }
    
    // If plugins are missing
    if (!empty($missing_plugins)) {
        add_action('admin_notices', function() use ($missing_plugins) {
            ?>
            <div class="notice notice-error">
                <p>
                    <strong>Mind QTRL Community Feed Bot:</strong> 
                    <?php _e('The following required plugin(s) are missing or inactive: ', 'mind-qtrl-community-feed-bot'); ?>
                    <strong><?php echo implode(', ', $missing_plugins); ?></strong>
                </p>
                <p><?php _e('Please install and activate these plugins for the feed bot to work properly.', 'mind-qtrl-community-feed-bot'); ?></p>
            </div>
            <?php
        });
    }
    
    return $requirements_met;
}

/**
 * Load plugin textdomain
 */
function mqcfb_load_textdomain() {
    load_plugin_textdomain('mind-qtrl-community-feed-bot', false, dirname(plugin_basename(__FILE__)) . '/languages');
}
add_action('init', 'mqcfb_load_textdomain');

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
function mqcfb_load_core_file() {
    $core_file = MQCFB_PLUGIN_DIR . 'includes/class-mind-qtrl-community-feed-bot.php';
    if (file_exists($core_file)) {
        require $core_file;
        return true;
    } else {
        error_log('Critical error: Core plugin file not found at: ' . $core_file);
        add_action('admin_notices', function() use ($core_file) {
            ?>
            <div class="notice notice-error">
                <p><strong>Mind QTRL Community Feed Bot Error:</strong> Core plugin file not found at: <?php echo esc_html($core_file); ?></p>
                <p>Please reinstall the plugin or contact support.</p>
            </div>
            <?php
        });
        return false;
    }
}

/**
 * Begins execution of the plugin.
 *
 * @since    0.0.1
 */
function run_mind_qtrl_community_feed_bot() {
    // Check for requirements first
    $requirements_met = mqcfb_check_requirements();
    
    // Check if core file loaded successfully
    $core_loaded = mqcfb_load_core_file();
    
    if ($requirements_met && $core_loaded) {
        try {
            $plugin = new Mind_QTRL_Community_Feed_Bot();
            $plugin->run();
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Error running Mind QTRL Community Feed Bot: ' . $e->getMessage());
            }
            
            add_action('admin_notices', function() use ($e) {
                ?>
                <div class="notice notice-error">
                    <p>
                        <strong>Mind QTRL Community Feed Bot Error:</strong> 
                        <?php echo esc_html($e->getMessage()); ?>
                    </p>
                </div>
                <?php
            });
        }
    }
}

// Start the plugin on plugins_loaded to ensure all plugins are loaded
add_action('plugins_loaded', 'run_mind_qtrl_community_feed_bot');