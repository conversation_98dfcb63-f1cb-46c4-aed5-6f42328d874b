# Changelog

All notable changes to the Mind Qtrl | Community Image Feed CE plugin will be documented in this file.

## 0.3.4 (2025-06-01)

### Fixed
- Fixed fatal error caused by duplicate method declaration in admin class
- Removed redundant `items_per_page_callback()` method

## 0.3.3 (2025-05-30)

### Added
- YouTube video support with proper thumbnails and playback
- Automatic extraction of YouTube videos from post content
- Improved media detection from Fluent Community activities
- Better handling of embedded media in posts

### Improved
- Enhanced lazy loading for YouTube thumbnails
- Fixed issues with media thumbnails not loading properly
- Improved CSS styling for video content
- Better error handling for media loading

## 0.3.2 (2025-05-25)

### Added
- Sample data fallback when API requests fail
- Improved error handling with better user feedback
- Automatic retry mechanisms for API requests

### Improved
- More reliable DOM integration with Fluent Community
- Fixed issues with REST API error handling
- Enhanced sample data display with clear notification
- Removed redundant JavaScript files for better performance

## 0.3.1 (2025-05-20)

### Improved
- Optimized REST API with better error handling
- Reduced excessive logging for better performance
- Added sample data fallback for development environments
- Simplified integration approach with cleaner code
- Fixed server errors when fetching media feeds
- Improved error handling and fallback mechanisms

## 0.3.0 (2025-05-15)

### Added
- Added infinite scroll option for seamless browsing of media content
- Added enhanced lazy loading for better performance and reduced bandwidth usage
- Added option to toggle infinite scroll in admin settings
- Added option to toggle lazy loading in admin settings
- Added consistent 3-column grid layout for media items
- Added end of content message for infinite scroll
- Added loading indicators for infinite scroll
- Added intersection observer for lazy loading images and videos
- Added admin settings page for plugin configuration
- Added option to toggle debug mode for easier troubleshooting
- Added option to set items per page for media feed (default: 9)
- Added option to set default tab for user profiles
- Added option to disable media tab redirection
- Added option to disable media tab entirely
- Added option to disable media feed entirely

### REST API Security Improvements
- Implemented proper permission checks based on user capabilities and profile visibility
- Reduced excessive error logging of sensitive information
- Added debug mode toggle in admin settings

### Custom Element Implementation
- Created a base element class following best practices from Mlive_Hybrid_Community_integration.md
- Implemented a proper media gallery custom element with Shadow DOM encapsulation
- Added comprehensive error handling and fallbacks

### Integration with Fluent Community
- Simplified the integration approach using the recommended hybrid method
- Implemented proper MutationObserver patterns for DOM manipulation
- Used event-driven architecture for communication between components

### Error Handling and Fallbacks
- Streamlined the fallback mechanisms to be more efficient
- Improved error messages for better user experience
- Added debug mode for easier troubleshooting

## 0.2.9 (2025-05-13)

### Fixed
- Fixed REST API authentication issues causing media feed and gallery grid not to load
- Implemented comprehensive nonce handling with multiple fallback mechanisms
- Added automatic retry mechanism for authentication errors
- Enhanced error recovery with user-friendly retry options
- Improved error messages with detailed information and retry buttons

### Improved
- Added multiple sources for nonce retrieval to ensure consistent authentication
- Enhanced debugging with detailed logging for API requests
- Implemented AJAX handler for refreshing the nonce when needed
- Added meta tag for nonce in the head for better accessibility
- Improved error handling in the REST API with better error reporting
- Enhanced user interface for error states with better styling and feedback

## 0.2.8 (2025-05-12)

### Fixed
- Fixed tab navigation issues when switching between Media and About tabs
- Improved handling of tab state to ensure proper navigation between tabs
- Added custom event listeners to better handle tab switching
- Fixed URL handling to maintain proper state when switching tabs
- Ensured the body data-route attribute is properly updated when switching tabs
- Added multiple fallback mechanisms for tab navigation

## 0.2.7 (2025-05-11)

### Added
- Implemented client-side fallback gallery when API fails
- Added ability to extract media from current page when server API is unavailable
- Added dedicated CSS for fallback gallery with responsive design
- Added "Show Basic Gallery" button to error messages for better user experience

### Improved
- Enhanced error handling with more user-friendly options
- Added comprehensive CSS styling for all gallery elements
- Improved media display with better responsive design
- Added multiple fallback mechanisms for media content display

## 0.2.6 (2025-05-10)

### Fixed
- Added extensive error logging to help diagnose server-side issues
- Improved SQL query error handling with detailed error messages
- Enhanced database fallback method with better exception handling
- Added validation for database connection availability

### Improved
- Added detailed logging for all database operations
- Enhanced JavaScript debugging with forced debug mode
- Improved error recovery in media queries
- Added comprehensive exception handling for all database operations

## 0.2.5 (2025-05-09)

### Fixed
- Completely redesigned the way Fluent Community portal activities with media content are fetched
- Added fallback method to fetch media content directly from the database when Fluent Community models are not available
- Improved SQL queries with better error handling and compatibility with different database configurations
- Fixed issues with JSON_EXTRACT queries by adding fallback methods for databases that don't support it

### Improved
- Enhanced media content filtering with more robust database queries
- Added detailed error logging for database operations
- Improved compatibility with different MySQL/MariaDB versions
- Added comprehensive fallback mechanisms for all database operations

## 0.2.4 (2025-05-08)

### Fixed
- Improved REST API endpoint for fetching media content with better error handling
- Enhanced debugging with detailed error logging for API requests
- Fixed issues with nonce retrieval and API authentication
- Added request timeout handling to prevent infinite loading states

### Improved
- Added comprehensive nonce detection from multiple sources
- Implemented AbortController for request timeouts
- Enhanced error messages with more detailed information
- Improved media content fetching with better fallback mechanisms
- Added detailed logging for easier troubleshooting

## 0.2.3 (2025-05-07)

### Fixed
- Fixed issue where clicking the media tab button didn't work
- Improved profile content area detection with multiple fallback methods
- Enhanced error handling for invalid container elements
- Added additional API URL and nonce detection methods

### Improved
- Added more robust container validation in fetchUserMedia function
- Improved loading state display with better user feedback
- Enhanced debugging information for troubleshooting
- Added fallback mechanisms when primary methods fail

## 0.2.2 (2025-05-06)

### Fixed
- Improved tab navigation in Fluent Community profile pages
- Enhanced compatibility with Fluent Community's tab behavior
- Fixed edge cases in tab content display

### Improved
- Added comprehensive testing for tab navigation
- Updated documentation with usage instructions
- Optimized performance for profile page navigation

## 0.2.1 (2025-05-05)

### Fixed
- Fixed issue where the About tab was not accessible after being redirected to the media tab
- Ensured the About tab works correctly when clicked, even when media tab is set as default
- Added mechanisms to prevent redirection loops when navigating between tabs

### Improved
- Added multiple methods to ensure the About tab remains functional
- Implemented session storage flag to remember user's tab preference
- Added URL parameter option to explicitly prevent redirection
- Enhanced tab navigation to preserve original functionality

## 0.2.0 (2025-05-04)

### Added
- Added feature to redirect default user profile route to the media tab
- Implemented both server-side and client-side approaches for route redirection
- Added automatic content loading when redirecting to the media tab

### Improved
- Enhanced URL handling for different base URL configurations
- Improved active tab state management during redirection
- Added better error handling for route redirection
- Added detailed logging for debugging route redirection issues

## 0.1.9 (2025-05-03)

### Fixed
- Fixed issue where clicking the media tab button didn't do anything
- Fixed direct navigation to media tab URLs not working properly
- Improved URL handling to properly update the URL without triggering Vue router navigation
- Fixed active tab state not being updated correctly when clicking the media tab

### Improved
- Enhanced direct content rendering approach to match Fluent Community's tab behavior
- Added better error handling for direct navigation to media tab URLs
- Improved URL path handling to work with different base URL configurations
- Added additional checks to ensure the media tab is properly displayed

## 0.1.8 (2025-05-02) - Tab Navigation Fix

### Fixed
- Media tab now works correctly with Fluent Community's tab navigation pattern
- Fixed issue where only the content inside profile_holder element should change when clicking tabs
- Implemented direct content rendering approach to avoid Vue router issues
- Fixed 403 error when loading media content with improved authentication and error handling

### Improved
- Added direct content rendering for media tab that matches Fluent Community's tab behavior
- Enhanced error messages with retry functionality
- Implemented media filtering with native HTML elements instead of Vue components
- Added load more functionality with proper error handling
- Improved media item click handling to open feed modals

## 0.1.7 (2025-05-01)

### Fixed
- Persistent 403 error when loading media content with comprehensive nonce handling
- Added multiple nonce injection methods to ensure REST API authentication works in all scenarios

### Improved
- Implemented direct nonce injection into the page via meta tags
- Added multiple fallback methods for nonce retrieval
- Enhanced error logging for authentication issues
- Added detailed debugging for nonce handling

## 0.1.6 (2025-04-30)

### Fixed
- Persistent issue with media tab not appearing during SPA navigation
- 403 error when loading media content with improved authentication handling
- Username detection during SPA navigation with multiple fallback methods

### Improved
- Implemented a more robust global navigation observer that works with all navigation methods
- Enhanced error handling with more specific error messages
- Added request timeout handling to prevent infinite loading states
- Improved username detection with multiple fallback methods based on Fluent Community integration guide
- Added better validation of API response data

## 0.1.5 (2025-04-29)

### Fixed
- Media tab not appearing when navigating to a user profile via SPA navigation without page refresh
- 403 error when loading media content by removing login requirement for public profiles
- Improved authentication handling for REST API requests

### Improved
- Enhanced SPA navigation detection with multiple fallback methods
- Added DOM mutation observer to detect profile navigation changes
- Implemented more robust retry mechanism for media tab addition
- Added global function for manual media tab addition (for debugging)

## 0.1.4 (2025-04-28)

### Improved
- Enhanced documentation and code comments for better maintainability
- Optimized performance for SPA navigation scenarios
- Added additional logging for troubleshooting complex navigation issues
- Refined error handling for edge cases in username detection

### Fixed
- Minor CSS issues in the media gallery display
- Edge case handling in route detection

## 0.1.3 (2025-04-27)

### Fixed
- Issue with media tab not showing up when navigating to a user profile via SPA navigation
- Vue component error with username property handling that was causing loading issues

### Improved
- Enhanced username detection with multiple fallback methods for better reliability
- Route change detection for Single Page Application navigation
- Added URL hash monitoring for better SPA navigation support
- More robust DOM element detection for profile pages

## 0.1.2 (2025-04-26)

### Fixed
- Issue with Vue component implementation getting stuck on loading
- Error handling in media feed fetching

### Improved
- Enhanced compatibility with different Fluent Community versions
- Added better debugging information for troubleshooting

## 0.1.1 (2025-04-24)

### Improved
- Enhanced Vue router integration with multi-layered approach for better compatibility
- Optimized performance by conditionally initializing Custom Elements only on user profile routes
- Added proper component lifecycle hooks for better resource management
- Implemented better cleanup when navigating away from media routes

### Added
- Multiple fallback mechanisms to ensure compatibility with different Fluent Community versions
- Improved event-based communication between components
- Route change detection for better user experience
- Better error handling and logging

### Fixed
- Fixed potential memory leaks by properly cleaning up resources
- Improved URL handling when navigating between routes

## 0.1.0 (2025-04-01)

### Added
- Initial release with Pure CE implementation
- "Media" tab to user profiles in Fluent Community
- Media gallery with Custom Elements
- Filtering options for media types (all/images/videos)
- Pagination and "Load More" functionality
- Integration with Fluent Community's feed modal
- REST API endpoints for retrieving user media
- Admin settings page for configuration
