# Mind Qtrl | Community Image Feed CE - Version 0.3.4

A WordPress plugin that integrates with Fluent Community to add a user media profile tab and image feed using Custom Elements.

## Description

Mind Qtrl Community Image Feed CE enhances Fluent Community (Mindstate.live) by adding a dedicated Media tab to user profiles. This tab displays all media content (images and videos) shared by the user in a clean, organized gallery format.

The plugin uses Custom Elements for seamless integration with Fluent Community without requiring build tools or complex Vue components.

## Features

- Adds a Media tab to user profiles in Fluent Community
- Sets the Media tab as the default tab when visiting user profiles
- Preserves access to the original About tab and other profile tabs
- Displays images and videos in a responsive grid layout with exactly 3 columns
- Supports filtering between all media, images only, or videos only
- Integrates with Fluent Community's feed modal for media viewing
- Infinite scroll for seamless browsing of media content
- Enhanced lazy loading for better performance and reduced bandwidth usage
- Configurable items per page (default: 9)
- Improved grid layout with consistent 3-column design
- Uses Custom Elements for modern, maintainable code
- Robust error handling and request timeout management
- **NEW:** YouTube video support with proper thumbnails and playback
- **NEW:** Automatic extraction of YouTube videos from post content
- **NEW:** Improved media detection from Fluent Community activities
- **NEW:** Sample data fallback when API requests fail
- **NEW:** Improved error handling with better user feedback
- **IMPROVED:** Optimized REST API with better error handling
- **IMPROVED:** Reduced excessive logging for better performance
- **IMPROVED:** Simplified integration approach with cleaner code
- **IMPROVED:** More reliable DOM integration with Fluent Community
- Multiple fallback mechanisms for API authentication
- Direct database access fallback when Fluent Community models are unavailable
- Compatible with different MySQL/MariaDB versions and configurations
- Client-side fallback gallery when server API is unavailable
- Responsive design with comprehensive CSS styling
- Multiple integration modes for maximum compatibility

## Installation

1. Upload the `mind-qtrl-community-image-feed-ce` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Ensure Fluent Community is installed and activated

## Usage

Once activated, the plugin automatically:

1. Adds a Media tab to all user profiles in Fluent Community
2. Sets the Media tab as the default tab when visiting user profiles
3. Loads media content when the Media tab is active

### Integration Modes

The plugin offers three integration modes that can be selected in the plugin settings:

1. **Modern (Custom Elements)** - Uses Custom Elements with Shadow DOM for better encapsulation and performance. Recommended for most sites.
2. **Legacy (Backward Compatibility)** - Uses the older integration method for backward compatibility with custom themes or plugins.
3. **Both (Maximum Compatibility)** - Loads both integration methods for maximum compatibility with all setups.

Choose the integration mode that works best with your Fluent Community setup.

### Accessing the About Tab

Even though the Media tab is set as the default, you can still access the About tab by:

1. Clicking on the About tab in the profile navigation
2. Adding `?no-redirect=1` to the profile URL to prevent redirection to the Media tab

## Requirements

- WordPress 5.6 or higher
- Fluent Community plugin (active)
- PHP 7.4 or higher

## Changelog

See the [CHANGELOG.md](CHANGELOG.md) file for a complete list of changes.

## Credits

Developed by Mind Qtrl for Mindstate.live.

## License

This plugin is licensed under the GPL v2 or later.
