<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @since      0.1.0
 * @package    MQCIFCE
 * @subpackage MQCIFCE/admin
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * The admin-specific functionality class.
 */
class MQCIFCE_Admin {

    /**
     * Initialize the class.
     */
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));

        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Check if the main Mind Qtrl menu exists
        global $submenu;
        $parent_slug = 'mind-qtrl-admin';

        // If the main Mind Qtrl menu doesn't exist, create it
        if (!isset($submenu[$parent_slug])) {
            add_menu_page(
                __('Mind Qtrl', 'mqcif-ce'),
                __('Mind Qtrl', 'mqcif-ce'),
                'manage_options',
                $parent_slug,
                array($this, 'display_admin_page'),
                MQCIFCE_PLUGIN_URL . 'admin/images/mind-qtrl-logo-icon.png',
                30
            );
        }

        // Add submenu page
        add_submenu_page(
            $parent_slug,
            __('Community Image Feed CE', 'mqcif-ce'),
            __('Image Feed CE', 'mqcif-ce'),
            'manage_options',
            'mqcifce-settings',
            array($this, 'display_admin_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Register a setting group
        register_setting('mqcifce_options_group', 'mqcifce_options');

        // Add settings section
        add_settings_section(
            'mqcifce_general_section',
            __('General Settings', 'mqcif-ce'),
            array($this, 'general_section_callback'),
            'mqcifce-settings'
        );

        // Add settings fields
        add_settings_field(
            'default_tab_enabled',
            __('Make "Media" tab the default profile tab', 'mqcif-ce'),
            array($this, 'default_tab_enabled_callback'),
            'mqcifce-settings',
            'mqcifce_general_section'
        );

        // Add integration mode setting field
        add_settings_field(
            'integration_mode',
            __('Integration Mode', 'mqcif-ce'),
            array($this, 'integration_mode_callback'),
            'mqcifce-settings',
            'mqcifce_general_section'
        );

        // Add debug mode setting field
        add_settings_field(
            'debug_mode',
            __('Debug Mode', 'mqcif-ce'),
            array($this, 'debug_mode_callback'),
            'mqcifce-settings',
            'mqcifce_general_section'
        );

        // Add items per page setting field
        add_settings_field(
            'items_per_page',
            __('Items Per Page', 'mqcif-ce'),
            array($this, 'items_per_page_callback'),
            'mqcifce-settings',
            'mqcifce_general_section'
        );

        // Add infinite scroll setting field
        add_settings_field(
            'infinite_scroll',
            __('Infinite Scroll', 'mqcif-ce'),
            array($this, 'infinite_scroll_callback'),
            'mqcifce-settings',
            'mqcifce_general_section'
        );

        // Add lazy loading setting field
        add_settings_field(
            'lazy_loading',
            __('Lazy Loading', 'mqcif-ce'),
            array($this, 'lazy_loading_callback'),
            'mqcifce-settings',
            'mqcifce_general_section'
        );
    }

    /**
     * General section callback
     */
    public function general_section_callback() {
        echo '<p>' . __('Configure the general settings for the Community Image Feed CE plugin.', 'mqcif-ce') . '</p>';
    }

    /**
     * Default tab enabled callback
     */
    public function default_tab_enabled_callback() {
        $options = get_option('mqcifce_options', array());
        $checked = isset($options['default_tab_enabled']) ? $options['default_tab_enabled'] : '0';

        echo '<label><input type="checkbox" name="mqcifce_options[default_tab_enabled]" value="1" ' . checked('1', $checked, false) . ' /> ' . __('Enable', 'mqcif-ce') . '</label>';
        echo '<p class="description">' . __('When enabled, users will land on the Media tab when visiting a profile.', 'mqcif-ce') . '</p>';
    }

    /**
     * Items per page callback
     */
    public function items_per_page_callback() {
        $options = get_option('mqcifce_options', array());
        $items_per_page = isset($options['items_per_page']) ? $options['items_per_page'] : '9';

        echo '<input type="number" name="mqcifce_options[items_per_page]" value="' . esc_attr($items_per_page) . '" min="3" max="30" step="3" />';
        echo '<p class="description">' . __('Number of media items to display per page. Default is 9. Must be a multiple of 3 for proper grid layout.', 'mqcif-ce') . '</p>';
    }

    /**
     * Integration mode callback
     */
    public function integration_mode_callback() {
        $options = get_option('mqcifce_options', array());
        $integration_mode = isset($options['integration_mode']) ? $options['integration_mode'] : 'modern';

        echo '<select name="mqcifce_options[integration_mode]" id="mqcifce_integration_mode">';
        echo '<option value="modern" ' . selected('modern', $integration_mode, false) . '>' . __('Modern (Custom Elements)', 'mqcif-ce') . '</option>';
        echo '<option value="legacy" ' . selected('legacy', $integration_mode, false) . '>' . __('Legacy (Backward Compatibility)', 'mqcif-ce') . '</option>';
        echo '<option value="both" ' . selected('both', $integration_mode, false) . '>' . __('Both (Maximum Compatibility)', 'mqcif-ce') . '</option>';
        echo '</select>';
        echo '<p class="description">' . __('Choose the integration mode for Fluent Community:', 'mqcif-ce') . '</p>';
        echo '<ul class="mqcifce-description-list">';
        echo '<li><strong>Modern:</strong> ' . __('Uses Custom Elements with Shadow DOM for better encapsulation and performance. Recommended for most sites.', 'mqcif-ce') . '</li>';
        echo '<li><strong>Legacy:</strong> ' . __('Uses the older integration method for backward compatibility with custom themes or plugins.', 'mqcif-ce') . '</li>';
        echo '<li><strong>Both:</strong> ' . __('Loads both integration methods for maximum compatibility with all setups.', 'mqcif-ce') . '</li>';
        echo '</ul>';
    }

    /**
     * Display admin page
     */
    public function display_admin_page() {
        ?>
        <div class="wrap mqco">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <form method="post" action="options.php">
                <?php
                settings_fields('mqcifce_options_group');
                do_settings_sections('mqcifce-settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Debug mode callback
     */
    public function debug_mode_callback() {
        $options = get_option('mqcifce_options', array());
        $checked = isset($options['debug_mode']) ? $options['debug_mode'] : '0';

        echo '<label><input type="checkbox" name="mqcifce_options[debug_mode]" value="1" ' . checked('1', $checked, false) . ' /> ' . __('Enable', 'mqcif-ce') . '</label>';
        echo '<p class="description">' . __('When enabled, detailed debug information will be logged. Only enable for troubleshooting.', 'mqcif-ce') . '</p>';
    }

    /**
     * Infinite scroll callback
     */
    public function infinite_scroll_callback() {
        $options = get_option('mqcifce_options', array());
        $checked = isset($options['infinite_scroll']) ? $options['infinite_scroll'] : '1';

        echo '<label><input type="checkbox" name="mqcifce_options[infinite_scroll]" value="1" ' . checked('1', $checked, false) . ' /> ' . __('Enable', 'mqcif-ce') . '</label>';
        echo '<p class="description">' . __('When enabled, more media items will be loaded automatically when the user scrolls to the bottom of the page.', 'mqcif-ce') . '</p>';
    }

    /**
     * Lazy loading callback
     */
    public function lazy_loading_callback() {
        $options = get_option('mqcifce_options', array());
        $checked = isset($options['lazy_loading']) ? $options['lazy_loading'] : '1';

        echo '<label><input type="checkbox" name="mqcifce_options[lazy_loading]" value="1" ' . checked('1', $checked, false) . ' /> ' . __('Enable', 'mqcif-ce') . '</label>';
        echo '<p class="description">' . __('When enabled, media thumbnails will be loaded only when they become visible in the viewport.', 'mqcif-ce') . '</p>';
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our settings page
        if (strpos($hook, 'mqcifce-settings') === false) {
            return;
        }

        // Enqueue admin CSS
        wp_enqueue_style(
            'mqcifce-admin-style',
            MQCIFCE_PLUGIN_URL . 'admin/css/mqcifce-admin.css',
            array(),
            MQCIFCE_VERSION
        );

        // Enqueue admin JS
        wp_enqueue_script(
            'mqcifce-admin-script',
            MQCIFCE_PLUGIN_URL . 'admin/js/mqcifce-admin.js',
            array('jquery'),
            MQCIFCE_VERSION,
            true
        );
    }
}
