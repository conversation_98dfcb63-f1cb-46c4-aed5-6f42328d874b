/**
 * Admin CSS for Mind Qtrl | Community Image Feed CE
 *
 * This file implements the Modern dark Mind Qtrl UI template for the admin interface.
 *
 * @since      0.1.0
 */

/* Modern dark Mind Qtrl UI template */

.toplevel_page_mind-qtrl-admin #wpwrap,
.mind-qtrl_page_mqcifce-settings #wpwrap {
    background: #1B1B1E !important;
}

/* Apply dark theme only to our plugin pages */
.mqco {
    color: #E1E1E6;
    background: #1B1B1E;
    padding: 20px;
    border-radius: 5px;
    margin-top: 20px;
}

.mqco h1 {
    color: #E1E1E6;
    font-size: 24px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #3A3A43;
}

.mqco h2 {
    color: #E1E1E6;
    font-size: 20px;
    margin: 25px 0 15px;
}

.mqco p {
    color: #A9A9B3;
    font-size: 14px;
    line-height: 1.6;
}

/* Description list for integration mode */
.mqcifce-description-list {
    margin-top: 10px;
    padding-left: 20px;
    color: #A9A9B3;
}

.mqcifce-description-list li {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.5;
}

.mqcifce-description-list strong {
    color: #E1E1E6;
    font-weight: 600;
}

.mqco .form-table {
    background: #27272F;
    border-radius: 5px;
    padding: 15px;
    margin-top: 20px;
}

.mqco .form-table th {
    color: #E1E1E6;
    font-weight: 500;
    padding: 20px 10px 20px 20px;
}

.mqco .form-table td {
    padding: 20px 20px 20px 10px;
}

.mqco input[type="text"],
.mqco input[type="number"],
.mqco select,
.mqco textarea {
    background: #1B1B1E;
    border: 1px solid #3A3A43;
    color: #E1E1E6;
    padding: 8px 12px;
    border-radius: 4px;
    width: 100%;
    max-width: 400px;
}

.mqco input[type="text"]:focus,
.mqco input[type="number"]:focus,
.mqco select:focus,
.mqco textarea:focus {
    border-color: #6366F1;
    box-shadow: 0 0 0 1px #6366F1;
}

.mqco .button-primary {
    background: #6366F1;
    border-color: #6366F1;
    color: white;
    padding: 8px 20px;
    height: auto;
    line-height: 1.4;
    text-shadow: none;
    box-shadow: none;
}

.mqco .button-primary:hover,
.mqco .button-primary:focus {
    background: #4F46E5;
    border-color: #4F46E5;
}

.mqco .button-secondary {
    background: #27272F;
    border-color: #3A3A43;
    color: #E1E1E6;
    text-shadow: none;
    box-shadow: none;
}

.mqco .button-secondary:hover,
.mqco .button-secondary:focus {
    background: #3A3A43;
    border-color: #4A4A53;
    color: #E1E1E6;
}

.mqco .description {
    color: #A9A9B3;
    font-style: italic;
    margin-top: 5px;
}

/* Checkbox styling */
.mqco input[type="checkbox"] {
    position: relative;
    width: 40px;
    height: 20px;
    -webkit-appearance: none;
    background: #27272F;
    outline: none;
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    transition: 0.5s;
    border: 1px solid #3A3A43;
}

.mqco input:checked[type="checkbox"] {
    background: #6366F1;
}

.mqco input[type="checkbox"]:before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 10px;
    top: 1px;
    left: 1px;
    background: #E1E1E6;
    transition: 0.5s;
    transform: scale(1.1);
}

.mqco input:checked[type="checkbox"]:before {
    left: 21px;
}

/* Mind Qtrl logo in header */
.mqco-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.mqco-header img {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.mqco-header h1 {
    margin: 0;
    padding: 0;
    border: none;
}
