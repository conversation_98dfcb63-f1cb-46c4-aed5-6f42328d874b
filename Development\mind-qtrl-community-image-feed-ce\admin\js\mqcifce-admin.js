/**
 * Admin JavaScript for Mind Qtrl | Community Image Feed CE
 *
 * @since      0.1.0
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Add Mind Qtrl logo to the header
        const $header = $('<div class="mqco-header"></div>');
        const $logo = $('<img src="' + mqcifceAdminVars.pluginUrl + 'admin/images/mind-qtrl-logo.png" alt="Mind Qtrl Logo">');
        const $title = $('h1.wp-heading-inline').clone();
        
        $header.append($logo).append($title);
        $('h1.wp-heading-inline').replaceWith($header);
        
        // Initialize any interactive elements
        initializeFormElements();
    });

    /**
     * Initialize form elements
     */
    function initializeFormElements() {
        // Add any form initialization code here
    }

})(jQuery);
