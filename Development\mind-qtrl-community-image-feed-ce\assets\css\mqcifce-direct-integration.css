/* Mind Qtrl Community Image Feed CE - Direct Integration CSS */

/* Media grid */
.mqcifce-media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 20px;
}

/* Fallback message */
.mqcifce-fallback-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    background-color: var(--fcom-secondary-content-bg, #f5f7fa);
    border-radius: 8px;
    color: var(--fcom-text-color, #606266);
}

.mqcifce-fallback-message i {
    font-size: 48px;
    margin-bottom: 20px;
    color: var(--fcom-primary-color, #409eff);
}

.mqcifce-fallback-message p {
    margin: 10px 0;
    font-size: 14px;
}

.mqcifce-fallback-message button {
    margin-top: 20px;
}

/* Media container */
.mqcifce-media-container {
    padding: 20px;
    background-color: var(--fcom-content-bg, #fff);
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Media filters */
.mqcifce-media-filters {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.mqcifce-filter-group {
    display: flex;
    gap: 20px;
    background-color: var(--fcom-secondary-content-bg, #f5f7fa);
    padding: 10px 20px;
    border-radius: 20px;
}

.mqcifce-filter-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.mqcifce-filter-group input {
    margin-right: 5px;
}

/* Media items */
.mqcifce-media-item {
    position: relative;
    aspect-ratio: 1 / 1;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.mqcifce-media-item:hover {
    transform: scale(1.05);
}

.mqcifce-media-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Video thumbnails */
.mqcifce-video-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
}

.mqcifce-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
}

/* Load more button */
.fcom_load_more {
    text-align: center;
    margin-top: 20px;
    padding: 10px;
}

/* Loading indicator */
.fcom_loading_wrap {
    text-align: center;
    padding: 40px;
    color: var(--fcom-text-color, #606266);
}

/* Error message */
.fcom_alert_error {
    background-color: #fef0f0;
    color: #f56c6c;
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
    display: flex;
    align-items: center;
}

.fcom_alert_error i {
    margin-right: 10px;
    font-size: 16px;
}

.fcom_alert_error button {
    margin-left: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .mqcifce-media-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .mqcifce-filter-group {
        flex-direction: column;
        gap: 10px;
    }
}
