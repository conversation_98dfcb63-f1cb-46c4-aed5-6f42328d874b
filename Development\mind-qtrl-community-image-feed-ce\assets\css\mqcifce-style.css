/**
 * Mind Qtrl | Community Image Feed CE
 * Main stylesheet
 */

/* Style the media tab */
.fcom_profile_media {
    display: inline-block;
    margin: 0;
    padding: 0;
}

.fcom_profile_media a {
    display: block;
    padding: 10px 15px;
    color: var(--fcom-menu-text, #333);
    text-decoration: none;
    transition: color 0.3s ease;
}

.fcom_profile_media a:hover {
    color: var(--fcom-primary-color, #4a90e2);
}

/* Custom Elements Styles */
mqcif-media-gallery {
    display: block;
    padding: 20px;
    background-color: var(--fcom-content-bg, #fff);
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

mqcif-media-filters {
    display: block;
    margin-bottom: 20px;
}

mqcif-media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 5px;
    padding: 5px;
    margin: 20px 0;
}

mqcif-media-item {
    position: relative;
    padding-top: 100%; /* Maintain square aspect ratio */
    overflow: hidden;
    background-color: #e0e0e0;
    cursor: pointer;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 2px;
    display: block;
}

mqcif-media-item:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

mqcif-media-item img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: opacity 0.3s ease-in-out;
}

/* Video thumbnail styles */
.mqcifce-video-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
}

.mqcifce-video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mqcifce-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 48px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mqcifce-play-icon i {
    color: white;
    font-size: 24px;
}

/* Loading and skeleton styles */
.mqcifce-skeleton-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 5px;
    padding: 5px;
    margin: 20px 0;
}

.mqcifce-skeleton-item {
    padding-top: 100%; /* Square aspect ratio */
    background-color: #e0e0e0;
    border-radius: 2px;
    animation: mqcifce-pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes mqcifce-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.mqcifce-spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 15px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: mqcifce-spin 1s linear infinite;
}

@keyframes mqcifce-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
mqcif-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.mqcifce-pagination-button {
    padding: 8px 16px;
    margin: 0 4px;
    background-color: var(--fcom-secondary-content-bg, #f5f5f5);
    border: 1px solid var(--fcom-border-color, #ddd);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mqcifce-pagination-button:hover {
    background-color: var(--fcom-hover-bg, #e9e9e9);
}

.mqcifce-pagination-button.active {
    background-color: var(--fcom-primary-color, #4a90e2);
    color: white;
    border-color: var(--fcom-primary-color, #4a90e2);
}

.mqcifce-pagination-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Load more button */
.mqcifce-load-more {
    display: block;
    margin: 20px auto;
    padding: 10px 20px;
    background-color: var(--fcom-primary-color, #4a90e2);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mqcifce-load-more:hover {
    background-color: var(--fcom-primary-hover, #3a80d2);
}

.mqcifce-load-more:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Error message */
.mqcifce-error {
    padding: 15px;
    margin: 20px 0;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

/* Vue Component Styles */
.mqcifce-media-page {
    padding: 20px;
    background-color: var(--fcom-content-bg, #fff);
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mqcifce-media-page .fcom_content_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.mqcifce-media-page .fcom_content_header h2 {
    margin: 0;
    font-size: 24px;
    color: var(--fcom-heading-color, #333);
}

.mqcifce-media-page .fcom_content_filters {
    margin-top: 10px;
}

.mqcifce-media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.mqcifce-media-item {
    position: relative;
    padding-top: 100%; /* Square aspect ratio */
    overflow: hidden;
    background-color: #e0e0e0;
    cursor: pointer;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 4px;
}

.mqcifce-media-item:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.mqcifce-media-item img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.mqcifce-media-page .fcom_empty_state {
    text-align: center;
    padding: 40px 20px;
    color: var(--fcom-text-secondary, #666);
}

.mqcifce-media-page .fcom_empty_state i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
    color: var(--fcom-border-color, #ddd);
}

.mqcifce-media-page .fcom_loading_wrap {
    text-align: center;
    padding: 40px 20px;
    color: var(--fcom-text-secondary, #666);
}

.mqcifce-media-page .fcom_loading_wrap i {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.mqcifce-media-page .fcom_load_more {
    text-align: center;
    margin: 20px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .mqcifce-media-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .mqcifce-media-page .fcom_content_header {
        flex-direction: column;
        align-items: flex-start;
    }

    .mqcifce-media-page .fcom_content_filters {
        margin-top: 15px;
        width: 100%;
        overflow-x: auto;
    }
}
