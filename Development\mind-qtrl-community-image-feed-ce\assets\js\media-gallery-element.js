/**
 * Media Gallery Custom Element for Mind Qtrl Community Image Feed CE
 *
 * This custom element displays a gallery of media items (images and videos)
 * for a specific user in Fluent Community.
 *
 * @since 0.3.0
 */

// Import base element if using ES modules
// import { MQCIFCEBaseElement } from './base-element.js';

// Get the base element class from the global namespace
const MQCIFCEBaseElement = window.MQCIFCE.elements.BaseElement;

/**
 * Media Gallery element class
 */
class MQCIFCEMediaGallery extends MQCIFCEBaseElement {
    /**
     * Observed attributes for this element
     */
    static get observedAttributes() {
        return ['username', 'media-type', 'items-per-page'];
    }

    /**
     * Constructor for the media gallery element
     */
    constructor() {
        super();

        // Initialize state
        this._state = {
            username: '',
            mediaType: 'all', // all, images, videos
            itemsPerPage: 9,
            currentPage: 1,
            isLoading: false,
            error: null,
            mediaItems: [],
            totalItems: 0,
            totalPages: 0,
            infiniteScroll: true,
            lazyLoading: true,
            hasMoreItems: true,
            isInfiniteScrollLoading: false
        };

        // Bind methods
        this.loadMedia = this.loadMedia.bind(this);
        this.handleFilterChange = this.handleFilterChange.bind(this);
        this.handlePageChange = this.handlePageChange.bind(this);
        this.handleRetry = this.handleRetry.bind(this);
        this.handleScroll = this.handleScroll.bind(this);
        this.setupIntersectionObserver = this.setupIntersectionObserver.bind(this);
        this.loadMoreItems = this.loadMoreItems.bind(this);
    }

    /**
     * Called when the element is connected to the DOM
     */
    connectedCallback() {
        super.connectedCallback();

        // Get attributes
        this._state.username = this.getAttribute('username') || '';
        this._state.mediaType = this.getAttribute('media-type') || 'all';
        this._state.itemsPerPage = parseInt(this.getAttribute('items-per-page') || '9', 10);

        // Get options from global variable
        const options = window.MQCIFCE.options || {};

        // Set infinite scroll and lazy loading from options
        this._state.infiniteScroll = options.infinite_scroll !== '0';
        this._state.lazyLoading = options.lazy_loading !== '0';

        // Set up scroll event listener for infinite scroll
        if (this._state.infiniteScroll) {
            this.debugLog('Setting up infinite scroll');
            window.addEventListener('scroll', this.handleScroll);
        }

        // Load media
        this.loadMedia();
    }

    /**
     * Called when the element is disconnected from the DOM
     */
    disconnectedCallback() {
        super.disconnectedCallback();

        // Remove scroll event listener
        if (this._state.infiniteScroll) {
            window.removeEventListener('scroll', this.handleScroll);
        }

        // Disconnect intersection observer if it exists
        if (this._intersectionObserver) {
            this._intersectionObserver.disconnect();
            this._intersectionObserver = null;
        }
    }

    /**
     * Called when an observed attribute changes
     */
    attributeChangedCallback(name, oldValue, newValue) {
        super.attributeChangedCallback(name, oldValue, newValue);

        // Update state based on attribute
        switch (name) {
            case 'username':
                this._state.username = newValue || '';
                break;
            case 'media-type':
                this._state.mediaType = newValue || 'all';
                break;
            case 'items-per-page':
                this._state.itemsPerPage = parseInt(newValue || '15', 10);
                break;
        }

        // Reload media if username or media-type changed
        if ((name === 'username' || name === 'media-type') && this.isConnected && oldValue !== newValue) {
            this.loadMedia();
        }
    }

    /**
     * Load media items from the API
     */
    async loadMedia() {
        // Check if we have a username
        if (!this._state.username) {
            this._state.error = 'No username provided';
            this.render();
            return;
        }

        // Set loading state
        this._state.isLoading = true;
        this._state.error = null;
        this.render();

        try {
            // Get REST API URL
            const restUrl = window.MQCIFCE.restUrl || window.FluentCommunityVars?.rest_url || '/wp-json/';

            // Build the API URL
            const apiUrl = `${restUrl}mqcifce/v1/user-media-feeds/${this._state.username}?page=${this._state.currentPage}&per_page=${this._state.itemsPerPage}&media_type=${this._state.mediaType}`;

            // Get the nonce
            const nonce = window.MQCIFCE.nonce || window.FluentCommunityVars?.nonce || '';

            // Fetch the media items
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': nonce
                }
            });

            // Check if the response is ok
            if (!response.ok) {
                // Try to parse error response
                try {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `Error: ${response.status} ${response.statusText}`);
                } catch (parseError) {
                    // If we can't parse the error, use a generic message
                    throw new Error(`Error loading media: ${response.status} ${response.statusText}`);
                }
            }

            // Parse the response
            const data = await response.json();

            // Update state
            const newItems = data.feeds || [];

            // If this is the first page, replace the items
            // Otherwise, append the new items (for infinite scroll)
            if (this._state.currentPage === 1) {
                this._state.mediaItems = newItems;
            } else {
                this._state.mediaItems = [...this._state.mediaItems, ...newItems];
            }

            this._state.totalItems = data.meta?.total || 0;
            this._state.totalPages = data.meta?.last_page || 0;
            this._state.isLoading = false;
            this._state.isInfiniteScrollLoading = false;

            // Check if we have more items to load
            this._state.hasMoreItems = this._state.currentPage < this._state.totalPages;

            // Log debug info
            this.debugLog('Media items loaded', {
                count: newItems.length,
                total: this._state.totalItems,
                pages: this._state.totalPages,
                currentPage: this._state.currentPage,
                hasMoreItems: this._state.hasMoreItems
            });

            // Render the updated state
            this.render();

            // Set up intersection observer for lazy loading
            if (this._state.lazyLoading) {
                this.setupIntersectionObserver();
            }
        } catch (error) {
            // Update error state
            this._state.isLoading = false;
            this._state.isInfiniteScrollLoading = false;
            this._state.error = error.message || 'Error loading media items';

            // Log error
            console.error('[MQCIFCE] Error loading media:', error);

            // Try to load sample data as fallback
            this.loadSampleData();

            // Render the error state
            this.render();
        }
    }

    /**
     * Load sample data as fallback when API fails
     */
    loadSampleData() {
        this.debugLog('Loading sample data as fallback');

        // Create sample media items
        const sampleItems = [];

        // Sample image URLs
        const sampleImages = [
            'https://via.placeholder.com/800x600/3498db/ffffff?text=Sample+Image+1',
            'https://via.placeholder.com/800x600/e74c3c/ffffff?text=Sample+Image+2',
            'https://via.placeholder.com/800x600/2ecc71/ffffff?text=Sample+Image+3',
            'https://via.placeholder.com/800x600/f39c12/ffffff?text=Sample+Image+4',
            'https://via.placeholder.com/800x600/9b59b6/ffffff?text=Sample+Image+5',
            'https://via.placeholder.com/800x600/1abc9c/ffffff?text=Sample+Image+6',
            'https://via.placeholder.com/800x600/d35400/ffffff?text=Sample+Image+7',
            'https://via.placeholder.com/800x600/34495e/ffffff?text=Sample+Image+8',
            'https://via.placeholder.com/800x600/7f8c8d/ffffff?text=Sample+Image+9',
        ];

        // Generate sample items
        for (let i = 0; i < 9; i++) {
            sampleItems.push({
                id: 1000 + i,
                content: `Sample media item ${i + 1}`,
                media: [
                    {
                        id: 2000 + i,
                        media_type: 'image/jpeg',
                        public_url: sampleImages[i]
                    }
                ]
            });
        }

        // Update state with sample data
        this._state.mediaItems = sampleItems;
        this._state.totalItems = sampleItems.length;
        this._state.totalPages = 1;
        this._state.isLoading = false;
        this._state.isInfiniteScrollLoading = false;
        this._state.hasMoreItems = false;

        // Add a note about sample data
        this._state.usingSampleData = true;

        // Render the updated state
        this.render();
    }

    /**
     * Handle scroll event for infinite scroll
     */
    handleScroll() {
        // Skip if infinite scroll is disabled or we're already loading
        if (!this._state.infiniteScroll || this._state.isLoading || this._state.isInfiniteScrollLoading) {
            return;
        }

        // Skip if we don't have more items to load
        if (!this._state.hasMoreItems) {
            return;
        }

        // Get the scroll position
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        // Calculate the scroll position threshold (80% of the way down)
        const threshold = documentHeight - (windowHeight * 1.2);

        // If we've scrolled past the threshold, load more items
        if (scrollTop > threshold) {
            this.loadMoreItems();
        }
    }

    /**
     * Load more items for infinite scroll
     */
    loadMoreItems() {
        // Skip if we're already loading or don't have more items
        if (this._state.isLoading || this._state.isInfiniteScrollLoading || !this._state.hasMoreItems) {
            return;
        }

        // Set loading state
        this._state.isInfiniteScrollLoading = true;

        // Add loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'mqcifce-infinite-scroll-loading';
        loadingIndicator.innerHTML = `
            <div class="mqcifce-spinner"></div>
            <p>Loading more items...</p>
        `;

        // Add to the end of the grid
        const grid = this.shadowRoot.querySelector('.mqcifce-media-grid');
        if (grid) {
            grid.appendChild(loadingIndicator);
        }

        // Increment page number
        this._state.currentPage++;

        // Load the next page
        this.debugLog(`Loading more items, page ${this._state.currentPage}`);
        this.loadMedia();
    }

    /**
     * Set up intersection observer for lazy loading images
     */
    setupIntersectionObserver() {
        // Skip if lazy loading is disabled
        if (!this._state.lazyLoading) {
            return;
        }

        // Skip if the browser doesn't support IntersectionObserver
        if (!('IntersectionObserver' in window)) {
            this.debugLog('IntersectionObserver not supported, lazy loading disabled');
            return;
        }

        // Disconnect existing observer if it exists
        if (this._intersectionObserver) {
            this._intersectionObserver.disconnect();
        }

        // Create a new IntersectionObserver
        this._intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const mediaItem = entry.target;

                    // Find the image or video element
                    const img = mediaItem.querySelector('img[data-src]');
                    const video = mediaItem.querySelector('video[data-src]');
                    const bgElement = mediaItem.querySelector('[data-bg-src]');

                    // Load the image or video
                    if (img && img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');

                        // Add loaded class for CSS transitions
                        img.classList.add('mqcifce-loaded');
                    }

                    if (video && video.dataset.src) {
                        video.src = video.dataset.src;
                        video.removeAttribute('data-src');

                        // Add loaded class for CSS transitions
                        video.classList.add('mqcifce-loaded');
                    }

                    // Handle background images
                    if (bgElement && bgElement.dataset.bgSrc) {
                        bgElement.style.backgroundImage = `url(${bgElement.dataset.bgSrc})`;
                        bgElement.removeAttribute('data-bg-src');

                        // Add loaded class for CSS transitions
                        bgElement.classList.add('mqcifce-bg-loaded');
                    }

                    // Stop observing this item
                    this._intersectionObserver.unobserve(mediaItem);
                }
            });
        }, {
            root: null, // Use the viewport
            rootMargin: '100px', // Load images 100px before they become visible
            threshold: 0.1 // Trigger when 10% of the item is visible
        });

        // Start observing all media items
        const mediaItems = this.shadowRoot.querySelectorAll('.mqcifce-media-item');
        mediaItems.forEach(item => {
            this._intersectionObserver.observe(item);
        });
    }

    /**
     * Handle filter change
     *
     * @param {Event} event - The event object
     */
    handleFilterChange(event) {
        const mediaType = event.target.value;
        this._state.mediaType = mediaType;
        this._state.currentPage = 1; // Reset to first page
        this.loadMedia();
    }

    /**
     * Handle page change
     *
     * @param {Event} event - The event object
     */
    handlePageChange(event) {
        const page = parseInt(event.target.dataset.page, 10);
        if (page !== this._state.currentPage) {
            this._state.currentPage = page;
            this.loadMedia();
        }
    }

    /**
     * Handle retry button click
     */
    handleRetry() {
        this.loadMedia();
    }

    /**
     * Render the element
     */
    render() {
        // Clear shadow DOM
        this.shadowRoot.innerHTML = '';

        // Add styles
        this.shadowRoot.appendChild(this.createStyle(this.getStyles()));

        // Create container
        const container = document.createElement('div');
        container.className = 'mqcifce-media-gallery';

        // Add header with filters
        container.appendChild(this.renderHeader());

        // Add sample data notice if using sample data
        if (this._state.usingSampleData) {
            const sampleDataNotice = document.createElement('div');
            sampleDataNotice.className = 'mqcifce-sample-data-notice';
            sampleDataNotice.innerHTML = `
                <p>⚠️ Showing sample data because the API request failed. This is for demonstration purposes only.</p>
            `;
            container.appendChild(sampleDataNotice);
        }

        // Add content
        if (this._state.isLoading && this._state.currentPage === 1) {
            // Only show full loading indicator for initial load
            container.appendChild(this.renderLoading());
        } else if (this._state.error && this._state.currentPage === 1 && !this._state.usingSampleData) {
            // Only show error for initial load and if not using sample data
            container.appendChild(this.renderError());
        } else if (this._state.mediaItems.length === 0) {
            container.appendChild(this.renderEmpty());
        } else {
            // Add media grid
            container.appendChild(this.renderMediaGrid());

            // Add infinite scroll loading indicator if needed
            if (this._state.infiniteScroll && this._state.isInfiniteScrollLoading) {
                const loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'mqcifce-infinite-scroll-loading';
                loadingIndicator.innerHTML = `
                    <div class="mqcifce-spinner"></div>
                    <p>Loading more items...</p>
                `;
                container.appendChild(loadingIndicator);
            } else if (this._state.infiniteScroll && !this._state.hasMoreItems && this._state.mediaItems.length > 0) {
                // Show end of content message
                const endMessage = document.createElement('div');
                endMessage.className = 'mqcifce-end-message';
                endMessage.textContent = 'No more items to load';
                container.appendChild(endMessage);
            }

            // Add pagination (will only be shown if infinite scroll is disabled)
            container.appendChild(this.renderPagination());
        }

        // Add container to shadow DOM
        this.shadowRoot.appendChild(container);

        // Set up intersection observer for lazy loading after render
        if (this._state.lazyLoading && !this._state.isLoading) {
            // Use setTimeout to ensure the DOM is fully rendered
            setTimeout(() => {
                this.setupIntersectionObserver();
            }, 0);
        }
    }

    /**
     * Render the header with filters
     *
     * @returns {HTMLElement} The header element
     */
    renderHeader() {
        const header = document.createElement('div');
        header.className = 'mqcifce-header';

        // Add title
        const title = document.createElement('h2');
        title.textContent = `Media Gallery for ${this._state.username}`;
        header.appendChild(title);

        // Add filters
        const filters = document.createElement('div');
        filters.className = 'mqcifce-filters';

        // Add filter label
        const filterLabel = document.createElement('label');
        filterLabel.textContent = 'Show: ';
        filters.appendChild(filterLabel);

        // Add filter select
        const filterSelect = document.createElement('select');
        filterSelect.className = 'mqcifce-filter-select';
        filterSelect.addEventListener('change', this.handleFilterChange);

        // Add filter options
        const options = [
            { value: 'all', text: 'All Media' },
            { value: 'images', text: 'Images Only' },
            { value: 'videos', text: 'Videos Only' }
        ];

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            optionElement.selected = this._state.mediaType === option.value;
            filterSelect.appendChild(optionElement);
        });

        filters.appendChild(filterSelect);
        header.appendChild(filters);

        return header;
    }

    /**
     * Render loading state
     *
     * @returns {HTMLElement} The loading element
     */
    renderLoading() {
        const loading = document.createElement('div');
        loading.className = 'mqcifce-loading';
        loading.innerHTML = `
            <div class="mqcifce-spinner"></div>
            <p>Loading media items...</p>
        `;
        return loading;
    }

    /**
     * Render error state
     *
     * @returns {HTMLElement} The error element
     */
    renderError() {
        const error = document.createElement('div');
        error.className = 'mqcifce-error';
        error.innerHTML = `
            <p>Error: ${this._state.error}</p>
            <button class="mqcifce-retry-button">Retry</button>
        `;

        // Add event listener to retry button
        const retryButton = error.querySelector('.mqcifce-retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', this.handleRetry);
        }

        return error;
    }

    /**
     * Render empty state
     *
     * @returns {HTMLElement} The empty element
     */
    renderEmpty() {
        const empty = document.createElement('div');
        empty.className = 'mqcifce-empty';
        empty.innerHTML = `
            <p>No media items found for ${this._state.username}.</p>
        `;
        return empty;
    }

    /**
     * Render media grid
     *
     * @returns {HTMLElement} The media grid element
     */
    renderMediaGrid() {
        const grid = document.createElement('div');
        grid.className = 'mqcifce-media-grid';

        // Add media items
        this._state.mediaItems.forEach(item => {
            grid.appendChild(this.renderMediaItem(item));
        });

        return grid;
    }

    /**
     * Render a single media item
     *
     * @param {Object} item - The media item
     * @returns {HTMLElement} The media item element
     */
    renderMediaItem(item) {
        const mediaItem = document.createElement('div');
        mediaItem.className = 'mqcifce-media-item';
        mediaItem.dataset.id = item.id;

        // Get media preview
        let mediaPreview = '';
        let isYouTube = false;
        let youtubeUrl = '';

        // Check if this is a YouTube video
        if (item.youtube_video_id) {
            isYouTube = true;
            const youtubeId = item.youtube_video_id;
            youtubeUrl = `https://www.youtube.com/watch?v=${youtubeId}`;
            const thumbnailUrl = item.youtube_thumbnail || `https://i.ytimg.com/vi/${youtubeId}/maxresdefault.jpg`;

            // Use data-src for lazy loading if enabled
            if (this._state.lazyLoading) {
                mediaPreview = `
                    <div class="mqcifce-video-preview mqcifce-youtube-preview">
                        <img
                            data-src="${thumbnailUrl}"
                            alt="YouTube video thumbnail"
                            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                            class="mqcifce-lazy-image"
                        />
                        <div class="mqcifce-youtube-play-icon">
                            <svg viewBox="0 0 68 48"><path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path><path d="M 45,24 27,14 27,34" fill="#fff"></path></svg>
                        </div>
                    </div>
                `;
            } else {
                mediaPreview = `
                    <div class="mqcifce-video-preview mqcifce-youtube-preview">
                        <img src="${thumbnailUrl}" alt="YouTube video thumbnail" loading="lazy" />
                        <div class="mqcifce-youtube-play-icon">
                            <svg viewBox="0 0 68 48"><path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path><path d="M 45,24 27,14 27,34" fill="#fff"></path></svg>
                        </div>
                    </div>
                `;
            }
        }
        // Check if the item has media
        else if (item.media && item.media.length > 0) {
            // Use the first media item
            const firstMedia = item.media[0];

            if (firstMedia.media_type.startsWith('image/')) {
                // Use data-src for lazy loading if enabled
                if (this._state.lazyLoading) {
                    mediaPreview = `
                        <img
                            data-src="${firstMedia.public_url}"
                            alt="${item.content || 'Media item'}"
                            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                            class="mqcifce-lazy-image"
                        />
                    `;
                } else {
                    mediaPreview = `<img src="${firstMedia.public_url}" alt="${item.content || 'Media item'}" loading="lazy" />`;
                }
            } else if (firstMedia.media_type.startsWith('video/')) {
                // Use data-src for lazy loading if enabled
                if (this._state.lazyLoading) {
                    mediaPreview = `
                        <div class="mqcifce-video-preview">
                            <video
                                data-src="${firstMedia.public_url}"
                                preload="none"
                                class="mqcifce-lazy-video"
                            ></video>
                            <div class="mqcifce-video-play-icon"></div>
                        </div>
                    `;
                } else {
                    mediaPreview = `
                        <div class="mqcifce-video-preview">
                            <video src="${firstMedia.public_url}" preload="metadata"></video>
                            <div class="mqcifce-video-play-icon"></div>
                        </div>
                    `;
                }
            }
        }
        // Check if the item has media preview in meta
        else if (item.meta && item.meta.media_preview) {
            // Check if it's a YouTube video in meta
            if (item.meta.media_preview.youtube_id) {
                isYouTube = true;
                const youtubeId = item.meta.media_preview.youtube_id;
                youtubeUrl = `https://www.youtube.com/watch?v=${youtubeId}`;
                const thumbnailUrl = item.meta.media_preview.url || `https://i.ytimg.com/vi/${youtubeId}/maxresdefault.jpg`;

                // Use data-src for lazy loading if enabled
                if (this._state.lazyLoading) {
                    mediaPreview = `
                        <div class="mqcifce-video-preview mqcifce-youtube-preview">
                            <img
                                data-src="${thumbnailUrl}"
                                alt="YouTube video thumbnail"
                                src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                                class="mqcifce-lazy-image"
                            />
                            <div class="mqcifce-youtube-play-icon">
                                <svg viewBox="0 0 68 48"><path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path><path d="M 45,24 27,14 27,34" fill="#fff"></path></svg>
                            </div>
                        </div>
                    `;
                } else {
                    mediaPreview = `
                        <div class="mqcifce-video-preview mqcifce-youtube-preview">
                            <img src="${thumbnailUrl}" alt="YouTube video thumbnail" loading="lazy" />
                            <div class="mqcifce-youtube-play-icon">
                                <svg viewBox="0 0 68 48"><path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path><path d="M 45,24 27,14 27,34" fill="#fff"></path></svg>
                            </div>
                        </div>
                    `;
                }
            } else {
                // Regular media preview
                const previewUrl = typeof item.meta.media_preview === 'string' ?
                    item.meta.media_preview :
                    (item.meta.media_preview.url || '');

                if (previewUrl) {
                    if (this._state.lazyLoading) {
                        mediaPreview = `
                            <img
                                data-src="${previewUrl}"
                                alt="${item.content || 'Media item'}"
                                src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                                class="mqcifce-lazy-image"
                            />
                        `;
                    } else {
                        mediaPreview = `<img src="${previewUrl}" alt="${item.content || 'Media item'}" loading="lazy" />`;
                    }
                }
            }
        }

        // Add media preview
        mediaItem.innerHTML = `
            <div class="mqcifce-media-preview">
                ${mediaPreview}
            </div>
        `;

        // Add click event listener
        mediaItem.addEventListener('click', () => {
            if (isYouTube && youtubeUrl) {
                // Open YouTube video in a new tab
                window.open(youtubeUrl, '_blank');
            } else {
                // Check if Fluent Community modal function exists
                if (window.FluentCommunityApp && window.FluentCommunityApp.openFeedModal) {
                    window.FluentCommunityApp.openFeedModal(item.id);
                } else {
                    // Fallback to opening the media directly
                    if (item.media && item.media.length > 0) {
                        window.open(item.media[0].public_url, '_blank');
                    } else if (item.meta && item.meta.media_preview && item.meta.media_preview.url) {
                        window.open(item.meta.media_preview.url, '_blank');
                    } else if (item.meta && item.meta.media_preview && typeof item.meta.media_preview === 'string') {
                        window.open(item.meta.media_preview, '_blank');
                    }
                }
            }
        });

        return mediaItem;
    }

    /**
     * Render pagination
     *
     * @returns {HTMLElement} The pagination element
     */
    renderPagination() {
        const pagination = document.createElement('div');
        pagination.className = 'mqcifce-pagination';

        // Don't show pagination if there's only one page or if infinite scroll is enabled
        if (this._state.totalPages <= 1 || this._state.infiniteScroll) {
            return pagination;
        }

        // Add previous button
        const prevButton = document.createElement('button');
        prevButton.className = 'mqcifce-pagination-button';
        prevButton.textContent = 'Previous';
        prevButton.dataset.page = this._state.currentPage - 1;
        prevButton.disabled = this._state.currentPage <= 1;
        if (!prevButton.disabled) {
            prevButton.addEventListener('click', this.handlePageChange);
        }
        pagination.appendChild(prevButton);

        // Add page buttons
        const maxPages = 5; // Maximum number of page buttons to show
        let startPage = Math.max(1, this._state.currentPage - Math.floor(maxPages / 2));
        let endPage = Math.min(this._state.totalPages, startPage + maxPages - 1);

        // Adjust start page if we're near the end
        if (endPage - startPage < maxPages - 1) {
            startPage = Math.max(1, endPage - maxPages + 1);
        }

        // Add first page button if not included in the range
        if (startPage > 1) {
            const firstButton = document.createElement('button');
            firstButton.className = 'mqcifce-pagination-button';
            firstButton.textContent = '1';
            firstButton.dataset.page = 1;
            firstButton.addEventListener('click', this.handlePageChange);
            pagination.appendChild(firstButton);

            // Add ellipsis if there's a gap
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'mqcifce-pagination-ellipsis';
                ellipsis.textContent = '...';
                pagination.appendChild(ellipsis);
            }
        }

        // Add page buttons
        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.className = 'mqcifce-pagination-button';
            if (i === this._state.currentPage) {
                pageButton.classList.add('active');
            }
            pageButton.textContent = i.toString();
            pageButton.dataset.page = i;
            pageButton.addEventListener('click', this.handlePageChange);
            pagination.appendChild(pageButton);
        }

        // Add last page button if not included in the range
        if (endPage < this._state.totalPages) {
            // Add ellipsis if there's a gap
            if (endPage < this._state.totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'mqcifce-pagination-ellipsis';
                ellipsis.textContent = '...';
                pagination.appendChild(ellipsis);
            }

            const lastButton = document.createElement('button');
            lastButton.className = 'mqcifce-pagination-button';
            lastButton.textContent = this._state.totalPages.toString();
            lastButton.dataset.page = this._state.totalPages;
            lastButton.addEventListener('click', this.handlePageChange);
            pagination.appendChild(lastButton);
        }

        // Add next button
        const nextButton = document.createElement('button');
        nextButton.className = 'mqcifce-pagination-button';
        nextButton.textContent = 'Next';
        nextButton.dataset.page = this._state.currentPage + 1;
        nextButton.disabled = this._state.currentPage >= this._state.totalPages;
        if (!nextButton.disabled) {
            nextButton.addEventListener('click', this.handlePageChange);
        }
        pagination.appendChild(nextButton);

        return pagination;
    }

    /**
     * Get the styles for the element
     *
     * @returns {string} The CSS styles
     */
    getStyles() {
        return `
            :host {
                display: block;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
                color: var(--fcom-text-color, #333);
                --primary-color: var(--fcom-primary-color, #4a90e2);
                --border-color: var(--fcom-border-color, #e1e1e1);
                --background-color: var(--fcom-background-color, #fff);
                --hover-bg: var(--fcom-hover-bg, #f5f5f5);
            }

            .mqcifce-media-gallery {
                background-color: var(--background-color);
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .mqcifce-header {
                padding: 16px;
                border-bottom: 1px solid var(--border-color);
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
            }

            .mqcifce-header h2 {
                margin: 0;
                font-size: 1.5rem;
                color: var(--fcom-heading-color, #333);
            }

            .mqcifce-filters {
                display: flex;
                align-items: center;
                margin-top: 8px;
            }

            .mqcifce-filter-select {
                margin-left: 8px;
                padding: 6px 12px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                background-color: var(--background-color);
                color: var(--fcom-text-color, #333);
                font-size: 14px;
            }

            .mqcifce-media-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
                padding: 16px;
            }

            .mqcifce-media-item {
                position: relative;
                aspect-ratio: 1 / 1;
                overflow: hidden;
                border-radius: 4px;
                cursor: pointer;
                transition: transform 0.2s ease;
            }

            .mqcifce-media-item:hover {
                transform: scale(1.02);
            }

            .mqcifce-media-preview {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f0f0f0;
            }

            .mqcifce-media-preview img,
            .mqcifce-media-preview video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .mqcifce-video-preview {
                position: relative;
                width: 100%;
                height: 100%;
            }

            .mqcifce-video-play-icon {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 48px;
                height: 48px;
                background-color: rgba(0, 0, 0, 0.7);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .mqcifce-video-play-icon::before {
                content: '';
                width: 0;
                height: 0;
                border-top: 10px solid transparent;
                border-bottom: 10px solid transparent;
                border-left: 16px solid white;
                margin-left: 4px;
            }

            /* YouTube specific styles */
            .mqcifce-youtube-preview {
                position: relative;
                width: 100%;
                height: 100%;
                background-color: #000;
            }

            .mqcifce-youtube-play-icon {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 68px;
                height: 48px;
                opacity: 0.9;
                transition: opacity 0.3s ease;
            }

            .mqcifce-youtube-preview:hover .mqcifce-youtube-play-icon {
                opacity: 1;
            }

            .mqcifce-youtube-play-icon svg {
                width: 100%;
                height: 100%;
            }

            .mqcifce-loading {
                padding: 32px;
                text-align: center;
            }

            .mqcifce-spinner {
                display: inline-block;
                width: 40px;
                height: 40px;
                border: 4px solid rgba(0, 0, 0, 0.1);
                border-radius: 50%;
                border-top-color: var(--primary-color);
                animation: mqcifce-spin 1s linear infinite;
                margin-bottom: 16px;
            }

            @keyframes mqcifce-spin {
                to { transform: rotate(360deg); }
            }

            .mqcifce-error {
                padding: 32px;
                text-align: center;
                color: #d32f2f;
            }

            .mqcifce-retry-button {
                margin-top: 16px;
                padding: 8px 16px;
                background-color: var(--primary-color);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            }

            .mqcifce-retry-button:hover {
                background-color: var(--fcom-primary-hover, #3a80d2);
            }

            .mqcifce-empty {
                padding: 32px;
                text-align: center;
                color: #757575;
            }

            .mqcifce-pagination {
                display: flex;
                justify-content: center;
                padding: 16px;
                border-top: 1px solid var(--border-color);
                gap: 8px;
                flex-wrap: wrap;
            }

            .mqcifce-pagination-button {
                padding: 6px 12px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                background-color: var(--background-color);
                color: var(--fcom-text-color, #333);
                cursor: pointer;
                font-size: 14px;
            }

            .mqcifce-pagination-button:hover:not(:disabled) {
                background-color: var(--hover-bg);
            }

            .mqcifce-pagination-button.active {
                background-color: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
            }

            .mqcifce-pagination-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .mqcifce-pagination-ellipsis {
                display: flex;
                align-items: center;
                padding: 0 8px;
            }

            /* Infinite scroll loading indicator */
            .mqcifce-infinite-scroll-loading {
                grid-column: 1 / -1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 20px;
                text-align: center;
            }

            /* End of content message */
            .mqcifce-end-message {
                grid-column: 1 / -1;
                text-align: center;
                padding: 20px;
                color: var(--fcom-secondary-text, #666);
                font-style: italic;
            }

            /* Sample data notice */
            .mqcifce-sample-data-notice {
                background-color: #fff3cd;
                color: #856404;
                padding: 10px 15px;
                margin-bottom: 20px;
                border-radius: 4px;
                border-left: 4px solid #ffeeba;
            }

            .mqcifce-sample-data-notice p {
                margin: 0;
                padding: 0;
            }

            /* Lazy loading styles */
            .mqcifce-lazy-image,
            .mqcifce-lazy-video {
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .mqcifce-lazy-image[src],
            .mqcifce-lazy-video[src],
            .mqcifce-loaded,
            .mqcifce-bg-loaded {
                opacity: 1;
            }

            /* Transition for background images */
            [data-bg-src] {
                background-color: #f0f0f0;
                transition: background-image 0.3s ease;
            }

            /* Placeholder for lazy loaded images */
            .mqcifce-media-preview {
                background-color: var(--fcom-secondary-bg, #f0f0f0);
                position: relative;
            }

            .mqcifce-media-preview::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                border: 3px solid rgba(0, 0, 0, 0.1);
                border-top-color: var(--primary-color);
                border-radius: 50%;
                animation: mqcifce-spin 1s linear infinite;
                opacity: 0.5;
            }

            .mqcifce-media-preview img[src],
            .mqcifce-media-preview video[src] {
                opacity: 1;
            }

            .mqcifce-media-preview img[src] + .mqcifce-media-preview::before,
            .mqcifce-media-preview video[src] + .mqcifce-media-preview::before {
                display: none;
            }

            @media (max-width: 768px) {
                .mqcifce-header {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .mqcifce-filters {
                    margin-top: 16px;
                }

                .mqcifce-media-grid {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 8px;
                }
            }

            @media (max-width: 480px) {
                .mqcifce-media-grid {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 4px;
                }
            }
        `;
    }
}

// Register the custom element
window.MQCIFCE.registerElement('mqcif-media-gallery', MQCIFCEMediaGallery);

// Store the element class in the global namespace
window.MQCIFCE.elements.MediaGallery = MQCIFCEMediaGallery;

// Export the class for ES modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MQCIFCEMediaGallery };
}
