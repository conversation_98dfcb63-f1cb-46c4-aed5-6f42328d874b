/**
 * Mind Qtrl Community Image Feed CE - Direct Integration Script
 *
 * This script is designed to be loaded directly in the page (not through WordPress enqueuing)
 * to ensure it runs regardless of WordPress caching or plugin activation issues.
 *
 * It implements a robust approach to adding the Media tab to user profiles in Fluent Community.
 */
(function() {
    'use strict';

    // Configuration
    const config = {
        debug: false, // Only enable debug logging when needed
        observerTimeout: 15000, // 15 seconds
        retryIntervals: [100, 500, 1000, 2000], // Retry intervals in milliseconds
        maxRetries: 3,
        forceDebug: false // Only force debug logging when needed
    };

    // Check if debug mode is enabled in the options
    if (window.MQCIFCE && window.MQCIFCE.options && window.MQCIFCE.options.debug_mode === '1') {
        config.debug = true;
    }

    // Debug logging
    function debugLog(...args) {
        if (config.debug || config.forceDebug) {
            console.log('[MQCIFCE Direct]', ...args);
        }
    }

    // Error logging
    function errorLog(...args) {
        console.error('[MQCIFCE Direct]', ...args);
    }

    // Initialize the integration
    function initialize() {
        debugLog('Initializing direct integration');

        // Check if we're on a user profile page
        if (isUserProfilePage()) {
            debugLog('On user profile page, setting up integration');

            // Try to add the Media tab immediately with retries
            addMediaTabWithRetries();
        } else {
            debugLog('Not on a user profile page, setting up observers only');
        }

        // Set up route change listener (includes URL observer)
        setupRouteChangeListener();

        // Set up custom event listener
        setupCustomEventListener();

        // Set up global navigation observer (new robust approach)
        setupGlobalNavigationObserver();

        // Add a global function for manual triggering (useful for debugging)
        window.mqcifceAddMediaTab = function() {
            debugLog('Manual media tab addition triggered');
            return addMediaTabWithRetries();
        };

        // Check again after a delay for slow-loading SPAs
        setTimeout(() => {
            if (isUserProfilePage() && !document.querySelector('.fcom_profile_media')) {
                debugLog('Profile page detected after delay, adding media tab');
                addMediaTabWithRetries();
            }
        }, 1500);

        debugLog('Integration setup complete');
    }

    // Set up a global navigation observer that works with all navigation methods
    function setupGlobalNavigationObserver() {
        debugLog('Setting up global navigation observer');

        // Store the current URL and route for comparison
        let lastUrl = window.location.href;
        let lastRoute = document.body.getAttribute('data-route') || '';

        // Create a function to check for navigation changes
        function checkForNavigationChanges() {
            const currentUrl = window.location.href;
            const currentRoute = document.body.getAttribute('data-route') || '';

            // Check if URL or route has changed
            if (currentUrl !== lastUrl || currentRoute !== lastRoute) {
                debugLog('Navigation change detected:');
                debugLog('- URL changed from', lastUrl, 'to', currentUrl);
                debugLog('- Route changed from', lastRoute, 'to', currentRoute);

                // Update stored values
                lastUrl = currentUrl;
                lastRoute = currentRoute;

                // Check if we're now on a profile page
                if (isUserProfilePage()) {
                    debugLog('Navigation led to a profile page, adding media tab');
                    addMediaTabWithRetries();
                }
            }
        }

        // Set up multiple detection methods

        // 1. Regular interval check (most reliable)
        const intervalId = setInterval(checkForNavigationChanges, 500);

        // 2. History API interception
        const originalPushState = window.history.pushState;
        const originalReplaceState = window.history.replaceState;

        window.history.pushState = function() {
            originalPushState.apply(this, arguments);
            debugLog('History pushState detected');
            setTimeout(checkForNavigationChanges, 50);
        };

        window.history.replaceState = function() {
            originalReplaceState.apply(this, arguments);
            debugLog('History replaceState detected');
            setTimeout(checkForNavigationChanges, 50);
        };

        // 3. PopState event (browser back/forward)
        window.addEventListener('popstate', () => {
            debugLog('PopState event detected');
            setTimeout(checkForNavigationChanges, 50);
        });

        // 4. HashChange event
        window.addEventListener('hashchange', () => {
            debugLog('HashChange event detected');
            setTimeout(checkForNavigationChanges, 50);
        });

        // 5. Vue router navigation guards (if available)
        if (window.FluentCommunityApp && window.FluentCommunityApp.$router) {
            window.FluentCommunityApp.$router.afterEach(() => {
                debugLog('Vue router navigation detected');
                setTimeout(checkForNavigationChanges, 50);
            });
        }

        // Clean up after a reasonable time
        setTimeout(() => {
            clearInterval(intervalId);
            debugLog('Global navigation observer interval stopped after timeout');
        }, config.observerTimeout);
    }

    // Check if we're on a user profile page
    function isUserProfilePage() {
        // Check URL pattern
        const isProfileUrl = window.location.pathname.includes('/feed/u/');
        if (isProfileUrl) {
            debugLog('Detected profile page from URL');
            return true;
        }

        // Check data-route attribute
        const dataRoute = document.body.getAttribute('data-route');
        if (dataRoute && (
            dataRoute === 'user_profile' ||
            dataRoute === 'user_profile_feeds' ||
            dataRoute === 'user_spaces' ||
            dataRoute === 'user_comments' ||
            dataRoute === 'update_profile' ||
            dataRoute === 'user_notification_settings' ||
            dataRoute === 'user_profile_media'
        )) {
            debugLog('Detected profile page from data-route:', dataRoute);
            return true;
        }

        // Check for profile elements in the DOM
        const hasProfileElements = !!document.querySelector('.fcom_profile_nav, .fcom_profile_header, .fcom_profile_content');
        if (hasProfileElements) {
            debugLog('Detected profile page from DOM elements');
            return true;
        }

        // Check for username in URL hash (for SPA navigation)
        if (window.location.hash && window.location.hash.includes('/u/')) {
            debugLog('Detected profile page from URL hash');
            return true;
        }

        return false;
    }

    // Helper function to add media tab with multiple retries
    function addMediaTabWithRetries() {
        // Try immediately
        if (addMediaTab()) {
            return true;
        }

        // Try with increasing delays
        const delays = [100, 300, 500, 1000, 2000];
        let attemptCount = 0;

        function attemptAdd() {
            if (attemptCount >= delays.length) {
                return;
            }

            setTimeout(() => {
                if (addMediaTab()) {
                    debugLog(`Media tab added successfully on attempt ${attemptCount + 1}`);
                    return;
                }

                attemptCount++;
                attemptAdd();
            }, delays[attemptCount]);
        }

        attemptAdd();
    }

    // Set up DOM mutation observer to detect profile navigation changes
    function setupDomMutationObserver() {
        debugLog('Setting up DOM mutation observer');

        // Create a mutation observer to watch for DOM changes
        const observer = new MutationObserver((mutations) => {
            // Check if any of the mutations involve the profile navigation
            const relevantMutation = mutations.some(mutation => {
                // Check if the mutation target is or contains profile navigation
                if (mutation.target.classList &&
                    (mutation.target.classList.contains('fcom_profile_nav') ||
                     mutation.target.classList.contains('fcom_profile_header') ||
                     mutation.target.classList.contains('fcom_profile_content'))) {
                    return true;
                }

                // Check if any added nodes are profile navigation
                if (mutation.addedNodes && mutation.addedNodes.length) {
                    return Array.from(mutation.addedNodes).some(node => {
                        if (node.classList) {
                            return node.classList.contains('fcom_profile_nav') ||
                                   node.classList.contains('fcom_profile_header') ||
                                   node.classList.contains('fcom_profile_content');
                        }

                        // Check if the node contains profile navigation
                        if (node.querySelector) {
                            return node.querySelector('.fcom_profile_nav, .fcom_profile_header, .fcom_profile_content');
                        }

                        return false;
                    });
                }

                return false;
            });

            // If we found a relevant mutation and we're on a profile page
            if (relevantMutation && isUserProfilePage()) {
                debugLog('Detected DOM changes in profile navigation');
                addMediaTabWithRetries();
            }
        });

        // Start observing the document with the configured parameters
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'data-route']
        });

        // Clean up after a reasonable time
        setTimeout(() => {
            observer.disconnect();
            debugLog('Mutation observer disconnected after timeout');
        }, config.observerTimeout);
    }

    // Set up URL change observer to detect SPA navigation
    function setupUrlChangeObserver() {
        debugLog('Setting up URL change observer');

        // Store the current URL for comparison
        let lastUrl = window.location.href;

        // Function to check for URL changes
        function checkForUrlChanges() {
            const currentUrl = window.location.href;

            // If the URL has changed
            if (currentUrl !== lastUrl) {
                debugLog('URL changed from', lastUrl, 'to', currentUrl);
                lastUrl = currentUrl;

                // Check if this is a profile URL
                if (currentUrl.includes('/feed/u/')) {
                    debugLog('New URL is a profile URL, adding media tab');
                    addMediaTabWithRetries();
                }
            }
        }

        // Set up an interval to check for URL changes
        const urlCheckInterval = setInterval(checkForUrlChanges, 500);

        // Clean up after a reasonable time
        setTimeout(() => {
            clearInterval(urlCheckInterval);
            debugLog('URL change observer stopped after timeout');
        }, config.observerTimeout);

        // Also listen for popstate and hashchange events
        window.addEventListener('popstate', () => {
            debugLog('Popstate event detected');

            // Check if this is a profile URL
            if (window.location.href.includes('/feed/u/')) {
                debugLog('New URL after popstate is a profile URL, adding media tab');
                addMediaTabWithRetries();
            }
        });

        window.addEventListener('hashchange', () => {
            debugLog('Hashchange event detected');

            // Check if this is a profile URL
            if (window.location.href.includes('/feed/u/')) {
                debugLog('New URL after hashchange is a profile URL, adding media tab');
                addMediaTabWithRetries();
            }
        });
    }

    // Set up retry mechanism
    function setupRetryMechanism() {
        let retryCount = 0;

        function retry() {
            if (retryCount >= config.maxRetries) {
                debugLog('Max retries reached, giving up');
                return;
            }

            const interval = config.retryIntervals[retryCount] || config.retryIntervals[config.retryIntervals.length - 1];
            debugLog(`Scheduling retry #${retryCount + 1} in ${interval}ms`);

            setTimeout(() => {
                debugLog(`Executing retry #${retryCount + 1}`);
                addMediaTab();
                retryCount++;
                retry();
            }, interval);
        }

        retry();
    }

    // Set up mutation observer to watch for DOM changes
    function setupMutationObserver() {
        debugLog('Setting up mutation observer');

        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    // Check if any profile navigation elements were added
                    const profileNav = document.querySelector('.fcom_profile_nav');
                    if (profileNav) {
                        debugLog('Found profile navigation via mutation observer');
                        addMediaTab();
                    }
                }

                // Also check for data-route changes
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-route') {
                    const newRoute = document.body.getAttribute('data-route');
                    debugLog('data-route changed to:', newRoute);

                    if (newRoute && (
                        newRoute === 'user_profile' ||
                        newRoute === 'user_profile_feeds' ||
                        newRoute === 'user_spaces' ||
                        newRoute === 'user_comments' ||
                        newRoute === 'update_profile' ||
                        newRoute === 'user_notification_settings'
                    )) {
                        debugLog('New route is a profile route, adding media tab');
                        addMediaTab();
                    }
                }
            }
        });

        // Start observing the document body
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['data-route']
        });

        // Set a timeout to stop the observer after a reasonable time
        setTimeout(() => {
            observer.disconnect();
            debugLog('Mutation observer disconnected after timeout');
        }, config.observerTimeout);
    }

    // Set up route change listener
    function setupRouteChangeListener() {
        debugLog('Setting up route change listener');

        // Try to find the Vue router
        let vueRouter = null;

        // Method 1: FluentCommunityApp global
        if (window.FluentCommunityApp && window.FluentCommunityApp.$router) {
            vueRouter = window.FluentCommunityApp.$router;
            debugLog('Found Vue router in FluentCommunityApp');
        }
        // Method 2: fluentFrameworkApp global
        else if (window.fluentFrameworkApp && window.fluentFrameworkApp.$router) {
            vueRouter = window.fluentFrameworkApp.$router;
            debugLog('Found Vue router in fluentFrameworkApp');
        }
        // Method 3: Find the portal element and get its Vue instance
        else {
            const portalElement = document.getElementById('fluent_com_portal');
            if (portalElement && portalElement.__vue__ && portalElement.__vue__.$router) {
                vueRouter = portalElement.__vue__.$router;
                debugLog('Found Vue router in portal element');
            }
        }

        // Set up a URL change observer regardless of Vue router availability
        setupUrlChangeObserver();

        // Set up mutation observer to detect DOM changes
        setupDomMutationObserver();

        // Check if we're on a media tab URL directly
        if (window.location.pathname.includes('/u/') && window.location.pathname.includes('/media')) {
            debugLog('Direct navigation to media tab detected');

            // Extract username from URL
            const pathParts = window.location.pathname.split('/');
            const uIndex = pathParts.indexOf('u');

            if (uIndex >= 0 && uIndex + 1 < pathParts.length) {
                const username = pathParts[uIndex + 1];

                if (username) {
                    debugLog('Username extracted from URL:', username);

                    // Wait for DOM to be ready
                    setTimeout(() => {
                        // Add the media tab first
                        addMediaTabWithRetries();

                        // Find the profile holder element
                        const profileHolder = document.querySelector('.fcom_profile_holder') ||
                                             document.querySelector('.fcom_profile_content_wrap') ||
                                             document.querySelector('.fcom_profile_content');

                        if (profileHolder) {
                            debugLog('Found profile holder for direct media navigation');

                            // Update active tab
                            setTimeout(() => {
                                const mediaTab = document.querySelector('.fcom_profile_media');
                                if (mediaTab) {
                                    const allTabs = document.querySelectorAll('.fcom_profile_nav li');
                                    allTabs.forEach(tab => tab.classList.remove('active'));
                                    mediaTab.classList.add('active');

                                    // Show loading state
                                    profileHolder.innerHTML = `
                                        <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                                            <i class="el-icon-loading" style="font-size: 32px;"></i>
                                            <div style="margin-top: 10px;">Loading media...</div>
                                        </div>
                                    `;

                                    // Fetch media content
                                    fetchUserMedia(username, profileHolder);
                                } else {
                                    errorLog('Media tab not found for direct media navigation');
                                }
                            }, 300);
                        } else {
                            errorLog('Profile holder not found for direct media navigation');
                        }
                    }, 500);
                }
            }
        }

        if (vueRouter) {
            // Listen for route changes
            vueRouter.afterEach((to) => {
                debugLog('Route changed to:', to.name || to.path);

                // Check if this is a profile route
                if (to.name && (
                    to.name === 'user_profile' ||
                    to.name === 'user_profile_feeds' ||
                    to.name === 'user_spaces' ||
                    to.name === 'user_comments' ||
                    to.name === 'update_profile' ||
                    to.name === 'user_notification_settings' ||
                    to.name === 'user_profile_media'
                )) {
                    debugLog('New route is a profile route, adding media tab');
                    addMediaTabWithRetries();
                }
                // Also check path for /u/ pattern
                else if (to.path && to.path.includes('/u/')) {
                    debugLog('New route path contains /u/, adding media tab');
                    addMediaTabWithRetries();
                }
            });

            // Also check the current route immediately
            const currentRoute = vueRouter.currentRoute;
            if (currentRoute) {
                debugLog('Checking current route:', currentRoute.name || currentRoute.path);

                // Check if this is a profile route
                if (currentRoute.name && (
                    currentRoute.name === 'user_profile' ||
                    currentRoute.name === 'user_profile_feeds' ||
                    currentRoute.name === 'user_spaces' ||
                    currentRoute.name === 'user_comments' ||
                    currentRoute.name === 'update_profile' ||
                    currentRoute.name === 'user_notification_settings' ||
                    currentRoute.name === 'user_profile_media'
                )) {
                    debugLog('Current route is a profile route, adding media tab');
                    addMediaTabWithRetries();
                }
                // Also check path for /u/ pattern
                else if (currentRoute.path && currentRoute.path.includes('/u/')) {
                    debugLog('Current route path contains /u/, adding media tab');
                    addMediaTabWithRetries();
                }
            }

            // Try to register the media route
            try {
                // Check if the route already exists
                const existingRoute = vueRouter.getRoutes().find(route => route.name === 'user_profile_media');

                if (!existingRoute) {
                    // Define the component
                    const MediaGalleryComponent = {
                        template: `
                            <div class="fcom_profile_content mqcifce-media-page">
                                <div v-if="isLoading" class="fcom_loading_wrap">
                                    <i class="el-icon-loading"></i>
                                    <span>Loading media...</span>
                                </div>
                                <div v-else-if="error" class="fcom_alert fcom_alert_error">
                                    <i class="el-icon-warning"></i>
                                    <span>{{ error }}</span>
                                </div>
                                <div v-else>
                                    <div class="fcom_content_header">
                                        <h2>{{ username }}'s Media Gallery</h2>
                                        <div class="fcom_content_filters">
                                            <el-radio-group v-model="mediaType" size="small" @change="filterMedia">
                                                <el-radio-button label="all">All Media</el-radio-button>
                                                <el-radio-button label="images">Images</el-radio-button>
                                                <el-radio-button label="videos">Videos</el-radio-button>
                                            </el-radio-group>
                                        </div>
                                    </div>

                                    <div v-if="feeds.length === 0" class="fcom_empty_state">
                                        <i class="el-icon-picture"></i>
                                        <p>No media found</p>
                                    </div>

                                    <div v-else class="mqcifce-media-grid">
                                        <div
                                            v-for="feed in feeds"
                                            :key="feed.id"
                                            class="mqcifce-media-item"
                                            @click="openFeedModal(feed)"
                                        >
                                            <div v-if="getMediaType(feed) === 'video'" class="mqcifce-video-thumbnail">
                                                <img :src="getMediaThumbnail(feed)" :alt="feed.content || 'Video'">
                                                <div class="mqcifce-play-icon">
                                                    <i class="el-icon-video-play"></i>
                                                </div>
                                            </div>
                                            <img v-else :src="getMediaThumbnail(feed)" :alt="feed.content || 'Image'">
                                        </div>
                                    </div>

                                    <div v-if="pagination.currentPage < pagination.lastPage" class="fcom_load_more">
                                        <el-button
                                            :loading="loadingMore"
                                            @click="loadMore"
                                            type="primary"
                                            plain
                                        >
                                            Load More
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        `,
                        data() {
                            return {
                                isLoading: true,
                                loadingMore: false,
                                error: null,
                                feeds: [],
                                mediaType: 'all',
                                pagination: {
                                    currentPage: 1,
                                    lastPage: 1,
                                    perPage: 15,
                                    total: 0
                                }
                            };
                        },
                        computed: {
                            apiBaseUrl() {
                                // Try to get the REST API URL from global vars
                                if (window.mqcifceVars && window.mqcifceVars.restUrl) {
                                    return window.mqcifceVars.restUrl;
                                }
                                // Fallback to WordPress REST API
                                return '/wp-json/mqcifce/v1';
                            },
                            username() {
                                // Get username from multiple sources with fallbacks

                                // 1. First try route params (most reliable in Vue router)
                                if (this.$route && this.$route.params && this.$route.params.username) {
                                    debugLog('Username from route params:', this.$route.params.username);
                                    return this.$route.params.username;
                                }

                                // 2. Try from URL path
                                const urlParts = window.location.pathname.split('/');
                                const usernameIndex = urlParts.indexOf('u') + 1;
                                if (usernameIndex < urlParts.length) {
                                    debugLog('Username from URL path:', urlParts[usernameIndex]);
                                    return urlParts[usernameIndex];
                                }

                                // 3. Try from URL hash (for SPA navigation)
                                if (window.location.hash) {
                                    const hashParts = window.location.hash.split('/');
                                    const hashUsernameIndex = hashParts.indexOf('u') + 1;
                                    if (hashUsernameIndex < hashParts.length) {
                                        debugLog('Username from URL hash:', hashParts[hashUsernameIndex]);
                                        return hashParts[hashUsernameIndex];
                                    }
                                }

                                // 4. Try from DOM
                                const profileHeader = document.querySelector('.fcom_profile_header');
                                if (profileHeader) {
                                    // Try data attribute
                                    const dataUsername = profileHeader.getAttribute('data-username');
                                    if (dataUsername) {
                                        debugLog('Username from profile header data attribute:', dataUsername);
                                        return dataUsername;
                                    }

                                    // Try profile title
                                    const profileTitle = profileHeader.querySelector('.fcom_profile_title');
                                    if (profileTitle && profileTitle.textContent) {
                                        const titleUsername = profileTitle.textContent.trim();
                                        debugLog('Username from profile title:', titleUsername);
                                        return titleUsername;
                                    }
                                }

                                // 5. Last resort: try to find any profile links
                                const profileLinks = document.querySelectorAll('a[href*="/feed/u/"]');
                                for (const link of profileLinks) {
                                    const href = link.getAttribute('href');
                                    if (href) {
                                        const linkParts = href.split('/');
                                        const linkUsernameIndex = linkParts.indexOf('u') + 1;
                                        if (linkUsernameIndex < linkParts.length) {
                                            debugLog('Username from profile link:', linkParts[linkUsernameIndex]);
                                            return linkParts[linkUsernameIndex];
                                        }
                                    }
                                }

                                // If all else fails, return empty string
                                errorLog('Could not determine username from any source');
                                return '';
                            }
                        },
                        created() {
                            // Fetch media feeds if we have a username
                            if (this.username) {
                                this.fetchMediaFeeds();
                            } else {
                                this.error = 'Username not found';
                                this.isLoading = false;
                            }
                        },
                        mounted() {
                            debugLog('Media route component mounted');

                            // Initialize custom elements if needed
                            this.initializeCustomElements();
                        },
                        beforeRouteLeave(to, from, next) {
                            // Clean up when leaving the route
                            if (typeof next === 'function') {
                                next();
                            }
                        },
                        methods: {
                            initializeCustomElements() {
                                // Check if custom elements are defined
                                if (!customElements.get('mqcif-media-gallery')) {
                                    debugLog('Custom elements not defined, defining them now');
                                    // Define custom elements if they're not already defined
                                    if (typeof defineCustomElements === 'function') {
                                        defineCustomElements();
                                    } else if (window.MQCIFCE && typeof window.MQCIFCE.defineCustomElements === 'function') {
                                        window.MQCIFCE.defineCustomElements();
                                    }
                                }
                            },
                            fetchMediaFeeds(page = 1, append = false) {
                                // Set loading state
                                if (append) {
                                    this.loadingMore = true;
                                } else {
                                    this.isLoading = true;
                                    this.feeds = []; // Clear feeds for initial load
                                }

                                // Build API URL
                                const apiUrl = `${this.apiBaseUrl}/user-media-feeds/${this.username}?page=${page}&per_page=${this.pagination.perPage}&media_type=${this.mediaType}`;

                                // Get nonce from global vars
                                const nonce = window.mqcifceVars && window.mqcifceVars.nonce ? window.mqcifceVars.nonce : '';

                                // Fetch data
                                fetch(apiUrl, {
                                    headers: {
                                        'X-WP-Nonce': nonce
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP error! status: ${response.status}`);
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    // Update state with new data
                                    if (append) {
                                        this.feeds = [...this.feeds, ...data.feeds];
                                    } else {
                                        this.feeds = data.feeds;
                                    }

                                    // Update pagination
                                    this.pagination = {
                                        currentPage: data.meta.current_page,
                                        lastPage: data.meta.last_page,
                                        perPage: data.meta.per_page,
                                        total: data.meta.total
                                    };

                                    debugLog('Fetched media feeds:', this.feeds.length);
                                })
                                .catch(error => {
                                    errorLog('Error fetching media feeds:', error);
                                    this.error = `Error loading media: ${error.message}`;
                                })
                                .finally(() => {
                                    // Reset loading states
                                    this.isLoading = false;
                                    this.loadingMore = false;
                                });
                            },
                            filterMedia(mediaType) {
                                this.mediaType = mediaType;
                                this.fetchMediaFeeds(1, false);
                            },
                            loadMore() {
                                if (this.pagination.currentPage < this.pagination.lastPage && !this.loadingMore) {
                                    const nextPage = this.pagination.currentPage + 1;
                                    this.fetchMediaFeeds(nextPage, true);
                                }
                            },
                            openFeedModal(feed) {
                                debugLog('Opening feed modal for feed:', feed.id);

                                // Try to use Fluent Community's modal system
                                if (window.FluentCommunityApp && window.FluentCommunityApp.$store) {
                                    window.FluentCommunityApp.$store.commit('app/setFeedModal', {
                                        show: true,
                                        feed: feed
                                    });
                                    debugLog('Opened feed modal using Fluent Community store');
                                } else {
                                    // Fallback: Try to find and click the comment button for this feed
                                    const feedElement = document.querySelector(`.fcom_feed_item[data-id="${feed.id}"]`);
                                    if (feedElement) {
                                        const commentBtn = feedElement.querySelector('.fcom_comment_btn_wrap');
                                        if (commentBtn) {
                                            commentBtn.click();
                                            debugLog('Opened feed modal by clicking comment button');
                                        }
                                    } else {
                                        // Create a custom event to open the modal
                                        const event = new CustomEvent('mqcifce-open-feed-modal', {
                                            detail: { feed }
                                        });
                                        document.dispatchEvent(event);
                                        debugLog('Dispatched custom event to open feed modal');
                                    }
                                }
                            },
                            getMediaType(feed) {
                                // Check for attached media
                                if (feed.media && feed.media.length > 0) {
                                    const media = feed.media[0];
                                    return media.media_type.startsWith('image/') ? 'image' : 'video';
                                }

                                // Check for embedded media
                                if (feed.meta && feed.meta.media_preview) {
                                    return feed.meta.media_preview.type;
                                }

                                // Default to image
                                return 'image';
                            },
                            getMediaThumbnail(feed) {
                                // Check for attached media
                                if (feed.media && feed.media.length > 0) {
                                    return feed.media[0].public_url;
                                }

                                // Check for embedded media
                                if (feed.meta && feed.meta.media_preview) {
                                    return feed.meta.media_preview.thumbnail || feed.meta.media_preview.url;
                                }

                                // Default placeholder
                                return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItaW1hZ2UiPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgcng9IjIiIHJ5PSIyIj48L3JlY3Q+PGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjIxIDE1IDE2IDEwIDUgMjEiPjwvcG9seWxpbmU+PC9zdmc+';
                            }
                        }
                    };

                    // Add the route
                    vueRouter.addRoute({
                        path: '/u/:username/media',
                        name: 'user_profile_media',
                        component: MediaGalleryComponent,
                        meta: {
                            data_route: 'user_profile_media',
                            active: 'media'
                        }
                    });

                    debugLog('Media route added to Vue router');
                } else {
                    debugLog('Media route already exists in Vue router');
                }
            } catch (error) {
                errorLog('Error adding media route:', error);
            }
        } else {
            debugLog('Vue router not found, skipping route change listener');
        }
    }

    // Set up default route redirection
    function setupDefaultRouteRedirection() {
        debugLog('Setting up default route redirection');

        // Check if we're on the base user profile route
        const isBaseProfileRoute = () => {
            // Check URL pattern
            const path = window.location.pathname;
            const urlParts = path.split('/');

            // Check if this is a user profile URL without a specific tab
            // Pattern: /feed/u/username (without additional segments)
            if (urlParts.includes('u')) {
                const uIndex = urlParts.indexOf('u');
                // If there's a username after 'u' but no additional segments, it's a base profile URL
                return uIndex > 0 && uIndex + 1 < urlParts.length && uIndex + 2 >= urlParts.length;
            }
            return false;
        };

        // Check if we should skip redirection
        const shouldSkipRedirection = () => {
            // Skip if there's a 'no-redirect' parameter in the URL
            if (window.location.search.includes('no-redirect')) {
                return true;
            }

            // Skip if there's a hash fragment indicating a specific section
            if (window.location.hash) {
                return true;
            }

            // Skip if we have a session storage flag indicating the user wants to see the About tab
            if (sessionStorage.getItem('mqcifce-show-about-tab')) {
                // Clear the flag after using it
                sessionStorage.removeItem('mqcifce-show-about-tab');
                return true;
            }

            return false;
        };

        // If we're on the base profile route, redirect to the media tab
        if (isBaseProfileRoute() && !shouldSkipRedirection()) {
            debugLog('On base profile route, redirecting to media tab');

            // Extract username from URL
            const urlParts = window.location.pathname.split('/');
            const uIndex = urlParts.indexOf('u');

            if (uIndex >= 0 && uIndex + 1 < urlParts.length) {
                const username = urlParts[uIndex + 1];

                if (username) {
                    debugLog('Username extracted from URL:', username);

                    // Construct the media tab URL
                    const baseUrl = urlParts.slice(0, uIndex).join('/');
                    const mediaUrl = `${baseUrl}/u/${username}/media`;

                    // Update URL and trigger content update
                    try {
                        // Update URL
                        window.history.pushState({ path: mediaUrl }, '', mediaUrl);
                        debugLog('URL updated to:', mediaUrl);

                        // Wait for DOM to be ready
                        setTimeout(() => {
                            // Find the profile holder element
                            const profileHolder = document.querySelector('.fcom_profile_holder') ||
                                                document.querySelector('.fcom_profile_content_wrap') ||
                                                document.querySelector('.fcom_profile_content');

                            if (profileHolder) {
                                debugLog('Found profile holder for default route redirection');

                                // Update active tab
                                setTimeout(() => {
                                    const mediaTab = document.querySelector('.fcom_profile_media');
                                    if (mediaTab) {
                                        const allTabs = document.querySelectorAll('.fcom_profile_nav li');
                                        allTabs.forEach(tab => tab.classList.remove('active'));
                                        mediaTab.classList.add('active');

                                        // Show loading state
                                        profileHolder.innerHTML = `
                                            <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                                                <i class="el-icon-loading" style="font-size: 32px;"></i>
                                                <div style="margin-top: 10px;">Loading media...</div>
                                            </div>
                                        `;

                                        // Fetch media content
                                        fetchUserMedia(username, profileHolder);
                                    } else {
                                        errorLog('Media tab not found for default route redirection');
                                    }
                                }, 300);
                            } else {
                                errorLog('Profile holder not found for default route redirection');

                                // Set up a mutation observer to detect when the profile holder becomes available
                                setupProfileHolderObserver(username, mediaUrl);
                            }
                        }, 500);
                    } catch (e) {
                        errorLog('Error redirecting to media tab:', e);
                    }
                }
            }
        }
    }

    // Function to set up a mutation observer to detect when the profile navigation becomes available
    function setupProfileNavObserver() {
        debugLog('Setting up mutation observer for profile navigation');

        // Create a mutation observer to watch for changes to the DOM
        const observer = new MutationObserver((mutations) => {
            // Check if the profile navigation has been added
            const profileNav = document.querySelector('.fcom_profile_nav');

            if (profileNav) {
                debugLog('Profile navigation found via mutation observer');

                // Disconnect the observer since we found what we're looking for
                observer.disconnect();

                // Try to add the media tab
                setTimeout(() => {
                    addMediaTabWithRetries();
                }, 100);
            }
        });

        // Start observing the document with the configured parameters
        observer.observe(document.body, { childList: true, subtree: true });

        // Set a timeout to stop the observer after 15 seconds to prevent memory leaks
        setTimeout(() => {
            if (observer) {
                debugLog('Stopping profile navigation observer after timeout');
                observer.disconnect();
            }
        }, 15000);
    }

    // Function to set up a mutation observer to detect when the profile holder becomes available
    function setupProfileHolderObserver(username, mediaUrl) {
        debugLog('Setting up mutation observer for profile holder');

        // Create a mutation observer to watch for changes to the DOM
        const observer = new MutationObserver((mutations) => {
            // Check if the profile holder has been added
            const profileHolder = document.querySelector('.fcom_profile_holder') ||
                                 document.querySelector('.fcom_profile_content_wrap') ||
                                 document.querySelector('.fcom_profile_content');

            if (profileHolder) {
                debugLog('Profile holder found via mutation observer');

                // Disconnect the observer since we found what we're looking for
                observer.disconnect();

                // Update active tab
                setTimeout(() => {
                    // Try to add the media tab first if it doesn't exist
                    if (!document.querySelector('.fcom_profile_media')) {
                        debugLog('Media tab not found, attempting to add it');
                        addMediaTabWithRetries();
                    }

                    // Now try to find the media tab
                    const mediaTab = document.querySelector('.fcom_profile_media');
                    if (mediaTab) {
                        const allTabs = document.querySelectorAll('.fcom_profile_nav li');
                        allTabs.forEach(tab => tab.classList.remove('active'));
                        mediaTab.classList.add('active');

                        // Show loading state
                        profileHolder.innerHTML = `
                            <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                                <i class="el-icon-loading" style="font-size: 32px;"></i>
                                <div style="margin-top: 10px;">Loading media...</div>
                            </div>
                        `;

                        // Fetch media content
                        fetchUserMedia(username || extractUsernameFromUrl() || extractUsernameFromDom(), profileHolder);

                        debugLog('Media tab activated via mutation observer');
                    } else {
                        errorLog('Media tab not found after mutation observer detected profile holder');
                    }
                }, 300);
            }
        });

        // Start observing the document with the configured parameters
        observer.observe(document.body, { childList: true, subtree: true });

        // Set a timeout to stop the observer after 15 seconds to prevent memory leaks
        setTimeout(() => {
            if (observer) {
                debugLog('Stopping profile holder observer after timeout');
                observer.disconnect();
            }
        }, 15000);
    }

    // Set up custom event listener
    function setupCustomEventListener() {
        debugLog('Setting up custom event listener');

        // Set up default route redirection
        setupDefaultRouteRedirection();

        // Listen for custom event to show about tab
        document.addEventListener('mqcifce-show-about', (event) => {
            debugLog('Custom event received to show about tab:', event.detail);

            if (event.detail && event.detail.username) {
                // Find the main content area - try multiple selectors
                let contentArea = document.querySelector('.fcom_profile_content');
                if (!contentArea) {
                    contentArea = document.querySelector('.fhr_content_layout_body');
                }
                if (!contentArea) {
                    contentArea = document.querySelector('.fcom_profile_content_wrap');
                }
                if (!contentArea) {
                    contentArea = document.querySelector('.fcom_profile_wrap');
                }
                if (!contentArea) {
                    contentArea = document.querySelector('.fcom_main_container');
                }

                if (!contentArea) {
                    errorLog('Content area not found for about tab custom event');
                    return;
                }

                debugLog('Found content area for about tab custom event:', contentArea.className || contentArea.id);

                // Show loading state
                contentArea.innerHTML = `
                    <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                        <i class="el-icon-loading" style="font-size: 32px;"></i>
                        <div style="margin-top: 10px;">Loading about tab...</div>
                    </div>
                `;

                // Try to find the user profile data in the Fluent Community app
                let userData = null;

                if (window.FluentCommunityApp && window.FluentCommunityApp.$store) {
                    userData = window.FluentCommunityApp.$store.state.user.profile;
                    debugLog('Found user data in FluentCommunityApp store:', userData ? 'yes' : 'no');
                } else if (window.fluentFrameworkApp && window.fluentFrameworkApp.$store) {
                    userData = window.fluentFrameworkApp.$store.state.user.profile;
                    debugLog('Found user data in fluentFrameworkApp store:', userData ? 'yes' : 'no');
                }

                if (userData) {
                    // Render the about tab content using the user data
                    contentArea.innerHTML = `
                        <div class="fcom_profile_about_content">
                            <h2>About ${userData.display_name || event.detail.username}</h2>
                            <div class="fcom_profile_bio">
                                ${userData.bio || 'No bio available.'}
                            </div>
                        </div>
                    `;
                    debugLog('Rendered about tab content from user data');
                } else {
                    // If we can't find the user data, show a generic about tab
                    contentArea.innerHTML = `
                        <div class="fcom_profile_about_content">
                            <h2>About ${event.detail.username}</h2>
                            <div class="fcom_profile_bio">
                                <p>Loading user profile information...</p>
                            </div>
                        </div>
                    `;
                    debugLog('Rendered generic about tab content');

                    // Try to fetch the user profile data from the API
                    const apiUrl = `/wp-json/fluent-community/v2/users/${event.detail.username}`;

                    fetch(apiUrl, {
                        method: 'GET',
                        credentials: 'same-origin'
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.user) {
                            // Update the about tab content with the fetched data
                            contentArea.innerHTML = `
                                <div class="fcom_profile_about_content">
                                    <h2>About ${data.user.display_name || event.detail.username}</h2>
                                    <div class="fcom_profile_bio">
                                        ${data.user.bio || 'No bio available.'}
                                    </div>
                                </div>
                            `;
                            debugLog('Updated about tab content from API data');
                        }
                    })
                    .catch(error => {
                        errorLog('Error fetching user profile data:', error);
                    });
                }

                // Update active tab state
                const allTabs = document.querySelectorAll('.fcom_profile_nav li');
                allTabs.forEach(tab => tab.classList.remove('active'));

                const aboutTab = document.querySelector('.fcom_profile_about');
                if (aboutTab) {
                    aboutTab.classList.add('active');
                    debugLog('Updated active tab state for about tab');
                }
            }
        });

        // Listen for custom event to show media gallery
        document.addEventListener('mqcifce-show-media', (event) => {
            debugLog('Custom event received to show media gallery:', event.detail);

            if (event.detail && event.detail.username) {
                // Find the main content area
                let contentArea = document.querySelector('.fcom_profile_content');
                if (!contentArea) {
                    contentArea = document.querySelector('.fhr_content_layout_body');
                }
                if (!contentArea) {
                    contentArea = document.querySelector('.fcom_profile_content_wrap');
                }
                if (!contentArea) {
                    contentArea = document.querySelector('.fcom_profile_wrap');
                }

                if (!contentArea) {
                    errorLog('Content area not found');

                    // Try to create a content area as a last resort
                    const appElement = document.getElementById('fluent_com_portal');
                    if (appElement) {
                        contentArea = document.createElement('div');
                        contentArea.className = 'mqcifce-content-area';
                        contentArea.style.width = '100%';
                        contentArea.style.padding = '20px';
                        contentArea.style.margin = '20px auto';
                        contentArea.style.maxWidth = '1200px';
                        appElement.appendChild(contentArea);
                        debugLog('Created custom content area');
                    } else {
                        errorLog('Could not find or create a content area');
                        return;
                    }
                }

                debugLog('Found content area:', contentArea.className || contentArea.id);

                // Clear the content area
                contentArea.innerHTML = '';

                // Update the body data-route attribute
                document.body.setAttribute('data-route', 'user_profile_media');
                debugLog('Updated body data-route to user_profile_media');

                // Try to use Vue to create the component
                if (window.Vue) {
                    // Create a new Vue instance with the same component
                    const MediaGalleryComponent = {
                        template: `
                            <div class="fcom_profile_content mqcifce-media-page">
                                <div v-if="isLoading" class="fcom_loading_wrap">
                                    <i class="el-icon-loading"></i>
                                    <span>Loading media...</span>
                                </div>
                                <div v-else-if="error" class="fcom_alert fcom_alert_error">
                                    <i class="el-icon-warning"></i>
                                    <span>{{ error }}</span>
                                </div>
                                <div v-else>
                                    <div class="fcom_content_header">
                                        <h2>{{ username }}'s Media Gallery</h2>
                                        <div class="fcom_content_filters">
                                            <el-radio-group v-model="mediaType" size="small" @change="filterMedia">
                                                <el-radio-button label="all">All Media</el-radio-button>
                                                <el-radio-button label="images">Images</el-radio-button>
                                                <el-radio-button label="videos">Videos</el-radio-button>
                                            </el-radio-group>
                                        </div>
                                    </div>

                                    <div v-if="feeds.length === 0" class="fcom_empty_state">
                                        <i class="el-icon-picture"></i>
                                        <p>No media found</p>
                                    </div>

                                    <div v-else class="mqcifce-media-grid">
                                        <div
                                            v-for="feed in feeds"
                                            :key="feed.id"
                                            class="mqcifce-media-item"
                                            @click="openFeedModal(feed)"
                                        >
                                            <div v-if="getMediaType(feed) === 'video'" class="mqcifce-video-thumbnail">
                                                <img :src="getMediaThumbnail(feed)" :alt="feed.content || 'Video'">
                                                <div class="mqcifce-play-icon">
                                                    <i class="el-icon-video-play"></i>
                                                </div>
                                            </div>
                                            <img v-else :src="getMediaThumbnail(feed)" :alt="feed.content || 'Image'">
                                        </div>
                                    </div>

                                    <div v-if="pagination.currentPage < pagination.lastPage" class="fcom_load_more">
                                        <el-button
                                            :loading="loadingMore"
                                            @click="loadMore"
                                            type="primary"
                                            plain
                                        >
                                            Load More
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        `,
                        data() {
                            return {
                                isLoading: true,
                                loadingMore: false,
                                error: null,
                                feeds: [],
                                _username: event.detail.username, // Store as private property
                                mediaType: 'all',
                                pagination: {
                                    currentPage: 1,
                                    lastPage: 1,
                                    perPage: 15,
                                    total: 0
                                }
                            };
                        },
                        computed: {
                            username() {
                                return this._username || '';
                            },
                            apiBaseUrl() {
                                // Try to get the REST API URL from global vars
                                if (window.mqcifceVars && window.mqcifceVars.restUrl) {
                                    return window.mqcifceVars.restUrl;
                                }
                                // Fallback to WordPress REST API
                                return '/wp-json/mqcifce/v1';
                            }
                        },
                        mounted() {
                            debugLog('Media gallery component mounted via custom event');
                            this.fetchMediaFeeds();
                        },
                        methods: {
                            fetchMediaFeeds(page = 1, append = false) {
                                // Validate username first
                                if (!this.username) {
                                    this.error = 'Error: Username not found. Please try refreshing the page.';
                                    this.isLoading = false;
                                    this.loadingMore = false;
                                    errorLog('Cannot fetch media feeds: Username is empty');
                                    return;
                                }

                                // Set loading state
                                if (append) {
                                    this.loadingMore = true;
                                } else {
                                    this.isLoading = true;
                                    this.feeds = []; // Clear feeds for initial load
                                }

                                // Build API URL
                                const apiUrl = `${this.apiBaseUrl}/user-media-feeds/${this.username}?page=${page}&per_page=${this.pagination.perPage}&media_type=${this.mediaType}`;

                                // Get nonce from multiple sources with fallbacks
                                let nonce = '';

                                // Try from mqcifceVars (directly injected in the page)
                                if (window.mqcifceVars && window.mqcifceVars.nonce) {
                                    nonce = window.mqcifceVars.nonce;
                                    debugLog('Using nonce from mqcifceVars');
                                }
                                // Try from wpApiSettings
                                else if (window.wpApiSettings && window.wpApiSettings.nonce) {
                                    nonce = window.wpApiSettings.nonce;
                                    debugLog('Using nonce from wpApiSettings');
                                }
                                // Try from Fluent Community app
                                else if (window.FluentCommunityApp &&
                                         window.FluentCommunityApp.$store &&
                                         window.FluentCommunityApp.$store.state &&
                                         window.FluentCommunityApp.$store.state.app &&
                                         window.FluentCommunityApp.$store.state.app.nonce) {
                                    nonce = window.FluentCommunityApp.$store.state.app.nonce;
                                    debugLog('Using nonce from FluentCommunityApp');
                                }

                                // If no nonce is found, try to get it from the DOM
                                if (!nonce) {
                                    // Look for REST API nonce in meta tags
                                    const nonceMeta = document.querySelector('meta[name="rest-nonce"]');
                                    if (nonceMeta) {
                                        nonce = nonceMeta.getAttribute('content');
                                        debugLog('Using nonce from meta tag');
                                    }
                                }

                                // If still no nonce, check for _wpnonce in the URL (last resort)
                                if (!nonce) {
                                    const urlParams = new URLSearchParams(window.location.search);
                                    if (urlParams.has('_wpnonce')) {
                                        nonce = urlParams.get('_wpnonce');
                                        debugLog('Using nonce from URL parameter');
                                    }
                                }

                                // Prepare headers with content type and accept
                                const headers = {
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json'
                                };

                                // Add nonce if available
                                if (nonce) {
                                    headers['X-WP-Nonce'] = nonce;
                                    debugLog('Added nonce to request headers:', nonce.substring(0, 5) + '...');
                                } else {
                                    errorLog('No REST API nonce found, request may fail with 403 error');
                                }

                                // Log request details for debugging
                                debugLog('Fetching media feeds from:', apiUrl);
                                debugLog('With headers:', headers);
                                debugLog('Username:', this.username);

                                // Fetch data with credentials included and timeout
                                const controller = new AbortController();
                                const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

                                fetch(apiUrl, {
                                    method: 'GET',
                                    credentials: 'same-origin', // Include cookies in the request
                                    headers: headers,
                                    signal: controller.signal
                                })
                                .then(response => {
                                    // Clear the timeout to prevent abort after success
                                    clearTimeout(timeoutId);

                                    if (!response.ok) {
                                        // Handle specific HTTP status codes
                                        if (response.status === 403) {
                                            throw new Error('Permission denied. This could be due to authentication issues.');
                                        } else if (response.status === 404) {
                                            throw new Error('User profile or media not found.');
                                        } else if (response.status === 500) {
                                            throw new Error('Server error. Please try again later.');
                                        } else {
                                            throw new Error(`HTTP error! status: ${response.status}`);
                                        }
                                    }

                                    return response.json().catch(err => {
                                        throw new Error('Invalid response format. Please try again.');
                                    });
                                })
                                .then(data => {
                                    // Validate the response data
                                    if (!data || !data.feeds) {
                                        throw new Error('Invalid response data: missing feeds');
                                    }

                                    // Update state with new data
                                    if (append) {
                                        this.feeds = [...this.feeds, ...data.feeds];
                                    } else {
                                        this.feeds = data.feeds;
                                    }

                                    // Update pagination if meta data is available
                                    if (data.meta) {
                                        this.pagination = {
                                            currentPage: data.meta.current_page || 1,
                                            lastPage: data.meta.last_page || 1,
                                            perPage: data.meta.per_page || 15,
                                            total: data.meta.total || 0
                                        };
                                    }

                                    // Clear any previous errors
                                    this.error = null;

                                    debugLog('Fetched media feeds:', this.feeds.length);
                                })
                                .catch(error => {
                                    // Clear the timeout to prevent abort after error
                                    clearTimeout(timeoutId);

                                    errorLog('Error fetching media feeds:', error);

                                    // Provide a user-friendly error message
                                    if (error.name === 'AbortError') {
                                        this.error = 'Request timed out. Please try again.';
                                    } else {
                                        this.error = `Error loading media: ${error.message}`;
                                    }

                                    // If we have no feeds and this is not an append operation, show a retry button
                                    if (!append && this.feeds.length === 0) {
                                        this.showRetryButton = true;
                                    }
                                })
                                .finally(() => {
                                    // Reset loading states
                                    this.isLoading = false;
                                    this.loadingMore = false;
                                });
                            },
                            filterMedia(mediaType) {
                                this.mediaType = mediaType;
                                this.fetchMediaFeeds(1, false);
                            },
                            loadMore() {
                                if (this.pagination.currentPage < this.pagination.lastPage && !this.loadingMore) {
                                    const nextPage = this.pagination.currentPage + 1;
                                    this.fetchMediaFeeds(nextPage, true);
                                }
                            },
                            openFeedModal(feed) {
                                debugLog('Opening feed modal for feed:', feed.id);

                                // Try to use Fluent Community's modal system
                                if (window.FluentCommunityApp && window.FluentCommunityApp.$store) {
                                    window.FluentCommunityApp.$store.commit('app/setFeedModal', {
                                        show: true,
                                        feed: feed
                                    });
                                    debugLog('Opened feed modal using Fluent Community store');
                                } else {
                                    // Fallback: Try to find and click the comment button for this feed
                                    const feedElement = document.querySelector(`.fcom_feed_item[data-id="${feed.id}"]`);
                                    if (feedElement) {
                                        const commentBtn = feedElement.querySelector('.fcom_comment_btn_wrap');
                                        if (commentBtn) {
                                            commentBtn.click();
                                            debugLog('Opened feed modal by clicking comment button');
                                        }
                                    }
                                }
                            },
                            getMediaType(feed) {
                                // Check for attached media
                                if (feed.media && feed.media.length > 0) {
                                    const media = feed.media[0];
                                    return media.media_type.startsWith('image/') ? 'image' : 'video';
                                }

                                // Check for embedded media
                                if (feed.meta && feed.meta.media_preview) {
                                    return feed.meta.media_preview.type;
                                }

                                // Default to image
                                return 'image';
                            },
                            getMediaThumbnail(feed) {
                                // Check for attached media
                                if (feed.media && feed.media.length > 0) {
                                    return feed.media[0].public_url;
                                }

                                // Check for embedded media
                                if (feed.meta && feed.meta.media_preview) {
                                    return feed.meta.media_preview.thumbnail || feed.meta.media_preview.url;
                                }

                                // Default placeholder
                                return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItaW1hZ2UiPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgcng9IjIiIHJ5PSIyIj48L3JlY3Q+PGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjIxIDE1IDE2IDEwIDUgMjEiPjwvcG9seWxpbmU+PC9zdmc+';
                            }
                        }
                    };

                    // Create a container for the Vue app
                    const vueContainer = document.createElement('div');
                    vueContainer.id = 'mqcifce-media-gallery-container';
                    contentArea.appendChild(vueContainer);

                    // Create the Vue app
                    new window.Vue({
                        el: '#mqcifce-media-gallery-container',
                        render: h => h(MediaGalleryComponent)
                    });

                    debugLog('Media gallery added using Vue component');
                } else {
                    // Fallback to custom elements approach
                    debugLog('Vue not available, using custom elements approach');

                    // Make sure custom elements are defined
                    if (!customElements.get('mqcif-media-gallery')) {
                        debugLog('Custom elements not defined, defining them now');
                        // Define custom elements if they're not already defined
                        if (typeof defineCustomElements === 'function') {
                            defineCustomElements();
                        } else if (window.MQCIFCE && typeof window.MQCIFCE.defineCustomElements === 'function') {
                            window.MQCIFCE.defineCustomElements();
                        }
                    }

                    // Create and add the media gallery element
                    const mediaGallery = document.createElement('mqcif-media-gallery');
                    mediaGallery.setAttribute('username', event.detail.username);
                    contentArea.appendChild(mediaGallery);

                    debugLog('Media gallery added using custom elements');
                }

                debugLog('Media gallery added to content area');

                // Update URL without triggering router
                if (window.history && window.history.pushState) {
                    const newUrl = `/feed/u/${event.detail.username}/media`;
                    window.history.pushState({ path: newUrl }, '', newUrl);
                    debugLog('URL updated to:', newUrl);

                    // Dispatch a custom event to notify our code that we've changed the URL
                    const event = new CustomEvent('mqcifce-url-changed', {
                        detail: { url: newUrl, tab: 'media' }
                    });
                    document.dispatchEvent(event);
                }
            }
        });

        // Listen for URL changes
        document.addEventListener('mqcifce-url-changed', function(event) {
            debugLog('Custom event received for URL change', event.detail);

            if (event.detail && event.detail.url) {
                // Update the body data-route attribute if needed
                if (event.detail.tab === 'about') {
                    document.body.setAttribute('data-route', 'user_profile');
                    debugLog('Updated body data-route to user_profile');
                } else if (event.detail.tab === 'media') {
                    document.body.setAttribute('data-route', 'user_profile_media');
                    debugLog('Updated body data-route to user_profile_media');
                }
            }
        });
    }

    // Add the Media tab to the profile navigation
    function addMediaTab() {
        debugLog('Attempting to add Media tab');

        // Find the profile navigation
        const profileNav = document.querySelector('.fcom_profile_nav');
        if (!profileNav) {
            debugLog('Profile navigation not found');

            // Set up a mutation observer to detect when the profile navigation becomes available
            setupProfileNavObserver();
            return false;
        }

        // Check if the Media tab already exists
        if (document.querySelector('.fcom_profile_media')) {
            debugLog('Media tab already exists');

            // Make sure the About tab is still clickable
            ensureAboutTabWorks();

            return true;
        }

        // Function to ensure the About tab works correctly
        function ensureAboutTabWorks() {
            // Find the About tab
            const aboutTab = Array.from(profileNav.querySelectorAll('li')).find(li => {
                const link = li.querySelector('a');
                return link && (
                    link.textContent.trim().toLowerCase() === 'about' ||
                    li.classList.contains('fcom_profile_about')
                );
            });

            if (!aboutTab) {
                debugLog('About tab not found');
                return;
            }

            // Get the link element
            const aboutLink = aboutTab.querySelector('a');
            if (!aboutLink) {
                debugLog('About link not found');
                return;
            }

            // Check if the link already has a click handler
            if (aboutLink.getAttribute('data-mqcifce-about-handler')) {
                debugLog('About tab already has a click handler');
                return;
            }

            // Get the username
            let username = extractUsernameFromUrl();
            if (!username) {
                username = extractUsernameFromDom();
            }
            if (!username) {
                username = extractUsernameFromVueRouter();
            }
            if (!username) {
                debugLog('Username not found for About tab');
                return;
            }

            // Add click handler to the About tab
            aboutLink.addEventListener('click', (event) => {
                event.preventDefault();
                debugLog('About tab clicked');

                // Store the current state before changing anything
                const previousState = {
                    url: window.location.href,
                    bodyDataRoute: document.body.getAttribute('data-route')
                };
                debugLog('Previous state before about tab click:', previousState);

                // Store the original href
                const originalHref = aboutLink.getAttribute('href');

                // Find the profile holder element using the same robust approach as the Media tab
                const findProfileHolder = () => {
                    // Try all possible selectors in order of specificity
                    const selectors = [
                        '.fcom_profile_holder',
                        '.fcom_profile_content_wrap',
                        '.fcom_profile_content',
                        '.fcom_main_container .fcom_profile_content',
                        '.fcom_main_container .fcom_profile_content_wrap',
                        '.fcom_profile_page .fcom_profile_content',
                        '.fcom_profile_page .fcom_profile_content_wrap',
                        '.fcom_profile_page .fcom_profile_holder',
                        '.fcom_main_container',
                        '.fcom_profile_page'
                    ];

                    for (const selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            debugLog(`Found profile holder using selector: ${selector}`);
                            return element;
                        }
                    }

                    // If we still can't find it, try to find it relative to the profile nav
                    const profileNav = document.querySelector('.fcom_profile_nav');
                    if (profileNav) {
                        // Try to find the closest parent that might contain the content
                        let parent = profileNav.parentElement;
                        while (parent && !parent.classList.contains('fcom_main_container') && !parent.classList.contains('fcom_profile_page')) {
                            parent = parent.parentElement;
                        }

                        // If we found a suitable parent, look for content areas within it
                        if (parent) {
                            const contentArea = parent.querySelector('.fcom_profile_content') ||
                                               parent.querySelector('.fcom_profile_content_wrap') ||
                                               parent.querySelector('.fcom_profile_holder');

                            if (contentArea) {
                                debugLog('Found profile holder relative to profile nav');
                                return contentArea;
                            }
                        }
                    }

                    return null;
                };

                const profileHolder = findProfileHolder();

                if (!profileHolder) {
                    errorLog('Profile holder element not found for About tab');
                    // If we can't find the profile holder, just navigate to the original href
                    if (originalHref) {
                        window.location.href = originalHref;
                        debugLog('Navigated to About tab using original href as fallback');
                    }
                    return;
                }

                // Update active tab state
                const allTabs = document.querySelectorAll('.fcom_profile_nav li');
                allTabs.forEach(tab => tab.classList.remove('active'));
                aboutTab.classList.add('active');

                // Try to use the router if available
                let vueRouter = null;
                if (window.FluentCommunityApp && window.FluentCommunityApp.$router) {
                    vueRouter = window.FluentCommunityApp.$router;
                } else if (window.fluentFrameworkApp && window.fluentFrameworkApp.$router) {
                    vueRouter = window.fluentFrameworkApp.$router;
                }

                // Set a flag to prevent redirection back to media tab
                try {
                    sessionStorage.setItem('mqcifce-show-about-tab', 'true');
                    sessionStorage.setItem('mqcifce-last-tab', 'about');
                    debugLog('Set flag to prevent redirection to media tab');
                } catch (e) {
                    errorLog('Error setting session storage flag:', e);
                }

                // Show loading state in the profile holder
                profileHolder.innerHTML = `
                    <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                        <i class="el-icon-loading" style="font-size: 32px;"></i>
                        <div style="margin-top: 10px;">Loading about tab...</div>
                    </div>
                `;

                // Get the base URL (e.g., /feed or empty)
                const pathParts = window.location.pathname.split('/');
                const baseUrlIndex = pathParts.indexOf('u');
                const baseUrl = baseUrlIndex > 0 ? pathParts.slice(0, baseUrlIndex).join('/') : '';

                // Construct the new URL (without the media segment)
                const newUrl = `${baseUrl}/u/${username}`;

                // First update the URL to ensure proper state
                try {
                    window.history.pushState({ path: newUrl }, '', newUrl);
                    debugLog('URL updated to About tab:', newUrl);
                } catch (e) {
                    errorLog('Error updating URL for About tab:', e);
                }

                if (vueRouter) {
                    // Then use the router to navigate
                    try {
                        // Use a more reliable approach with route name and params
                        vueRouter.push({
                            name: 'user_profile',
                            params: { username: username }
                        }).catch(err => {
                            errorLog('Vue router navigation error:', err);

                            // If Vue router navigation fails, try a different approach
                            // Dispatch a custom event to notify our code that we want to show the About tab
                            const event = new CustomEvent('mqcifce-show-about', {
                                detail: { username: username }
                            });
                            document.dispatchEvent(event);
                            debugLog('Dispatched custom event to show About tab');

                            // As a last resort, reload the page
                            setTimeout(() => {
                                if (originalHref) {
                                    window.location.href = originalHref;
                                    debugLog('Navigated to About tab using original href as fallback');
                                } else {
                                    window.location.reload();
                                    debugLog('Reloaded page as last resort for About tab');
                                }
                            }, 500);
                        });
                        debugLog('Navigated to About tab using Vue router');
                    } catch (e) {
                        errorLog('Error navigating with Vue router:', e);

                        // If Vue router navigation fails, reload the page
                        setTimeout(() => {
                            if (originalHref) {
                                window.location.href = originalHref;
                                debugLog('Navigated to About tab using original href as fallback');
                            } else {
                                window.location.reload();
                                debugLog('Reloaded page as last resort for About tab');
                            }
                        }, 500);
                    }
                } else {
                    // If no Vue router is available, try to load the About tab content directly
                    // This is a more reliable approach than reloading the page

                    // Dispatch a custom event to notify our code that we want to show the About tab
                    const event = new CustomEvent('mqcifce-show-about', {
                        detail: { username: username }
                    });
                    document.dispatchEvent(event);
                    debugLog('Dispatched custom event to show About tab');

                    // As a last resort, reload the page after a short delay
                    setTimeout(() => {
                        if (originalHref) {
                            window.location.href = originalHref;
                            debugLog('Navigated to About tab using original href as fallback');
                        } else {
                            window.location.reload();
                            debugLog('Reloaded page as last resort for About tab');
                        }
                    }, 500);
                }
            });

            // Mark the link as having a handler
            aboutLink.setAttribute('data-mqcifce-about-handler', 'true');
            debugLog('Added click handler to About tab');
        }

        // Function to ensure all other tabs work correctly
        function ensureAllTabsWork(username) {
            debugLog('Ensuring all tabs work correctly');

            // Define the tab selectors and their corresponding route names
            const tabConfig = [
                { selector: '.fcom_profile_posts', routeName: 'user_profile_feeds', label: 'Feed' },
                { selector: '.fcom_profile_spaces', routeName: 'user_spaces', label: 'Tribes' },
                { selector: '.fcom_profile_comments', routeName: 'user_comments', label: 'Comments' }
            ];

            // Process each tab
            tabConfig.forEach(config => {
                const tab = Array.from(profileNav.querySelectorAll('li')).find(li => {
                    return li.classList.contains(config.selector.substring(1));
                });

                if (!tab) {
                    debugLog(`${config.label} tab not found, skipping`);
                    return;
                }

                const tabLink = tab.querySelector('a');
                if (!tabLink) {
                    debugLog(`${config.label} tab link not found, skipping`);
                    return;
                }

                // Check if we've already added a handler
                if (tabLink.getAttribute('data-mqcifce-tab-handler') === 'true') {
                    debugLog(`${config.label} tab already has a handler, skipping`);
                    return;
                }

                // Get the original href
                const originalHref = tabLink.getAttribute('href');

                // Add click handler to ensure it works
                tabLink.addEventListener('click', (event) => {
                    // Only handle if we're on the Media tab
                    const mediaTab = document.querySelector('.fcom_profile_media');
                    if (!mediaTab || !mediaTab.classList.contains('active')) {
                        debugLog(`Not on Media tab, letting default ${config.label} tab behavior work`);
                        return;
                    }

                    // Prevent default navigation
                    event.preventDefault();
                    debugLog(`${config.label} tab clicked while on Media tab, handling navigation`);

                    // Find the profile holder element using the same robust approach
                    const findProfileHolder = () => {
                        // Try all possible selectors in order of specificity
                        const selectors = [
                            '.fcom_profile_holder',
                            '.fcom_profile_content_wrap',
                            '.fcom_profile_content',
                            '.fcom_main_container .fcom_profile_content',
                            '.fcom_main_container .fcom_profile_content_wrap',
                            '.fcom_profile_page .fcom_profile_content',
                            '.fcom_profile_page .fcom_profile_content_wrap',
                            '.fcom_profile_page .fcom_profile_holder',
                            '.fcom_main_container',
                            '.fcom_profile_page'
                        ];

                        for (const selector of selectors) {
                            const element = document.querySelector(selector);
                            if (element) {
                                debugLog(`Found profile holder using selector: ${selector}`);
                                return element;
                            }
                        }

                        return null;
                    };

                    const profileHolder = findProfileHolder();

                    if (!profileHolder) {
                        errorLog(`Profile holder element not found for ${config.label} tab`);
                        // If we can't find the profile holder, just navigate to the original href
                        if (originalHref) {
                            window.location.href = originalHref;
                            debugLog(`Navigated to ${config.label} tab using original href as fallback`);
                        }
                        return;
                    }

                    // Update active tab immediately
                    const allTabs = document.querySelectorAll('.fcom_profile_nav li');
                    allTabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');

                    // Show loading state in the profile holder
                    profileHolder.innerHTML = `
                        <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                            <i class="el-icon-loading" style="font-size: 32px;"></i>
                            <div style="margin-top: 10px;">Loading ${config.label.toLowerCase()}...</div>
                        </div>
                    `;

                    // Try to find the Vue router
                    let vueRouter = null;
                    if (window.FluentCommunityApp && window.FluentCommunityApp.$router) {
                        vueRouter = window.FluentCommunityApp.$router;
                        debugLog('Found Vue router in FluentCommunityApp');
                    } else if (window.fluentFrameworkApp && window.fluentFrameworkApp.$router) {
                        vueRouter = window.fluentFrameworkApp.$router;
                        debugLog('Found Vue router in fluentFrameworkApp');
                    }

                    // Try to navigate using Vue router if available
                    if (vueRouter && config.routeName) {
                        try {
                            // Navigate to the tab
                            vueRouter.push({
                                name: config.routeName,
                                params: { username: username }
                            }).catch(err => {
                                errorLog(`Vue router navigation error for ${config.label} tab:`, err);

                                // Fallback to original href
                                if (originalHref) {
                                    window.location.href = originalHref;
                                    debugLog(`Navigated to ${config.label} tab using original href as fallback`);
                                }
                            });
                            debugLog(`Navigated to ${config.label} tab using Vue router`);
                        } catch (e) {
                            errorLog(`Error navigating to ${config.label} tab with Vue router:`, e);

                            // Fallback to original href
                            if (originalHref) {
                                window.location.href = originalHref;
                                debugLog(`Navigated to ${config.label} tab using original href as fallback`);
                            }
                        }
                    } else {
                        // If no Vue router is available or no route name, use the original href
                        if (originalHref) {
                            window.location.href = originalHref;
                            debugLog(`Navigated to ${config.label} tab using original href`);
                        }
                    }
                });

                // Mark the link as having a handler
                tabLink.setAttribute('data-mqcifce-tab-handler', 'true');
                debugLog(`Added click handler to ${config.label} tab`);
            });

            debugLog('All tabs should now work correctly');
        }


        // Try multiple methods to get the username
        let username = extractUsernameFromUrl();

        if (!username) {
            debugLog('Username not found in URL, trying alternative methods');
            username = extractUsernameFromDom();
        }

        if (!username) {
            debugLog('Username not found in DOM, trying Vue router');
            username = extractUsernameFromVueRouter();
        }

        if (!username) {
            debugLog('Username not found using any method');
            return false;
        }

        debugLog('Username found:', username);

        // Function to extract username from URL
        function extractUsernameFromUrl() {
            // Method 1: From pathname
            const urlParts = window.location.pathname.split('/');
            const usernameIndex = urlParts.indexOf('u') + 1;
            if (usernameIndex < urlParts.length) {
                return urlParts[usernameIndex];
            }

            // Method 2: From hash (for SPA navigation)
            if (window.location.hash) {
                const hashParts = window.location.hash.split('/');
                const hashUsernameIndex = hashParts.indexOf('u') + 1;
                if (hashUsernameIndex < hashParts.length) {
                    return hashParts[hashUsernameIndex];
                }
            }

            return null;
        }

        // Function to extract username from DOM
        function extractUsernameFromDom() {
            // Method 1: From profile header
            const profileHeader = document.querySelector('.fcom_profile_header');
            if (profileHeader) {
                // Try to get username from data attribute
                const username = profileHeader.getAttribute('data-username');
                if (username) {
                    return username;
                }

                // Try to get username from profile title
                const profileTitle = profileHeader.querySelector('.fcom_profile_title');
                if (profileTitle && profileTitle.textContent) {
                    return profileTitle.textContent.trim();
                }
            }

            // Method 2: From profile URL in the page
            const profileLinks = document.querySelectorAll('a[href*="/feed/u/"]');
            for (const link of profileLinks) {
                const href = link.getAttribute('href');
                if (href) {
                    const linkParts = href.split('/');
                    const linkUsernameIndex = linkParts.indexOf('u') + 1;
                    if (linkUsernameIndex < linkParts.length) {
                        return linkParts[linkUsernameIndex];
                    }
                }
            }

            return null;
        }

        // Function to extract username from Vue router
        function extractUsernameFromVueRouter() {
            // Try to find the Vue router
            let vueRouter = null;

            if (window.FluentCommunityApp && window.FluentCommunityApp.$router) {
                vueRouter = window.FluentCommunityApp.$router;
            } else if (window.fluentFrameworkApp && window.fluentFrameworkApp.$router) {
                vueRouter = window.fluentFrameworkApp.$router;
            }

            if (vueRouter && vueRouter.currentRoute) {
                // Get username from route params
                const params = vueRouter.currentRoute.params;
                if (params && params.username) {
                    return params.username;
                }
            }

            return null;
        }

        // Create the Media tab
        const mediaTab = document.createElement('li');
        mediaTab.className = 'fcom_profile_media';

        // Create the link
        const mediaLink = document.createElement('a');
        mediaLink.href = `/feed/u/${username}/media`;
        mediaLink.innerHTML = '<i class="el-icon-picture"></i> Media';

        // Try to find the Vue router
        let vueRouter = null;
        if (window.FluentCommunityApp && window.FluentCommunityApp.$router) {
            vueRouter = window.FluentCommunityApp.$router;
            debugLog('Found Vue router in FluentCommunityApp');
        } else if (window.fluentFrameworkApp && window.fluentFrameworkApp.$router) {
            vueRouter = window.fluentFrameworkApp.$router;
            debugLog('Found Vue router in fluentFrameworkApp');
        }

        // Add click handler
        mediaLink.addEventListener('click', (event) => {
            event.preventDefault();
            debugLog('Media tab clicked');

            // Store the current state before changing anything
            const previousState = {
                url: window.location.href,
                bodyDataRoute: document.body.getAttribute('data-route')
            };
            debugLog('Previous state before media tab click:', previousState);

            // Update active tab immediately
            const allTabs = document.querySelectorAll('.fcom_profile_nav li');
            allTabs.forEach(tab => tab.classList.remove('active'));
            mediaTab.classList.add('active');

            // Try multiple approaches to find the profile content area
            // This is more robust and handles different Fluent Community versions
            const findProfileHolder = () => {
                // Try all possible selectors in order of specificity
                const selectors = [
                    '.fcom_profile_holder',
                    '.fcom_profile_content_wrap',
                    '.fcom_profile_content',
                    '.fcom_main_container .fcom_profile_content',
                    '.fcom_main_container .fcom_profile_content_wrap',
                    '.fcom_profile_page .fcom_profile_content',
                    '.fcom_profile_page .fcom_profile_content_wrap',
                    '.fcom_profile_page .fcom_profile_holder',
                    '.fcom_main_container',
                    '.fcom_profile_page'
                ];

                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        debugLog(`Found profile holder using selector: ${selector}`);
                        return element;
                    }
                }

                // If we still can't find it, try to find it relative to the profile nav
                const profileNav = document.querySelector('.fcom_profile_nav');
                if (profileNav) {
                    // Try to find the closest parent that might contain the content
                    let parent = profileNav.parentElement;
                    while (parent && !parent.classList.contains('fcom_main_container') && !parent.classList.contains('fcom_profile_page')) {
                        parent = parent.parentElement;
                    }

                    // If we found a suitable parent, look for content areas within it
                    if (parent) {
                        const contentArea = parent.querySelector('.fcom_profile_content') ||
                                           parent.querySelector('.fcom_profile_content_wrap') ||
                                           parent.querySelector('.fcom_profile_holder');

                        if (contentArea) {
                            debugLog('Found profile holder relative to profile nav');
                            return contentArea;
                        }
                    }
                }

                return null;
            };

            // Find the profile holder element (this is where content should be updated)
            const profileHolder = findProfileHolder();

            if (!profileHolder) {
                errorLog('Profile holder element not found after exhaustive search');
                // Create a custom event to notify that we need to show media
                // This is a fallback approach that might be handled by other components
                const mediaEvent = new CustomEvent('mqcifce-show-media', {
                    detail: { username: username }
                });
                document.dispatchEvent(mediaEvent);
                debugLog('Dispatched custom event as fallback');
                return;
            }

            debugLog('Found profile holder:', profileHolder.className);

            // Show loading state in the profile holder
            profileHolder.innerHTML = `
                <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                    <i class="el-icon-loading" style="font-size: 32px;"></i>
                    <div style="margin-top: 10px;">Loading media...</div>
                </div>
            `;

            // Update URL without page refresh
            if (window.history && window.history.pushState) {
                // Get the base URL (e.g., /feed or empty)
                const pathParts = window.location.pathname.split('/');
                const baseUrlIndex = pathParts.indexOf('u');
                const baseUrl = baseUrlIndex > 0 ? pathParts.slice(0, baseUrlIndex).join('/') : '';

                // Construct the new URL
                const newUrl = `${baseUrl}/u/${username}/media`;

                try {
                    window.history.pushState({ path: newUrl }, '', newUrl);
                    debugLog('URL updated to:', newUrl);
                } catch (e) {
                    errorLog('Error updating URL:', e);
                }
            }

            // Fetch media content directly
            fetchUserMedia(username, profileHolder);
        });

        mediaTab.appendChild(mediaLink);
        profileNav.appendChild(mediaTab);

        // Make sure the About tab is still clickable after adding the Media tab
        ensureAboutTabWorks();

        // Make sure all other tabs work correctly
        ensureAllTabsWork(username);

        debugLog('Media tab added successfully');
        return true;
    }

    // Function to fetch and display user media directly
    function fetchUserMedia(username, container, retryCount = 0, mediaType = 'all') {
        debugLog('Fetching media for user:', username, 'Retry count:', retryCount, 'Media type:', mediaType);

        if (!username) {
            errorLog('Cannot fetch media: Username is empty');
            if (container && container.innerHTML) {
                container.innerHTML = `
                    <div class="fcom_alert fcom_alert_error">
                        <i class="el-icon-warning"></i>
                        <span>Error: Username not found. Please try refreshing the page.</span>
                    </div>
                `;
            }
            return;
        }

        // Make sure the container is valid
        if (!container || typeof container.innerHTML !== 'string') {
            errorLog('Cannot fetch media: Invalid container element');
            // Try to find a container as a fallback
            container = document.querySelector('.fcom_profile_content') ||
                        document.querySelector('.fcom_profile_holder') ||
                        document.querySelector('.fcom_profile_content_wrap') ||
                        document.querySelector('.fcom_main_container');

            if (!container) {
                errorLog('Cannot fetch media: No valid container found for fallback');
                return;
            }
            debugLog('Using fallback container:', container.className || container.id);
        }

        // Show loading state immediately with retry information if applicable
        container.innerHTML = `
            <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                <i class="el-icon-loading"></i>
                <div style="margin-top: 10px;">Loading media for ${username}${retryCount > 0 ? ` (Retry ${retryCount})` : ''}...</div>
            </div>
        `;

        // Get REST API base URL with multiple fallbacks
        let apiBaseUrl = '/wp-json/mqcifce/v1';

        if (window.mqcifceVars && window.mqcifceVars.restUrl) {
            apiBaseUrl = window.mqcifceVars.restUrl;
            debugLog('Using REST API URL from mqcifceVars');
        } else if (window.FluentCommunityVars && window.FluentCommunityVars.rest && window.FluentCommunityVars.rest.base) {
            apiBaseUrl = window.FluentCommunityVars.rest.base + '/mqcifce/v1';
            debugLog('Using REST API URL from FluentCommunityVars');
        } else if (window.fluentFrameworkVars && window.fluentFrameworkVars.rest && window.fluentFrameworkVars.rest.base) {
            apiBaseUrl = window.fluentFrameworkVars.rest.base + '/mqcifce/v1';
            debugLog('Using REST API URL from fluentFrameworkVars');
        } else if (window.wpApiSettings && window.wpApiSettings.root) {
            apiBaseUrl = window.wpApiSettings.root + 'mqcifce/v1';
            debugLog('Using REST API URL from wpApiSettings');
        }

        // Build API URL
        const apiUrl = `${apiBaseUrl}/user-media-feeds/${username}?page=1&per_page=15&media_type=all`;
        debugLog('API URL:', apiUrl);

        // Get nonce from multiple sources with fallbacks
        const nonce = getNonce();

        // Prepare headers with content type and accept
        const headers = {
            'Accept': 'application/json'
        };

        // Add nonce if available
        if (nonce) {
            headers['X-WP-Nonce'] = nonce;
            debugLog('Added nonce to request headers:', nonce.substring(0, 5) + '...');
        } else {
            // If no nonce is found, try to extract it from cookies as a last resort
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.indexOf('wp_rest_nonce=') === 0) {
                    const cookieNonce = cookie.substring('wp_rest_nonce='.length, cookie.length);
                    headers['X-WP-Nonce'] = cookieNonce;
                    debugLog('Added nonce from cookies to request headers:', cookieNonce.substring(0, 5) + '...');
                    break;
                }
            }

            if (!headers['X-WP-Nonce']) {
                errorLog('No REST API nonce found, attempting request without nonce');
            }
        }

        // Log request details for debugging
        debugLog('Fetching media feeds from:', apiUrl);
        debugLog('With headers:', headers);

        // Fetch data with credentials included and timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        fetch(apiUrl, {
            method: 'GET',
            credentials: 'same-origin', // Include cookies in the request
            headers: headers,
            signal: controller.signal
        })
        .then(response => {
            // Clear the timeout to prevent abort after success
            clearTimeout(timeoutId);

            if (!response.ok) {
                // Try to get more detailed error information
                return response.text().then(text => {
                    try {
                        // Try to parse as JSON for structured error
                        const errorData = JSON.parse(text);
                        debugLog('Error response data:', errorData);

                        // Handle specific HTTP status codes
                        if (response.status === 403) {
                            throw new Error('Permission denied. This could be due to authentication issues.');
                        } else if (response.status === 404) {
                            throw new Error('User profile or media not found.');
                        } else if (response.status === 500) {
                            throw new Error('Server error. Please try again later.');
                        } else {
                            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                        }
                    } catch (e) {
                        // If not JSON or other error, use the text or status
                        debugLog('Error parsing response:', e);

                        // Handle specific HTTP status codes
                        if (response.status === 403) {
                            throw new Error('Permission denied. This could be due to authentication issues.');
                        } else if (response.status === 404) {
                            throw new Error('User profile or media not found.');
                        } else if (response.status === 500) {
                            throw new Error('Server error. Please try again later.');
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}, Details: ${text || 'No details available'}`);
                        }
                    }
                });
            }

            return response.json().catch(err => {
                throw new Error('Invalid response format. Please try again.');
            });
        })
        .then(data => {
            // Validate the response data
            if (!data || !data.feeds) {
                throw new Error('Invalid response data: missing feeds');
            }

            const feeds = data.feeds;
            const pagination = {
                currentPage: data.meta.current_page || 1,
                lastPage: data.meta.last_page || 1,
                perPage: data.meta.per_page || 15,
                total: data.meta.total || 0
            };

            debugLog('Fetched media feeds:', feeds.length);

            // Render the media gallery
            renderMediaGallery(container, username, feeds, pagination);
        })
        .catch(error => {
            // Clear the timeout to prevent abort after error
            clearTimeout(timeoutId);

            errorLog('Error fetching media feeds:', error);

            // Handle AbortError separately
            if (error.name === 'AbortError') {
                errorLog('Fetch request aborted due to timeout');
                container.innerHTML = `
                    <div class="fcom_alert fcom_alert_error">
                        <i class="el-icon-warning"></i>
                        <span>Request timed out. Please try again later.</span>
                        <button class="el-button el-button--text" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.parentNode.parentNode, '${mediaType}')">
                            <i class="el-icon-refresh"></i> Retry
                        </button>
                    </div>
                `;
                return;
            }

            // Check if it's a 403 error (likely nonce issue)
            if (error.message && (error.message.includes('403') ||
                                 error.message.includes('Permission denied') ||
                                 error.message.includes('authentication'))) {
                errorLog('Authentication error detected, attempting to refresh nonce');

                // Try to refresh the nonce using Fluent Community's mechanism
                refreshNonceViaFluentCommunity((success, newNonce) => {
                    if (success) {
                        debugLog('Successfully refreshed nonce, retrying fetch');

                        // Show refreshing message
                        container.innerHTML = `
                            <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                                <i class="el-icon-loading"></i>
                                <div style="margin-top: 10px;">Refreshing connection...</div>
                            </div>
                        `;

                        // Retry the fetch with the new nonce after a short delay
                        setTimeout(() => {
                            fetchUserMedia(username, container, retryCount + 1, mediaType);
                        }, 500);
                    } else {
                        // Show error message with retry button if nonce refresh failed
                        container.innerHTML = `
                            <div class="fcom_alert fcom_alert_error">
                                <i class="el-icon-warning"></i>
                                <span>Authentication error. Please try again.</span>
                                <button class="el-button el-button--text" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.parentNode.parentNode, '${mediaType}')">
                                    <i class="el-icon-refresh"></i> Retry
                                </button>
                            </div>
                        `;
                    }
                });
                return;
            }

            // For server errors, implement exponential backoff retry
            if (error.message && error.message.includes('Server error') && retryCount < 3) {
                const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
                debugLog(`Server error, retrying in ${retryDelay}ms (retry ${retryCount + 1}/3)`);

                container.innerHTML = `
                    <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                        <i class="el-icon-loading"></i>
                        <div style="margin-top: 10px;">Server error, retrying in ${retryDelay/1000}s...</div>
                    </div>
                `;

                setTimeout(() => {
                    fetchUserMedia(username, container, retryCount + 1, mediaType);
                }, retryDelay);
                return;
            }

            // For other errors, show standard error message
            container.innerHTML = `
                <div class="fcom_alert fcom_alert_error">
                    <i class="el-icon-warning"></i>
                    <span>Error loading media: ${error.message}</span>
                    <button class="el-button el-button--text" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.parentNode.parentNode, '${mediaType}')">
                        <i class="el-icon-refresh"></i> Retry
                    </button>
                </div>
            `;
        });
    }

    // Function to render the media gallery
    function renderMediaGallery(container, username, feeds, pagination) {
        // Create the media gallery HTML
        let html = `
            <div class="fcom_profile_content mqcifce-media-page">
                <div class="fcom_content_header">
                    <h2>${username}'s Media Gallery</h2>
                    <div class="fcom_content_filters">
                        <div class="el-radio-group">
                            <label class="el-radio-button is-active">
                                <input type="radio" name="mediaType" value="all" checked>
                                <span>All Media</span>
                            </label>
                            <label class="el-radio-button">
                                <input type="radio" name="mediaType" value="images">
                                <span>Images</span>
                            </label>
                            <label class="el-radio-button">
                                <input type="radio" name="mediaType" value="videos">
                                <span>Videos</span>
                            </label>
                        </div>
                    </div>
                </div>
        `;

        if (feeds.length === 0) {
            html += `
                <div class="fcom_empty_state">
                    <i class="el-icon-picture"></i>
                    <p>No media found</p>
                </div>
            `;
        } else {
            html += '<div class="mqcifce-media-grid">';

            feeds.forEach(feed => {
                const mediaType = getMediaType(feed);
                const thumbnail = getMediaThumbnail(feed);

                if (mediaType === 'video') {
                    html += `
                        <div class="mqcifce-media-item" data-feed-id="${feed.id}">
                            <div class="mqcifce-video-thumbnail">
                                <img src="${thumbnail}" alt="${feed.content || 'Video'}">
                                <div class="mqcifce-play-icon">
                                    <i class="el-icon-video-play"></i>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="mqcifce-media-item" data-feed-id="${feed.id}">
                            <img src="${thumbnail}" alt="${feed.content || 'Image'}">
                        </div>
                    `;
                }
            });

            html += '</div>';

            // Add load more button if needed
            if (pagination.currentPage < pagination.lastPage) {
                html += `
                    <div class="fcom_load_more">
                        <button class="el-button el-button--primary is-plain"
                                data-username="${username}"
                                data-page="${pagination.currentPage + 1}"
                                onclick="window.MQCIFCE_DirectIntegration.loadMoreMedia(this)">
                            Load More
                        </button>
                    </div>
                `;
            }
        }

        html += '</div>';

        // Set the HTML
        container.innerHTML = html;

        // Add event listeners to media items
        const mediaItems = container.querySelectorAll('.mqcifce-media-item');
        mediaItems.forEach(item => {
            item.addEventListener('click', () => {
                const feedId = item.getAttribute('data-feed-id');
                const feed = feeds.find(f => f.id == feedId);

                if (feed) {
                    openFeedModal(feed);
                }
            });
        });

        // Add event listeners to filter buttons
        const filterButtons = container.querySelectorAll('input[name="mediaType"]');
        filterButtons.forEach(button => {
            button.addEventListener('change', (event) => {
                const mediaType = event.target.value;

                // Update active state
                filterButtons.forEach(btn => {
                    const label = btn.parentNode;
                    if (btn === event.target) {
                        label.classList.add('is-active');
                    } else {
                        label.classList.remove('is-active');
                    }
                });

                // Fetch media with the selected filter
                container.innerHTML = `
                    <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                        <i class="el-icon-loading" style="font-size: 32px;"></i>
                        <div style="margin-top: 10px;">Loading media...</div>
                    </div>
                `;

                fetchFilteredMedia(username, container, mediaType);
            });
        });
    }

    // Function to fetch filtered media
    function fetchFilteredMedia(username, container, mediaType, retryCount = 0) {
        debugLog('Fetching filtered media for user:', username, 'type:', mediaType, 'retry count:', retryCount);

        // Get REST API base URL with multiple fallbacks
        let apiBaseUrl = '/wp-json/mqcifce/v1';

        if (window.mqcifceVars && window.mqcifceVars.restUrl) {
            apiBaseUrl = window.mqcifceVars.restUrl;
            debugLog('Using REST API URL from mqcifceVars');
        } else if (window.FluentCommunityVars && window.FluentCommunityVars.rest && window.FluentCommunityVars.rest.base) {
            apiBaseUrl = window.FluentCommunityVars.rest.base + '/mqcifce/v1';
            debugLog('Using REST API URL from FluentCommunityVars');
        } else if (window.fluentFrameworkVars && window.fluentFrameworkVars.rest && window.fluentFrameworkVars.rest.base) {
            apiBaseUrl = window.fluentFrameworkVars.rest.base + '/mqcifce/v1';
            debugLog('Using REST API URL from fluentFrameworkVars');
        } else if (window.wpApiSettings && window.wpApiSettings.root) {
            apiBaseUrl = window.wpApiSettings.root + 'mqcifce/v1';
            debugLog('Using REST API URL from wpApiSettings');
        }

        // Build API URL
        const apiUrl = `${apiBaseUrl}/user-media-feeds/${username}?page=1&per_page=15&media_type=${mediaType}`;
        debugLog('API URL:', apiUrl);

        // Get nonce from multiple sources with fallbacks
        const nonce = getNonce();

        // Prepare headers with content type and accept
        const headers = {
            'Accept': 'application/json'
        };

        // Add nonce if available
        if (nonce) {
            headers['X-WP-Nonce'] = nonce;
            debugLog('Added nonce to request headers:', nonce.substring(0, 5) + '...');
        } else {
            // If no nonce is found, try to extract it from cookies as a last resort
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.indexOf('wp_rest_nonce=') === 0) {
                    const cookieNonce = cookie.substring('wp_rest_nonce='.length, cookie.length);
                    headers['X-WP-Nonce'] = cookieNonce;
                    debugLog('Added nonce from cookies to request headers:', cookieNonce.substring(0, 5) + '...');
                    break;
                }
            }

            if (!headers['X-WP-Nonce']) {
                errorLog('No REST API nonce found, attempting request without nonce');
            }
        }

        // Log request details for debugging
        debugLog('Fetching filtered media from:', apiUrl);
        debugLog('With headers:', headers);

        // Create an AbortController to handle request timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        // Fetch data with timeout
        fetch(apiUrl, {
            method: 'GET',
            credentials: 'same-origin',
            headers: headers,
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId); // Clear the timeout

            // Log response status for debugging
            debugLog('Response status:', response.status);

            if (!response.ok) {
                // Try to get more detailed error information
                return response.text().then(text => {
                    try {
                        // Try to parse as JSON for structured error
                        const errorData = JSON.parse(text);
                        throw new Error(`API Error (${response.status}): ${errorData.message || 'Unknown error'}`);
                    } catch (e) {
                        // If not JSON or other error, use the text or status
                        throw new Error(`HTTP error! Status: ${response.status}, Details: ${text || 'No details available'}`);
                    }
                });
            }
            return response.json();
        })
        .then(data => {
            debugLog('Filtered media data received:', data);

            const feeds = data.feeds;
            const pagination = {
                currentPage: data.meta.current_page || 1,
                lastPage: data.meta.last_page || 1,
                perPage: data.meta.per_page || 15,
                total: data.meta.total || 0
            };

            renderMediaGallery(container, username, feeds, pagination);
        })
        .catch(error => {
            clearTimeout(timeoutId); // Clear the timeout in case of error

            // Handle specific error types
            if (error.name === 'AbortError') {
                errorLog('Request timed out after 30 seconds');
                container.innerHTML = `
                    <div class="fcom_alert fcom_alert_error">
                        <i class="el-icon-warning"></i>
                        <span>Request timed out. Please try again later.</span>
                        <button class="el-button el-button--text" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.parentNode.parentNode, '${mediaType}')">
                            <i class="el-icon-refresh"></i> Retry
                        </button>
                    </div>
                `;
            }
            // Check if it's a 403 error (likely nonce issue)
            else if (error.message && (error.message.includes('403') ||
                                     error.message.includes('Permission denied') ||
                                     error.message.includes('authentication'))) {
                errorLog('Authentication error detected, attempting to refresh nonce');

                // Try to refresh the nonce using Fluent Community's mechanism
                refreshNonceViaFluentCommunity((success, newNonce) => {
                    if (success) {
                        debugLog('Successfully refreshed nonce, retrying fetch');

                        // Show refreshing message
                        container.innerHTML = `
                            <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                                <i class="el-icon-loading"></i>
                                <div style="margin-top: 10px;">Refreshing connection...</div>
                            </div>
                        `;

                        // Retry the fetch with the new nonce after a short delay
                        setTimeout(() => {
                            fetchFilteredMedia(username, container, mediaType, retryCount + 1);
                        }, 500);
                    } else {
                        // Show error message with retry button if nonce refresh failed
                        container.innerHTML = `
                            <div class="fcom_alert fcom_alert_error">
                                <i class="el-icon-warning"></i>
                                <span>Authentication error. Please try again.</span>
                                <button class="el-button el-button--text" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.parentNode.parentNode, '${mediaType}')">
                                    <i class="el-icon-refresh"></i> Retry
                                </button>
                            </div>
                        `;
                    }
                });
            }
            // For server errors, implement exponential backoff retry
            else if (error.message && error.message.includes('Server error') && retryCount < 3) {
                const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
                debugLog(`Server error, retrying in ${retryDelay}ms (retry ${retryCount + 1}/3)`);

                container.innerHTML = `
                    <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                        <i class="el-icon-loading"></i>
                        <div style="margin-top: 10px;">Server error, retrying in ${retryDelay/1000}s...</div>
                    </div>
                `;

                setTimeout(() => {
                    fetchFilteredMedia(username, container, mediaType, retryCount + 1);
                }, retryDelay);
            } else {
                errorLog('Error fetching filtered media:', error);

                // Show error message with retry button
                const errorMessage = `
                    <div class="fcom_alert fcom_alert_error">
                        <i class="el-icon-warning"></i>
                        <span>Error loading media: ${error.message}</span>
                        <button class="el-button el-button--text" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.parentNode.parentNode, '${mediaType}')">
                            <i class="el-icon-refresh"></i> Retry
                        </button>
                        <button class="el-button el-button--text" onclick="window.MQCIFCE_DirectIntegration.useFallbackGallery('${username}', this.parentNode.parentNode, '${mediaType}')">
                            <i class="el-icon-picture"></i> Show Basic Gallery
                        </button>
                    </div>
                `;

                // Add the error message to the container
                container.innerHTML = errorMessage;
            }
        });
    }

    // Function to load more media
    function loadMoreMedia(button) {
        const username = button.getAttribute('data-username');
        const page = parseInt(button.getAttribute('data-page'));
        const mediaType = document.querySelector('input[name="mediaType"]:checked').value;

        // Show loading state
        button.disabled = true;
        button.innerHTML = '<i class="el-icon-loading"></i> Loading...';

        // Get REST API base URL with multiple fallbacks
        let apiBaseUrl = '/wp-json/mqcifce/v1';

        if (window.mqcifceVars && window.mqcifceVars.restUrl) {
            apiBaseUrl = window.mqcifceVars.restUrl;
            debugLog('Using REST API URL from mqcifceVars');
        } else if (window.FluentCommunityVars && window.FluentCommunityVars.rest && window.FluentCommunityVars.rest.base) {
            apiBaseUrl = window.FluentCommunityVars.rest.base + '/mqcifce/v1';
            debugLog('Using REST API URL from FluentCommunityVars');
        } else if (window.fluentFrameworkVars && window.fluentFrameworkVars.rest && window.fluentFrameworkVars.rest.base) {
            apiBaseUrl = window.fluentFrameworkVars.rest.base + '/mqcifce/v1';
            debugLog('Using REST API URL from fluentFrameworkVars');
        } else if (window.wpApiSettings && window.wpApiSettings.root) {
            apiBaseUrl = window.wpApiSettings.root + 'mqcifce/v1';
            debugLog('Using REST API URL from wpApiSettings');
        }

        // Build API URL
        const apiUrl = `${apiBaseUrl}/user-media-feeds/${username}?page=${page}&per_page=15&media_type=${mediaType}`;
        debugLog('API URL for load more:', apiUrl);

        // Get nonce using the helper function
        const nonce = getNonce();

        // Prepare headers
        const headers = {
            'Accept': 'application/json'
        };

        // Add nonce if available
        if (nonce) {
            headers['X-WP-Nonce'] = nonce;
            debugLog('Added nonce to request headers:', nonce.substring(0, 5) + '...');
        } else {
            // If no nonce is found, try to extract it from cookies as a last resort
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.indexOf('wp_rest_nonce=') === 0) {
                    const cookieNonce = cookie.substring('wp_rest_nonce='.length, cookie.length);
                    headers['X-WP-Nonce'] = cookieNonce;
                    debugLog('Added nonce from cookies to request headers:', cookieNonce.substring(0, 5) + '...');
                    break;
                }
            }

            if (!headers['X-WP-Nonce']) {
                errorLog('No REST API nonce found, attempting request without nonce');
            }
        }

        // Create an AbortController to handle request timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        // Fetch data with timeout
        fetch(apiUrl, {
            method: 'GET',
            credentials: 'same-origin',
            headers: headers,
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId); // Clear the timeout

            // Log response status for debugging
            debugLog('Load more response status:', response.status);

            if (!response.ok) {
                // Try to get more detailed error information
                return response.text().then(text => {
                    try {
                        // Try to parse as JSON for structured error
                        const errorData = JSON.parse(text);
                        throw new Error(`API Error (${response.status}): ${errorData.message || 'Unknown error'}`);
                    } catch (e) {
                        // If not JSON or other error, use the text or status
                        throw new Error(`HTTP error! Status: ${response.status}, Details: ${text || 'No details available'}`);
                    }
                });
            }
            return response.json();
        })
        .then(data => {
            debugLog('Load more data received:', data);

            const feeds = data.feeds;
            const pagination = {
                currentPage: data.meta.current_page || 1,
                lastPage: data.meta.last_page || 1,
                perPage: data.meta.per_page || 15,
                total: data.meta.total || 0
            };

            // Get the media grid
            const mediaGrid = document.querySelector('.mqcifce-media-grid');

            if (mediaGrid) {
                // Add new items to the grid
                feeds.forEach(feed => {
                    const mediaType = getMediaType(feed);
                    const thumbnail = getMediaThumbnail(feed);

                    const mediaItem = document.createElement('div');
                    mediaItem.className = 'mqcifce-media-item';
                    mediaItem.setAttribute('data-feed-id', feed.id);

                    if (mediaType === 'video') {
                        mediaItem.innerHTML = `
                            <div class="mqcifce-video-thumbnail">
                                <img src="${thumbnail}" alt="${feed.content || 'Video'}">
                                <div class="mqcifce-play-icon">
                                    <i class="el-icon-video-play"></i>
                                </div>
                            </div>
                        `;
                    } else {
                        mediaItem.innerHTML = `
                            <img src="${thumbnail}" alt="${feed.content || 'Image'}">
                        `;
                    }

                    // Add click handler
                    mediaItem.addEventListener('click', () => {
                        openFeedModal(feed);
                    });

                    mediaGrid.appendChild(mediaItem);
                });

                // Update or remove the load more button
                const loadMoreContainer = document.querySelector('.fcom_load_more');

                if (loadMoreContainer) {
                    if (pagination.currentPage < pagination.lastPage) {
                        loadMoreContainer.innerHTML = `
                            <button class="el-button el-button--primary is-plain"
                                    data-username="${username}"
                                    data-page="${pagination.currentPage + 1}"
                                    onclick="window.MQCIFCE_DirectIntegration.loadMoreMedia(this)">
                                Load More
                            </button>
                        `;
                    } else {
                        loadMoreContainer.remove();
                    }
                }
            }
        })
        .catch(error => {
            clearTimeout(timeoutId); // Clear the timeout in case of error

            // Handle specific error types
            if (error.name === 'AbortError') {
                errorLog('Load more request timed out after 30 seconds');

                // Reset the button
                button.disabled = false;
                button.innerHTML = 'Load More';

                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'fcom_alert fcom_alert_error';
                errorMessage.style.marginTop = '10px';
                errorMessage.innerHTML = `
                    <i class="el-icon-warning"></i>
                    <span>Request timed out. Please try again.</span>
                `;

                const loadMoreContainer = document.querySelector('.fcom_load_more');
                if (loadMoreContainer) {
                    loadMoreContainer.appendChild(errorMessage);
                }
            }
            // Check if it's a 403 error (likely nonce issue)
            else if (error.message && (error.message.includes('403') ||
                                     error.message.includes('Permission denied') ||
                                     error.message.includes('authentication'))) {
                errorLog('Authentication error detected when loading more, attempting to refresh nonce');

                // Try to refresh the nonce using Fluent Community's mechanism
                refreshNonceViaFluentCommunity((success, newNonce) => {
                    if (success) {
                        debugLog('Successfully refreshed nonce, retrying load more');

                        // Update button text to show we're retrying
                        button.innerHTML = 'Retrying...';

                        // Retry the load more with the new nonce after a short delay
                        setTimeout(() => {
                            loadMoreMedia(button);
                        }, 500);
                    } else {
                        // Reset the button
                        button.disabled = false;
                        button.innerHTML = 'Load More';

                        // Show error message
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'fcom_alert fcom_alert_error';
                        errorMessage.style.marginTop = '10px';
                        errorMessage.innerHTML = `
                            <i class="el-icon-warning"></i>
                            <span>Authentication error. Please try again.</span>
                        `;

                        const loadMoreContainer = document.querySelector('.fcom_load_more');
                        if (loadMoreContainer) {
                            loadMoreContainer.appendChild(errorMessage);
                        }
                    }
                });
            } else {
                errorLog('Error loading more media:', error);

                // Reset the button
                button.disabled = false;
                button.innerHTML = 'Load More';

                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'fcom_alert fcom_alert_error';
                errorMessage.style.marginTop = '10px';
                errorMessage.innerHTML = `
                    <i class="el-icon-warning"></i>
                    <span>Error loading more media: ${error.message}</span>
                `;

                const loadMoreContainer = document.querySelector('.fcom_load_more');
                if (loadMoreContainer) {
                    loadMoreContainer.appendChild(errorMessage);
                }
            }
        });
    }

    // Helper function to get media type
    function getMediaType(feed) {
        // Check for attached media
        if (feed.media && feed.media.length > 0) {
            const media = feed.media[0];
            return media.media_type.startsWith('image/') ? 'image' : 'video';
        }

        // Check for embedded media
        if (feed.meta && feed.meta.media_preview) {
            return feed.meta.media_preview.type;
        }

        // Default to image
        return 'image';
    }

    // Helper function to get nonce from various sources
    function getNonce() {
        let nonce = '';
        let nonceSource = '';

        // Try Fluent Community's store first (most reliable source)
        if (window.FluentCommunityApp &&
            window.FluentCommunityApp.$store &&
            window.FluentCommunityApp.$store.state &&
            window.FluentCommunityApp.$store.state.app &&
            window.FluentCommunityApp.$store.state.app.nonce) {
            nonce = window.FluentCommunityApp.$store.state.app.nonce;
            nonceSource = 'FluentCommunityApp.$store';
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try from FluentCommunityVars
        if (window.FluentCommunityVars &&
            window.FluentCommunityVars.rest &&
            window.FluentCommunityVars.rest.nonce) {
            nonce = window.FluentCommunityVars.rest.nonce;
            nonceSource = 'FluentCommunityVars.rest';
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try from fluentFrameworkVars
        if (window.fluentFrameworkVars &&
            window.fluentFrameworkVars.rest &&
            window.fluentFrameworkVars.rest.nonce) {
            nonce = window.fluentFrameworkVars.rest.nonce;
            nonceSource = 'fluentFrameworkVars.rest';
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try from mqcifceVars (directly injected in the page)
        if (window.mqcifceVars && window.mqcifceVars.nonce) {
            nonce = window.mqcifceVars.nonce;
            nonceSource = 'mqcifceVars';
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try from wpApiSettings
        if (window.wpApiSettings && window.wpApiSettings.nonce) {
            nonce = window.wpApiSettings.nonce;
            nonceSource = 'wpApiSettings';
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Look for REST API nonce in meta tags
        const nonceMeta = document.querySelector('meta[name="rest-nonce"]');
        if (nonceMeta) {
            nonce = nonceMeta.getAttribute('content');
            nonceSource = 'meta tag';
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try to find nonce in the DOM (Fluent Community might inject it)
        const nonceElements = document.querySelectorAll('[data-nonce]');
        for (const element of nonceElements) {
            const dataNonce = element.getAttribute('data-nonce');
            if (dataNonce) {
                nonce = dataNonce;
                nonceSource = 'DOM data-nonce attribute';
                break;
            }
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try to find nonce in script tags
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
            const content = script.textContent;
            if (content && content.includes('nonce') && content.includes('rest')) {
                const match = content.match(/nonce['":\s]+([a-zA-Z0-9]+)/);
                if (match && match[1]) {
                    nonce = match[1];
                    nonceSource = 'script tag';
                    break;
                }
            }
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try to extract nonce from inline JSON
        const jsonScripts = document.querySelectorAll('script[type="application/json"]');
        for (const script of jsonScripts) {
            try {
                const content = script.textContent;
                if (content && content.includes('nonce')) {
                    const data = JSON.parse(content);
                    if (data && data.nonce) {
                        nonce = data.nonce;
                        nonceSource = 'JSON script';
                        break;
                    }
                }
            } catch (e) {
                // Ignore JSON parsing errors
            }
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // Try to extract nonce from cookies as a last resort
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.indexOf('wp_rest_nonce=') === 0) {
                nonce = cookie.substring('wp_rest_nonce='.length, cookie.length);
                nonceSource = 'cookie';
                break;
            }
        }

        // If we found a nonce, return it
        if (nonce) {
            debugLog(`Using nonce from ${nonceSource}: ${nonce.substring(0, 5)}...`);
            return nonce;
        }

        // No nonce found
        errorLog('No REST API nonce found from any source');
        return '';
    }

    // Function to refresh the nonce using Fluent Community's endpoint
    function refreshNonceViaFluentCommunity(callback) {
        // First try Fluent Community's endpoint
        if (window.FluentCommunityApp &&
            window.FluentCommunityApp.$store &&
            window.FluentCommunityVars &&
            window.FluentCommunityVars.ajaxUrl) {

            debugLog('Using Fluent Community nonce refresh mechanism');

            // Get the current ajax nonce
            const ajaxNonce = window.FluentCommunityApp.$store.state.app.ajax_nonce || '';

            // Make an AJAX request to Fluent Community's nonce refresh endpoint
            const xhr = new XMLHttpRequest();
            xhr.open('POST', window.FluentCommunityVars.ajaxUrl, true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.timeout = 10000; // 10 second timeout

            xhr.onload = () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response && response.rest_nonce) {
                            // Update the nonce in all possible locations
                            updateAllNonces(response.rest_nonce, response.ajax_nonce);

                            debugLog('Successfully refreshed nonce via Fluent Community');

                            if (typeof callback === 'function') {
                                callback(true, response.rest_nonce);
                            }

                            return;
                        }
                    } catch (e) {
                        errorLog('Error parsing Fluent Community nonce refresh response', e);
                    }
                }

                // Fall back to our own nonce refresh mechanism
                fallbackNonceRefresh(callback);
            };

            xhr.ontimeout = () => {
                errorLog('Timeout refreshing nonce via Fluent Community');
                fallbackNonceRefresh(callback);
            };

            xhr.onerror = () => {
                errorLog('Error refreshing nonce via Fluent Community');
                fallbackNonceRefresh(callback);
            };

            xhr.send('action=fluent_community_renew_nonce&ajax_nonce=' + encodeURIComponent(ajaxNonce));
            return true;
        } else {
            // Try our WordPress AJAX endpoint
            return fallbackNonceRefresh(callback);
        }
    }

    // Function to update all nonce locations
    function updateAllNonces(restNonce, ajaxNonce) {
        // Update our global variable
        if (window.mqcifceVars) {
            window.mqcifceVars.nonce = restNonce;
        }

        // Update Fluent Community's store
        if (window.FluentCommunityApp && window.FluentCommunityApp.$store) {
            if (window.FluentCommunityApp.$store.state.app) {
                window.FluentCommunityApp.$store.state.app.nonce = restNonce;

                if (ajaxNonce) {
                    window.FluentCommunityApp.$store.state.app.ajax_nonce = ajaxNonce;
                }
            }
        }

        // Update Fluent Community's vars
        if (window.FluentCommunityVars && window.FluentCommunityVars.rest) {
            window.FluentCommunityVars.rest.nonce = restNonce;
        }

        // Update Fluent Framework vars
        if (window.fluentFrameworkVars && window.fluentFrameworkVars.rest) {
            window.fluentFrameworkVars.rest.nonce = restNonce;
        }

        // Update WP API Settings
        if (window.wpApiSettings) {
            window.wpApiSettings.nonce = restNonce;
        }

        // Update the meta tag
        const metaTag = document.querySelector('meta[name="rest-nonce"]');
        if (metaTag) {
            metaTag.setAttribute('content', restNonce);
        }

        debugLog('Updated nonce in all possible locations');
    }

    // Fallback nonce refresh mechanism
    function fallbackNonceRefresh(callback) {
        debugLog('Using fallback nonce refresh mechanism');

        // Check if our refresh function exists
        if (typeof window.mqcifceRefreshNonce === 'function') {
            // Refresh the nonce
            window.mqcifceRefreshNonce();

            // Wait a moment for the nonce to be refreshed
            setTimeout(() => {
                debugLog('Retrying with fresh nonce from our refresh mechanism');

                // Get the fresh nonce
                const freshNonce = window.mqcifceVars?.nonce ||
                                  document.querySelector('meta[name="rest-nonce"]')?.content ||
                                  '';

                if (freshNonce) {
                    // Update all nonce locations
                    updateAllNonces(freshNonce);

                    if (typeof callback === 'function') {
                        callback(true, freshNonce);
                    }
                } else {
                    if (typeof callback === 'function') {
                        callback(false);
                    }
                }
            }, 500);

            return true;
        } else {
            errorLog('Nonce refresh function not available');

            // Try to get a fresh nonce from the meta tag
            const metaNonce = document.querySelector('meta[name="rest-nonce"]')?.content;
            if (metaNonce) {
                debugLog('Found nonce in meta tag');

                // Update all nonce locations
                updateAllNonces(metaNonce);

                if (typeof callback === 'function') {
                    callback(true, metaNonce);
                }

                return true;
            }

            if (typeof callback === 'function') {
                callback(false);
            }

            return false;
        }
    }

    // Helper function to get media thumbnail
    function getMediaThumbnail(feed) {
        // Check for attached media
        if (feed.media && feed.media.length > 0) {
            return feed.media[0].public_url;
        }

        // Check for embedded media
        if (feed.meta && feed.meta.media_preview) {
            return feed.meta.media_preview.thumbnail || feed.meta.media_preview.url;
        }

        // Default placeholder
        return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItaW1hZ2UiPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgcng9IjIiIHJ5PSIyIj48L3JlY3Q+PGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjIxIDE1IDE2IDEwIDUgMjEiPjwvcG9seWxpbmU+PC9zdmc+';
    }

    // Function to open feed modal
    function openFeedModal(feed) {
        debugLog('Opening feed modal for feed:', feed.id);

        // Try to use Fluent Community's modal system
        if (window.FluentCommunityApp && window.FluentCommunityApp.$store) {
            window.FluentCommunityApp.$store.commit('app/setFeedModal', {
                show: true,
                feed: feed
            });
            debugLog('Opened feed modal using Fluent Community store');
        } else {
            // Fallback: Try to find and click the comment button for this feed
            const feedElement = document.querySelector(`.fcom_feed_item[data-id="${feed.id}"]`);
            if (feedElement) {
                const commentBtn = feedElement.querySelector('.fcom_comment_btn_wrap');
                if (commentBtn) {
                    commentBtn.click();
                    debugLog('Opened feed modal by clicking comment button');
                }
            } else {
                // Create a custom event to open the modal
                const event = new CustomEvent('mqcifce-open-feed-modal', {
                    detail: { feed }
                });
                document.dispatchEvent(event);
                debugLog('Dispatched custom event to open feed modal');
            }
        }
    }

    // Initialize when the DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Also initialize when the Fluent Community app is loaded
    document.addEventListener('fluent_community/app_loaded', () => {
        debugLog('Fluent Community app loaded event detected');
        initialize();
    });

    // Function to use a fallback gallery when the API fails
    function useFallbackGallery(username, container, mediaType) {
        debugLog('Using fallback gallery for user:', username);

        // Create a basic gallery structure
        const galleryHTML = `
            <div class="mqcifce-media-container">
                <div class="mqcifce-media-filters">
                    <div class="mqcifce-filter-group">
                        <label>
                            <input type="radio" name="mediaType" value="all" ${mediaType === 'all' ? 'checked' : ''} onchange="window.MQCIFCE_DirectIntegration.filterMedia(this, '${username}')">
                            <span>All Media</span>
                        </label>
                        <label>
                            <input type="radio" name="mediaType" value="images" ${mediaType === 'images' ? 'checked' : ''} onchange="window.MQCIFCE_DirectIntegration.filterMedia(this, '${username}')">
                            <span>Images</span>
                        </label>
                        <label>
                            <input type="radio" name="mediaType" value="videos" ${mediaType === 'videos' ? 'checked' : ''} onchange="window.MQCIFCE_DirectIntegration.filterMedia(this, '${username}')">
                            <span>Videos</span>
                        </label>
                    </div>
                </div>

                <div class="mqcifce-media-grid">
                    <div class="mqcifce-fallback-message">
                        <i class="el-icon-picture-outline"></i>
                        <p>We're having trouble loading media from the server.</p>
                        <p>This is a basic gallery view. Some features may be limited.</p>
                        <button class="el-button el-button--primary" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.closest('.mqcifce-media-container').parentNode, '${mediaType}')">
                            <i class="el-icon-refresh"></i> Try Again
                        </button>
                    </div>
                </div>

                <div class="fcom_load_more" style="display: none;">
                    <button class="el-button el-button--primary is-plain"
                            data-username="${username}"
                            data-page="2"
                            onclick="window.MQCIFCE_DirectIntegration.loadMoreMedia(this)">
                        Load More
                    </button>
                </div>
            </div>
        `;

        // Add the gallery to the container
        container.innerHTML = galleryHTML;

        // Try to fetch user media from the profile page
        fetchMediaFromProfilePage(username, container);
    }

    // Function to fetch media from the profile page when API fails
    function fetchMediaFromProfilePage(username, container) {
        debugLog('Attempting to fetch media from profile page for user:', username);

        // Find all feed items on the page
        const feedItems = document.querySelectorAll('.fcom_feed_item');
        debugLog('Found', feedItems.length, 'feed items on the page');

        // Filter for feed items fom this user that have media
        const userMediaItems = [];

        feedItems.forEach(item => {
            // Check if this feed item is from the target user
            const feedUsername = item.querySelector('.fcom_feed_author_name')?.textContent.trim();

            if (feedUsername && feedUsername.toLowerCase() === username.toLowerCase()) {
                // Check if this feed item has media
                const mediaElements = item.querySelectorAll('.fcom_feed_media img, .fcom_feed_media video');

                if (mediaElements.length > 0) {
                    // Extract media information
                    const mediaUrls = [];
                    mediaElements.forEach(media => {
                        const mediaType = media.tagName.toLowerCase() === 'video' ? 'video' : 'image';
                        const mediaUrl = media.src || media.getAttribute('data-src');

                        if (mediaUrl) {
                            mediaUrls.push({
                                type: mediaType,
                                url: mediaUrl
                            });
                        }
                    });

                    if (mediaUrls.length > 0) {
                        userMediaItems.push({
                            id: item.getAttribute('data-id') || `fallback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                            content: item.querySelector('.fcom_feed_content')?.textContent.trim() || '',
                            media: mediaUrls
                        });
                    }
                }
            }
        });

        debugLog('Found', userMediaItems.length, 'media items for user:', username);

        // Add the media items to the gallery
        const mediaGrid = container.querySelector('.mqcifce-media-grid');

        if (mediaGrid && userMediaItems.length > 0) {
            // Clear the fallback message
            mediaGrid.innerHTML = '';

            // Add each media item to the grid
            userMediaItems.forEach(item => {
                item.media.forEach(media => {
                    const mediaItem = document.createElement('div');
                    mediaItem.className = 'mqcifce-media-item';
                    mediaItem.setAttribute('data-feed-id', item.id);

                    if (media.type === 'video') {
                        mediaItem.innerHTML = `
                            <div class="mqcifce-video-thumbnail">
                                <img src="${media.url}" alt="${item.content || 'Video'}">
                                <div class="mqcifce-play-icon">
                                    <i class="el-icon-video-play"></i>
                                </div>
                            </div>
                        `;
                    } else {
                        mediaItem.innerHTML = `
                            <img src="${media.url}" alt="${item.content || 'Image'}">
                        `;
                    }

                    // Add click handler
                    mediaItem.addEventListener('click', () => {
                        // Try to find the original feed item and click it
                        const feedItem = document.querySelector(`.fcom_feed_item[data-id="${item.id}"]`);
                        if (feedItem) {
                            feedItem.click();
                        } else {
                            // Fallback: open the image in a new tab
                            window.open(media.url, '_blank');
                        }
                    });

                    mediaGrid.appendChild(mediaItem);
                });
            });
        } else if (mediaGrid) {
            // No media items found, show a message
            mediaGrid.innerHTML = `
                <div class="mqcifce-fallback-message">
                    <i class="el-icon-picture-outline"></i>
                    <p>No media found for this user on the current page.</p>
                    <p>Try navigating to different pages to find more content.</p>
                    <button class="el-button el-button--primary" onclick="window.MQCIFCE_DirectIntegration.retryFetchMedia('${username}', this.closest('.mqcifce-media-container').parentNode, 'all')">
                        <i class="el-icon-refresh"></i> Try Again
                    </button>
                </div>
            `;
        }
    }

    // Make functions available globally for debugging and manual triggering
    window.MQCIFCE_DirectIntegration = {
        initialize,
        addMediaTab,
        isUserProfilePage,
        fetchUserMedia,
        loadMoreMedia: function(button) {
            loadMoreMedia(button);
        },
        filterMedia: function(input, username) {
            const mediaType = input.value;
            const container = input.closest('.mqcifce-media-container').parentNode;
            fetchFilteredMedia(username, container, mediaType);
        },
        retryFetchMedia: function(username, container, mediaType = 'all') {
            container.innerHTML = `
                <div class="fcom_loading_wrap" style="padding: 40px; text-align: center;">
                    <i class="el-icon-loading" style="font-size: 32px;"></i>
                    <div style="margin-top: 10px;">Loading media...</div>
                </div>
            `;

            // Reset retry count when manually retrying
            if (mediaType === 'all') {
                fetchUserMedia(username, container, 0, mediaType);
            } else {
                fetchFilteredMedia(username, container, mediaType, 0);
            }
        },
        useFallbackGallery: function(username, container, mediaType = 'all') {
            useFallbackGallery(username, container, mediaType);
        }
    };
})();
