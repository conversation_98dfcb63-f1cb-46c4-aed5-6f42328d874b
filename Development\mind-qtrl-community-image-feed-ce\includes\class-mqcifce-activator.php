<?php
/**
 * Fired during plugin activation.
 *
 * @since      0.1.0
 * @package    MQCIFCE
 * @subpackage MQCIFCE/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Class for handling plugin activation.
 */
class MQCIFCE_Activator {

    /**
     * Activate the plugin.
     *
     * @since    0.1.0
     */
    public static function activate() {
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Services\Helper')) {
            // Deactivate the plugin
            deactivate_plugins(plugin_basename(MQCIFCE_PLUGIN_FILE));

            // Set transient for admin notice
            set_transient('mqcifce_activation_error', true, 5);

            // Display an error message and stop execution
            wp_die(
                sprintf(
                    __('Error: Mind Qtrl | Community Image Feed CE requires Fluent Community to be installed and activated. <a href="%s">Return to Plugins page</a>.', 'mqcif-ce'),
                    admin_url('plugins.php')
                )
            );
        }
    }
}
