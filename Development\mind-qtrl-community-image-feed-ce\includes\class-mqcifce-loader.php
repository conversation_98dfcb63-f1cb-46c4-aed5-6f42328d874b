<?php
/**
 * The core plugin loader class.
 *
 * @since      0.1.0
 * @package    MQCIFCE
 * @subpackage MQCIFCE/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * The core plugin loader class.
 */
class MQCIFCE_Loader {

    /**
     * Initialize the plugin.
     */
    public function __construct() {
        // Load dependencies
        $this->load_dependencies();

        // Initialize components
        $this->init_components();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * @since    0.1.0
     * @access   private
     */
    private function load_dependencies() {
        // Frontend functionality
        require_once MQCIFCE_PLUGIN_DIR . 'frontend/class-mqcifce-frontend.php';

        // Admin functionality
        require_once MQCIFCE_PLUGIN_DIR . 'admin/class-mqcifce-admin.php';

        // REST API functionality
        require_once MQCIFCE_PLUGIN_DIR . 'rest-api/class-mqcifce-rest-api.php';
    }

    /**
     * Initialize all plugin components.
     */
    private function init_components() {
        // Initialize frontend
        if (class_exists('MQCIFCE_Frontend')) {
            $frontend = new MQCIFCE_Frontend();
        }

        // Initialize admin
        if (is_admin() && class_exists('MQCIFCE_Admin')) {
            $admin = new MQCIFCE_Admin();
        }

        // Initialize REST API
        if (class_exists('MQCIFCE_REST_API')) {
            $rest_api = new MQCIFCE_REST_API();
        }
    }

    /**
     * Run the plugin.
     */
    public function run() {
        // Nothing to do here for now
    }
}
