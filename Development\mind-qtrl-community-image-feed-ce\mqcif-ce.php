<?php
/**
 * Plugin Name: Mind Qtrl | Community Image Feed CE
 * Plugin URI: https://mindqtrl.com/plugins/mind-qtrl-community-image-feed-ce/
 * Description: Integrates with Fluent Community to add a user media profile tab and image feed using Custom Elements. Features 3-column grid layout, infinite scroll, YouTube video support, and sample data fallback.
 * Version: 0.3.4
 * Author: Mind Qtrl
 * Author URI: https://mindqtrl.com/
 * Text Domain: mqcif-ce
 * Domain Path: /languages
 * License: GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('MQCIFCE_VERSION', '0.3.4');
define('MQCIFCE_PLUGIN_FILE', __FILE__);
define('MQCIFCE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MQCIFCE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MQCIFCE_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Enable test mode for development environments
if (defined('WP_DEBUG') && WP_DEBUG) {
    define('MQCIFCE_TEST_MODE', true);
} else {
    define('MQCIFCE_TEST_MODE', false);
}

/**
 * The code that runs during plugin activation.
 */
if (!function_exists('mqcifce_activate')) {
    function mqcifce_activate() {
        require_once MQCIFCE_PLUGIN_DIR . 'includes/class-mqcifce-activator.php';
        MQCIFCE_Activator::activate();
    }
}

/**
 * The code that runs during plugin deactivation.
 */
if (!function_exists('mqcifce_deactivate')) {
    function mqcifce_deactivate() {
        require_once MQCIFCE_PLUGIN_DIR . 'includes/class-mqcifce-deactivator.php';
        MQCIFCE_Deactivator::deactivate();
    }
}

register_activation_hook(MQCIFCE_PLUGIN_FILE, 'mqcifce_activate');
register_deactivation_hook(MQCIFCE_PLUGIN_FILE, 'mqcifce_deactivate');

/**
 * Display admin notice if activation fails due to missing dependency
 */
if (!function_exists('mqcifce_admin_notice')) {
    function mqcifce_admin_notice() {
        // Check if our transient is set
        if (get_transient('mqcifce_activation_error')) {
            ?>
            <div class="notice notice-error is-dismissible">
                <p><?php _e('Error: Mind Qtrl | Community Image Feed CE requires Fluent Community to be installed and activated.', 'mqcif-ce'); ?></p>
            </div>
            <?php
            // Delete the transient
            delete_transient('mqcifce_activation_error');
        }
    }
}
add_action('admin_notices', 'mqcifce_admin_notice');

/**
 * Include required files
 */
// Core functionality
require_once MQCIFCE_PLUGIN_DIR . 'includes/class-mqcifce-loader.php';

/**
 * Initialize the plugin
 */
function run_mqcifce() {
    // Check if Fluent Community is active
    if (!defined('FLUENT_COMMUNITY_PLUGIN_VERSION')) {
        add_action('admin_notices', function() {
            echo '<div class="error"><p>Mind Qtrl | Community Image Feed CE requires Fluent Community plugin to be installed and activated.</p></div>';
        });
        return;
    }

    $mqcifce_loader = new MQCIFCE_Loader();
    $mqcifce_loader->run();
}

// Initialize the plugin on plugins_loaded to ensure Fluent Community is loaded
add_action('plugins_loaded', 'run_mqcifce');
