<?php
/**
 * The REST API functionality of the plugin.
 *
 * @since      0.1.0
 * @package    MQCIFCE
 * @subpackage MQCIFCE/rest-api
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * The REST API functionality class.
 */
class MQCIFCE_REST_API {

    /**
     * Initialize the class.
     */
    public function __construct() {
        // Register REST API routes
        add_action('rest_api_init', array($this, 'register_rest_routes'));
    }

    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        // Endpoint for direct media items
        register_rest_route('mqcifce/v1', '/user-media/(?P<username>[\w-]+)', [
            'methods' => 'GET',
            'callback' => array($this, 'get_user_media'),
            'permission_callback' => array($this, 'rest_permission_check'),
            'args' => [
                'username' => [
                    'validate_callback' => function($param, $request, $key) {
                        return is_string($param) && !empty(trim($param));
                    },
                    'sanitize_callback' => 'sanitize_user',
                    'required' => true
                ]
            ]
        ]);

        // Endpoint for feed items with media
        register_rest_route('mqcifce/v1', '/user-media-feeds/(?P<username>[\w-]+)', [
            'methods' => 'GET',
            'callback' => array($this, 'get_user_media_feeds_callback'),
            'permission_callback' => array($this, 'rest_permission_check'),
            'args' => [
                'username' => [
                    'validate_callback' => function($param, $request, $key) {
                        return is_string($param) && !empty(trim($param));
                    },
                    'sanitize_callback' => 'sanitize_user',
                    'required' => true
                ],
                'page' => [
                    'validate_callback' => 'is_numeric',
                    'sanitize_callback' => 'absint',
                    'default' => 1
                ],
                'per_page' => [
                    'validate_callback' => 'is_numeric',
                    'sanitize_callback' => 'absint',
                    'default' => 15
                ],
                'media_type' => [
                    'validate_callback' => function($param) {
                        return in_array($param, ['all', 'images', 'videos']);
                    },
                    'default' => 'all'
                ]
            ]
        ]);
    }

    /**
     * Permission check for REST API endpoints
     *
     * @param WP_REST_Request $request The request object
     * @return bool|WP_Error Whether the request has permission or error
     */
    public function rest_permission_check($request) {
        // Get the current route
        $route = $request->get_route();

        // For user media endpoints, we'll check if the profile is public
        if (strpos($route, '/user-media/') !== false || strpos($route, '/user-media-feeds/') !== false) {
            // Extract username from route
            $username = $request['username'];

            // Check if user exists
            $user = get_user_by('login', $username);
            if (!$user) {
                return new WP_Error(
                    'rest_user_not_found',
                    __('User not found.', 'mqcif-ce'),
                    ['status' => 404]
                );
            }

            // Check if Fluent Community is active and if we can determine profile visibility
            $is_public_profile = true; // Default to public

            if (class_exists('\FluentCommunity\App\Services\Helper') &&
                method_exists('\FluentCommunity\App\Services\Helper', 'isProfilePublic')) {
                $is_public_profile = \FluentCommunity\App\Services\Helper::isProfilePublic($user->ID);
            }

            // If profile is public, allow access
            if ($is_public_profile) {
                return true;
            }

            // If profile is not public, check if user is logged in
            if (!is_user_logged_in()) {
                return new WP_Error(
                    'rest_forbidden',
                    __('You must be logged in to view this profile.', 'mqcif-ce'),
                    ['status' => rest_authorization_required_code()]
                );
            }

            // If user is logged in, allow access
            // Note: Fluent Community will handle additional permission checks for private profiles
            return true;
        }

        // For all other endpoints, require authentication
        if (!is_user_logged_in()) {
            return new WP_Error(
                'rest_forbidden',
                __('You must be logged in to access this endpoint.', 'mqcif-ce'),
                ['status' => rest_authorization_required_code()]
            );
        }

        return true;
    }

    /**
     * Get user media items
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function get_user_media($request) {
        // Check if Fluent Community is active
        if (!class_exists('\FluentCommunity\App\Models\Media')) {
            return new WP_Error('fluent_community_inactive', 'Fluent Community is not active', ['status' => 500]);
        }

        $username = $request->get_param('username');

        // Get user ID from username
        $user = get_user_by('login', $username);
        if (!$user) {
            return new WP_Error('user_not_found', 'User not found', ['status' => 404]);
        }

        // Query media items using Fluent Community's Media model
        $media = \FluentCommunity\App\Models\Media::where('user_id', $user->ID)
            ->where('is_active', 1)
            ->orderBy('created_at', 'DESC')
            ->get();

        // Format the media items for the frontend
        $formatted_media = [];
        foreach ($media as $item) {
            $type = strpos($item->media_type, 'image') !== false ? 'image' : 'video';

            $formatted_media[] = [
                'id' => $item->id,
                'type' => $type,
                'url' => $item->public_url,
                'thumbnail' => $type === 'image' ? $item->public_url : '', // For videos, you might need a thumbnail
                'title' => $item->getFileTitle(),
                'created_at' => $item->created_at
            ];
        }

        // Apply filter to allow other plugins to modify the media items
        $formatted_media = apply_filters('mqcifce_user_media_items', $formatted_media, $user->ID);

        return rest_ensure_response($formatted_media);
    }

    /**
     * Get user feed items that contain media (images or embedded videos)
     *
     * @param WP_REST_Request $request The request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function get_user_media_feeds_callback($request) {
        try {
            // Get and validate parameters
            $username = $request['username'];
            $page = isset($request['page']) ? intval($request['page']) : 1;
            $per_page = isset($request['per_page']) ? min(intval($request['per_page']), 50) : 9; // Limit max per_page
            $media_type = isset($request['media_type']) ? $request['media_type'] : 'all';

            // Set a longer timeout for this request
            set_time_limit(60); // 60 seconds timeout

            // Debug information (only log if debug mode is enabled)
            $options = get_option('mqcifce_options', array());
            $debug_mode = isset($options['debug_mode']) && $options['debug_mode'] === '1';

            if ($debug_mode) {
                error_log('MQCIFCE API: Fetching media feeds for user: ' . $username);
                error_log('MQCIFCE API: Page: ' . $page . ', Per page: ' . $per_page . ', Media type: ' . $media_type);

                // Only log headers in debug mode
                error_log('MQCIFCE API: Request headers: ' . json_encode($request->get_headers()));
            }

            // Check nonce validity but don't log sensitive information
            $nonce = $request->get_header('X-WP-Nonce');
            $nonce_valid = false;

            if ($nonce) {
                $nonce_valid = wp_verify_nonce($nonce, 'wp_rest');

                // If nonce is invalid but user is logged in, proceed anyway
                if (!$nonce_valid && is_user_logged_in()) {
                    $nonce_valid = true; // Consider it valid if user is logged in
                }
            } else {
                // Check if user is logged in via cookie
                if (is_user_logged_in()) {
                    $nonce_valid = true; // Consider it valid if user is logged in
                }
            }

            // Get user by username
            $user = get_user_by('login', $username);
            if (!$user) {
                error_log('MQCIFCE API: User not found: ' . $username);
                return new WP_Error(
                    'rest_user_not_found',
                    __('User not found.', 'mqcif-ce'),
                    ['status' => 404]
                );
            }

            // Check if Feed model exists
            if (!class_exists('\FluentCommunity\App\Models\Feed')) {
                if ($debug_mode) {
                    error_log('MQCIFCE: Fluent Community Feed model not found, trying fallback method');
                }

                // Use fallback method to fetch media content directly from the database
                $fallback_feeds = $this->get_media_feeds_fallback($user->ID, $page, $per_page, $media_type);

                if (is_wp_error($fallback_feeds)) {
                    return $fallback_feeds;
                }

                return new WP_REST_Response($fallback_feeds, 200);
            }

            // If we're in a test environment, return sample data
            if (defined('MQCIFCE_TEST_MODE') && MQCIFCE_TEST_MODE) {
                return new WP_REST_Response([
                    'feeds' => $this->get_sample_media_feeds($user->ID),
                    'meta' => [
                        'total' => 10,
                        'per_page' => $per_page,
                        'current_page' => $page,
                        'last_page' => 1
                    ]
                ], 200);
            }

            // Start building the query
            try {
                $query = \FluentCommunity\App\Models\Feed::where('user_id', $user->ID)
                    ->where('is_active', 1);

                // Apply media type filter if specified
                if ($media_type === 'images' || $media_type === 'videos') {
                    try {
                        // First try with JSON_EXTRACT which works on MySQL 5.7+
                        if ($media_type === 'images') {
                            $query->where(function($q) {
                                $q->whereHas('media', function($mq) {
                                    $mq->where('media_type', 'like', 'image/%');
                                })
                                ->orWhereRaw("JSON_EXTRACT(meta, '$.media_preview.type') = 'image'");
                            });
                        } else { // videos
                            $query->where(function($q) {
                                $q->whereHas('media', function($mq) {
                                    $mq->where('media_type', 'like', 'video/%');
                                })
                                ->orWhereRaw("JSON_EXTRACT(meta, '$.media_preview.type') = 'video'");
                            });
                        }
                    } catch (\Exception $jsonExtractException) {
                        error_log('MQCIFCE: JSON_EXTRACT not supported for media type filter, using fallback method: ' . $jsonExtractException->getMessage());

                        // Fallback for databases that don't support JSON_EXTRACT
                        if ($media_type === 'images') {
                            $query->where(function($q) {
                                $q->whereHas('media', function($mq) {
                                    $mq->where('media_type', 'like', 'image/%');
                                })
                                ->orWhere('meta', 'LIKE', '%"type":"image"%'); // Simple LIKE query as fallback
                            });
                        } else { // videos
                            $query->where(function($q) {
                                $q->whereHas('media', function($mq) {
                                    $mq->where('media_type', 'like', 'video/%');
                                })
                                ->orWhere('meta', 'LIKE', '%"type":"video"%'); // Simple LIKE query as fallback
                            });
                        }
                    }
                }

                // Crucial Media Filter: Filter for posts that have attached media OR have a media preview in meta
                // Use a more robust approach to handle different database configurations
                try {
                    // First try with JSON_EXTRACT which works on MySQL 5.7+
                    $query->where(function($q) {
                        $q->has('media') // Check for related records in the media table
                          ->orWhereRaw("JSON_EXTRACT(meta, '$.media_preview') IS NOT NULL"); // Check for 'media_preview' key in the JSON meta column
                    });
                } catch (\Exception $jsonExtractException) {
                    error_log('MQCIFCE: JSON_EXTRACT not supported, using fallback method: ' . $jsonExtractException->getMessage());

                    // Fallback for databases that don't support JSON_EXTRACT
                    $query->where(function($q) {
                        $q->has('media') // Check for related records in the media table
                          ->orWhere('meta', 'LIKE', '%media_preview%'); // Simple LIKE query as fallback
                    });
                }

                // Eager load necessary relationships for performance and modal compatibility
                $query->with([
                    'xprofile' => function($q) {
                        // If ProfileHelper exists, use it to get public fields
                        if (class_exists('\FluentCommunity\App\Services\ProfileHelper')) {
                            $publicFields = \FluentCommunity\App\Services\ProfileHelper::getXProfilePublicFields();
                            $q->select(array_merge(['user_id'], $publicFields));
                        }
                    },
                    'media', // For attached images/videos
                    'reactions', // Needed for modal like/reaction counts and state
                    'comments' // Needed for modal comment counts
                    // Include 'space' if relevant
                ]);

                // Select specific columns if known to improve performance
                if (property_exists('\FluentCommunity\App\Models\Feed', 'publicColumns')) {
                    $query->select(\FluentCommunity\App\Models\Feed::$publicColumns);
                }

                // Order by most recent
                $query->orderBy('created_at', 'DESC');
            } catch (\Exception $queryException) {
                error_log('MQCIFCE: Error building query: ' . $queryException->getMessage());
                error_log('MQCIFCE: Exception trace: ' . $queryException->getTraceAsString());
                return new WP_Error(
                    'rest_query_error',
                    __('Error building query: ', 'mqcif-ce') . $queryException->getMessage(),
                    ['status' => 500]
                );
            }

            // Execute the query with pagination
            try {
                $paginatedFeeds = $query->paginate($per_page, ['*'], 'page', $page);
            } catch (\Exception $paginationException) {
                error_log('MQCIFCE: Error paginating results: ' . $paginationException->getMessage());
                error_log('MQCIFCE: Exception trace: ' . $paginationException->getTraceAsString());
                return new WP_Error(
                    'rest_pagination_error',
                    __('Error paginating results: ', 'mqcif-ce') . $paginationException->getMessage(),
                    ['status' => 500]
                );
            }

            // Check if FeedsHelper class exists
            if (!class_exists('\FluentCommunity\App\Services\FeedsHelper')) {
                error_log('MQCIFCE: Fluent Community FeedsHelper class not found');
                return new WP_Error(
                    'rest_fc_dependency_missing',
                    __('Fluent Community FeedsHelper class not found.', 'mqcif-ce'),
                    ['status' => 500]
                );
            }

            // Transform feeds for frontend
            $transformedFeeds = [];
            try {
                foreach ($paginatedFeeds->items() as $feed) {
                    // Use Fluent Community's helper to ensure correct structure for the frontend/modal
                    $transformedFeed = \FluentCommunity\App\Services\FeedsHelper::transformFeed($feed);

                    // Extract YouTube video ID if present
                    $youtube_video_id = $this->extract_youtube_id_from_activity($feed);
                    if ($youtube_video_id) {
                        $transformedFeed['youtube_video_id'] = $youtube_video_id;
                        $transformedFeed['youtube_thumbnail'] = $this->get_youtube_thumbnail_url($youtube_video_id);

                        // If no media preview exists, add one for YouTube
                        if (!isset($transformedFeed['meta']['media_preview'])) {
                            $transformedFeed['meta']['media_preview'] = [
                                'type' => 'video',
                                'url' => $transformedFeed['youtube_thumbnail'],
                                'youtube_id' => $youtube_video_id
                            ];
                        }
                    }

                    $transformedFeeds[] = $transformedFeed;
                }
            } catch (\Exception $transformException) {
                error_log('MQCIFCE: Error transforming feeds: ' . $transformException->getMessage());
                error_log('MQCIFCE: Exception trace: ' . $transformException->getTraceAsString());
                return new WP_Error(
                    'rest_transform_error',
                    __('Error transforming feeds: ', 'mqcif-ce') . $transformException->getMessage(),
                    ['status' => 500]
                );
            }

            // Apply filter to allow other plugins to modify the feeds
            $transformedFeeds = apply_filters('mqcifce_user_media_feeds', $transformedFeeds, $user->ID);

            // Construct the response data
            $response_data = [
                'feeds' => $transformedFeeds,
                'meta' => [
                    'total' => $paginatedFeeds->total(),
                    'per_page' => $paginatedFeeds->perPage(),
                    'current_page' => $paginatedFeeds->currentPage(),
                    'last_page' => $paginatedFeeds->lastPage(),
                ]
            ];

            error_log('MQCIFCE: Successfully fetched ' . count($transformedFeeds) . ' media feeds');
            return new WP_REST_Response($response_data, 200);

        } catch (\Exception $e) {
            error_log('MQCIFCE: Error in get_user_media_feeds_callback: ' . $e->getMessage());
            error_log('MQCIFCE: Exception trace: ' . $e->getTraceAsString());

            // Provide a more user-friendly error message
            $error_message = 'Server error while fetching media feeds. ';

            // Add more specific error information based on the exception
            if (strpos($e->getMessage(), 'database') !== false || strpos($e->getMessage(), 'SQL') !== false) {
                $error_message .= 'Database error occurred. ';
            } elseif (strpos($e->getMessage(), 'timeout') !== false) {
                $error_message .= 'Request timed out. ';
            } elseif (strpos($e->getMessage(), 'memory') !== false) {
                $error_message .= 'Server memory limit reached. ';
            }

            $error_message .= 'Please try again later.';

            // Try to use the fallback method if the error is related to Fluent Community models
            if (strpos($e->getMessage(), 'class') !== false ||
                strpos($e->getMessage(), 'undefined') !== false ||
                strpos($e->getMessage(), 'method') !== false) {

                error_log('MQCIFCE: Attempting fallback method after exception');

                try {
                    // Get user by username
                    $user = get_user_by('login', $username);
                    if ($user) {
                        $fallback_feeds = $this->get_media_feeds_fallback($user->ID, $page, $per_page, $media_type);

                        if (!is_wp_error($fallback_feeds)) {
                            error_log('MQCIFCE: Fallback method successful');
                            return new WP_REST_Response($fallback_feeds, 200);
                        }
                    }
                } catch (\Exception $fallback_e) {
                    error_log('MQCIFCE: Fallback method also failed: ' . $fallback_e->getMessage());
                }
            }

            return new WP_Error(
                'rest_api_error',
                $error_message,
                ['status' => 500, 'original_error' => $e->getMessage()]
            );
        }
    }

    /**
     * Fallback method to fetch media feeds directly from the database
     * This is used when Fluent Community models are not available
     *
     * @param int $user_id User ID
     * @param int $page Page number
     * @param int $per_page Items per page
     * @param string $media_type Media type filter (all, images, videos)
     * @return array|WP_Error Response data or error
     */
    private function get_media_feeds_fallback($user_id, $page, $per_page, $media_type) {
        global $wpdb;

        try {
            error_log('MQCIFCE: Using fallback method to fetch media feeds');
            error_log('MQCIFCE: User ID: ' . $user_id . ', Page: ' . $page . ', Per page: ' . $per_page . ', Media type: ' . $media_type);

            // Check if wpdb is available
            if (!$wpdb) {
                error_log('MQCIFCE: $wpdb global not available');
                return new WP_Error(
                    'rest_db_not_available',
                    __('Database connection not available.', 'mqcif-ce'),
                    ['status' => 500]
                );
            }

            // Get table names with prefixes
            $feeds_table = $wpdb->prefix . 'fcom_feeds';
            $media_table = $wpdb->prefix . 'fcom_media';
            $users_table = $wpdb->base_prefix . 'users';
            $xprofile_table = $wpdb->prefix . 'fcom_xprofiles';

            // Check if tables exist
            $feeds_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$feeds_table}'") === $feeds_table;
            $media_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$media_table}'") === $media_table;

            if (!$feeds_table_exists || !$media_table_exists) {
                error_log('MQCIFCE: Required tables not found: ' . (!$feeds_table_exists ? $feeds_table . ' ' : '') . (!$media_table_exists ? $media_table : ''));
                return new WP_Error(
                    'rest_tables_missing',
                    __('Required database tables not found.', 'mqcif-ce'),
                    ['status' => 500]
                );
            }

            // Calculate offset
            $offset = ($page - 1) * $per_page;

            // Base query to get feeds with media
            $base_query = "
                SELECT f.*, u.user_login, u.display_name, u.user_email, xp.avatar_url
                FROM {$feeds_table} f
                LEFT JOIN {$users_table} u ON f.user_id = u.ID
                LEFT JOIN {$xprofile_table} xp ON f.user_id = xp.user_id
                WHERE f.user_id = %d
                AND f.is_active = 1
                AND (
                    EXISTS (
                        SELECT 1 FROM {$media_table} m
                        WHERE m.feed_id = f.id
            ";

            // Add media type filter if specified
            if ($media_type === 'images') {
                $base_query .= " AND m.media_type LIKE 'image/%'";
            } elseif ($media_type === 'videos') {
                $base_query .= " AND m.media_type LIKE 'video/%'";
            }

            $base_query .= "
                    )
                    OR f.meta LIKE '%media_preview%'
                )
            ";

            // Add media type filter for embedded media
            if ($media_type === 'images') {
                $base_query .= " AND (EXISTS (SELECT 1 FROM {$media_table} m WHERE m.feed_id = f.id AND m.media_type LIKE 'image/%') OR f.meta LIKE '%\"type\":\"image\"%')";
            } elseif ($media_type === 'videos') {
                $base_query .= " AND (EXISTS (SELECT 1 FROM {$media_table} m WHERE m.feed_id = f.id AND m.media_type LIKE 'video/%') OR f.meta LIKE '%\"type\":\"video\"%')";
            }

            // Add order and limit
            $base_query .= "
                ORDER BY f.created_at DESC
                LIMIT %d OFFSET %d
            ";

            // Prepare and execute the query
            try {
                error_log('MQCIFCE: Preparing SQL query: ' . $base_query);
                $query = $wpdb->prepare($base_query, $user_id, $per_page, $offset);
                error_log('MQCIFCE: Executing SQL query: ' . $query);
                $feeds = $wpdb->get_results($query);

                if ($wpdb->last_error) {
                    error_log('MQCIFCE: Database error: ' . $wpdb->last_error);
                    return new WP_Error(
                        'rest_db_error',
                        __('Database error: ', 'mqcif-ce') . $wpdb->last_error,
                        ['status' => 500]
                    );
                }

                error_log('MQCIFCE: Query executed successfully, found ' . count($feeds) . ' feeds');
            } catch (\Exception $e) {
                error_log('MQCIFCE: Exception during query execution: ' . $e->getMessage());
                error_log('MQCIFCE: Exception trace: ' . $e->getTraceAsString());
                return new WP_Error(
                    'rest_query_exception',
                    __('Exception during query execution: ', 'mqcif-ce') . $e->getMessage(),
                    ['status' => 500]
                );
            }

            // Get total count for pagination
            $count_query = "
                SELECT COUNT(*)
                FROM {$feeds_table} f
                WHERE f.user_id = %d
                AND f.is_active = 1
                AND (
                    EXISTS (
                        SELECT 1 FROM {$media_table} m
                        WHERE m.feed_id = f.id
            ";

            // Add media type filter if specified
            if ($media_type === 'images') {
                $count_query .= " AND m.media_type LIKE 'image/%'";
            } elseif ($media_type === 'videos') {
                $count_query .= " AND m.media_type LIKE 'video/%'";
            }

            $count_query .= "
                    )
                    OR f.meta LIKE '%media_preview%'
                )
            ";

            // Add media type filter for embedded media
            if ($media_type === 'images') {
                $count_query .= " AND (EXISTS (SELECT 1 FROM {$media_table} m WHERE m.feed_id = f.id AND m.media_type LIKE 'image/%') OR f.meta LIKE '%\"type\":\"image\"%')";
            } elseif ($media_type === 'videos') {
                $count_query .= " AND (EXISTS (SELECT 1 FROM {$media_table} m WHERE m.feed_id = f.id AND m.media_type LIKE 'video/%') OR f.meta LIKE '%\"type\":\"video\"%')";
            }

            try {
                error_log('MQCIFCE: Preparing count SQL query: ' . $count_query);
                $prepared_count_query = $wpdb->prepare($count_query, $user_id);
                error_log('MQCIFCE: Executing count SQL query: ' . $prepared_count_query);
                $total = $wpdb->get_var($prepared_count_query);

                if ($wpdb->last_error) {
                    error_log('MQCIFCE: Database error in count query: ' . $wpdb->last_error);
                    return new WP_Error(
                        'rest_db_error',
                        __('Database error in count query: ', 'mqcif-ce') . $wpdb->last_error,
                        ['status' => 500]
                    );
                }

                error_log('MQCIFCE: Count query executed successfully, total: ' . $total);
            } catch (\Exception $e) {
                error_log('MQCIFCE: Exception during count query execution: ' . $e->getMessage());
                error_log('MQCIFCE: Exception trace: ' . $e->getTraceAsString());
                return new WP_Error(
                    'rest_count_query_exception',
                    __('Exception during count query execution: ', 'mqcif-ce') . $e->getMessage(),
                    ['status' => 500]
                );
            }

            // Get media for each feed
            $transformed_feeds = [];
            foreach ($feeds as $feed) {
                // Get media for this feed
                try {
                    $media_query = $wpdb->prepare(
                        "SELECT * FROM {$media_table} WHERE feed_id = %d",
                        $feed->id
                    );
                    error_log('MQCIFCE: Executing media query for feed ID ' . $feed->id . ': ' . $media_query);
                    $media = $wpdb->get_results($media_query);

                    if ($wpdb->last_error) {
                        error_log('MQCIFCE: Database error in media query: ' . $wpdb->last_error);
                        // Continue with empty media array instead of failing the whole request
                        $media = [];
                    } else {
                        error_log('MQCIFCE: Found ' . count($media) . ' media items for feed ID ' . $feed->id);
                    }
                } catch (\Exception $e) {
                    error_log('MQCIFCE: Exception during media query execution: ' . $e->getMessage());
                    // Continue with empty media array instead of failing the whole request
                    $media = [];
                }

                // Extract YouTube video ID if present
                $youtube_video_id = null;
                if (isset($feed->content)) {
                    $youtube_video_id = $this->extract_youtube_id_from_activity($feed);
                }

                // Transform feed to match the expected format
                $transformed_feed = $this->transform_feed_fallback($feed, $media, $youtube_video_id);
                $transformed_feeds[] = $transformed_feed;
            }

            // Construct the response data
            $response_data = [
                'feeds' => $transformed_feeds,
                'meta' => [
                    'total' => (int) $total,
                    'per_page' => (int) $per_page,
                    'current_page' => (int) $page,
                    'last_page' => ceil($total / $per_page),
                ]
            ];

            error_log('MQCIFCE: Successfully fetched ' . count($transformed_feeds) . ' media feeds using fallback method');
            return $response_data;

        } catch (\Exception $e) {
            error_log('MQCIFCE: Error in fallback method: ' . $e->getMessage());
            error_log('MQCIFCE: Exception trace: ' . $e->getTraceAsString());
            return new WP_Error(
                'rest_fallback_error',
                $e->getMessage(),
                ['status' => 500]
            );
        }
    }

    /**
     * Transform a feed object to match the expected format for the frontend
     * This is used by the fallback method
     *
     * @param object $feed Feed object from the database
     * @param array $media Media objects for this feed
     * @param string|null $youtube_video_id YouTube video ID if present
     * @return array Transformed feed
     */
    private function transform_feed_fallback($feed, $media, $youtube_video_id = null) {
        // Parse meta JSON if it exists
        $meta = !empty($feed->meta) ? json_decode($feed->meta, true) : [];

        // Basic feed data
        $transformed = [
            'id' => $feed->id,
            'user_id' => $feed->user_id,
            'content' => $feed->content,
            'created_at' => $feed->created_at,
            'updated_at' => $feed->updated_at,
            'is_active' => (bool) $feed->is_active,
            'meta' => $meta,
            'xprofile' => [
                'user_id' => $feed->user_id,
                'display_name' => $feed->display_name,
                'user_login' => $feed->user_login,
                'avatar_url' => $feed->avatar_url,
                'email' => $feed->user_email,
            ],
            'media' => [],
            'reactions' => [],
            'comments' => [],
        ];

        // Add media
        foreach ($media as $media_item) {
            $transformed['media'][] = [
                'id' => $media_item->id,
                'feed_id' => $media_item->feed_id,
                'media_type' => $media_item->media_type,
                'file_name' => $media_item->file_name,
                'file_path' => $media_item->file_path,
                'public_url' => $media_item->public_url,
                'created_at' => $media_item->created_at,
                'updated_at' => $media_item->updated_at,
            ];
        }

        // Add YouTube video ID and thumbnail if present
        if ($youtube_video_id) {
            $transformed['youtube_video_id'] = $youtube_video_id;
            $transformed['youtube_thumbnail'] = $this->get_youtube_thumbnail_url($youtube_video_id);

            // If no media preview exists, add one for YouTube
            if (!isset($transformed['meta']['media_preview'])) {
                $transformed['meta']['media_preview'] = [
                    'type' => 'video',
                    'url' => $transformed['youtube_thumbnail'],
                    'youtube_id' => $youtube_video_id
                ];
            }
        }

        return $transformed;
    }

    /**
     * Extract YouTube video ID from activity content or oEmbed meta
     *
     * @param object $activity Activity object
     * @return string|null YouTube video ID or null if not found
     */
    private function extract_youtube_id_from_activity($activity) {
        // Check if we have content
        $content = isset($activity->content) ? $activity->content : '';

        // If no content but we have a feed relation, check feed content
        if (empty($content) && isset($activity->feed) && isset($activity->feed->content)) {
            $content = $activity->feed->content;
        }

        // Check for oEmbed data in meta
        $oembed_data = null;
        if (isset($activity->meta) && is_string($activity->meta)) {
            $meta = json_decode($activity->meta, true);
            if (isset($meta['oembed_data'])) {
                $oembed_data = $meta['oembed_data'];
            }
        }

        // Content to parse
        $content_to_parse = !empty($oembed_data) ? $oembed_data : $content;

        if (!empty($content_to_parse)) {
            // Regex for YouTube video ID from various URL formats and iframe embeds
            $regex = '/(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/';
            preg_match($regex, $content_to_parse, $matches);

            if (isset($matches[1]) && $matches[1]) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * Get YouTube thumbnail URL for a video ID
     *
     * @param string $video_id YouTube video ID
     * @return string YouTube thumbnail URL
     */
    private function get_youtube_thumbnail_url($video_id) {
        if (!$video_id) {
            return '';
        }

        // Return the maxresdefault thumbnail (HD)
        return "https://i.ytimg.com/vi/{$video_id}/maxresdefault.jpg";
    }

    /**
     * Generate sample media feeds for testing
     *
     * @param int $user_id User ID
     * @return array Sample media feeds
     */
    private function get_sample_media_feeds($user_id) {
        $user = get_user_by('ID', $user_id);
        $feeds = [];

        // Get user avatar URL
        $avatar_url = get_avatar_url($user_id);

        // Sample image URLs
        $sample_images = [
            'https://via.placeholder.com/800x600/3498db/ffffff?text=Sample+Image+1',
            'https://via.placeholder.com/800x600/e74c3c/ffffff?text=Sample+Image+2',
            'https://via.placeholder.com/800x600/2ecc71/ffffff?text=Sample+Image+3',
            'https://via.placeholder.com/800x600/f39c12/ffffff?text=Sample+Image+4',
            'https://via.placeholder.com/800x600/9b59b6/ffffff?text=Sample+Image+5',
            'https://via.placeholder.com/800x600/1abc9c/ffffff?text=Sample+Image+6',
        ];

        // Sample YouTube video IDs
        $sample_youtube_ids = [
            'dQw4w9WgXcQ', // Rick Astley - Never Gonna Give You Up
            'jNQXAC9IVRw', // Me at the zoo (first YouTube video)
            'hY7m5jjJ9mM', // CATS will make you LAUGH YOUR HEAD OFF
        ];

        // Generate sample feeds - first 6 with images
        for ($i = 1; $i <= 6; $i++) {
            $feed_id = 1000 + $i;
            $image_url = $sample_images[$i - 1];

            $feeds[] = [
                'id' => $feed_id,
                'user_id' => $user_id,
                'content' => "This is a sample image post #{$i} for testing purposes.",
                'created_at' => date('Y-m-d H:i:s', time() - ($i * 86400)), // Each post is 1 day older
                'updated_at' => date('Y-m-d H:i:s', time() - ($i * 86400)),
                'is_active' => true,
                'meta' => [
                    'media_preview' => [
                        'type' => 'image',
                        'url' => $image_url
                    ]
                ],
                'xprofile' => [
                    'user_id' => $user_id,
                    'display_name' => $user->display_name,
                    'user_login' => $user->user_login,
                    'avatar_url' => $avatar_url,
                    'email' => $user->user_email,
                ],
                'media' => [
                    [
                        'id' => 2000 + $i,
                        'feed_id' => $feed_id,
                        'media_type' => 'image/jpeg',
                        'file_name' => "sample-image-{$i}.jpg",
                        'file_path' => "uploads/sample-image-{$i}.jpg",
                        'public_url' => $image_url,
                        'created_at' => date('Y-m-d H:i:s', time() - ($i * 86400)),
                        'updated_at' => date('Y-m-d H:i:s', time() - ($i * 86400)),
                    ]
                ],
                'reactions' => [],
                'comments' => [],
            ];
        }

        // Generate sample feeds - next 3 with YouTube videos
        for ($i = 1; $i <= 3; $i++) {
            $feed_id = 2000 + $i;
            $youtube_id = $sample_youtube_ids[$i - 1];
            $youtube_thumbnail = $this->get_youtube_thumbnail_url($youtube_id);

            $feeds[] = [
                'id' => $feed_id,
                'user_id' => $user_id,
                'content' => "This is a sample YouTube post #{$i} for testing purposes. https://www.youtube.com/watch?v={$youtube_id}",
                'created_at' => date('Y-m-d H:i:s', time() - ($i * 86400)), // Each post is 1 day older
                'updated_at' => date('Y-m-d H:i:s', time() - ($i * 86400)),
                'is_active' => true,
                'meta' => [
                    'media_preview' => [
                        'type' => 'video',
                        'url' => $youtube_thumbnail,
                        'youtube_id' => $youtube_id
                    ],
                    'oembed_data' => "<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/{$youtube_id}\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>"
                ],
                'xprofile' => [
                    'user_id' => $user_id,
                    'display_name' => $user->display_name,
                    'user_login' => $user->user_login,
                    'avatar_url' => $avatar_url,
                    'email' => $user->user_email,
                ],
                'media' => [],
                'reactions' => [],
                'comments' => [],
                'youtube_video_id' => $youtube_id
            ];
        }

        return $feeds;
    }
}
