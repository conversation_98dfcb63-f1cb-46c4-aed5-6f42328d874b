/*
 * MQCMP Main Stylesheet
 * Styles for <mqcmp-vidstack-player> and Vidstack player Shadow DOM
 * Uses Fluent Community CSS variables for theming
 */

mqcmp-vidstack-player {
  display: block;
  width: 100%;
  max-width: 720px;
  margin: 1.5em auto;
  aspect-ratio: 16 / 9;
  border-radius: var(--fcom-border-radius, 8px);
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  background: transparent !important;
  outline: none;
  position: relative;
  z-index: 10; /* Ensure player is above other elements */
}

mqcmp-vidstack-player:focus {
  outline: 2px solid var(--fcom-primary-color, #3498db);
  outline-offset: 2px;
}

mqcmp-vidstack-player:hover {
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
}

/* Shadow DOM theming is handled by JS, but fallback here */
media-player {
  width: 100%;
  height: 100%;
  border-radius: inherit;
  font-family: var(--fcom-font-family, inherit);
}

/* Accessibility: visually indicate focus */
media-player:focus {
  outline: 2px solid var(--fcom-primary-color, #3498db);
}

/* Future extension: add more styles as needed */

/* Hide any remaining YouTube iframes in containers with our player */
.feed_media_ext_video iframe[src*="youtube.com"],
.feed_media_ext_video iframe[src*="youtu.be"],
.feed_media_ext_video iframe[src*="vimeo.com"] {
  display: none !important;
}

/* Vidstack will handle iframe visibility automatically with Default Layout */

/* Old poster container styles removed - using Vidstack's built-in poster handling */

/* Ensure our player is visible and above other elements */
.feed_media_ext_video mqcmp-vidstack-player {
  display: block !important;
  position: relative;
  z-index: 10;
}

/* Error overlay styling */
.mqcmp-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border-radius: inherit;
}

.mqcmp-error-message {
  background-color: rgba(30, 30, 30, 0.9);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
  max-width: 80%;
}

.mqcmp-retry-button {
  background-color: var(--fcom-primary-color, #3498db);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  font-size: 14px;
}

.mqcmp-retry-button:hover {
  background-color: var(--fcom-primary-color-hover, #2980b9);
}

/* Fix z-index issues */
.feed_media_ext_video {
  position: relative;
  z-index: 1; /* Lower z-index for container */
}

/* Ensure our player is visible and properly sized */
.feed_media_ext_video mqcmp-vidstack-player {
  display: block !important;
  position: relative;
  z-index: 10;
  width: 100% !important;
  max-width: 100% !important;
}

/* Fix for Vidstack player in Shadow DOM */
media-player {
  --media-background: transparent !important;
  --media-control-background: rgba(20, 20, 30, 0.7) !important;
  --media-control-hover-background: rgba(30, 30, 40, 0.8) !important;
}

/* Ensure poster images are visible */
media-poster {
  z-index: 1 !important;
  display: block !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  background-color: #000 !important;
}

/* Style for poster images */
media-poster img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

/* Ensure poster is visible before play */
media-player:not([data-playing]) media-poster {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Default Layout styling */
media-player {
  --media-brand: var(--fcom-primary-color, #3498db) !important;
  --media-focus-ring-color: var(--fcom-primary-color, #3498db) !important;
  --media-control-icon-size: 24px !important; /* Control icon size */
  --media-menu-icon-size: 24px !important; /* Menu icon size */
  --media-slider-height: 6px !important; /* Slider height */
  --media-slider-track-height: 6px !important; /* Slider track height */
  --media-slider-thumb-size: 14px !important; /* Slider thumb size */
  font-family: var(--fcom-font-family, inherit) !important;
  --media-font-family: var(--fcom-font-family, inherit) !important;
  --media-controls-color: #fff !important;
  --media-controls-background: rgba(20, 20, 30, 0.7) !important;
  --media-controls-height: 48px !important;
  --media-tooltip-background: rgba(20, 20, 30, 0.9) !important;
  --media-tooltip-color: #fff !important;
  --media-menu-background: rgba(20, 20, 30, 0.9) !important;
  --media-menu-color: #fff !important;
  --media-menu-item-focus-background: rgba(255, 255, 255, 0.1) !important;
  --media-menu-item-hover-background: rgba(255, 255, 255, 0.1) !important;
  --media-menu-item-active-background: rgba(255, 255, 255, 0.2) !important;
  --media-menu-item-active-color: #fff !important;
  --media-menu-item-checked-background: rgba(255, 255, 255, 0.1) !important;
  --media-menu-item-checked-color: #fff !important;
  --media-menu-item-focus-ring-color: var(--fcom-primary-color, #3498db) !important;
}

/* Ensure Default Layout is visible and properly sized */
media-player media-video-layout {
  display: block !important;
  width: 100% !important;
  height: 100% !important;
}

/* Fix SVG icon sizes - using !important with very specific selectors */
media-player svg,
.vds-video-layout svg,
media-player .vds-icon svg,
media-player button svg,
media-player .vds-slider svg,
media-player .vds-time-slider svg,
media-player .vds-volume-slider svg,
media-player .vds-play-button svg,
media-player .vds-mute-button svg,
media-player .vds-fullscreen-button svg,
media-player .vds-caption-button svg,
media-player .vds-pip-button svg,
media-player .vds-seek-button svg {
  width: 24px !important;
  height: 24px !important;
  max-width: 24px !important;
  max-height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
}

/* Fix menu icon sizes */
media-player [aria-haspopup="menu"] svg,
.vds-video-layout [aria-haspopup="menu"] svg {
  width: 24px !important;
  height: 24px !important;
  max-width: 24px !important;
  max-height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
}

/* Additional specific rules for SVG icons */
.vds-play-button svg,
.vds-mute-button svg,
.vds-fullscreen-button svg,
.vds-caption-button svg,
.vds-pip-button svg,
.vds-seek-button svg,
.vds-menu-button svg,
.vds-settings-button svg,
.vds-time-slider svg,
.vds-volume-slider svg,
.vds-slider-thumb svg,
.vds-slider-track svg,
.vds-slider-preview svg,
.vds-slider-chapters svg,
.vds-slider-value svg,
.vds-slider-track-fill svg,
.vds-slider-track-progress svg,
.vds-slider-track-background svg,
.vds-slider-thumb-container svg,
.vds-slider-preview-container svg,
.vds-slider-chapters-container svg,
.vds-slider-value-container svg,
.vds-slider-track-fill-container svg,
.vds-slider-track-progress-container svg,
.vds-slider-track-background-container svg,
.vds-slider-thumb-container-container svg {
  width: 24px !important;
  height: 24px !important;
  max-width: 24px !important;
  max-height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
  transform: scale(1) !important;
}

/* Target the Shadow DOM elements */
media-player::part(play-button-icon),
media-player::part(mute-button-icon),
media-player::part(fullscreen-button-icon),
media-player::part(caption-button-icon),
media-player::part(pip-button-icon),
media-player::part(seek-button-icon),
media-player::part(menu-button-icon),
media-player::part(settings-button-icon),
media-player::part(time-slider-icon),
media-player::part(volume-slider-icon),
media-player::part(slider-thumb-icon),
media-player::part(slider-track-icon),
media-player::part(slider-preview-icon),
media-player::part(slider-chapters-icon),
media-player::part(slider-value-icon),
media-player::part(slider-track-fill-icon),
media-player::part(slider-track-progress-icon),
media-player::part(slider-track-background-icon),
media-player::part(slider-thumb-container-icon),
media-player::part(slider-preview-container-icon),
media-player::part(slider-chapters-container-icon),
media-player::part(slider-value-container-icon),
media-player::part(slider-track-fill-container-icon),
media-player::part(slider-track-progress-container-icon),
media-player::part(slider-track-background-container-icon),
media-player::part(slider-thumb-container-container-icon) {
  width: 24px !important;
  height: 24px !important;
  max-width: 24px !important;
  max-height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
  transform: scale(1) !important;
}

/* Ensure poster is hidden when playing or after play has started */
media-player[data-playing] media-poster,
media-player[data-playing] .vds-poster,
media-player[data-can-play] media-poster,
media-player[data-can-play] .vds-poster,
media-player[data-provider-ready] media-poster,
media-player[data-provider-ready] .vds-poster {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

/* Old custom poster container rules removed - using Vidstack's built-in poster handling */

/* Ensure video is visible when playing */
media-player[data-playing] video,
media-player[data-playing] iframe {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix z-index issues */
media-player .vds-controls {
  z-index: 10 !important;
}

media-player .vds-scrim {
  z-index: 5 !important;
}

media-player .vds-poster {
  z-index: 4 !important;
}

media-player video,
media-player iframe {
  z-index: 3 !important;
}

/* Fix for Default Layout in Shadow DOM */
media-player::part(controls) {
  --media-control-background: rgba(20, 20, 30, 0.7) !important;
  --media-control-hover-background: rgba(30, 30, 40, 0.8) !important;
}
