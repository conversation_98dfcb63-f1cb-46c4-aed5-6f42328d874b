/**
 * MQCMP Vidstack Player Custom Element
 * Wraps Vidstack <media-player> with Shadow DOM and progressive enhancement.
 *
 * @element mqcmp-vidstack-player
 * @attr src - Video source URL
 * @attr poster - Poster image URL
 */
// --- HYBRID CONFIG PATCH FOR SPA/PORTAL ---
(function() {
    if (typeof window !== 'undefined' && window.MQCMP_CONFIG) {
        // Store a direct debug reference at the top level for easier access
        window.MQCMP_CONFIG.debug = window.MQCMP_CONFIG.accessibilityOptions?.debug || false;

        if (typeof MQCMP_ACCESSIBILITY_OPTIONS === 'undefined') {
            window.MQCMP_ACCESSIBILITY_OPTIONS = window.MQCMP_CONFIG.accessibilityOptions;
        } else if (window.MQCMP_CONFIG.accessibilityOptions) {
            // Ensure debug setting is synchronized
            window.MQCMP_ACCESSIBILITY_OPTIONS.debug = window.MQCMP_CONFIG.accessibilityOptions.debug;
        }

        if (typeof MQCMP_PLAYER_LAYOUT === 'undefined') {
            window.MQCMP_PLAYER_LAYOUT = window.MQCMP_CONFIG.playerLayout;
        }
        if (typeof MQCMP_FCOM_CSS_VARS === 'undefined') {
            window.MQCMP_FCOM_CSS_VARS = window.MQCMP_CONFIG.fcomCssVars;
        }
        if (typeof MQCMP_ENABLED_CONTEXTS === 'undefined') {
            window.MQCMP_ENABLED_CONTEXTS = window.MQCMP_CONFIG.enabledContexts;
        }
        if (typeof MQCMP_SUPPORTED_PROVIDERS === 'undefined') {
            window.MQCMP_SUPPORTED_PROVIDERS = window.MQCMP_CONFIG.supportedProviders;
        }
        if (typeof MQCMP_AJAX_URL === 'undefined') {
            window.MQCMP_AJAX_URL = window.MQCMP_CONFIG.ajaxUrl;
        }

        mqcmpDebugLog('MQCMP Config initialized:', window.MQCMP_CONFIG);
    }
})();

// --- DEBUG LOGGING UTILITY ---
function mqcmpIsDebugEnabled() {
    // Check both config locations for backward compatibility
    if (window.MQCMP_CONFIG && typeof window.MQCMP_CONFIG.debug === 'boolean') {
        return window.MQCMP_CONFIG.debug;
    }

    if (window.MQCMP_CONFIG && window.MQCMP_CONFIG.accessibilityOptions &&
        typeof window.MQCMP_CONFIG.accessibilityOptions.debug === 'boolean') {
        return window.MQCMP_CONFIG.accessibilityOptions.debug;
    }

    if (window.MQCMP_ACCESSIBILITY_OPTIONS && window.MQCMP_ACCESSIBILITY_OPTIONS.debug) {
        // Handle string "1" or "0" case that might come from PHP
        if (window.MQCMP_ACCESSIBILITY_OPTIONS.debug === "1" ||
            window.MQCMP_ACCESSIBILITY_OPTIONS.debug === true) {
            return true;
        }
    }

    return false;
}

function mqcmpDebugLog(...args) {
    if (mqcmpIsDebugEnabled()) {
        console.log('[MQCMP]', ...args);
    }
}

function mqcmpDebugWarn(...args) {
    if (mqcmpIsDebugEnabled()) {
        console.warn('[MQCMP]', ...args);
    }
}

function mqcmpDebugError(...args) {
    if (mqcmpIsDebugEnabled()) {
        console.error('[MQCMP]', ...args);
    }
}

// Add version information check that logs on script load
mqcmpDebugLog('MQCMP Main Script loaded - version 0.4.0');

// Global variable to track skipped iframes for retry
window.MQCMP_SKIPPED_IFRAMES = [];

/**
 * Get the provider type based on the source URL
 * @param {string} src - The source URL
 * @returns {string} - The provider type (youtube, vimeo, video)
 */
function mqcmp_get_provider_type(src) {
    if (src.includes('youtube.com') || src.includes('youtu.be')) {
        return 'youtube';
    } else if (src.includes('vimeo.com')) {
        return 'vimeo';
    } else {
        return 'video';
    }
}

/**
 * Get the source type based on the provider type
 * @param {string} providerType - The provider type
 * @returns {string} - The source type (video/youtube, video/vimeo, video/mp4)
 */
function mqcmp_get_source_type(providerType) {
    switch (providerType) {
        case 'youtube': return 'video/youtube';
        case 'vimeo': return 'video/vimeo';
        default: return 'video/mp4';
    }
}

/**
 * Get the poster URL for a video
 * @param {string} src - The source URL
 * @returns {string} - The poster URL or empty string if not available
 */
function mqcmp_get_poster_url(src) {
    if (!src) return '';

    // For YouTube, extract the video ID and construct a thumbnail URL
    if (src.includes('youtube.com') || src.includes('youtu.be')) {
        const videoId = mqcmp_extract_youtube_id(src);
        if (videoId) {
            // Try multiple resolutions in case maxresdefault is not available
            return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
            // Note: If maxresdefault fails to load, we'll use a fallback in the img onerror handler
        }
    }

    // For Vimeo, we would need to make an API call to get the thumbnail
    // For now, return a placeholder image for Vimeo
    if (src.includes('vimeo.com')) {
        // We'll fetch the actual thumbnail via AJAX later
        return '';
    }

    // Default placeholder for other providers
    return '';
}

/**
 * Extract the YouTube video ID from a URL
 * @param {string} url - The YouTube URL
 * @returns {string|null} - The video ID or null if not found
 */
function mqcmp_extract_youtube_id(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}
mqcmpDebugLog('Debug mode is', mqcmpIsDebugEnabled() ? 'enabled' : 'disabled');

class MQCMPVidstackPlayer extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        this._player = null;
        this._retryTimeout = null;
        this._retryCount = 0;
        this._maxRetries = 3;
        mqcmpDebugLog('<mqcmp-vidstack-player> constructed');
    }

    connectedCallback() {
        const src = this.getAttribute('src');
        const poster = this.getAttribute('poster') || '';

        // Determine provider type using helper function
        let providerType = mqcmp_detect_provider_type(src);

        // Create appropriate provider markup based on type with explicit source elements
        let providerMarkup = '';
        let customProvider = mqcmp_match_custom_provider(src);
        let sourceType = '';

        if (providerType === 'youtube') {
            sourceType = 'video/youtube';
            providerMarkup = `<media-provider type="youtube">
                <source src="${src || ''}" type="${sourceType}">
            </media-provider>`;
        } else if (providerType === 'vimeo') {
            sourceType = 'video/vimeo';
            providerMarkup = `<media-provider type="vimeo">
                <source src="${src || ''}" type="${sourceType}">
            </media-provider>`;
        } else if (customProvider && customProvider.provider_type) {
            // Handle custom provider with specific type if available
            sourceType = customProvider.mime_type || 'video/mp4';
            providerMarkup = `<media-provider type="${customProvider.provider_type}">
                <source src="${src || ''}" type="${sourceType}">
            </media-provider>`;
            mqcmpDebugLog('Using custom provider type:', customProvider.provider_type);
        } else {
            // Default to standard HTML5 video provider
            sourceType = 'video/mp4';
            providerMarkup = `<media-provider>
                <source src="${src || ''}" type="${sourceType}">
            </media-provider>`;
        }

        mqcmpDebugLog('Creating provider with source type:', sourceType);

        // Get video title for accessibility
        const videoTitle = this.getAttribute('title') || 'Video player';

        // Use explicit source elements instead of src attribute on media-player
        this.shadowRoot.innerHTML = `
            <media-player poster="${poster}" aria-label="${videoTitle}" tabindex="0">
                ${providerMarkup}
                <media-poster></media-poster>
                <media-loading-indicator></media-loading-indicator>
                ${mqcmp_render_controls()}
            </media-player>
            <style>
                :host { display: block; }
                media-player {
                    width: 100%;
                    aspect-ratio: 16/9;
                    border-radius: var(--fcom-border-radius, 8px);
                    overflow: hidden;
                }
                media-player:focus-visible {
                    outline: 2px solid var(--fcom-primary-color, #3498db);
                    outline-offset: 2px;
                }
                media-poster {
                    z-index: 1;
                    object-fit: cover;
                    width: 100%;
                    height: 100%;
                }
                .mqcmp-error-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.7);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10;
                }
                .mqcmp-error-message {
                    background: #fff;
                    padding: 20px;
                    border-radius: 8px;
                    text-align: center;
                    max-width: 80%;
                    color: #333;
                }
                .mqcmp-error-details {
                    font-size: 0.9em;
                    color: #666;
                    margin-top: 5px;
                }
                .mqcmp-retry-button {
                    background: var(--fcom-primary-color, #3498db);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-top: 10px;
                }
                .mqcmp-retry-button:hover {
                    background: var(--fcom-primary-color-hover, #2980b9);
                }
                .mqcmp-retry-button:focus-visible {
                    outline: 2px solid #fff;
                    outline-offset: 2px;
                }
                /* High contrast mode support */
                @media (forced-colors: active) {
                    .mqcmp-retry-button {
                        border: 1px solid ButtonText;
                    }
                    .mqcmp-error-message {
                        border: 1px solid ButtonText;
                    }
                }
            </style>
        `;

        this._player = this.shadowRoot.querySelector('media-player');
        if (this._player) {
            // Add event listeners for both success and error cases
            this._player.addEventListener('can-play', () => {
                mqcmpDebugLog('Vidstack player initialized:', src);
                this.setAttribute('data-can-play', 'true');

                // Clear any retry attempts if we successfully loaded
                if (this._retryTimeout) {
                    clearTimeout(this._retryTimeout);
                    this._retryTimeout = null;
                }

                // Remove any error overlays that might be present
                const errorOverlay = this.shadowRoot.querySelector('.mqcmp-error-overlay');
                if (errorOverlay) {
                    errorOverlay.remove();
                }
            });

            // Also listen for the media-can-play event for backward compatibility
            this._player.addEventListener('media-can-play', () => {
                mqcmpDebugLog('Vidstack player media-can-play event:', src);
                this.setAttribute('data-can-play', 'true');
            });

            this._player.addEventListener('media-error', (event) => {
                mqcmpDebugError('Vidstack player media-error event:', event.detail);
                this._handlePlayerError(event.detail);
            });

            // Also listen for the error event for better compatibility
            this._player.addEventListener('error', (event) => {
                mqcmpDebugError('Vidstack player error event:', event.detail || event);
                this._handlePlayerError(event.detail || { message: 'Player error occurred' });
            });

            // Loading events
            this._player.addEventListener('media-loading', () => {
                mqcmpDebugLog('Vidstack player loading:', src);
                this.setAttribute('data-loading', 'true');
            });

            // Loaded metadata event
            this._player.addEventListener('media-loaded-metadata', () => {
                mqcmpDebugLog('Vidstack player loaded metadata:', src);
                this.setAttribute('data-loaded-metadata', 'true');
            });

            // Provider change events
            this._player.addEventListener('provider-change', (event) => {
                mqcmpDebugLog('Vidstack provider changed:', event.detail);
                if (event.detail && event.detail.provider) {
                    mqcmpDebugLog('Provider type:', event.detail.provider.type);
                    mqcmpDebugLog('Provider state:', event.detail.provider.state);

                    // If provider is ready, mark the player as ready
                    if (event.detail.provider.state === 'ready') {
                        this.setAttribute('data-provider-ready', 'true');
                        mqcmpDebugLog('Provider is ready:', event.detail.provider.type);
                    }
                }
            });

            // Also listen for the provider-setup event
            this._player.addEventListener('provider-setup', (event) => {
                mqcmpDebugLog('Vidstack provider setup:', event.detail);
                this.setAttribute('data-provider-setup', 'true');
            });

            // Add more detailed error logging
            this._player.addEventListener('provider-setup-error', (event) => {
                mqcmpDebugError('Vidstack provider setup error:', event.detail);
                // Try to recover from provider setup errors
                this._handleProviderSetupError(event.detail);
            });

            // Track load state changes
            this._player.addEventListener('load-state-change', (event) => {
                mqcmpDebugLog('Vidstack load state changed:', event.detail);
            });

            // Network state changes
            this._player.addEventListener('network-state-change', (event) => {
                mqcmpDebugLog('Vidstack network state changed:', event.detail);

                // Handle network errors
                if (event.detail && event.detail.state === 3) { // NETWORK_NO_SOURCE
                    mqcmpDebugError('Network error: No source available');
                    this._handleNetworkError();
                }
            });
        }

        this.setAttribute('data-enhanced', 'true');
        mqcmp_apply_fcom_theme(this.shadowRoot);
    }

    // Add error handling method
    _handlePlayerError(error) {
        // Create error overlay
        const errorOverlay = document.createElement('div');
        errorOverlay.className = 'mqcmp-error-overlay';

        // Get error message if available
        let errorMessage = 'Error loading video. Please try again later.';
        let errorDetails = '';

        if (error) {
            if (error.message) {
                errorMessage = `Error: ${error.message}`;
            }

            if (error.code) {
                errorDetails = `Error code: ${error.code}`;
            }

            mqcmpDebugError('Player error details:', error);
        }

        errorOverlay.innerHTML = `
            <div class="mqcmp-error-message">
                <p>${errorMessage}</p>
                ${errorDetails ? `<p class="mqcmp-error-details">${errorDetails}</p>` : ''}
                <button class="mqcmp-retry-button">Retry</button>
            </div>
        `;

        // Add retry functionality
        const retryButton = errorOverlay.querySelector('.mqcmp-retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', () => {
                // Remove error overlay
                if (errorOverlay.parentNode) {
                    errorOverlay.parentNode.removeChild(errorOverlay);
                }

                // Reload the player
                if (this._player) {
                    this._player.load();
                }
            });
        }

        // Remove any existing error overlays first
        const existingOverlay = this.shadowRoot.querySelector('.mqcmp-error-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Add to shadow DOM
        this.shadowRoot.appendChild(errorOverlay);

        // Set up automatic retry after delay (if not already set and not exceeded max retries)
        if (!this._retryTimeout && this._retryCount < this._maxRetries) {
            this._retryCount++;
            mqcmpDebugLog(`Setting up retry attempt ${this._retryCount} of ${this._maxRetries}...`);

            this._retryTimeout = setTimeout(() => {
                mqcmpDebugLog(`Attempting automatic retry ${this._retryCount} after error...`);
                if (this._player) {
                    this._player.load();
                }
                this._retryTimeout = null;
            }, 5000); // 5 second retry
        } else if (this._retryCount >= this._maxRetries) {
            mqcmpDebugWarn(`Maximum retry attempts (${this._maxRetries}) reached. Giving up automatic retries.`);

            // Update error message to indicate we've given up
            const errorMessage = this.shadowRoot.querySelector('.mqcmp-error-message p');
            if (errorMessage) {
                errorMessage.textContent = 'Error loading video after multiple attempts. Please try again later or check the video URL.';
            }
        }
    }

    // Handle provider setup errors specifically
    _handleProviderSetupError(error) {
        mqcmpDebugError('Provider setup error:', error);

        // Create error overlay with more specific message
        const errorOverlay = document.createElement('div');
        errorOverlay.className = 'mqcmp-error-overlay';

        let errorMessage = 'Error setting up video provider. This may be due to an unsupported format or restricted content.';
        let providerType = '';

        if (this._player) {
            const provider = this._player.querySelector('media-provider');
            if (provider && provider.getAttribute('type')) {
                providerType = provider.getAttribute('type');
                errorMessage += ` (Provider: ${providerType})`;
            }
        }

        errorOverlay.innerHTML = `
            <div class="mqcmp-error-message">
                <p>${errorMessage}</p>
                <button class="mqcmp-retry-button">Retry</button>
            </div>
        `;

        // Add retry functionality
        const retryButton = errorOverlay.querySelector('.mqcmp-retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', () => {
                // Remove error overlay
                if (errorOverlay.parentNode) {
                    errorOverlay.parentNode.removeChild(errorOverlay);
                }

                // Reload the player
                if (this._player) {
                    this._player.load();
                }
            });
        }

        // Remove any existing error overlays first
        const existingOverlay = this.shadowRoot.querySelector('.mqcmp-error-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Add to shadow DOM
        this.shadowRoot.appendChild(errorOverlay);
    }

    // Handle network errors specifically
    _handleNetworkError() {
        mqcmpDebugError('Network error detected');

        // Create error overlay with network-specific message
        const errorOverlay = document.createElement('div');
        errorOverlay.className = 'mqcmp-error-overlay';

        errorOverlay.innerHTML = `
            <div class="mqcmp-error-message">
                <p>Network error: Unable to load the video. Please check your internet connection.</p>
                <button class="mqcmp-retry-button">Retry</button>
            </div>
        `;

        // Add retry functionality
        const retryButton = errorOverlay.querySelector('.mqcmp-retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', () => {
                // Remove error overlay
                if (errorOverlay.parentNode) {
                    errorOverlay.parentNode.removeChild(errorOverlay);
                }

                // Reload the player
                if (this._player) {
                    this._player.load();
                }
            });
        }

        // Remove any existing error overlays first
        const existingOverlay = this.shadowRoot.querySelector('.mqcmp-error-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Add to shadow DOM
        this.shadowRoot.appendChild(errorOverlay);

        // Set up automatic retry for network errors (more aggressive)
        if (!this._retryTimeout && this._retryCount < this._maxRetries) {
            this._retryCount++;
            mqcmpDebugLog(`Setting up network retry attempt ${this._retryCount} of ${this._maxRetries}...`);

            // For network errors, we use a shorter timeout and increase it with each retry
            const retryDelay = 2000 + (this._retryCount * 1000); // 3s, 4s, 5s

            this._retryTimeout = setTimeout(() => {
                mqcmpDebugLog(`Attempting automatic retry ${this._retryCount} after network error...`);
                if (this._player) {
                    this._player.load();
                }
                this._retryTimeout = null;
            }, retryDelay);
        } else if (this._retryCount >= this._maxRetries) {
            mqcmpDebugWarn(`Maximum retry attempts (${this._maxRetries}) reached. Giving up automatic retries.`);

            // Update error message to indicate we've given up
            const errorMessage = this.shadowRoot.querySelector('.mqcmp-error-message p');
            if (errorMessage) {
                errorMessage.textContent = 'Network error: Unable to load the video after multiple attempts. Please check your internet connection and try again later.';
            }
        }
    }
}

// Register custom element if not already defined
if (!window.customElements.get('mqcmp-vidstack-player')) {
    window.customElements.define('mqcmp-vidstack-player', MQCMPVidstackPlayer);
}

/**
 * Apply Fluent Community CSS variables to Vidstack player theme.
 * Supports dynamic light/dark mode switching.
 */
function mqcmp_apply_fcom_theme(shadowRoot) {
    if (typeof MQCMP_FCOM_CSS_VARS !== 'object') return;
    const style = document.createElement('style');
    let cssVars = '';
    for (const [key, value] of Object.entries(MQCMP_FCOM_CSS_VARS)) {
        cssVars += `${key}: ${value};\n`;
    }
    style.textContent = `:host { ${cssVars} }`;
    shadowRoot.appendChild(style);
}

// Listen for body class changes (light/dark mode)
const mqcmp_mode_observer = new MutationObserver(() => {
    document.querySelectorAll('mqcmp-vidstack-player').forEach(el => {
        if (el.shadowRoot) mqcmp_apply_fcom_theme(el.shadowRoot);
    });
});
mqcmp_mode_observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });

/**
 * Detect YouTube/Vimeo iframes in Fluent Community activities.
 * Logs found elements and their src. Uses MutationObserver for dynamic content.
 */
const MQCMP_SELECTORS = [
    '.fcom-activity-item iframe[src*="youtube.com"], .fcom-activity-item iframe[src*="youtu.be"], .feed_media_ext_video iframe[src*="youtube.com"], .feed_media_ext_video iframe[src*="youtu.be"]',
    '.fcom-activity-item iframe[src*="vimeo.com"], .feed_media_ext_video iframe[src*="vimeo.com"]'
];

/**
 * Find all YouTube/Vimeo iframes in Fluent Community activities.
 * @returns {HTMLIFrameElement[]} Array of found iframes
 */
function mqcmp_find_media_iframes() {
    let found = [];
    MQCMP_SELECTORS.forEach(sel => {
        document.querySelectorAll(sel).forEach(iframe => {
            found.push(iframe);
            mqcmpDebugLog('Found media iframe:', iframe.src);
        });
    });
    return found;
}

/**
 * Fetch video thumbnail via AJAX and set as poster on the custom element.
 * @param {HTMLElement} playerEl - The mqcmp-vidstack-player element
 * @param {string} src - The video URL
 */
function mqcmp_set_player_poster(playerEl, src) {
    let videoType = '';
    if (/youtube\.com|youtu\.be/.test(src)) {
        videoType = 'youtube';
    } else if (/vimeo\.com/.test(src)) {
        videoType = 'vimeo';
    } else {
        const custom = mqcmp_match_custom_provider(src);
        if (custom && custom.thumbnail_url) {
            playerEl.setAttribute('poster', custom.thumbnail_url);
            return;
        }
        // Placeholder: implement AJAX or callback for custom providers
        return;
    }
    fetch(MQCMP_AJAX_URL + '?action=mqcmp_get_thumbnail&video_url=' + encodeURIComponent(src) + '&video_type=' + videoType, {
        credentials: 'same-origin'
    })
    .then(res => res.json())
    .then(data => {
        if (data.success && data.data && data.data.thumbnail_url) {
            playerEl.setAttribute('poster', data.data.thumbnail_url);
            // If already connected, update poster in shadow DOM
            if (playerEl.shadowRoot) {
                const mp = playerEl.shadowRoot.querySelector('media-player');
                if (mp) mp.setAttribute('poster', data.data.thumbnail_url);
            }
        } else {
            mqcmpDebugWarn('No thumbnail found for', src);
        }
    })
    .catch(err => {
        mqcmpDebugError('Error fetching thumbnail:', err);
    });
}

/**
 * Check if an element is within an enabled context.
 * @param {Element} el
 * @returns {boolean}
 */
function mqcmp_is_enabled_context(el) {
    const enabledContexts = window.MQCMP_CONFIG?.enabledContexts || ['.fcom-activity-item', '.feed_media_ext_video'];
    if (!Array.isArray(enabledContexts)) return true;

    // Special case for feed_media_ext_video class which is used in the live site
    if (el.closest('.feed_media_ext_video')) return true;

    return enabledContexts.some(sel => el.closest(sel));
}

/**
 * Check if a video src matches a custom provider.
 * @param {string} src
 * @returns {object|null} Provider object if matched, else null
 */
function mqcmp_match_custom_provider(src) {
    const customProviders = window.MQCMP_CONFIG?.customProviders || [];
    if (!Array.isArray(customProviders)) return null;
    for (const provider of customProviders) {
        if (provider.match_regex && new RegExp(provider.match_regex).test(src)) {
            return provider;
        }
    }
    return null;
}

/**
 * Helper function to detect provider type from URL
 * @param {string} url - The video URL
 * @returns {string} Provider type ('youtube', 'vimeo', or custom provider type)
 */
function mqcmp_detect_provider_type(url) {
    if (!url) return '';

    // Check built-in providers first
    if (/youtube\.com|youtu\.be/.test(url)) {
        return 'youtube';
    } else if (/vimeo\.com/.test(url)) {
        return 'vimeo';
    }

    // Check for custom providers
    const customProvider = mqcmp_match_custom_provider(url);
    if (customProvider && customProvider.provider_type) {
        return customProvider.provider_type;
    }

    return '';
}

/**
 * Check if a video src matches a supported provider.
 * @param {string} src
 * @returns {boolean}
 */
function mqcmp_is_supported_provider(src) {
    if (!src) return false;
    // Check built-in providers
    const supportedProviders = window.MQCMP_CONFIG?.supportedProviders || ['youtube', 'vimeo'];
    if (Array.isArray(supportedProviders) && supportedProviders.some(provider => {
        if (provider === 'youtube') return /youtube\.com|youtu\.be/.test(src);
        if (provider === 'vimeo') return /vimeo\.com/.test(src);
        return false;
    })) return true;
    // Check custom providers
    return !!mqcmp_match_custom_provider(src);
}

// Update replacement logic to only process supported providers and handle errors better
function mqcmp_replace_iframes_with_player(iframes) {
    iframes.forEach(iframe => {
        try {
            const src = iframe.src;

            // Skip if not in enabled context
            if (!mqcmp_is_enabled_context(iframe)) {
                mqcmpDebugLog('Skipping iframe, not in enabled context:', src);
                return;
            }

            // Skip if not a supported provider
            if (!mqcmp_is_supported_provider(src)) {
                mqcmpDebugLog('Skipping iframe, not a supported provider:', src);
                return;
            }

            // Check if this iframe has already been processed
            if (iframe.hasAttribute('data-mqcmp-processed')) {
                mqcmpDebugLog('Skipping already processed iframe:', src);
                return;
            }

            // Check if iframe is visible and has dimensions (if enabled)
            if (window.MQCMP_CONFIG?.skipInvisibleIframes !== false &&
                (iframe.offsetWidth === 0 || iframe.offsetHeight === 0)) {
                mqcmpDebugLog('Skipping invisible iframe:', src);

                // Add to a list of skipped iframes for later retry
                if (!window.MQCMP_SKIPPED_IFRAMES) {
                    window.MQCMP_SKIPPED_IFRAMES = [];
                }
                window.MQCMP_SKIPPED_IFRAMES.push(iframe);

                return;
            }

            // Check if custom elements are defined
            if (!window.customElements.get('media-player') ||
                !window.customElements.get('media-provider')) {
                mqcmpDebugWarn('Custom elements not defined yet, skipping iframe:', src);

                // Add to a list of skipped iframes for later retry
                if (!window.MQCMP_SKIPPED_IFRAMES) {
                    window.MQCMP_SKIPPED_IFRAMES = [];
                }
                window.MQCMP_SKIPPED_IFRAMES.push(iframe);

                // Try again later
                setTimeout(mqcmp_retry_skipped_iframes, 500);
                return;
            }

            // Generate a unique ID for this player instance
            const uniqueId = 'mqcmp-player-' + Math.random().toString(36).substring(2, 11);

            // Determine provider type and source type
            const providerType = mqcmp_get_provider_type(src);
            const sourceType = mqcmp_get_source_type(providerType);
            const posterUrl = mqcmp_get_poster_url(src);

            // Create a container for the player
            const container = document.createElement('div');
            container.setAttribute('id', uniqueId);
            container.style.width = '100%';
            container.style.maxWidth = '100%';
            container.style.margin = '1.5em auto';
            container.style.aspectRatio = '16 / 9';
            container.style.borderRadius = 'var(--fcom-border-radius, 8px)';
            container.style.boxShadow = '0 2px 16px rgba(0,0,0,0.08)';
            container.style.background = '#111114';
            container.style.outline = 'none';
            container.style.position = 'relative';
            container.style.zIndex = '10';

            // Copy dimensions from iframe if available
            if (iframe.width) container.style.width = iframe.width + 'px';
            if (iframe.height) container.style.height = iframe.height + 'px';

            // Copy relevant attributes from the iframe
            Array.from(iframe.attributes).forEach(attr => {
                if (attr.name.startsWith('aria-')) {
                    container.setAttribute(attr.name, attr.value);
                }
            });

            // Mark the iframe as processed to prevent duplicate processing
            iframe.setAttribute('data-mqcmp-processed', 'true');

            // Store reference to original iframe for potential fallback
            const originalIframe = iframe.cloneNode(true);

            // Completely hide the original iframe before replacement to prevent conflicts
            iframe.style.display = 'none';
            iframe.style.visibility = 'hidden';
            iframe.style.opacity = '0';
            iframe.style.pointerEvents = 'none';
            iframe.setAttribute('data-mqcmp-hidden', 'true');

            // Replace the iframe with our container
            iframe.parentNode.replaceChild(container, iframe);

            // No custom poster image needed - Vidstack will handle this with media-poster

            // No custom poster container needed - Vidstack will handle this

            // Create the player using simplified Vidstack Default Layout approach
            mqcmpDebugLog('Creating player using simplified Vidstack Default Layout');

            // Create the media player element with proper Vidstack approach
            const mediaPlayer = document.createElement('media-player');
            mediaPlayer.setAttribute('loading', 'eager');
            mediaPlayer.setAttribute('aspect-ratio', '16/9');
            mediaPlayer.setAttribute('title', 'Video');
            mediaPlayer.setAttribute('crossorigin', '');
            mediaPlayer.setAttribute('playsinline', '');
            mediaPlayer.setAttribute('src', src); // Set source directly on player - Vidstack will handle provider creation
            mediaPlayer.setAttribute('view-type', 'video'); // Explicitly set view type
            mediaPlayer.setAttribute('stream-type', 'on-demand'); // Set stream type
            mediaPlayer.style.position = 'absolute';
            mediaPlayer.style.top = '0';
            mediaPlayer.style.left = '0';
            mediaPlayer.style.width = '100%';
            mediaPlayer.style.height = '100%';
            mediaPlayer.style.zIndex = '10';

            // Create empty media provider element - Vidstack will populate this automatically
            const mediaProvider = document.createElement('media-provider');

            // Create the media poster element (Vidstack will handle this properly)
            const mediaPoster = document.createElement('media-poster');
            if (posterUrl) {
                mediaPoster.setAttribute('src', posterUrl);
                mediaPoster.setAttribute('alt', 'Video Thumbnail');
                mediaPoster.setAttribute('class', 'vds-poster');
            }

            // Create the video layout element for Default Layout (no manual controls needed)
            const videoLayout = document.createElement('media-video-layout');

            // Assemble the player in the correct order per Vidstack documentation
            mediaPlayer.appendChild(mediaProvider);
            if (posterUrl) {
                mediaPlayer.appendChild(mediaPoster);
            }
            mediaPlayer.appendChild(videoLayout);
            container.appendChild(mediaPlayer);

            // Set up improved event listeners with better conflict handling
            if (mediaPlayer) {
                // Prevent event bubbling conflicts
                mediaPlayer.addEventListener('can-play', (event) => {
                    event.stopPropagation();
                    container.setAttribute('data-can-play', 'true');
                    mediaPlayer.setAttribute('data-can-play', 'true');
                    mqcmpDebugLog('Player can play:', uniqueId);
                });

                mediaPlayer.addEventListener('playing', (event) => {
                    event.stopPropagation();
                    container.setAttribute('data-playing', 'true');
                    mediaPlayer.setAttribute('data-playing', 'true');
                    mqcmpDebugLog('Player playing event:', uniqueId);

                    // Ensure YouTube iframe is completely hidden when Vidstack is playing
                    const youtubeIframes = container.querySelectorAll('iframe[src*="youtube"], iframe[src*="youtu.be"]');
                    youtubeIframes.forEach(iframe => {
                        iframe.style.display = 'none';
                        iframe.style.visibility = 'hidden';
                        iframe.style.opacity = '0';
                    });
                });

                mediaPlayer.addEventListener('pause', (event) => {
                    event.stopPropagation();
                    mqcmpDebugLog('Player pause event:', uniqueId);
                });

                mediaPlayer.addEventListener('error', (event) => {
                    event.stopPropagation();
                    mqcmpDebugError('Player error:', event.detail || event);
                    // Log more details about the error
                    if (event.detail) {
                        mqcmpDebugError('Error details:', JSON.stringify(event.detail));
                    }

                    if (window.MQCMP_CONFIG?.enableFallback !== false) {
                        container.parentNode.replaceChild(originalIframe, container);
                    }
                });

                mediaPlayer.addEventListener('provider-change', (event) => {
                    mqcmpDebugLog('Provider changed:', event.detail);
                    if (event.detail && event.detail.provider) {
                        mqcmpDebugLog('Provider type:', event.detail.provider.type);
                        mqcmpDebugLog('Provider state:', event.detail.provider.state);

                        // If provider is ready, mark the player as ready
                        if (event.detail.provider.state === 'ready') {
                            container.setAttribute('data-provider-ready', 'true');
                            mqcmpDebugLog('Provider is ready:', event.detail.provider.type);
                        }
                    }
                });

                // Add load event listener
                mediaPlayer.addEventListener('load', () => {
                    mqcmpDebugLog('Player load event:', uniqueId);
                });

                // Add loaded-metadata event listener
                mediaPlayer.addEventListener('loaded-metadata', () => {
                    mqcmpDebugLog('Player loaded-metadata event:', uniqueId);
                });
            }

            // Add fallback mechanism in case player fails to initialize or play
            if (window.MQCMP_CONFIG?.enableFallback !== false) {
                setTimeout(() => {
                    const playerElement = document.getElementById(uniqueId);
                    if (playerElement && !playerElement.hasAttribute('data-playing')) {
                        mqcmpDebugWarn('Player failed to initialize or play, reverting to original iframe:', src);
                        playerElement.parentNode.replaceChild(originalIframe, playerElement);
                    }
                }, 5000); // 5 second timeout for initialization and playback
            }

            mqcmpDebugLog('Replaced iframe with Vidstack player ID:', uniqueId);
        } catch (e) {
            mqcmpDebugError('Error replacing iframe:', e);
            // Don't modify the iframe if there was an error
        }
    });
}

/**
 * Retry processing iframes that were skipped due to being invisible
 */
function mqcmp_retry_skipped_iframes() {
    if (!window.MQCMP_SKIPPED_IFRAMES || !window.MQCMP_SKIPPED_IFRAMES.length) {
        return;
    }

    const stillSkipped = [];

    window.MQCMP_SKIPPED_IFRAMES.forEach(iframe => {
        // Check if iframe is still in the DOM
        if (!iframe.isConnected) {
            return;
        }

        // Check if iframe is now visible
        if (iframe.offsetWidth > 0 && iframe.offsetHeight > 0) {
            mqcmpDebugLog('Retrying previously skipped iframe:', iframe.src);
            mqcmp_replace_iframes_with_player([iframe]);
        } else {
            stillSkipped.push(iframe);
        }
    });

    window.MQCMP_SKIPPED_IFRAMES = stillSkipped;

    // Schedule next retry if there are still skipped iframes
    if (stillSkipped.length > 0) {
        setTimeout(mqcmp_retry_skipped_iframes, 1000);
    }
}

// Enhanced detection with progressive enhancement and hybrid integration
function mqcmp_replace_all_found_iframes() {
    const iframes = mqcmp_find_media_iframes();
    mqcmpDebugLog('Found ' + iframes.length + ' media iframes to process');
    mqcmp_replace_iframes_with_player(iframes);

    // Start retrying skipped iframes
    setTimeout(mqcmp_retry_skipped_iframes, 1000);
}

// Progressive enhancement initialization
function mqcmp_initialize_with_progressive_enhancement() {
    // Check if Fluent Community's core is available
    const fluentCommunityAvailable =
        typeof window.FluentCommunityApp !== 'undefined' ||
        typeof window.FluentCommunityUtil !== 'undefined' ||
        document.querySelector('.fcom-activity-item') !== null;

    // Check for modern browser features
    const supportsCustomElements = 'customElements' in window;
    const supportsIntersectionObserver = 'IntersectionObserver' in window;

    mqcmpDebugLog('Progressive enhancement check:', {
        fluentCommunityAvailable,
        supportsCustomElements,
        supportsIntersectionObserver
    });

    if (fluentCommunityAvailable) {
        // Full integration with Fluent Community
        mqcmp_setup_fluent_community_integration();
    } else {
        // Standalone functionality
        mqcmp_setup_standalone_functionality();
    }

    // Apply enhancements based on browser capabilities
    if (supportsCustomElements) {
        mqcmpDebugLog('Custom elements supported, using enhanced features');
    } else {
        mqcmpDebugLog('Custom elements not supported, using fallback');
    }
}

// Fluent Community specific integration
function mqcmp_setup_fluent_community_integration() {
    mqcmpDebugLog('Setting up Fluent Community integration');

    // Listen for Fluent Community events
    document.addEventListener('fcom:route:changed', (e) => {
        mqcmpDebugLog('Fluent Community route changed:', e.detail);
        setTimeout(mqcmp_replace_all_found_iframes, 100);
    });

    document.addEventListener('fcom:feed:updated', (e) => {
        mqcmpDebugLog('Fluent Community feed updated:', e.detail);
        setTimeout(mqcmp_replace_all_found_iframes, 100);
    });

    // Initial replacement
    mqcmp_replace_all_found_iframes();
}

// Standalone functionality for non-Fluent Community environments
function mqcmp_setup_standalone_functionality() {
    mqcmpDebugLog('Setting up standalone functionality');
    mqcmp_replace_all_found_iframes();
}

// Initialize on DOM ready with progressive enhancement
document.addEventListener('DOMContentLoaded', () => {
    mqcmp_initialize_with_progressive_enhancement();
});

// Enhanced MutationObserver with debouncing for better performance
let mqcmp_mutation_timeout;
function mqcmp_observe_and_replace_activity_media() {
    const observer = new MutationObserver(() => {
        // Debounce mutations to prevent excessive processing
        clearTimeout(mqcmp_mutation_timeout);
        mqcmp_mutation_timeout = setTimeout(() => {
            mqcmp_replace_all_found_iframes();
        }, 250);
    });
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: false, // Don't watch attribute changes for performance
        characterData: false // Don't watch text changes for performance
    });
}
mqcmp_observe_and_replace_activity_media();

/**
 * Global initialization function that can be called manually
 * Added for troubleshooting and direct access from developer tools
 */
window.mqcmpInitialize = function() {
    mqcmpDebugLog('Manual initialization triggered');
    mqcmp_replace_all_found_iframes();
};

/**
 * Observe DOM for new activity items and run detection.
 */
function mqcmp_observe_activity_media() {
    const observer = new MutationObserver(() => {
        mqcmp_find_media_iframes();
    });
    observer.observe(document.body, { childList: true, subtree: true });
}

document.addEventListener('DOMContentLoaded', () => {
    mqcmp_find_media_iframes();
    mqcmp_observe_activity_media();
});

// Helper to apply accessibility options to controls
function mqcmp_accessibility_attrs(ctrl) {
    const accessibilityOptions = window.MQCMP_CONFIG?.accessibilityOptions || {};
    if (!accessibilityOptions || !accessibilityOptions.aria_labels) return '';
    const label = accessibilityOptions.aria_labels[ctrl];
    return label ? ` aria-label="${label}"` : '';
}

// Update mqcmp_render_controls to use ARIA labels and focus outline
function mqcmp_render_controls() {
    const playerLayout = window.MQCMP_CONFIG?.playerLayout || ['play', 'time', 'mute', 'volume', 'fullscreen'];
    if (!Array.isArray(playerLayout)) {
        return `
            <media-controls>
                <media-play-button${mqcmp_accessibility_attrs('play')}></media-play-button>
                <media-time></media-time>
                <media-mute-button${mqcmp_accessibility_attrs('mute')}></media-mute-button>
                <media-volume-range></media-volume-range>
                <media-fullscreen-button${mqcmp_accessibility_attrs('fullscreen')}></media-fullscreen-button>
            </media-controls>
        `;
    }
    let controls = '';
    playerLayout.forEach(ctrl => {
        switch (ctrl) {
            case 'play': controls += `<media-play-button${mqcmp_accessibility_attrs('play')}></media-play-button>`; break;
            case 'time': controls += '<media-time></media-time>'; break;
            case 'mute': controls += `<media-mute-button${mqcmp_accessibility_attrs('mute')}></media-mute-button>`; break;
            case 'volume': controls += '<media-volume-range></media-volume-range>'; break;
            case 'fullscreen': controls += `<media-fullscreen-button${mqcmp_accessibility_attrs('fullscreen')}></media-fullscreen-button>`; break;
            // Extend for more controls
        }
    });
    return `<media-controls>${controls}</media-controls>`;
}

// Add focus outline if enabled
if (window.MQCMP_CONFIG?.accessibilityOptions?.focus_outline === false) {
    const style = document.createElement('style');
    style.textContent = 'mqcmp-vidstack-player:focus { outline: none !important; }';
    document.head.appendChild(style);
}
