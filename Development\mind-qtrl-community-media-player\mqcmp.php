<?php
/**
 * Plugin Name: Mind Qtrl | Community Media Player (MQCMP)
 * Description: Replaces YouTube/Vimeo players in Fluent Community with a Vidstack player styled for Mind Qtrl.
 * Version: 0.4.0
 * Author: Mind Qtrl Team
 * Text Domain: mqcmp
 * Domain Path: /languages
 * License: GPL2+
 *
 * @package MQCMP
 */

/**
 * Enqueue Vidstack Player CDN and plugin assets on the frontend.
 */
function mqcmp_enqueue_assets() {
    // Only enqueue on frontend and when not in admin
    if (is_admin()) {
        return;
    }
    // Vidstack Player CDN (1.12.12)
    wp_enqueue_script(
        'mqcmp-vidstack',
        'https://cdn.vidstack.io/player@1.12.12',
        array(),
        null,
        true
    );
    // Add type="module" attribute to the Vidstack script
    add_filter('script_loader_tag', function($tag, $handle) {
        if ('mqcmp-vidstack' === $handle) {
            return str_replace('<script ', '<script type="module" ', $tag);
        }
        return $tag;
    }, 10, 2);
    // Main plugin JS (depends on Vidstack)
    wp_enqueue_script(
        'mqcmp-main',
        plugins_url('assets/js/mqcmp-main.js', __FILE__),
        array('mqcmp-vidstack'),
        '0.4.0',
        true
    );
    // Vidstack Theme CSS
    wp_enqueue_style(
        'mqcmp-vidstack-theme',
        'https://cdn.jsdelivr.net/npm/vidstack@1/dist/theme.css',
        array(),
        null
    );

    // Vidstack Default Layout Theme CSS
    wp_enqueue_style(
        'mqcmp-vidstack-default-layout',
        'https://cdn.jsdelivr.net/npm/vidstack@1/dist/layouts/default/theme.css',
        array('mqcmp-vidstack-theme'),
        null
    );

    // Main plugin CSS
    wp_enqueue_style(
        'mqcmp-main',
        plugins_url('assets/css/mqcmp-main.css', __FILE__),
        array('mqcmp-vidstack-theme', 'mqcmp-vidstack-default-layout'),
        '0.4.0'
    );
}
add_action('wp_enqueue_scripts', 'mqcmp_enqueue_assets');

/**
 * Expose Fluent Community CSS variables to JS for theming.
 *
 * @return void
 */
function mqcmp_expose_fcom_css_vars() {
    // Example: get variables from Fluent Community (adjust as needed)
    $css_vars = array(
        '--fcom-primary-color'   => '#3498db',
        '--fcom-bg-color'        => '#fff',
        '--fcom-text-color'      => '#222',
        '--fcom-border-radius'   => '6px',
        '--fcom-font-family'     => 'inherit',
        // Add more as needed
    );
    // Allow filtering for future expansion
    $css_vars = apply_filters('mqcmp_fcom_css_vars', $css_vars);
    wp_localize_script('mqcmp-main', 'MQCMP_FCOM_CSS_VARS', $css_vars);
}
add_action('wp_enqueue_scripts', 'mqcmp_expose_fcom_css_vars', 11);

/**
 * Expose AJAX URL to JS for MQCMP operations.
 *
 * @return void
 */
function mqcmp_localize_ajax_url() {
    // Ensure the main script is enqueued before localizing
    if (wp_script_is('mqcmp-main', 'enqueued')) {
        wp_localize_script('mqcmp-main', 'MQCMP_AJAX_URL', admin_url('admin-ajax.php'));
    }
}
add_action('wp_enqueue_scripts', 'mqcmp_localize_ajax_url', 12);

/**
 * Filter to allow customizing which activity types/locations the player is enabled for.
 *
 * @param array $contexts List of enabled contexts (CSS selectors or types)
 * @return array
 */
function mqcmp_enabled_contexts_filter($contexts) {
    // Default: all activity items
    return apply_filters('mqcmp_enabled_contexts', $contexts);
}

// Expose enabled contexts to JS
add_action('wp_enqueue_scripts', function() {
    $contexts = mqcmp_enabled_contexts_filter(['.fcom-activity-item', '.feed_media_ext_video']);
    wp_localize_script('mqcmp-main', 'MQCMP_ENABLED_CONTEXTS', $contexts);
}, 12);

/**
 * Filter to allow customizing which video providers are supported.
 *
 * @param array $providers List of supported providers (e.g., youtube, vimeo)
 * @return array
 */
function mqcmp_supported_providers_filter($providers) {
    return apply_filters('mqcmp_supported_providers', $providers);
}

// Expose supported providers to JS
add_action('wp_enqueue_scripts', function() {
    $providers = mqcmp_supported_providers_filter(['youtube', 'vimeo']);
    wp_localize_script('mqcmp-main', 'MQCMP_SUPPORTED_PROVIDERS', $providers);
}, 13);

/**
 * Filter to allow customizing the player layout and controls.
 *
 * @param array $layout Player layout config (array of control names)
 * @return array
 */
function mqcmp_player_layout_filter($layout) {
    return apply_filters('mqcmp_player_layout', $layout);
}

// Expose player layout to JS
add_action('wp_enqueue_scripts', function() {
    $layout = mqcmp_player_layout_filter([
        'play', 'time', 'mute', 'volume', 'fullscreen'
    ]);
    wp_localize_script('mqcmp-main', 'MQCMP_PLAYER_LAYOUT', $layout);
}, 14);

/**
 * Filter to allow customizing player accessibility options.
 *
 * @param array $options Accessibility options (e.g., focus_outline, aria_labels)
 * @return array
 */
function mqcmp_accessibility_options_filter($options) {
    return apply_filters('mqcmp_accessibility_options', $options);
}

// Expose accessibility options to JS
add_action('wp_enqueue_scripts', function() {
    $options = mqcmp_accessibility_options_filter([
        'focus_outline' => true,
        'aria_labels' => [
            'play' => __('Play video', 'mqcmp'),
            'mute' => __('Mute', 'mqcmp'),
            'fullscreen' => __('Fullscreen', 'mqcmp'),
        ],
        'keyboard_navigation' => true
    ]);
    wp_localize_script('mqcmp-main', 'MQCMP_ACCESSIBILITY_OPTIONS', $options);
}, 15);

/**
 * Filter to allow registering custom video providers.
 *
 * @param array $providers Array of custom providers (id, match_regex, thumbnail_callback)
 * @return array
 */
function mqcmp_custom_providers_filter($providers) {
    return apply_filters('mqcmp_custom_providers', $providers);
}

// Expose custom providers to JS
add_action('wp_enqueue_scripts', function() {
    $providers = mqcmp_custom_providers_filter([]);
    wp_localize_script('mqcmp-main', 'MQCMP_CUSTOM_PROVIDERS', $providers);
}, 16);

/**
 * Filter to allow injecting custom JS/CSS for advanced use cases.
 *
 * @param string $custom_code Custom JS/CSS code
 * @return string
 */
function mqcmp_custom_js_css_filter($custom_code) {
    return apply_filters('mqcmp_custom_js_css', $custom_code);
}

// Output custom JS/CSS in the page footer when plugin is active
add_action('wp_footer', function() {
    $custom_code = mqcmp_custom_js_css_filter('');
    if ($custom_code) {
        echo $custom_code;
    }
}, 99);

/**
 * Get the best available YouTube HD thumbnail URL.
 *
 * @param string $video_url_or_id YouTube video URL or ID.
 * @return string|false Thumbnail URL or false on failure.
 */
function mqcmp_get_youtube_thumbnail_url($video_url_or_id) {
    // Extract video ID from URL or use as-is
    if (preg_match('~(?:youtu\.be/|youtube\.com/(?:embed/|v/|watch\?v=|watch\?.+&v=))([\w-]{11})~', $video_url_or_id, $matches)) {
        $video_id = $matches[1];
    } elseif (preg_match('/^[\w-]{11}$/', $video_url_or_id)) {
        $video_id = $video_url_or_id;
    } else {
        return false;
    }
    $thumbs = [
        "https://img.youtube.com/vi/{$video_id}/maxresdefault.jpg",
        "https://img.youtube.com/vi/{$video_id}/sddefault.jpg",
        "https://img.youtube.com/vi/{$video_id}/hqdefault.jpg",
        "https://img.youtube.com/vi/{$video_id}/mqdefault.jpg",
        "https://img.youtube.com/vi/{$video_id}/default.jpg",
    ];
    foreach ($thumbs as $url) {
        $headers = @get_headers($url);
        if ($headers && strpos($headers[0], '200') !== false) {
            return $url;
        }
    }
    return false;
}

/**
 * Get the best available Vimeo HD thumbnail URL.
 *
 * @param string $video_url_or_id Vimeo video URL or ID.
 * @return string|false Thumbnail URL or false on failure.
 */
function mqcmp_get_vimeo_thumbnail_url($video_url_or_id) {
    // Extract video ID from URL or use as-is
    if (preg_match('~vimeo\.com/(?:video/)?(\d+)~', $video_url_or_id, $matches)) {
        $video_id = $matches[1];
    } elseif (preg_match('/^\d+$/', $video_url_or_id)) {
        $video_id = $video_url_or_id;
    } else {
        return false;
    }
    $oembed_url = 'https://vimeo.com/api/oembed.json?url=' . urlencode('https://vimeo.com/' . $video_id);
    $response = wp_remote_get($oembed_url);
    if (is_wp_error($response)) {
        return false;
    }
    $data = json_decode(wp_remote_retrieve_body($response), true);
    if (!isset($data['thumbnail_url'])) {
        return false;
    }
    // Try to get the highest resolution thumbnail
    $thumb_url = $data['thumbnail_url'];
    // Vimeo thumbnails often end with _XXXxYYY.jpg, try to get the largest
    $hd_thumb = preg_replace('/_(\d+x\d+)\.jpg$/', '_1280x720.jpg', $thumb_url);
    // Check if HD version exists
    $headers = @get_headers($hd_thumb);
    if ($headers && strpos($headers[0], '200') !== false) {
        return $hd_thumb;
    }
    return $thumb_url;
}

/**
 * AJAX handler for fetching video thumbnails (YouTube/Vimeo).
 */
function mqcmp_ajax_get_thumbnail() {
    $video_url = isset($_REQUEST['video_url']) ? sanitize_text_field($_REQUEST['video_url']) : '';
    $video_type = isset($_REQUEST['video_type']) ? sanitize_text_field($_REQUEST['video_type']) : '';
    $thumb_url = false;
    if ($video_type === 'youtube') {
        $thumb_url = mqcmp_get_youtube_thumbnail_url($video_url);
    } elseif ($video_type === 'vimeo') {
        $thumb_url = mqcmp_get_vimeo_thumbnail_url($video_url);
    }
    if ($thumb_url) {
        wp_send_json_success(['thumbnail_url' => $thumb_url]);
    } else {
        wp_send_json_error(['message' => __('Thumbnail not found', 'mqcmp')]);
    }
}
add_action('wp_ajax_mqcmp_get_thumbnail', 'mqcmp_ajax_get_thumbnail');
add_action('wp_ajax_nopriv_mqcmp_get_thumbnail', 'mqcmp_ajax_get_thumbnail');

// --- HYBRID FLUENT COMMUNITY PORTAL INTEGRATION ---
// Output config and scripts for both classic WP and Fluent Community portal
add_action('wp_footer', 'mqcmp_output_portal_config_and_scripts', 99);
add_action('fluent_community/portal_footer', 'mqcmp_output_portal_config_and_scripts', 99);

function mqcmp_output_portal_config_and_scripts() {
    // Output config as a global JS object (not via wp_localize_script)
    ?>    <script>
    window.MQCMP_CONFIG = {
        ajaxUrl: '<?php echo esc_url(admin_url('admin-ajax.php')); ?>',
        fcomCssVars: <?php echo json_encode(apply_filters('mqcmp_fcom_css_vars', array(
            '--fcom-primary-color'   => '#3498db',
            '--fcom-bg-color'        => '#fff',
            '--fcom-text-color'      => '#222',
            '--fcom-border-radius'   => '6px',
            '--fcom-font-family'     => 'inherit',
        ))); ?>,
        enabledContexts: <?php echo json_encode(apply_filters('mqcmp_enabled_contexts', ['.fcom-activity-item', '.feed_media_ext_video'])); ?>,
        supportedProviders: <?php echo json_encode(apply_filters('mqcmp_supported_providers', ['youtube', 'vimeo'])); ?>,
        playerLayout: <?php echo json_encode(apply_filters('mqcmp_player_layout', ['play', 'time', 'mute', 'volume', 'fullscreen'])); ?>,
        accessibilityOptions: <?php
            $accessibility_options = apply_filters('mqcmp_accessibility_options', [
                'focus_outline' => true,
                'aria_labels' => [
                    'play' => __('Play video', 'mqcmp'),
                    'mute' => __('Mute', 'mqcmp'),
                    'fullscreen' => __('Fullscreen', 'mqcmp'),
                ],
                'keyboard_navigation' => true
            ]);
            // Ensure debug is explicitly set as boolean
            $debug_enabled = (bool) get_option('mqcmp_debug_logging', false);
            $accessibility_options['debug'] = $debug_enabled;
            echo json_encode($accessibility_options);
        ?>,
        // Add direct debug property for easier access
        debug: <?php echo $debug_enabled ? 'true' : 'false'; ?>,
        // Add fallback setting
        enableFallback: <?php echo (bool) get_option('mqcmp_enable_fallback', true) ? 'true' : 'false'; ?>,
        // Control whether to skip invisible iframes (default: false to ensure hidden iframes are replaced)
        skipInvisibleIframes: <?php echo (bool) get_option('mqcmp_skip_invisible_iframes', false) ? 'true' : 'false'; ?>
    };
    </script>    <script type="module" src="<?php echo esc_url(plugins_url('assets/js/mqcmp-main.js', __FILE__)); ?>?ver=0.4.0"></script>
    <link rel="stylesheet" href="<?php echo esc_url(plugins_url('assets/css/mqcmp-main.css', __FILE__)); ?>?ver=0.4.0" />
    <script type="module" src="https://cdn.vidstack.io/player@1.12.12"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vidstack@1/dist/theme.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vidstack@1/dist/layouts/default/theme.css" />
    <?php
}

// --- Admin Menu: Mind Qtrl (conditional) and Media Player submenu ---
add_action('admin_menu', 'mqcmp_add_admin_menu');
function mqcmp_add_admin_menu() {
    global $submenu;
    $parent_slug = 'mind-qtrl-admin';
    // If the main Mind Qtrl menu doesn't exist, create it
    if (!isset($submenu[$parent_slug])) {
        add_menu_page(
            __('Mind Qtrl', 'mqcmp'),
            __('Mind Qtrl', 'mqcmp'),
            'manage_options',
            $parent_slug,
            '',
            '',
            30
        );
    }
    // Add Media Player submenu
    add_submenu_page(
        $parent_slug,
        __('Community Media Player', 'mqcmp'),
        __('Media Player', 'mqcmp'),
        'manage_options',
        'mqcmp-settings',
        'mqcmp_render_settings_page'
    );
}

// --- Settings: Debug Logging Toggle and Player Settings ---
add_action('admin_init', 'mqcmp_register_settings');
function mqcmp_register_settings() {
    // Debug logging setting
    register_setting('mqcmp_settings_group', 'mqcmp_debug_logging', [
        'type' => 'boolean',
        'default' => false,
        'sanitize_callback' => 'rest_sanitize_boolean',
    ]);

    // Fallback to original player setting
    register_setting('mqcmp_settings_group', 'mqcmp_enable_fallback', [
        'type' => 'boolean',
        'default' => true,
        'sanitize_callback' => 'rest_sanitize_boolean',
    ]);

    // Skip invisible iframes setting
    register_setting('mqcmp_settings_group', 'mqcmp_skip_invisible_iframes', [
        'type' => 'boolean',
        'default' => false,
        'sanitize_callback' => 'rest_sanitize_boolean',
    ]);
}

function mqcmp_render_settings_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('Mind Qtrl Community Media Player Settings', 'mqcmp'); ?></h1>

        <div class="mqco" style="background: #1B1B1E; color: #E1E1E6; padding: 20px; border-radius: 5px; margin-top: 20px;">
            <form method="post" action="options.php">
                <?php settings_fields('mqcmp_settings_group'); ?>
                <?php do_settings_sections('mqcmp_settings_group'); ?>

                <h2><?php _e('Debug Settings', 'mqcmp'); ?></h2>
                <p><?php _e('These settings help troubleshoot issues with the video player.', 'mqcmp'); ?></p>

                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php _e('Enable Browser Console Debug Logging', 'mqcmp'); ?></th>
                        <td>
                            <input type="checkbox" name="mqcmp_debug_logging" value="1" <?php checked(1, get_option('mqcmp_debug_logging', false)); ?> />
                            <label for="mqcmp_debug_logging"><?php _e('Show debug logs in browser console for troubleshooting.', 'mqcmp'); ?></label>
                            <p class="description"><?php _e('This will output detailed logs to the browser console to help troubleshoot player issues.', 'mqcmp'); ?></p>
                        </td>
                    </tr>
                </table>

                <h2><?php _e('Player Settings', 'mqcmp'); ?></h2>
                <p><?php _e('Configure how the video player behaves and appears.', 'mqcmp'); ?></p>

                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php _e('Fallback to Original Player', 'mqcmp'); ?></th>
                        <td>
                            <input type="checkbox" name="mqcmp_enable_fallback" value="1" <?php checked(1, get_option('mqcmp_enable_fallback', true)); ?> />
                            <label for="mqcmp_enable_fallback"><?php _e('Automatically revert to original player if Vidstack fails to load.', 'mqcmp'); ?></label>
                            <p class="description"><?php _e('If the Vidstack player fails to initialize within 5 seconds, the original iframe will be restored.', 'mqcmp'); ?></p>
                        </td>
                    </tr>
                    <tr valign="top">
                        <th scope="row"><?php _e('Skip Invisible Iframes', 'mqcmp'); ?></th>
                        <td>
                            <input type="checkbox" name="mqcmp_skip_invisible_iframes" value="1" <?php checked(1, get_option('mqcmp_skip_invisible_iframes', false)); ?> />
                            <label for="mqcmp_skip_invisible_iframes"><?php _e('Skip iframes that are not visible when first detected.', 'mqcmp'); ?></label>
                            <p class="description"><?php _e('By default, this option is disabled to ensure all iframes are replaced regardless of visibility. Enable this option only if you want to skip invisible iframes, which may cause issues with hidden YouTube/Vimeo embeds.', 'mqcmp'); ?></p>
                        </td>
                    </tr>
                </table>

                <?php submit_button(); ?>
            </form>
        </div>

        <div class="mqco" style="background: #1B1B1E; color: #E1E1E6; padding: 20px; border-radius: 5px; margin-top: 20px;">
            <h2><?php _e('Troubleshooting', 'mqcmp'); ?></h2>
            <p><?php _e('If you\'re experiencing issues with the video player, try these steps:', 'mqcmp'); ?></p>
            <ol>
                <li><?php _e('Enable debug logging above and check your browser console for errors.', 'mqcmp'); ?></li>
                <li><?php _e('Make sure your theme is not conflicting with the player styles.', 'mqcmp'); ?></li>
                <li><?php _e('Try disabling other plugins that might be affecting video embeds.', 'mqcmp'); ?></li>
                <li><?php _e('Check if your browser is up to date and supports modern web standards.', 'mqcmp'); ?></li>
            </ol>

            <p><?php _e('You can also manually trigger player initialization by running this in your browser console:', 'mqcmp'); ?></p>
            <code style="display: block; background: #2D2D30; padding: 10px; margin: 10px 0; color: #D7BA7D;">window.mqcmpInitialize()</code>
        </div>
    </div>
    <?php
}

// --- Output debug flag to frontend config ---
add_filter('mqcmp_accessibility_options', function($options) {
    $debug_enabled = (bool) get_option('mqcmp_debug_logging', false);
    $options['debug'] = $debug_enabled ? true : false; // Ensure boolean type, not string
    return $options;
});