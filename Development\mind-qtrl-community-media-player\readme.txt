=== Mind Qtrl | Community Media Player (MQCMP) ===
Contributors: mindqtrl
Tags: video, media, player, community, vidstack, youtube, vimeo, fluent community
Requires at least: 5.8
Tested up to: 6.5
Stable tag: 0.1.14
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

== Description ==
Replaces YouTube/Vimeo players in Fluent Community with a Vidstack player styled for Mind Qtrl. Modular, extensible, and ready for internationalization.

== Installation ==
1. Upload the plugin files to the `/wp-content/plugins/mqcmp` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.

== Changelog ==
= 0.1.14 =
* Fixed critical issue with poster images not displaying
* Simplified player initialization to focus on custom elements approach
* Added explicit CSS styles to ensure poster images are visible
* Removed JavaScript API approach to avoid conflicts
* Enhanced poster image styling with proper z-index and positioning
* Improved player initialization reliability
* Fixed CSS issues with poster image display

= 0.1.13 =
* Implemented hybrid approach combining JavaScript API and custom elements
* Added support for Vidstack Player JavaScript API for more reliable initialization
* Enhanced provider initialization with explicit source types
* Improved event handling with comprehensive event listeners
* Added fallback mechanism between JavaScript API and custom elements
* Fixed issues with YouTube and Vimeo provider initialization
* Added detailed logging for better troubleshooting
= 0.1.12 =
* Fixed critical issue with Vidstack player initialization
* Improved provider initialization with explicit source elements
* Enhanced event handling for better compatibility
* Added additional event listeners for more robust player detection
* Improved error handling and reporting
= 0.1.11 =
* Fixed critical issue with YouTube/Vimeo players not being replaced in the feed
* Changed default behavior to process all iframes regardless of visibility
* Added new setting to control iframe visibility detection
* Improved compatibility with CSS that hides iframes
* Enhanced player detection for better reliability
= 0.1.9 =
* Downgraded Vidstack CDN to version 1.12.12 for improved compatibility
* Maintained dual-source attribute approach for better player rendering
* Enhanced integration with Fluent Community based on hybrid approach
* Improved overall stability and performance
= 0.1.8 =
* Fixed critical issue with Vidstack player not displaying
* Improved compatibility with Vidstack library version 1.12.13
* Updated player initialization to ensure proper rendering
* Enhanced player configuration for better compatibility
* Maintained all improvements from previous versions
= 0.1.7 =
* Added improved support for custom video providers
* Enhanced error handling with specific error messages for different error types
* Implemented automatic retry mechanism with configurable retry count
* Added progressive retry delays for network errors
* Improved accessibility with ARIA labels, keyboard navigation, and high contrast mode support
* Added focus indicators for better keyboard navigation
* Enhanced error messages with more detailed information
* Added support for custom provider detection

= 0.1.6 =
* Fixed critical issue with Vidstack player not displaying after update
* Updated provider configuration to set src attribute on media-provider element
* Added more detailed error logging for better troubleshooting
* Enhanced player styling with improved CSS for better display
* Added comprehensive error handling with detailed error messages
* Improved player initialization and event handling
= 0.1.5 =
* Fixed YouTube and Vimeo provider configuration for better compatibility
* Added proper error handling with user-friendly error messages
* Added fallback mechanism to revert to original player if Vidstack fails to load
* Enhanced admin settings page with troubleshooting information
* Added option to enable/disable fallback to original player
* Fixed CSS styling conflicts with Fluent Community
* Improved player initialization and error detection
* Added comprehensive error overlay with retry functionality
* Fixed z-index and positioning issues for better display

= 0.1.4 =
* Added Vidstack theme CSS files to fix missing player controls
* Added unique IDs to each player instance for better management
* Improved iframe replacement logic to prevent duplicate players
* Added CSS to hide any remaining YouTube iframes
* Fixed z-index issues to ensure player is displayed correctly
* Enhanced player styling for better visual appearance

= 0.1.3 =
* Fixed issue where YouTube embeds weren't being properly replaced
* Added support for videos in .feed_media_ext_video containers
* Enhanced debug logging functionality for easier troubleshooting
* Added global initialization function (window.mqcmpInitialize)
* Performance optimizations and code cleanup

= 0.1.2 =
* Added admin interface with debug logging toggle
* Added hybrid Fluent Community portal integration
* Added conditional console logging in JavaScript
* Made Mind Qtrl menu conditional with Media Player submenu

= 0.1.1 =
* Codebase checked for errors and updated for compatibility.
* Version bump and changelog update for maintenance release.

= 0.1.0 =
* Initial release.

== Upgrade Notice ==
= 0.1.14 =
This update fixes a critical issue with poster images not displaying. It simplifies the player initialization to focus on the custom elements approach, adds explicit CSS styles to ensure poster images are visible, and improves player initialization reliability. Highly recommended for all users.

= 0.1.13 =
This update implements a hybrid approach combining the JavaScript API and custom elements for more reliable player initialization. It adds support for the Vidstack Player JavaScript API, enhances provider initialization with explicit source types, and improves event handling with comprehensive event listeners. Highly recommended for all users.
= 0.1.12 =
This update fixes critical issues with Vidstack player initialization. It improves provider initialization with explicit source elements, enhances event handling for better compatibility, and adds additional event listeners for more robust player detection. Highly recommended for all users.
= 0.1.11 =
This update fixes a critical issue with YouTube/Vimeo players not being replaced in the feed. It changes the default behavior to process all iframes regardless of visibility, which significantly improves compatibility with CSS that hides iframes. Highly recommended for all users.
= 0.1.9 =
This update downgrades the Vidstack CDN to version 1.12.12 for improved compatibility while maintaining all the improvements from previous versions. It also enhances integration with Fluent Community based on the recommended hybrid approach.
= 0.1.8 =
This update fixes a critical issue with the Vidstack player not displaying. It improves compatibility with the Vidstack library while maintaining all the improvements from previous versions. Highly recommended for all users.
= 0.1.7 =
This update adds improved support for custom video providers, enhanced error handling with automatic retries, and better accessibility features. Recommended for all users.

= 0.1.6 =
This update fixes a critical issue with the Vidstack player not displaying after the latest update. It also improves error handling and player styling.
= 0.1.5 =
This update significantly improves video playback reliability with better provider configuration, error handling, and fallback mechanisms. It also enhances the admin interface with troubleshooting tools.

= 0.1.4 =
This update fixes missing player controls and improves the overall appearance and functionality of the video player.

= 0.1.3 =
This update fixes YouTube embed replacement in feed pages and adds better debugging tools.

= 0.1.0 =
Initial release.

== Frequently Asked Questions ==

== Screenshots ==

== Other Notes ==
