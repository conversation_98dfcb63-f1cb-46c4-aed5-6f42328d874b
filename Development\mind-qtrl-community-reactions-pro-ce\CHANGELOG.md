# Changelog

All notable changes to the Mind Qtrl | Community Reactions Pro CE plugin will be documented in this file.

## 0.1.8 (2025-04-25)

### Fixed
- Fixed issue with custom elements not being properly registered in the DOM
- Fixed timing issues with script loading and initialization
- Fixed error that occurred when clicking reaction buttons
- Fixed issue with reaction box not appearing on hover

### Improved
- Enhanced script loading with proper module dependencies
- Added fallback for browsers that don't support ES modules
- Improved initialization with retry logic and better error handling
- Added global namespace for custom elements to ensure proper registration
- Enhanced debugging and logging for easier troubleshooting

## 0.1.7 (2025-04-15)

### Added
- Pure Custom Elements implementation as the default approach
- Support for custom reaction types with images
- Improved tooltip display for reaction types
- 3D flip transition animation when selecting a reaction type

### Fixed
- Fixed positioning of reaction boxes in modals and single activity pages
- Fixed issue with multiple reaction boxes appearing simultaneously

## 0.1.6 (2025-04-01)

### Added
- Option to sync colors with the first 'like' reaction
- Improved positioning of reaction boxes
- Support for custom glow colors for each reaction type

### Fixed
- Fixed issue with reaction boxes not appearing in some browsers
- Fixed z-index issues with reaction boxes

## 0.1.5 (2025-03-15)

### Added
- Initial implementation of Custom Elements approach
- Support for custom reaction types
- Configurable delay for reaction box display
- Horizontal alignment of reaction boxes with parent container

### Fixed
- Fixed compatibility issues with Fluent Community
- Fixed styling issues with reaction boxes
