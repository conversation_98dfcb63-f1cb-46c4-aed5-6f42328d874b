# Mind Qtrl | Community Reactions Pro CE

A WordPress plugin that enhances Fluent Community with advanced reaction functionality using Web Components (Custom Elements).

## Description

Mind Qtrl | Community Reactions Pro CE seamlessly integrates with the Fluent Community plugin to provide enhanced reaction functionality using modern Web Components (Custom Elements). This approach provides better encapsulation, reusability, and performance compared to traditional JavaScript integration. It allows site administrators to replace the default heart icon with custom images, add additional reaction types with custom names and images, and customize the appearance of reactions throughout the community portal.

## Features

- Replace the default heart icon with a custom image (recommended size: 32x32px)
- Upload and manage custom images for each reaction type
- Add up to 7 custom reaction types with customizable names (love, haha, wow, sad, angry, etc.)
- Edit reaction type names to display in custom tooltips
- Drag and drop to reorder custom reaction types
- Add or delete custom reaction types with an intuitive interface
- Di<PERSON><PERSON> who reacted to posts with custom tooltips
- Modern dark admin interface with Mind Qtrl branding
- Enhanced hover effects for reaction buttons
- Hover reaction box with custom reaction types
- Synchronized hover color with reaction type glow colors
- Seamless integration with Fluent Community

## Requirements

- WordPress 5.0 or higher
- Fluent Community plugin must be active

## Installation

1. Upload the `mind-qtrl-community-reactions-pro-ce` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure the plugin settings via the 'Mind QTRL > Reactions Pro CE' menu in the WordPress admin

## Usage

After activating the plugin, navigate to the 'Mind QTRL > Reactions Pro CE' menu in the WordPress admin to configure the plugin settings:

### Basic Settings

1. **Replace Like Icon**: Toggle to replace the default heart icon with a custom image
2. **Custom Like Icon**: Upload a custom image (recommended size: 32x32px) to replace the default heart icon
3. **Improve Hover Effects**: Toggle to enable enhanced hover effects for reaction buttons
4. **Hover Effect Color**: Choose the color for hover effects (automatically syncs with the first reaction type's glow color when Custom Reaction Types are enabled)
5. **Remove Hover Background**: Toggle to remove the background color that appears when hovering over reaction buttons
6. **Hide Loading Error Text**: Toggle to hide the "Failed to load the list" text that briefly flashes when loading reaction user lists
7. **Use Pure Custom Elements**: Toggle to use a pure Custom Elements approach that completely replaces Fluent Community reaction elements instead of enhancing them
8. **Change Delay Time**: Toggle to customize the delay time before the reaction box disappears after mouse leaves
9. **Customize Tooltips**: Toggle to customize tooltip appearance for reaction types

### Custom Reaction Types

1. **Enable Custom Reaction Types**: Toggle to enable additional reaction types with custom images and names
2. **Reaction Type Settings**: Configure each reaction type:
   - Toggle to enable/disable each reaction type
   - Upload custom images for each reaction type (recommended size: 32x32px)
   - Edit reaction type names by clicking the edit icon
   - Choose custom glow colors for each reaction type
   - Drag and drop to reorder reaction types (except the main Like reaction)
   - Add new reaction types (up to 7 custom types) with the "Add New Reaction Type" button
   - Delete custom reaction types by clicking the close icon in the top-right corner

## Integration with Fluent Community

The plugin seamlessly integrates with Fluent Community's frontend portal using Web Components (Custom Elements), enhancing the reaction functionality without modifying core files. This approach provides better encapsulation, reusability, and performance compared to traditional JavaScript integration.

### Integration Methods

- Uses Web Components (Custom Elements) for better encapsulation and reusability
- Leverages Shadow DOM for style isolation and encapsulation
- Implements custom events for communication between components
- Maintains compatibility with Fluent Community updates
- Provides fallbacks for browsers that don't support Custom Elements

### How It Works

1. **Custom Like Icon**: Replaces the default heart icon with your custom image
2. **Custom Reaction Types**: Adds tooltips with custom names for each reaction type
3. **Hover Effects**: Enhances the user experience with smooth hover transitions
4. **Hover Reaction Box**: Displays all available reaction types when hovering over reaction buttons
5. **Tooltips**: Shows the reaction name when hovering over reaction buttons
6. **Targeted Selection**: Only affects actual Fluent Community like/reaction buttons
7. **Drag-and-Drop Ordering**: Allows intuitive reordering of custom reaction types
8. **Flexible Management**: Add or delete custom reaction types as needed

## Customization

The plugin provides extensive customization options through the admin interface. You can:

- Upload custom images for each reaction type
- Edit reaction type names
- Choose custom glow colors for each reaction type
- Enable/disable specific reaction types
- Customize hover effects and colors
- Reorder reaction types with drag-and-drop
- Add new reaction types (up to 7 custom types)
- Delete unwanted reaction types

## Changelog

### 0.1.8 - April 25, 2025
- Fixed issue with custom elements not being properly registered in the DOM
- Fixed timing issues with script loading and initialization
- Fixed error that occurred when clicking reaction buttons
- Fixed issue with reaction box not appearing on hover
- Enhanced script loading with proper module dependencies
- Added fallback for browsers that don't support ES modules
- Improved initialization with retry logic and better error handling
- Added global namespace for custom elements to ensure proper registration
- Enhanced debugging and logging for easier troubleshooting

### 0.1.7 - April 15, 2025
- Pure Custom Elements implementation as the default approach
- Support for custom reaction types with images
- Improved tooltip display for reaction types
- 3D flip transition animation when selecting a reaction type
- Fixed positioning of reaction boxes in modals and single activity pages
- Fixed issue with multiple reaction boxes appearing simultaneously

### 0.1.6 - April 1, 2025
- Option to sync colors with the first 'like' reaction
- Improved positioning of reaction boxes
- Support for custom glow colors for each reaction type
- Fixed issue with reaction boxes not appearing in some browsers
- Fixed z-index issues with reaction boxes

### 0.1.5 - March 15, 2025
- Replaced background-image method with img elements for custom reaction icons
- Added 3D flip animation for reaction icon transitions
- Improved icon display with proper HTML structure
- Enhanced accessibility with alt text for reaction images
- Fixed icon transitions when switching between reaction types
- Added specific CSS for icon images
- Improved compatibility with different browsers
- Enhanced performance by using native img elements
- Better support for high-resolution displays
- Simplified icon replacement logic

### 0.1.4 - May 29, 2025
- Fixed reaction box not appearing when hovering over mqcrp-custom-icon elements
- Added special handling for custom icons to ensure proper reaction box display
- Enhanced CSS selectors to target both standard and custom icon elements
- Improved event delegation for custom icon elements
- Added proper wrapping of custom icon elements in mqcrp-reaction-parent containers
- Enhanced positioning.js to handle custom icon elements
- Fixed transform properties to maintain proper alignment for custom icons
- Added direct event listeners for custom icon hover events
- Improved reaction box visibility with better CSS targeting
- Enhanced reaction box positioning for custom icons

### 0.1.3 - May 28, 2025
- Updated reaction box positioning to align left side with parent element
- Added arrow indicator pointing down from the reaction box
- Fixed reaction box positioning in modals and single activity pages
- Added special handling for elements with overflow:hidden
- Improved positioning system with better left alignment
- Enhanced dark mode support for the arrow indicator
- Added positioning.js file for special positioning cases
- Fixed transform properties to maintain proper left alignment
- Improved z-index handling for better stacking context
- Added subtle animation for smoother appearance

### 0.1.2 - May 27, 2025
- Updated WordPress admin menu structure to match the original plugin
- Fixed "Reactions Pro CE" submenu item to display as "Reactions Pro" under the Mind Qtrl menu
- Improved admin CSS to target both toplevel and submenu pages
- Fixed all form field names from mqcrp_settings to mqcrpce_settings for proper settings storage
- Enhanced admin interface with consistent styling for both menu configurations
- Fixed reaction box not appearing when hovering over mqcrp-custom-icon elements
- Added special handling for custom icons to ensure proper reaction box display
- Improved event delegation for custom icon elements
- Enhanced CSS for better visibility and interaction with custom icons

### 0.1.1 - May 26, 2025
- Fixed constant naming consistency by replacing MQCRP_PLUGIN_URL with MQCRPCE_PLUGIN_URL in public CSS loading
- Updated MQCRP_VERSION references to MQCRPCE_VERSION in admin display for proper version display
- Improved plugin stability with consistent constant naming throughout the codebase
- Enhanced compatibility when running alongside the standard version of the plugin

### 0.1.0 - May 25, 2025
- Initial release of the Custom Elements version
- Implemented Web Components architecture for better encapsulation
- Added Shadow DOM for style isolation
- Created custom elements for reaction functionality
- Improved performance with more efficient DOM operations
- Enhanced browser compatibility with polyfills
- Added custom events for component communication
- Improved accessibility with ARIA attributes
- Implemented declarative templates for better maintainability
- Added proper lifecycle hooks for custom elements
