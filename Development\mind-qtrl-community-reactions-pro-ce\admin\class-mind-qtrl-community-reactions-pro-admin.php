<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @since      0.1.0
 * @package    Mind_Qtrl_Community_Reactions_Pro_CE
 * @subpackage Mind_Qtrl_Community_Reactions_Pro_CE/admin
 */

class Mind_Qtrl_Community_Reactions_Pro_Admin {

    /**
     * The ID of this plugin.
     *
     * @since    0.0.1
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    0.0.1
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    0.0.1
     * @updated  0.0.5
     * @param      string    $plugin_name       The name of this plugin.
     * @param      string    $version    The version of this plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    0.1.0
     */
    public function enqueue_styles() {
        // Enqueue admin styles
        wp_enqueue_style( $this->plugin_name, MQCRPCE_PLUGIN_URL . 'admin/css/mind-qtrl-community-reactions-pro-admin.css', array(), $this->version, 'all' );

        // Enqueue WordPress color picker
        wp_enqueue_style( 'wp-color-picker' );

        // Enqueue WordPress notifications
        wp_enqueue_style( 'wp-admin' );
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    0.1.0
     */
    public function enqueue_scripts() {
        // Enqueue WordPress media uploader
        wp_enqueue_media();

        // Enqueue WordPress color picker
        wp_enqueue_script('wp-color-picker');

        // Enqueue jQuery UI for sortable functionality
        wp_enqueue_script('jquery-ui-sortable');

        // Enqueue admin script
        wp_enqueue_script( $this->plugin_name, MQCRPCE_PLUGIN_URL . 'admin/js/mind-qtrl-community-reactions-pro-admin.js', array( 'jquery', 'wp-color-picker', 'jquery-ui-sortable' ), $this->version, false );

        // Pass AJAX URL and nonce to script
        wp_localize_script( $this->plugin_name, 'mqcrp_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mqcrp_save_settings'),
            'success_message' => __('Settings saved successfully!', 'mind-qtrl-community-reactions-pro-ce'),
            'error_message' => __('Error saving settings. Please try again.', 'mind-qtrl-community-reactions-pro-ce')
        ));
    }

    /**
     * Add menu item for the plugin.
     *
     * @since    0.0.1
     * @updated  0.1.1
     */
    public function add_plugin_admin_menu() {
        // Check if the Mind Qtrl menu already exists
        global $menu, $submenu;
        $mind_qtrl_exists = false;

        foreach ($menu as $item) {
            if (isset($item[2]) && $item[2] === 'mind-qtrl-admin') {
                $mind_qtrl_exists = true;
                break;
            }
        }

        if ($mind_qtrl_exists) {
            // Check if our submenu already exists to prevent duplicates
            $submenu_exists = false;
            if (isset($submenu['mind-qtrl-admin'])) {
                foreach ($submenu['mind-qtrl-admin'] as $item) {
                    if (isset($item[2]) && $item[2] === 'mind-qtrl-community-reactions-pro-ce') {
                        $submenu_exists = true;
                        break;
                    }
                }
            }

            // Only add if it doesn't exist yet
            if (!$submenu_exists) {
                // Add as submenu to existing Mind Qtrl menu
                add_submenu_page(
                    'mind-qtrl-admin',
                    'MQ Reactions Pro',
                    'Reactions Pro',
                    'manage_options',
                    'mind-qtrl-community-reactions-pro-ce',
                    array( $this, 'display_plugin_admin_page' )
                );
            }
        } else {
            // Create main Mind Qtrl menu
            add_menu_page(
                'Mind Qtrl',
                'Mind Qtrl',
                'manage_options',
                'mind-qtrl-admin',
                function() {
                    // This is a placeholder callback that redirects to our plugin page
                    wp_redirect(admin_url('admin.php?page=mind-qtrl-community-reactions-pro-ce'));
                    exit;
                },
                plugin_dir_url( dirname( __FILE__ ) ) . 'admin/images/mind-qtrl-logo-icon.png',
                30
            );

            // Add our plugin as a submenu
            add_submenu_page(
                'mind-qtrl-admin',
                'MQ Reactions Pro',
                'Reactions Pro',
                'manage_options',
                'mind-qtrl-community-reactions-pro-ce',
                array( $this, 'display_plugin_admin_page' )
            );
        }
    }

    /**
     * Display the Mind Qtrl main page.
     *
     * @since    0.0.4
     * @updated  0.0.6
     * @deprecated 0.0.6 No longer used as we're using a different menu approach
     */
    public function display_mind_qtrl_page() {
        // This method is kept for backward compatibility but is no longer used
        wp_redirect(admin_url('admin.php?page=mind-qtrl-community-reactions-pro'));
        exit;
    }

    /**
     * Register plugin settings.
     *
     * @since    0.0.1
     */
    public function register_settings() {
        register_setting(
            'mqcrpce_settings_group',
            'mqcrpce_settings',
            array( $this, 'sanitize_settings' )
        );
    }

    /**
     * Sanitize settings before saving.
     *
     * @since    0.0.1
     * @param    array    $input    The settings to sanitize.
     * @return   array              The sanitized settings.
     */
    public function sanitize_settings( $input ) {
        // Sanitize each setting
        $sanitized_input = array();

        // Boolean settings
        $sanitized_input['replace_like_icon'] = isset($input['replace_like_icon']) ? (bool) $input['replace_like_icon'] : false;
        $sanitized_input['enable_custom_reactions'] = isset($input['enable_custom_reactions']) ? (bool) $input['enable_custom_reactions'] : false;
        $sanitized_input['improve_hover_styling'] = isset($input['improve_hover_styling']) ? (bool) $input['improve_hover_styling'] : false;
        $sanitized_input['hide_failed_load_text'] = isset($input['hide_failed_load_text']) ? (bool) $input['hide_failed_load_text'] : false;
        $sanitized_input['custom_delay_time'] = isset($input['custom_delay_time']) ? (bool) $input['custom_delay_time'] : false;
        $sanitized_input['customize_tooltips'] = isset($input['customize_tooltips']) ? (bool) $input['customize_tooltips'] : false;
        $sanitized_input['sync_tooltip_text_color'] = isset($input['sync_tooltip_text_color']) ? (bool) $input['sync_tooltip_text_color'] : false;
        $sanitized_input['custom_tooltip_text_color'] = isset($input['custom_tooltip_text_color']) ? (bool) $input['custom_tooltip_text_color'] : false;

        // Background color options
        $sanitized_input['sync_tooltip_bg_color_light'] = isset($input['sync_tooltip_bg_color_light']) ? (bool) $input['sync_tooltip_bg_color_light'] : true;
        $sanitized_input['sync_tooltip_bg_color_dark'] = isset($input['sync_tooltip_bg_color_dark']) ? (bool) $input['sync_tooltip_bg_color_dark'] : false;
        $sanitized_input['custom_tooltip_bg_color'] = isset($input['custom_tooltip_bg_color']) ? (bool) $input['custom_tooltip_bg_color'] : false;

        // For backward compatibility
        $sanitized_input['sync_tooltip_bg_color'] = $sanitized_input['sync_tooltip_bg_color_light'];

        // Sanitize color values
        $sanitized_input['tooltip_bg_color'] = isset($input['tooltip_bg_color']) ? sanitize_hex_color($input['tooltip_bg_color']) : '#f0f0f0';
        $sanitized_input['tooltip_text_color'] = isset($input['tooltip_text_color']) ? sanitize_hex_color($input['tooltip_text_color']) : '#333333';

        // Sanitize slider values
        $sanitized_input['light_bg_factor'] = isset($input['light_bg_factor']) ?
            floatval(min(max(floatval($input['light_bg_factor']), 0.5), 0.95)) : 0.85; // Limit between 0.5 and 0.95
        $sanitized_input['dark_bg_factor'] = isset($input['dark_bg_factor']) ?
            floatval(min(max(floatval($input['dark_bg_factor']), 0.2), 0.8)) : 0.5; // Limit between 0.2 and 0.8

        // Ensure only one tooltip background option is enabled
        if (($sanitized_input['sync_tooltip_bg_color_light'] && $sanitized_input['sync_tooltip_bg_color_dark']) ||
            ($sanitized_input['sync_tooltip_bg_color_light'] && $sanitized_input['custom_tooltip_bg_color']) ||
            ($sanitized_input['sync_tooltip_bg_color_dark'] && $sanitized_input['custom_tooltip_bg_color'])) {

            // Prioritize in this order: custom > dark > light
            if ($sanitized_input['custom_tooltip_bg_color']) {
                $sanitized_input['sync_tooltip_bg_color_light'] = false;
                $sanitized_input['sync_tooltip_bg_color_dark'] = false;
            } else if ($sanitized_input['sync_tooltip_bg_color_dark']) {
                $sanitized_input['sync_tooltip_bg_color_light'] = false;
            }

            // Update backward compatibility
            $sanitized_input['sync_tooltip_bg_color'] = $sanitized_input['sync_tooltip_bg_color_light'];
        }

        // Ensure only one tooltip text color option is enabled
        if ($sanitized_input['sync_tooltip_text_color'] && $sanitized_input['custom_tooltip_text_color']) {
            // If both are somehow enabled, prioritize custom text color
            $sanitized_input['sync_tooltip_text_color'] = false;
        }

        // Numeric settings
        $sanitized_input['delay_time_seconds'] = isset($input['delay_time_seconds']) ?
            floatval(min(max(floatval($input['delay_time_seconds']), 0.5), 5)) : 1.0; // Limit between 0.5 and 5

        // Custom image URL
        $sanitized_input['custom_like_icon'] = isset($input['custom_like_icon']) ? esc_url_raw($input['custom_like_icon']) : '';
        $sanitized_input['hover_color'] = isset($input['hover_color']) ? sanitize_hex_color($input['hover_color']) : '#3498db';

        // Reaction types
        if (isset($input['reaction_types']) && is_array($input['reaction_types'])) {
            $sanitized_input['reaction_types'] = array();

            foreach ($input['reaction_types'] as $type => $settings) {
                $is_first_type = ($type === 'like');

                $sanitized_settings = array(
                    'enabled' => isset($settings['enabled']) ? (bool) $settings['enabled'] : false,
                    'color' => isset($settings['color']) ? sanitize_hex_color($settings['color']) : '#3498db',
                    'order' => isset($settings['order']) ? intval($settings['order']) : 0
                );

                // Handle the first reaction type (Like) differently
                if ($is_first_type) {
                    $sanitized_settings['icon'] = isset($settings['icon']) ? sanitize_text_field($settings['icon']) : '';
                    $sanitized_settings['name'] = isset($settings['name']) ? sanitize_text_field($settings['name']) : 'Like';
                    // Sync color with main hover color
                    $sanitized_settings['color'] = isset($input['hover_color']) ? sanitize_hex_color($input['hover_color']) : '#8770FF';
                } else {
                    // For other reaction types, handle custom name and image
                    $sanitized_settings['name'] = isset($settings['name']) ? sanitize_text_field($settings['name']) : ucfirst($type);
                    $sanitized_settings['image'] = isset($settings['image']) ? esc_url_raw($settings['image']) : '';
                }

                $sanitized_input['reaction_types'][$type] = $sanitized_settings;
            }
        }

        return $sanitized_input;
    }

    /**
     * Display the admin page.
     *
     * @since    0.1.0
     */
    public function display_plugin_admin_page() {
        include_once MQCRPCE_PLUGIN_PATH . 'admin/partials/mind-qtrl-community-reactions-pro-admin-display.php';
    }

    /**
     * Register AJAX handlers.
     *
     * @since    0.0.5
     */
    public function register_ajax_handlers() {
        add_action('wp_ajax_mqcrp_save_settings', array($this, 'ajax_save_settings'));
    }

    /**
     * Handle AJAX save settings request.
     *
     * @since    0.0.5
     * @updated  0.0.7
     */
    public function ajax_save_settings() {
        // Check nonce for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqcrp_save_settings')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to save settings'));
        }

        // Get settings from POST data
        if (!isset($_POST['settings'])) {
            wp_send_json_error(array('message' => 'No settings data received'));
        }

        // Parse settings from serialized form data
        parse_str($_POST['settings'], $settings);

        // Sanitize and save settings
        if (isset($settings['mqcrpce_settings'])) {
            $sanitized_settings = $this->sanitize_settings($settings['mqcrpce_settings']);

            // Get current settings to compare
            $current_settings = get_option('mqcrpce_settings', array());

            // Force update even if WordPress thinks nothing changed
            if ($sanitized_settings == $current_settings) {
                // Settings are the same, but we'll update anyway to ensure it works
                update_option('mqcrpce_settings', $sanitized_settings, false);
                wp_send_json_success(array('message' => 'Settings saved successfully'));
            } else {
                // Settings are different, update normally
                $result = update_option('mqcrpce_settings', $sanitized_settings);

                if ($result) {
                    wp_send_json_success(array('message' => 'Settings saved successfully'));
                } else {
                    // Try one more time with forced update
                    $force_result = update_option('mqcrpce_settings', $sanitized_settings, false);
                    if ($force_result) {
                        wp_send_json_success(array('message' => 'Settings saved successfully (forced update)'));
                    } else {
                        wp_send_json_error(array('message' => 'Failed to update settings in database'));
                    }
                }
            }
        } else {
            wp_send_json_error(array('message' => 'Invalid settings data'));
        }
    }
}
