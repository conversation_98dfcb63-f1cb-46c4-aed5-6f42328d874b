/**
 * Mind Qtrl Community Reactions Pro Admin CSS
 * Following the Mind Qtrl UI template color scheme
 */

/* Admin Page Styling */
.toplevel_page_mind-qtrl-admin #wpwrap,
.mind-qtrl_page_mind-qtrl-community-reactions-pro-ce #wpwrap {
    background: #1B1B1E !important;
}

.wp-core-ui .button.hover, .wp-core-ui .button:hover, .wp-core-ui .button-secondary:hover {
    color: #fff;
    font-weight: 600;
}

.mqcrp-admin {
    max-width: 1200px;
    margin: 0 auto;
    color: #ffffff;
}

.mqcrp-admin-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: #1e1e2d;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.media-modal-content {
    background: #1e1e2d;
}

.media-frame-content {
    background: #111114;
    border-top: 1px solid #222226;
}

h1, h2, h4, h5, h1, h2, h3, h4, h5 {
    color: #8770FF !important;
}

.attachments-browser .media-toolbar {
    background: #1e1e2d;
    color: #fff;
    border: #222226;
}

.wp-core-ui .button-primary[disabled], .wp-core-ui .button-primary:disabled, .wp-core-ui .button-primary-disabled, .wp-core-ui .button-primary.disabled {
    color: #fff !important;
    background: #8770FF !important;
    border-color: #8770FF !important;
}

.media-sidebar {
    background: #181819;
    border-left: 1px solid #222226;
}

.mqcrp-logo {
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: center;
}

.mqcrp-logo img {
    max-width: 150px;
    height: auto;
    display: block;
}

.mqcrp-title-container {
    flex: 1;
    text-align: left;
    min-width: 250px;
}

.mqcrp-title-container h1 {
    color: #ffffff;
    margin: 0;
    font-size: 24px;
    font-weight: 500;
    padding-left: 10px;
}

.mqcrp-version {
    flex: 1;
    font-size: 14px;
    color: #8770FF;
    font-style: italic;
    padding: 5px 10px;
    background: rgba(135, 112, 255, 0.1);
    border-radius: 4px;
    text-align: right;
    margin-right: 10px;
    display: inline-block;
    max-width: fit-content;
    margin-left: auto;
}

.mqcrp-card {
    background: #1e1e2d;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    margin-bottom: 20px;
    padding: 20px;
}

.mqcrp-card h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #333344;
    color: #8770FF;
}

/* Switch Toggle */
.mqcrp-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    cursor: pointer;
    z-index: 1;
}

.mqcrp-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
    z-index: -1;
}

.mqcrp-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333344;
    transition: .4s;
    border-radius: 34px;
    z-index: 2;
}

.mqcrp-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: #1B1B1E;
    transition: .4s;
    border-radius: 50%;
    z-index: 3;
}

input:checked + .mqcrp-slider {
    background-color: #8770FF;
}

input:focus + .mqcrp-slider {
    box-shadow: 0 0 1px #8770FF;
}

input:checked + .mqcrp-slider:before {
    transform: translateX(26px);
    background-color: #8770FF;
}

/* Improve toggle accessibility */
.mqcrp-switch:hover .mqcrp-slider {
    box-shadow: 0 0 5px rgba(135, 112, 255, 0.5);
}

.mqcrp-switch:active .mqcrp-slider:before {
    width: 30px;
}

/* Toggle container */
.mqcrp-toggle-container {
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle;
}

/* Make sure the toggle is clickable */
.mqcrp-switch, .mqcrp-slider {
    pointer-events: auto !important;
}

/* Ensure visual feedback for toggle state */
.mqcrp-switch input:checked ~ .mqcrp-slider,
.mqcrp-slider.checked {
    background-color: #8770FF !important;
}

.mqcrp-switch input:checked ~ .mqcrp-slider:before,
.mqcrp-slider.checked:before {
    transform: translateX(26px) !important;
    background-color: white !important;
}

/* Add transition for smoother toggle */
.mqcrp-slider, .mqcrp-slider:before {
    transition: all 0.3s ease !important;
}

/* Expandable container */
.mqcrp-expandable-container {
    margin-top: 15px;
    padding: 15px;
    background: rgba(135, 112, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid #8770FF;
    transition: all 0.3s ease;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transform: translateY(-10px);
}

.mqcrp-expandable-container.expanded {
    max-height: 200px;
    opacity: 1;
    transform: translateY(0);
}

/* Range slider styling */
.mqcrp-slider-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
}

/* Tooltip options styling */
.mqcrp-tooltip-options {
    margin-bottom: 20px;
}

/* Color picker container */
.mqcrp-color-picker-container {
    margin-top: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(135, 112, 255, 0.03);
    border-radius: 4px;
    border-left: 2px solid rgba(135, 112, 255, 0.2);
}

.mqcrp-color-picker-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 13px;
}

/* Slider container */
.mqcrp-slider-container {
    margin-top: 10px;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: rgba(135, 112, 255, 0.03);
    border-radius: 4px;
    border-left: 2px solid rgba(135, 112, 255, 0.2);
}

.mqcrp-slider-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 13px;
}

.mqcrp-range-slider {
    width: 100%;
    margin: 10px 0;
}

.mqcrp-slider-value {
    text-align: center;
    font-weight: bold;
    color: #8770FF;
    margin-bottom: 5px;
}

.mqcrp-option-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(135, 112, 255, 0.1);
}

.mqcrp-option-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.mqcrp-option-label {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.mqcrp-option-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.mqcrp-option-description {
    font-size: 12px;
    color: #777;
    font-style: italic;
}

/* Tooltip preview */
.mqcrp-tooltip-preview {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(135, 112, 255, 0.1);
}

.mqcrp-tooltip-preview-title {
    font-weight: 600;
    margin-bottom: 10px;
}

.mqcrp-tooltip-preview-container {
    display: flex;
    justify-content: center;
    padding: 15px 0;
}

.mqcrp-tooltip-preview-item {
    display: inline-block;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: var(--tooltip-bg-color, #f0f0f0);
    color: var(--tooltip-text-color, #333);
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.mqcrp-tooltip-preview-item::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid var(--tooltip-bg-color, #f0f0f0);
}

.mqcrp-range-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: #333344;
    outline: none;
    margin: 15px 0;
}

.mqcrp-range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #8770FF;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

.mqcrp-range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #8770FF;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    border: none;
}

.mqcrp-range-slider::-webkit-slider-thumb:hover,
.mqcrp-range-slider::-moz-range-thumb:hover {
    background: #A08DFF;
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(135, 112, 255, 0.4);
}

.mqcrp-slider-value {
    text-align: center;
    font-weight: 600;
    color: #8770FF;
    font-size: 14px;
    margin-top: 5px;
}

/* Centered Headings */
.mqcrp-centered-heading {
    text-align: center;
    margin-bottom: 10px;
}

.mqcrp-centered-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 20px auto;
    display: block;
    width: 100%;
}

/* Reaction Grid */
.mqcrp-reaction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.mqcrp-add-reaction {
    background: #2a2a3c;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 2px dashed #444455;
    min-height: 200px;
}

.mqcrp-add-reaction:hover {
    background: #333344;
    border-color: #8770FF;
    box-shadow: 0 4px 12px rgba(135, 112, 255, 0.3);
}

.mqcrp-add-reaction .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #8770FF;
    margin-bottom: 10px;
}

.mqcrp-add-reaction-text {
    color: #ffffff;
    font-size: 14px;
}

.mqcrp-reaction-count {
    margin-top: 10px;
    font-size: 12px;
    color: #aaaabc;
}

.mqcrp-reaction-item {
    background: #2a2a3c;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.mqcrp-reaction-item.draggable {
    cursor: grab;
}

.mqcrp-reaction-item.dragging {
    opacity: 0.8;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

.mqcrp-reaction-item-placeholder {
    border: 2px dashed #8770FF;
    background-color: rgba(135, 112, 255, 0.1);
    border-radius: 8px;
    margin: 0;
    min-height: 200px;
}

.mqcrp-delete-reaction {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    color: #aaaabc;
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.2s ease;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mqcrp-delete-reaction:hover {
    opacity: 1;
    color: #ff5555;
}

.mqcrp-reaction-item:hover {
    box-shadow: 0 4px 12px rgba(135, 112, 255, 0.3);
    transform: translateY(-2px);
    border: 1px solid rgba(135, 112, 255, 0.3);
}

.mqcrp-reaction-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.mqcrp-reaction-item h4 {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mqcrp-editable-name {
    margin-right: 5px;
}

.mqcrp-edit-name {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 0;
    margin-left: 5px;
    opacity: 0.5;
    transition: opacity 0.2s;
}

.mqcrp-edit-name:hover {
    opacity: 1;
    color: #3498db;
}

.mqcrp-reaction-icon {
    font-size: 24px;
    margin: 15px 0;
}

.mqcrp-reaction-color {
    margin-top: 15px;
}

.mqcrp-reaction-color label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #aaaabc;
}

.mqcrp-reaction-color input {
    width: 100%;
    height: 30px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    box-shadow: 0 0 0 1px #333344;
}

.mqcrp-reaction-color input[readonly] {
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: 0 0 0 1px #444455;
}

.mqcrp-reaction-color .description {
    font-size: 11px;
    margin-top: 5px;
    font-style: italic;
}

/* Reaction image upload */
.reaction-image-upload {
    margin: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.reaction-preview-image {
    width: 48px;
    height: 48px;
    margin: 0 auto 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: auto;
    margin-right: auto;
}

/* Image Upload */
.mqcrp-image-upload {
    margin-bottom: 15px;
    justify-content: center;
    display: flex;
    flex-direction: column;
}

.mqcrp-preview-image {
    margin: 10px 0;
    width: 64px;
    height: 64px;
    border: 1px solid #333344;
    border-radius: 8px;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1e1e2d;
}

.mqcrp-preview-image img,
.reaction-preview-image img {
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin: 0 auto;
}

.mqcrp-no-image {
    color: #aaaabc;
    font-size: 12px;
    text-align: center;
}

.mqcrp-remove-image {
    margin-left: 5px;
    background: #da3633 !important;
    border-color: #c42f2c !important;
    color: white !important;
}

/* Form Elements */
.mqcrp-admin .form-table {
    background: transparent;
    border-collapse: collapse;
}

.mqcrp-admin .form-table th {
    color: #ffffff;
    font-weight: 500;
    padding: 15px;
}

.mqcrp-admin .form-table td {
    padding: 15px;
}

.mqcrp-admin input[type="text"],
.mqcrp-admin select {
    background: #1e1e2d;
    border: 1px solid #333344;
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    width: 100%;
    max-width: 300px;
}

.mqcrp-admin .description {
    color: #aaaabc;
    font-style: italic;
    margin-top: 5px;
}

/* Submit Button */
.mqcrp-submit-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    gap: 15px;
}

.mqcrp-notices-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.mqcrp-admin .submit {
    margin-top: 30px;
}

/* Save Button with Spinner */
#mqcrp-save-settings {
    position: relative;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mqcrp-spinner {
    position: relative;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: mqcrp-spin 1s linear infinite;
}

@keyframes mqcrp-spin {
    to { transform: rotate(360deg); }
}

/* Success Message */
#mqcrp-settings-saved {
    margin: 0;
}

/* Inline Notices */
.mqcrp-inline-notice {
    margin: 0 0 10px 0;
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.mqcrp-inline-notice:last-child {
    margin-bottom: 0;
}

.mqcrp-inline-notice p {
    margin: 0.5em 0;
}

/* Make notices more compact in the submit container */
.mqcrp-notices-container .notice {
    min-height: 0;
    padding: 8px 12px;
    transition: all 0.3s ease;
}

/* Visible notice animation */
.mqcrp-notice-visible {
    animation: mqcrp-notice-appear 0.3s ease-in-out;
}

@keyframes mqcrp-notice-appear {
    from { opacity: 0; transform: translateX(-10px); }
    to { opacity: 1; transform: translateX(0); }
}

.mqcrp-admin .button-primary {
    background: #8770FF;
    border-color: #6550dd;
    color: #ffffff;
    padding: 8px 20px;
    height: auto;
    font-size: 14px;
    transition: all 0.3s ease;
}

.mqcrp-admin .button-primary:hover,
.mqcrp-admin .button-primary:focus {
    background: #6550dd;
    border-color: #5440cc;
}

.mqcrp-admin .button {
    background: #8770FF;
    border-color: #333344;
    color: #ffffff;
    transition: all 0.3s ease;
    max-width: 200px;
}

.mqcrp-admin .button:hover,
.mqcrp-admin .button:focus {
    background: #A08DFF;;
    border-color: #444455;
}

.mqcrp-admin-footer {
    margin-top: 30px;
    text-align: center;
    color: #aaaabc;
    font-style: italic;
}
