/**
 * Mind Qtrl Community Reactions Pro Admin JavaScript
 */
jQuery(document).ready(function($) {
    // Counter for new reaction types
    let newReactionCounter = 1;
    // Debug function to log toggle state changes
    function logToggleState(checkbox) {
        console.log('Toggle state changed:', {
            id: checkbox.attr('id') || 'unnamed-checkbox',
            checked: checkbox.prop('checked')
        });
    }

    // Initialize toggle states on page load
    function initializeToggles() {
        $('.mqcrp-switch input[type="checkbox"]').each(function() {
            const checkbox = $(this);
            const slider = checkbox.siblings('.mqcrp-slider');

            if (checkbox.prop('checked')) {
                slider.addClass('checked');
            } else {
                slider.removeClass('checked');
            }
        });
    }

    // Run initialization
    initializeToggles();
    // Fix for slider toggle functionality
    $('.mqcrp-slider').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Find the checkbox within the parent switch
        const checkbox = $(this).siblings('input[type="checkbox"]');

        // Toggle the checkbox state
        const newState = !checkbox.prop('checked');
        checkbox.prop('checked', newState).trigger('change');

        // Force visual update
        if (newState) {
            $(this).addClass('checked');
        } else {
            $(this).removeClass('checked');
        }

        // Log the toggle state change
        logToggleState(checkbox);
    });

    // Also handle clicks on the switch container
    $('.mqcrp-switch').on('click', function(e) {
        // Only handle clicks on the switch itself, not on the checkbox
        if (!$(e.target).is('input[type="checkbox"]')) {
            e.preventDefault();

            // Find the checkbox
            const checkbox = $(this).find('input[type="checkbox"]');

            // Toggle the checkbox state
            const newState = !checkbox.prop('checked');
            checkbox.prop('checked', newState).trigger('change');

            // Force visual update
            if (newState) {
                $(this).find('.mqcrp-slider').addClass('checked');
            } else {
                $(this).find('.mqcrp-slider').removeClass('checked');
            }

            // Log the toggle state change
            logToggleState(checkbox);
        }
    });
    // Toggle reaction icons container based on checkbox
    $('#enable_custom_reactions').on('change', function() {
        if ($(this).is(':checked')) {
            $('#reaction-icons-container').slideDown();
        } else {
            $('#reaction-icons-container').slideUp();
        }
    });

    // Toggle delay time container based on checkbox
    $('#custom_delay_time').on('change', function() {
        if ($(this).is(':checked')) {
            $('#delay-time-container').slideDown().addClass('expanded');
        } else {
            $('#delay-time-container').slideUp().removeClass('expanded');
        }
    });

    // Update delay time value display when slider changes
    $('#delay_time_seconds').on('input', function() {
        $('#delay-time-value').text($(this).val());
    });

    // Toggle tooltip options container based on checkbox
    $('#customize_tooltips').on('change', function() {
        if ($(this).is(':checked')) {
            $('#tooltip-options-container').slideDown().addClass('expanded');
            updateTooltipPreview(); // Initialize preview
        } else {
            $('#tooltip-options-container').slideUp().removeClass('expanded');
        }
    });

    // Handle exclusive toggles (only one can be ON at a time)
    $('.mqcrp-exclusive-toggle').on('change', function() {
        const group = $(this).data('group');
        const isChecked = $(this).is(':checked');

        if (isChecked) {
            // Turn off other toggles in the same group
            $('.mqcrp-exclusive-toggle[data-group="' + group + '"]').not(this).prop('checked', false);
        }

        // Update the preview
        updateTooltipPreview();
    });

    // Toggle custom tooltip background color container
    $('#custom_tooltip_bg_color').on('change', function() {
        if ($(this).is(':checked')) {
            $('#tooltip-bg-color-container').slideDown();
        } else {
            $('#tooltip-bg-color-container').slideUp();
        }
        updateTooltipPreview();
    });

    // Toggle light background factor container
    $('#sync_tooltip_bg_color_light').on('change', function() {
        if ($(this).is(':checked')) {
            $('#light-bg-factor-container').slideDown();
        } else {
            $('#light-bg-factor-container').slideUp();
        }
        updateTooltipPreview();
    });

    // Toggle dark background factor container
    $('#sync_tooltip_bg_color_dark').on('change', function() {
        if ($(this).is(':checked')) {
            $('#dark-bg-factor-container').slideDown();
        } else {
            $('#dark-bg-factor-container').slideUp();
        }
        updateTooltipPreview();
    });

    // Toggle custom tooltip text color container
    $('#custom_tooltip_text_color').on('change', function() {
        if ($(this).is(':checked')) {
            $('#tooltip-text-color-container').slideDown();
        } else {
            $('#tooltip-text-color-container').slideUp();
        }
        updateTooltipPreview();
    });

    // Update light background factor value display
    $('#light_bg_factor').on('input', function() {
        $('#light-bg-factor-value').text($(this).val());
        updateTooltipPreview();
    });

    // Update dark background factor value display
    $('#dark_bg_factor').on('input', function() {
        $('#dark-bg-factor-value').text($(this).val());
        updateTooltipPreview();
    });

    // Function to update tooltip preview
    function updateTooltipPreview() {
        const preview = $('#tooltip-preview');
        const syncTextColor = $('#sync_tooltip_text_color').is(':checked');
        const customTextColor = $('#custom_tooltip_text_color').is(':checked');
        const syncBgColorLight = $('#sync_tooltip_bg_color_light').is(':checked');
        const syncBgColorDark = $('#sync_tooltip_bg_color_dark').is(':checked');
        const customBgColor = $('#custom_tooltip_bg_color').is(':checked');
        const glowColor = $('#hover_color').val() || '#8770FF';
        const tooltipBgColor = $('#tooltip_bg_color').val() || '#f0f0f0';
        const tooltipTextColor = $('#tooltip_text_color').val() || '#333333';
        const lightBgFactor = parseFloat($('#light_bg_factor').val()) || 0.85;
        const darkBgFactor = parseFloat($('#dark_bg_factor').val()) || 0.5;

        // Set default styling first
        preview.css('--tooltip-bg-color', '#f0f0f0');
        preview.css('--tooltip-text-color', '#333');

        // Apply text color settings
        if (syncTextColor) {
            preview.css('--tooltip-text-color', glowColor);
        } else if (customTextColor) {
            preview.css('--tooltip-text-color', tooltipTextColor);
        }

        // Apply background color settings
        if (syncBgColorLight) {
            // Calculate a lighter version of the glow color for background
            const lighterColor = getLighterColor(glowColor, lightBgFactor);
            preview.css('--tooltip-bg-color', lighterColor);
        } else if (syncBgColorDark) {
            // Calculate a darker version of the glow color for background
            const darkerColor = getDarkerColor(glowColor, darkBgFactor);
            preview.css('--tooltip-bg-color', darkerColor);
        } else if (customBgColor) {
            preview.css('--tooltip-bg-color', tooltipBgColor);
        }
    }

    // Helper function to get a darker version of a color
    function getDarkerColor(hex, factor) {
        // Convert hex to RGB
        let r = parseInt(hex.substring(1, 3), 16);
        let g = parseInt(hex.substring(3, 5), 16);
        let b = parseInt(hex.substring(5, 7), 16);

        // Make it darker
        r = Math.max(0, Math.round(r * factor));
        g = Math.max(0, Math.round(g * factor));
        b = Math.max(0, Math.round(b * factor));

        // Convert back to hex
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // Update preview when tooltip background color changes
    $('#tooltip_bg_color').wpColorPicker({
        change: function(event, ui) {
            updateTooltipPreview();
        }
    });

    // Update preview when tooltip text color changes
    $('#tooltip_text_color').wpColorPicker({
        change: function(event, ui) {
            updateTooltipPreview();
        }
    });

    // Helper function to get a lighter version of a color
    function getLighterColor(hex, factor) {
        // Convert hex to RGB
        let r = parseInt(hex.substring(1, 3), 16);
        let g = parseInt(hex.substring(3, 5), 16);
        let b = parseInt(hex.substring(5, 7), 16);

        // Make it lighter
        r = Math.min(255, Math.round(r + (255 - r) * factor));
        g = Math.min(255, Math.round(g + (255 - g) * factor));
        b = Math.min(255, Math.round(b + (255 - b) * factor));

        // Convert back to hex
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // Update preview when hover color changes
    $('#hover_color').on('change', function() {
        updateTooltipPreview();
    });

    // Initialize color pickers
    if ($.fn.wpColorPicker) {
        $('.mqcrp-admin .color-picker').wpColorPicker();
    }

    // Media uploader for custom like icon
    var mainMediaUploader;

    $('.mqcrp-upload-image').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var imageContainer = button.siblings('.mqcrp-preview-image');
        var imageInput = button.siblings('input[type="hidden"]');
        var removeButton = button.siblings('.mqcrp-remove-image');

        // If the media uploader already exists, open it
        if (mainMediaUploader) {
            mainMediaUploader.open();
            return;
        }

        // Create the media uploader
        mainMediaUploader = wp.media({
            title: 'Select Custom Like Icon',
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        // When an image is selected, run a callback
        mainMediaUploader.on('select', function() {
            var attachment = mainMediaUploader.state().get('selection').first().toJSON();

            // Set the image URL as the input value
            imageInput.val(attachment.url);

            // Update the preview image
            imageContainer.html('<img src="' + attachment.url + '" alt="Custom Like Icon">');

            // Show the remove button
            removeButton.show();

            // Update hover color to match the first reaction type's glow color if custom reactions are enabled
            if ($('#enable_custom_reactions').is(':checked')) {
                var firstReactionColor = $('.reaction-color-picker').first().val();
                $('#hover_color').val(firstReactionColor);
            }
        });

        // Open the uploader dialog
        mainMediaUploader.open();
    });

    // Remove image button
    $('.mqcrp-remove-image').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var imageContainer = button.siblings('.mqcrp-preview-image');
        var imageInput = button.siblings('input[type="hidden"]');

        // Clear the input value
        imageInput.val('');

        // Update the preview
        imageContainer.html('<span class="mqcrp-no-image">No image selected</span>');

        // Hide the remove button
        button.hide();
    });

    // Media uploader for reaction type images
    $('.mqcrp-upload-reaction-image').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var imageContainer = button.siblings('.mqcrp-preview-image');
        var imageInput = button.siblings('input[type="hidden"]');
        var removeButton = button.siblings('.mqcrp-remove-reaction-image');
        var reactionName = button.closest('.mqcrp-reaction-item').find('.mqcrp-editable-name').text().trim();

        // Create a new media uploader instance for each reaction
        var reactionMediaUploader = wp.media({
            title: 'Select ' + reactionName + ' Icon',
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        // When an image is selected, run a callback
        reactionMediaUploader.on('select', function() {
            var attachment = reactionMediaUploader.state().get('selection').first().toJSON();

            // Set the image URL as the input value
            imageInput.val(attachment.url);

            // Update the preview image
            imageContainer.html('<img src="' + attachment.url + '" alt="' + reactionName + ' Icon">');

            // Show the remove button
            removeButton.show();
        });

        // Open the uploader dialog
        reactionMediaUploader.open();
    });

    // Remove reaction image button
    $('.mqcrp-remove-reaction-image').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var imageContainer = button.siblings('.mqcrp-preview-image');
        var imageInput = button.siblings('input[type="hidden"]');

        // Clear the input value
        imageInput.val('');

        // Update the preview
        imageContainer.html('<span class="mqcrp-no-image">No image</span>');

        // Hide the remove button
        button.hide();
    });

    // Edit reaction name functionality
    $('.mqcrp-edit-name').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var nameSpan = button.siblings('.mqcrp-editable-name');
        var nameInput = button.siblings('.mqcrp-name-input');
        var currentName = nameSpan.text().trim();

        // Prompt for new name
        var newName = prompt('Enter new name for this reaction type:', currentName);

        if (newName !== null && newName.trim() !== '') {
            // Update the displayed name
            nameSpan.text(newName.trim());

            // Update the hidden input value
            nameInput.val(newName.trim());
        }
    });

    // Sync main glow color with first reaction type's glow color (two-way sync)
    $('#hover_color').on('change', function() {
        // Update the first reaction type's glow color
        $('.reaction-color-picker').first().val($(this).val());
    });

    // Sync first reaction type's glow color with main glow color
    $('.reaction-color-picker').first().on('change', function() {
        // Update the main glow color
        $('#hover_color').val($(this).val());
    });

    // Also sync hover color when custom reactions toggle changes
    $('#enable_custom_reactions').on('change', function() {
        if ($(this).is(':checked')) {
            // If custom reactions are enabled, sync with first reaction type's glow color
            var firstReactionColor = $('.reaction-color-picker').first().val();
            $('#hover_color').val(firstReactionColor);
        }
    });

    // Handle adding new reaction type
    $('#mqcrp-add-reaction-btn').on('click', function() {
        // Check if we've reached the maximum number of reaction types
        const reactionCount = $('.mqcrp-reaction-item').length;
        if (reactionCount >= 8) { // 1 main + 7 custom
            alert('You can only have up to 7 custom reaction types.');
            return;
        }

        // Generate a unique ID for the new reaction type
        const newType = 'custom_' + newReactionCounter++;
        const newName = 'Custom Reaction';
        const newOrder = reactionCount; // Set order to be after existing items

        // Create new reaction item HTML
        const newReactionHtml = `
            <div class="mqcrp-reaction-item draggable" data-type="${newType}" data-order="${newOrder}">
                <button type="button" class="mqcrp-delete-reaction" title="Delete this reaction type">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>

                <div class="mqcrp-reaction-header">
                    <h4>
                        <span class="mqcrp-editable-name" data-type="${newType}">
                            ${newName}
                        </span>
                        <button type="button" class="mqcrp-edit-name" title="Edit name">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <input type="hidden" name="mqcrp_settings[reaction_types][${newType}][name]" value="${newName}" class="mqcrp-name-input">
                        <input type="hidden" name="mqcrp_settings[reaction_types][${newType}][order]" value="${newOrder}" class="mqcrp-order-input">
                    </h4>
                </div>

                <div class="mqcrp-toggle-container">
                    <label class="mqcrp-switch" for="reaction_type_${newType}_enabled">
                        <input type="checkbox" name="mqcrp_settings[reaction_types][${newType}][enabled]" id="reaction_type_${newType}_enabled" value="1" checked>
                        <span class="mqcrp-slider checked"></span>
                    </label>
                </div>

                <div class="mqcrp-image-upload reaction-image-upload">
                    <input type="hidden" name="mqcrp_settings[reaction_types][${newType}][image]" value="" class="reaction-image-input">
                    <div class="mqcrp-preview-image reaction-preview-image">
                        <span class="mqcrp-no-image">No image</span>
                    </div>
                    <button type="button" class="button mqcrp-upload-reaction-image">Upload Image</button>
                    <button type="button" class="button mqcrp-remove-reaction-image" style="display:none;">Remove</button>
                </div>

                <div class="mqcrp-reaction-color">
                    <label>Glow Color:</label>
                    <input type="color" name="mqcrp_settings[reaction_types][${newType}][color]" value="#8770FF" class="reaction-color-picker">
                </div>
            </div>
        `;

        // Insert the new reaction item before the add button
        $(this).before(newReactionHtml);

        // Update the reaction count display
        const newCount = $('.mqcrp-reaction-item').length - 1; // Subtract 1 for the main reaction
        $('.mqcrp-reaction-count').text(newCount + '/7 Custom Types');

        // Hide the add button if we've reached the maximum
        if (newCount >= 7) {
            $(this).hide();
        }

        // Initialize the new reaction item's functionality
        initializeToggles();
        makeReactionItemsDraggable();

        // Attach event handlers to the new elements
        attachEventHandlers();
    });

    // Handle deleting a reaction type
    $(document).on('click', '.mqcrp-delete-reaction', function() {
        if (confirm('Are you sure you want to delete this reaction type?')) {
            // Remove the reaction item
            $(this).closest('.mqcrp-reaction-item').remove();

            // Update the reaction count display
            const newCount = $('.mqcrp-reaction-item').length - 1; // Subtract 1 for the main reaction
            $('.mqcrp-reaction-count').text(newCount + '/7 Custom Types');

            // Show the add button if it was hidden
            if (newCount < 7) {
                $('#mqcrp-add-reaction-btn').show();
            }

            // Update the order of remaining items
            updateReactionOrder();
        }
    });

    // Function to make reaction items draggable
    function makeReactionItemsDraggable() {
        if (typeof $.fn.sortable !== 'undefined') {
            $('#mqcrp-sortable-reactions').sortable({
                items: '.mqcrp-reaction-item.draggable',
                handle: '.mqcrp-reaction-header',
                placeholder: 'mqcrp-reaction-item-placeholder',
                opacity: 0.8,
                cursor: 'grab',
                update: function(event, ui) {
                    updateReactionOrder();
                }
            });
        }
    }

    // Function to update the order of reaction items
    function updateReactionOrder() {
        $('.mqcrp-reaction-item').each(function(index) {
            // Skip the first item (main reaction)
            if ($(this).data('type') === 'like') {
                $(this).find('.mqcrp-order-input').val(0);
                return;
            }

            // Update the order input
            $(this).find('.mqcrp-order-input').val(index);
            $(this).attr('data-order', index);
        });
    }

    // Function to attach event handlers to new elements
    function attachEventHandlers() {
        // Attach edit name handler
        $('.mqcrp-edit-name').off('click').on('click', function(e) {
            e.preventDefault();

            var button = $(this);
            var nameSpan = button.siblings('.mqcrp-editable-name');
            var nameInput = button.siblings('.mqcrp-name-input');
            var currentName = nameSpan.text().trim();

            // Prompt for new name
            var newName = prompt('Enter new name for this reaction type:', currentName);

            if (newName !== null && newName.trim() !== '') {
                // Update the displayed name
                nameSpan.text(newName.trim());

                // Update the hidden input value
                nameInput.val(newName.trim());
            }
        });

        // Attach upload image handler
        $('.mqcrp-upload-reaction-image').off('click').on('click', function(e) {
            e.preventDefault();

            var button = $(this);
            var imageContainer = button.siblings('.mqcrp-preview-image');
            var imageInput = button.siblings('input[type="hidden"]');
            var removeButton = button.siblings('.mqcrp-remove-reaction-image');
            var reactionName = button.closest('.mqcrp-reaction-item').find('.mqcrp-editable-name').text().trim();

            // Create a new media uploader instance for each reaction
            var reactionMediaUploader = wp.media({
                title: 'Select ' + reactionName + ' Icon',
                button: {
                    text: 'Use this image'
                },
                multiple: false
            });

            // When an image is selected, run a callback
            reactionMediaUploader.on('select', function() {
                var attachment = reactionMediaUploader.state().get('selection').first().toJSON();

                // Set the image URL as the input value
                imageInput.val(attachment.url);

                // Update the preview image
                imageContainer.html('<img src="' + attachment.url + '" alt="' + reactionName + ' Icon">');

                // Show the remove button
                removeButton.show();
            });

            // Open the uploader dialog
            reactionMediaUploader.open();
        });

        // Attach remove image handler
        $('.mqcrp-remove-reaction-image').off('click').on('click', function(e) {
            e.preventDefault();

            var button = $(this);
            var imageContainer = button.siblings('.mqcrp-preview-image');
            var imageInput = button.siblings('input[type="hidden"]');

            // Clear the input value
            imageInput.val('');

            // Update the preview
            imageContainer.html('<span class="mqcrp-no-image">No image</span>');

            // Hide the remove button
            button.hide();
        });
    }

    // Initialize draggable functionality
    makeReactionItemsDraggable();

    // Attach event handlers to existing elements
    attachEventHandlers();

    // Update reaction order on page load
    updateReactionOrder();

    // Handle AJAX form submission
    $('#mqcrp-save-settings').on('click', function(e) {
        e.preventDefault();
        console.log('Save settings button clicked');

        // Show spinner and disable button
        const $button = $(this);
        const $buttonText = $button.find('.mqcrp-button-text');
        const $spinner = $button.find('.mqcrp-spinner');
        const originalText = $buttonText.text();

        $buttonText.text('Saving...');
        $spinner.show();
        $button.prop('disabled', true);

        // Hide any existing notices
        $('#mqcrp-settings-saved, #mqcrp-settings-error').hide().removeClass('mqcrp-notice-visible');

        // Get form data
        const formData = $('#mqcrp-settings-form').serialize();
        console.log('Form data:', formData);

        // Check if mqcrp_ajax object exists
        if (typeof mqcrp_ajax === 'undefined') {
            console.error('mqcrp_ajax is not defined. wp_localize_script may not be working.');
            $('#mqcrp-settings-error').show().find('p').text('Error: AJAX configuration is missing. Using fallback method.');
            $buttonText.text(originalText);
            $spinner.hide();
            $button.prop('disabled', false);

            // Show and trigger the fallback submit button
            $('#submit').show();
            setTimeout(function() {
                $('#submit').click();
            }, 500);
            return;
        }

        console.log('AJAX URL:', mqcrp_ajax.ajax_url);
        console.log('Nonce:', mqcrp_ajax.nonce);

        // Send AJAX request
        $.ajax({
            url: mqcrp_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'mqcrp_save_settings',
                nonce: mqcrp_ajax.nonce,
                settings: formData
            },
            success: function(response) {
                console.log('AJAX response:', response);
                if (response.success) {
                    // Show success message
                    $('#mqcrp-settings-saved').show().addClass('mqcrp-notice-visible');

                    // Automatically hide the message after 5 seconds
                    setTimeout(function() {
                        $('#mqcrp-settings-saved').fadeOut().removeClass('mqcrp-notice-visible');
                    }, 5000);
                } else {
                    // Show error message
                    $('#mqcrp-settings-error').show().addClass('mqcrp-notice-visible').find('p').text('Error: ' + (response.data ? response.data.message : 'Unknown error'));
                    console.error('Error saving settings:', response.data ? response.data.message : 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                // Show error message
                $('#mqcrp-settings-error').show().addClass('mqcrp-notice-visible').find('p').text('Error: ' + status + ' - ' + error);
                console.error('AJAX error:', xhr.responseText);
                console.error('Status:', status);
                console.error('Error:', error);

                // If AJAX fails completely, use the fallback method
                if (status === 'error' || status === 'timeout' || status === 'parsererror') {
                    console.log('AJAX failed completely, using fallback method');
                    $('#mqcrp-settings-error').find('p').text('Error: AJAX request failed. Using fallback method.');

                    // Show and trigger the fallback submit button
                    $('#submit').show();
                    setTimeout(function() {
                        $('#submit').click();
                    }, 1000);
                }
            },
            complete: function() {
                // Reset button state
                $buttonText.text(originalText);
                $spinner.hide();
                $button.prop('disabled', false);
            }
        });
    });

    // Make notices dismissible
    $(document).on('click', '.notice-dismiss', function() {
        $(this).closest('.notice').hide();
    });

    // Add dismiss button to inline notices if they don't have one
    $('.mqcrp-inline-notice').each(function() {
        if ($(this).find('.notice-dismiss').length === 0) {
            $(this).append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');
        }
    });
});
