<?php
/**
 * Admin area display for the plugin.
 *
 * @since      0.1.1
 * @package    Mind_Qtrl_Community_Reactions_Pro_CE
 * @subpackage Mind_Qtrl_Community_Reactions_Pro_CE/admin/partials
 */

// Get plugin settings
$options = get_option('mqcrpce_settings', array());

// Default values
$replace_like_icon = isset($options['replace_like_icon']) ? $options['replace_like_icon'] : true;
$like_icon_color = isset($options['like_icon_color']) ? $options['like_icon_color'] : '#3498db';
$enable_custom_reactions = isset($options['enable_custom_reactions']) ? $options['enable_custom_reactions'] : true;
$improve_hover_styling = isset($options['improve_hover_styling']) ? $options['improve_hover_styling'] : true;
$hover_color = isset($options['hover_color']) ? $options['hover_color'] : '#3498db';
$remove_hover_background = isset($options['remove_hover_background']) ? $options['remove_hover_background'] : true;
$hide_failed_load_text = isset($options['hide_failed_load_text']) ? $options['hide_failed_load_text'] : true;
$custom_delay_time = isset($options['custom_delay_time']) ? $options['custom_delay_time'] : false;
$delay_time_seconds = isset($options['delay_time_seconds']) ? $options['delay_time_seconds'] : 1.0;
// Ensure the delay time is within the valid range
$delay_time_seconds = min(max(floatval($delay_time_seconds), 0.5), 5.0);

// Pure CE implementation option
$use_pure_ce = isset($options['use_pure_ce']) ? $options['use_pure_ce'] : false;

// Tooltip customization options
$customize_tooltips = isset($options['customize_tooltips']) ? $options['customize_tooltips'] : false;
$sync_tooltip_text_color = isset($options['sync_tooltip_text_color']) ? $options['sync_tooltip_text_color'] : false;
$custom_tooltip_text_color = isset($options['custom_tooltip_text_color']) ? $options['custom_tooltip_text_color'] : false;
$tooltip_text_color = isset($options['tooltip_text_color']) ? $options['tooltip_text_color'] : '#333333';

// Background color options
$sync_tooltip_bg_color_light = isset($options['sync_tooltip_bg_color_light']) ? $options['sync_tooltip_bg_color_light'] : true;
$sync_tooltip_bg_color_dark = isset($options['sync_tooltip_bg_color_dark']) ? $options['sync_tooltip_bg_color_dark'] : false;
$custom_tooltip_bg_color = isset($options['custom_tooltip_bg_color']) ? $options['custom_tooltip_bg_color'] : false;
$tooltip_bg_color = isset($options['tooltip_bg_color']) ? $options['tooltip_bg_color'] : '#f0f0f0';

// Slider values
$light_bg_factor = isset($options['light_bg_factor']) ? floatval($options['light_bg_factor']) : 0.85;
$dark_bg_factor = isset($options['dark_bg_factor']) ? floatval($options['dark_bg_factor']) : 0.5;

// For backward compatibility
$sync_tooltip_bg_color = $sync_tooltip_bg_color_light;

// Reaction types
$reaction_types = isset($options['reaction_types']) ? $options['reaction_types'] : array(
    'like' => array(
        'enabled' => true,
        'icon' => 'thumbs-up',
        'color' => '#3498db'
    ),
    'love' => array(
        'enabled' => true,
        'icon' => 'heart',
        'color' => '#e74c3c'
    ),
    'haha' => array(
        'enabled' => true,
        'icon' => 'laugh',
        'color' => '#f39c12'
    ),
    'wow' => array(
        'enabled' => true,
        'icon' => 'surprise',
        'color' => '#9b59b6'
    ),
    'sad' => array(
        'enabled' => true,
        'icon' => 'sad-tear',
        'color' => '#3498db'
    ),
    'angry' => array(
        'enabled' => true,
        'icon' => 'angry',
        'color' => '#e74c3c'
    )
);
?>

<div class="wrap mqcrp-admin">
    <div class="mqcrp-admin-header">
        <div class="mqcrp-title-container">
            <h1>Mind Qtrl | Community Reactions Pro</h1>
        </div>
        <div class="mqcrp-logo">
            <img src="<?php echo MQCRPCE_PLUGIN_URL; ?>admin/images/mind-qtrl-logo.png" alt="Mind Qtrl Logo">
        </div>
        <div class="mqcrp-version">
            <span>Version <?php echo MQCRPCE_VERSION; ?></span>
        </div>
    </div>

    <div class="mqcrp-admin-content">
        <form method="post" action="options.php" id="mqcrp-settings-form">
            <?php settings_fields('mqcrpce_settings_group'); ?>

            <div class="mqcrp-card">
                <h2>Basic Settings</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="replace_like_icon">Replace Like Icon</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="replace_like_icon">
                                    <input type="checkbox" name="mqcrpce_settings[replace_like_icon]" id="replace_like_icon" value="1" <?php checked($replace_like_icon, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Enable this option to replace the default Fluent Community heart icon with your custom image</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="custom_like_icon">Custom Like Icon</label>
                        </th>
                        <td>
                            <div class="mqcrp-image-upload">
                                <input type="hidden" name="mqcrpce_settings[custom_like_icon]" id="custom_like_icon" value="<?php echo esc_attr(isset($options['custom_like_icon']) ? $options['custom_like_icon'] : ''); ?>">
                                <div class="mqcrp-preview-image">
                                    <?php if (!empty($options['custom_like_icon'])) : ?>
                                        <img src="<?php echo esc_url($options['custom_like_icon']); ?>" alt="Custom Like Icon">
                                    <?php else : ?>
                                        <span class="mqcrp-no-image">No image selected</span>
                                    <?php endif; ?>
                                </div>
                                <button type="button" class="button mqcrp-upload-image">Upload Image</button>
                                <button type="button" class="button mqcrp-remove-image" <?php echo empty($options['custom_like_icon']) ? 'style="display:none;"' : ''; ?>>Remove Image</button>
                                <p class="description">Upload a custom image to replace the default heart icon. For best results, use a 32x32px transparent PNG image.</p>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="improve_hover_styling">Improve Hover Effects</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="improve_hover_styling">
                                    <input type="checkbox" name="mqcrpce_settings[improve_hover_styling]" id="improve_hover_styling" value="1" <?php checked($improve_hover_styling, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Enable smooth scaling and color transition effects when users hover over reaction buttons</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="hover_color">Glow Color</label>
                        </th>
                        <td>
                            <input type="color" name="mqcrpce_settings[hover_color]" id="hover_color" value="<?php echo esc_attr($hover_color); ?>">
                            <p class="description">Choose the glow color for hover effects. This will automatically sync with your first reaction type's glow color when Custom Reaction Types are enabled.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="remove_hover_background">Remove Hover Background</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="remove_hover_background">
                                    <input type="checkbox" name="mqcrpce_settings[remove_hover_background]" id="remove_hover_background" value="1" <?php checked($remove_hover_background, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Remove the background color that appears when hovering over reaction buttons in the Fluent Community feed</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="hide_failed_load_text">Hide Loading Error Text</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="hide_failed_load_text">
                                    <input type="checkbox" name="mqcrpce_settings[hide_failed_load_text]" id="hide_failed_load_text" value="1" <?php checked($hide_failed_load_text, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Hide the "Failed to load the list" text that briefly flashes when loading reaction user lists and replace it with a loading spinner</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="use_pure_ce">Use Pure Custom Elements</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="use_pure_ce">
                                    <input type="checkbox" name="mqcrpce_settings[use_pure_ce]" id="use_pure_ce" value="1" <?php checked($use_pure_ce, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Use a pure Custom Elements approach that completely replaces Fluent Community reaction elements instead of enhancing them. This provides better encapsulation and performance.</p>
                            <p class="description"><strong>Note:</strong> This is an experimental feature. If you experience any issues, you can disable it and return to the hybrid approach.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="custom_delay_time">Change Delay Time</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="custom_delay_time">
                                    <input type="checkbox" name="mqcrpce_settings[custom_delay_time]" id="custom_delay_time" value="1" <?php checked($custom_delay_time, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Customize the delay time before the reaction box disappears after mouse leaves</p>
                            <div id="delay-time-container" class="mqcrp-expandable-container" style="<?php echo $custom_delay_time ? '' : 'display: none;'; ?>">
                                <div class="mqcrp-slider-container">
                                    <input type="range" name="mqcrpce_settings[delay_time_seconds]" id="delay_time_seconds" min="0.5" max="5" step="0.25" value="<?php echo esc_attr($delay_time_seconds); ?>" class="mqcrp-range-slider">
                                    <div class="mqcrp-slider-value">
                                        <span id="delay-time-value"><?php echo esc_html($delay_time_seconds); ?></span> seconds
                                    </div>
                                </div>
                                <p class="description">Set the delay time from 0.5 to 5 seconds before the reaction box disappears after mouse leaves</p>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="customize_tooltips">Customize Tooltips</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="customize_tooltips">
                                    <input type="checkbox" name="mqcrpce_settings[customize_tooltips]" id="customize_tooltips" value="1" <?php checked($customize_tooltips, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Customize tooltip appearance for reaction types</p>
                            <div id="tooltip-options-container" class="mqcrp-expandable-container" style="<?php echo $customize_tooltips ? '' : 'display: none;'; ?>">
                                <div class="mqcrp-tooltip-options">
                                    <div class="mqcrp-option-row">
                                        <label for="sync_tooltip_text_color" class="mqcrp-option-label">
                                            <span class="mqcrp-option-title">Sync tooltip text color with glow color</span>
                                            <span class="mqcrp-option-description">Text color will match the reaction type's glow color</span>
                                        </label>
                                        <div class="mqcrp-toggle-container">
                                            <label class="mqcrp-switch" for="sync_tooltip_text_color">
                                                <input type="checkbox" name="mqcrpce_settings[sync_tooltip_text_color]" id="sync_tooltip_text_color" value="1" <?php checked($sync_tooltip_text_color, true); ?> class="mqcrp-exclusive-toggle" data-group="tooltip-text">
                                                <span class="mqcrp-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mqcrp-option-row">
                                        <label for="custom_tooltip_text_color" class="mqcrp-option-label">
                                            <span class="mqcrp-option-title">Use custom tooltip text color</span>
                                            <span class="mqcrp-option-description">Set a custom text color for all tooltips</span>
                                        </label>
                                        <div class="mqcrp-toggle-container">
                                            <label class="mqcrp-switch" for="custom_tooltip_text_color">
                                                <input type="checkbox" name="mqcrpce_settings[custom_tooltip_text_color]" id="custom_tooltip_text_color" value="1" <?php checked($custom_tooltip_text_color, true); ?> class="mqcrp-exclusive-toggle" data-group="tooltip-text">
                                                <span class="mqcrp-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div id="tooltip-text-color-container" class="mqcrp-color-picker-container" style="<?php echo $custom_tooltip_text_color ? '' : 'display: none;'; ?>">
                                        <label for="tooltip_text_color" class="mqcrp-color-picker-label">Tooltip Text Color</label>
                                        <input type="text" name="mqcrpce_settings[tooltip_text_color]" id="tooltip_text_color" value="<?php echo esc_attr($tooltip_text_color); ?>" class="color-picker" data-default-color="#333333">
                                    </div>
                                    <div class="mqcrp-option-row">
                                        <label for="sync_tooltip_bg_color_light" class="mqcrp-option-label">
                                            <span class="mqcrp-option-title">Use lighter glow color for tooltip background</span>
                                            <span class="mqcrp-option-description">Background color will be a lighter version of the reaction type's glow color</span>
                                        </label>
                                        <div class="mqcrp-toggle-container">
                                            <label class="mqcrp-switch" for="sync_tooltip_bg_color_light">
                                                <input type="checkbox" name="mqcrpce_settings[sync_tooltip_bg_color_light]" id="sync_tooltip_bg_color_light" value="1" <?php checked($sync_tooltip_bg_color_light, true); ?> class="mqcrp-exclusive-toggle" data-group="tooltip-bg">
                                                <span class="mqcrp-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div id="light-bg-factor-container" class="mqcrp-slider-container" style="<?php echo $sync_tooltip_bg_color_light ? '' : 'display: none;'; ?>">
                                        <label for="light_bg_factor" class="mqcrp-slider-label">Lightness Factor:</label>
                                        <input type="range" name="mqcrpce_settings[light_bg_factor]" id="light_bg_factor" min="0.5" max="0.95" step="0.05" value="<?php echo esc_attr($light_bg_factor); ?>" class="mqcrp-range-slider">
                                        <div class="mqcrp-slider-value">
                                            <span id="light-bg-factor-value"><?php echo esc_html($light_bg_factor); ?></span>
                                        </div>
                                        <p class="description">Higher values make the background color lighter</p>
                                    </div>

                                    <div class="mqcrp-option-row">
                                        <label for="sync_tooltip_bg_color_dark" class="mqcrp-option-label">
                                            <span class="mqcrp-option-title">Use darker glow color for tooltip background</span>
                                            <span class="mqcrp-option-description">Background color will be a darker version of the reaction type's glow color</span>
                                        </label>
                                        <div class="mqcrp-toggle-container">
                                            <label class="mqcrp-switch" for="sync_tooltip_bg_color_dark">
                                                <input type="checkbox" name="mqcrpce_settings[sync_tooltip_bg_color_dark]" id="sync_tooltip_bg_color_dark" value="1" <?php checked($sync_tooltip_bg_color_dark, true); ?> class="mqcrp-exclusive-toggle" data-group="tooltip-bg">
                                                <span class="mqcrp-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div id="dark-bg-factor-container" class="mqcrp-slider-container" style="<?php echo $sync_tooltip_bg_color_dark ? '' : 'display: none;'; ?>">
                                        <label for="dark_bg_factor" class="mqcrp-slider-label">Darkness Factor:</label>
                                        <input type="range" name="mqcrpce_settings[dark_bg_factor]" id="dark_bg_factor" min="0.2" max="0.8" step="0.05" value="<?php echo esc_attr($dark_bg_factor); ?>" class="mqcrp-range-slider">
                                        <div class="mqcrp-slider-value">
                                            <span id="dark-bg-factor-value"><?php echo esc_html($dark_bg_factor); ?></span>
                                        </div>
                                        <p class="description">Higher values make the background color darker</p>
                                    </div>
                                    <div class="mqcrp-option-row">
                                        <label for="custom_tooltip_bg_color" class="mqcrp-option-label">
                                            <span class="mqcrp-option-title">Use custom tooltip background color</span>
                                            <span class="mqcrp-option-description">Set a custom background color for all tooltips</span>
                                        </label>
                                        <div class="mqcrp-toggle-container">
                                            <label class="mqcrp-switch" for="custom_tooltip_bg_color">
                                                <input type="checkbox" name="mqcrpce_settings[custom_tooltip_bg_color]" id="custom_tooltip_bg_color" value="1" <?php checked($custom_tooltip_bg_color, true); ?> class="mqcrp-exclusive-toggle" data-group="tooltip-bg">
                                                <span class="mqcrp-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div id="tooltip-bg-color-container" class="mqcrp-color-picker-container" style="<?php echo $custom_tooltip_bg_color ? '' : 'display: none;'; ?>">
                                        <label for="tooltip_bg_color" class="mqcrp-color-picker-label">Tooltip Background Color</label>
                                        <input type="text" name="mqcrpce_settings[tooltip_bg_color]" id="tooltip_bg_color" value="<?php echo esc_attr($tooltip_bg_color); ?>" class="color-picker" data-default-color="#f0f0f0">
                                    </div>
                                </div>
                                <div class="mqcrp-tooltip-preview">
                                    <div class="mqcrp-tooltip-preview-title">Preview:</div>
                                    <div class="mqcrp-tooltip-preview-container">
                                        <div class="mqcrp-tooltip-preview-item" id="tooltip-preview">
                                            Custom Reaction Type
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="mqcrp-card">
                <h2>Custom Reaction Types</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="enable_custom_reactions">Enable Custom Reaction Types</label>
                        </th>
                        <td>
                            <div class="mqcrp-toggle-container">
                                <label class="mqcrp-switch" for="enable_custom_reactions">
                                    <input type="checkbox" name="mqcrpce_settings[enable_custom_reactions]" id="enable_custom_reactions" value="1" <?php checked($enable_custom_reactions, true); ?>>
                                    <span class="mqcrp-slider"></span>
                                </label>
                            </div>
                            <p class="description">Enable additional reaction types beyond the basic like. Each type can have a custom image, name, and color.</p>
                        </td>
                    </tr>
                </table>

                <div id="reaction-icons-container" style="<?php echo $enable_custom_reactions ? '' : 'display: none;'; ?>">
                    <h3 class="mqcrp-centered-heading">Reaction Type Settings</h3>
                    <p class="description mqcrp-centered-description">Drag reaction types to reorder them (except the main Like reaction). You can add up to 7 custom reaction types.</p>
                    <div class="mqcrp-reaction-grid" id="mqcrp-sortable-reactions">
                        <?php
                        $reaction_count = count($reaction_types);
                        foreach ($reaction_types as $type => $settings) :
                            $is_first_type = ($type === 'like');
                            $reaction_name = isset($settings['name']) ? $settings['name'] : ucfirst($type);
                            $reaction_image = isset($settings['image']) ? $settings['image'] : '';
                            $order = isset($settings['order']) ? intval($settings['order']) : 0;
                        ?>
                            <div class="mqcrp-reaction-item <?php echo !$is_first_type ? 'draggable' : ''; ?>" data-type="<?php echo esc_attr($type); ?>" data-order="<?php echo esc_attr($order); ?>">
                                <?php if (!$is_first_type) : ?>
                                <button type="button" class="mqcrp-delete-reaction" title="Delete this reaction type">
                                    <span class="dashicons dashicons-no-alt"></span>
                                </button>
                                <?php endif; ?>

                                <div class="mqcrp-reaction-header">
                                    <h4>
                                        <span class="mqcrp-editable-name" data-type="<?php echo esc_attr($type); ?>">
                                            <?php echo esc_html($reaction_name); ?>
                                        </span>
                                        <button type="button" class="mqcrp-edit-name" title="Edit name">
                                            <span class="dashicons dashicons-edit"></span>
                                        </button>
                                        <input type="hidden" name="mqcrpce_settings[reaction_types][<?php echo $type; ?>][name]" value="<?php echo esc_attr($reaction_name); ?>" class="mqcrp-name-input">
                                        <input type="hidden" name="mqcrpce_settings[reaction_types][<?php echo $type; ?>][order]" value="<?php echo esc_attr($order); ?>" class="mqcrp-order-input">
                                    </h4>
                                </div>

                                <div class="mqcrp-toggle-container">
                                    <label class="mqcrp-switch" for="reaction_type_<?php echo $type; ?>_enabled">
                                        <input type="checkbox" name="mqcrpce_settings[reaction_types][<?php echo $type; ?>][enabled]" id="reaction_type_<?php echo $type; ?>_enabled" value="1" <?php checked($settings['enabled'], true); ?>>
                                        <span class="mqcrp-slider"></span>
                                    </label>
                                </div>

                                <?php if (!$is_first_type) : ?>
                                <div class="mqcrp-image-upload reaction-image-upload">
                                    <input type="hidden" name="mqcrpce_settings[reaction_types][<?php echo $type; ?>][image]" value="<?php echo esc_attr($reaction_image); ?>" class="reaction-image-input">
                                    <div class="mqcrp-preview-image reaction-preview-image">
                                        <?php if (!empty($reaction_image)) : ?>
                                            <img src="<?php echo esc_url($reaction_image); ?>" alt="<?php echo esc_attr($reaction_name); ?> Icon">
                                        <?php else : ?>
                                            <span class="mqcrp-no-image">No image</span>
                                        <?php endif; ?>
                                    </div>
                                    <button type="button" class="button mqcrp-upload-reaction-image">Upload Image</button>
                                    <button type="button" class="button mqcrp-remove-reaction-image" <?php echo empty($reaction_image) ? 'style="display:none;"' : ''; ?>>Remove</button>
                                </div>
                                <?php else : ?>
                                <div class="mqcrp-reaction-icon">
                                    <input type="hidden" name="mqcrpce_settings[reaction_types][<?php echo $type; ?>][icon]" value="<?php echo esc_attr($settings['icon']); ?>">
                                    <p class="description">Uses main custom like image</p>
                                </div>
                                <?php endif; ?>

                                <div class="mqcrp-reaction-color">
                                    <label>Glow Color:</label>
                                    <?php if ($is_first_type) : ?>
                                        <input type="color" name="mqcrpce_settings[reaction_types][<?php echo $type; ?>][color]" value="<?php echo esc_attr($settings['color']); ?>" class="reaction-color-picker" readonly>
                                        <p class="description">Syncs with main Glow Color setting</p>
                                    <?php else : ?>
                                        <input type="color" name="mqcrpce_settings[reaction_types][<?php echo $type; ?>][color]" value="<?php echo esc_attr($settings['color']); ?>" class="reaction-color-picker">
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if ($reaction_count <= 7) : ?>
                        <div class="mqcrp-add-reaction" id="mqcrp-add-reaction-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <span class="mqcrp-add-reaction-text">Add New Reaction Type</span>
                            <span class="mqcrp-reaction-count"><?php echo $reaction_count - 1; ?>/7 Custom Types</span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="mqcrp-submit-container">
                <div class="mqcrp-notices-container">
                    <div id="mqcrp-settings-saved" class="notice notice-success is-dismissible mqcrp-inline-notice" style="display: none;">
                        <p><strong>Settings saved successfully!</strong></p>
                    </div>
                    <div id="mqcrp-settings-error" class="notice notice-error is-dismissible mqcrp-inline-notice" style="display: none;">
                        <p><strong>Error saving settings. Please try again.</strong></p>
                    </div>
                </div>
                <button type="button" id="mqcrp-save-settings" class="button button-primary">
                    <span class="mqcrp-button-text">Save Settings</span>
                    <span class="mqcrp-spinner" style="display: none;"></span>
                </button>
                <input type="submit" name="submit" id="submit" class="button" value="Save Settings (Fallback)" style="display: none;">
            </div>
        </form>

        <div class="mqcrp-admin-footer">
            <p>Mind Qtrl | Community Reactions Pro v<?php echo MQCRPCE_VERSION; ?> - Enhancing your community experience.</p>
        </div>
    </div>
</div>