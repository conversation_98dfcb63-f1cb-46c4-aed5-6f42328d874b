== Mind Qtrl Community Reactions Pro Changelog ==

= 0.4.0 - May 18, 2025 =
* Added synchronization of glow colors for selected custom reaction types
* Enhanced active reaction styling with custom glow colors
* Improved glow effect for custom reaction types
* Added data attributes for better tracking of active reaction types
* Extended pulse animation to all active custom reaction elements
* Improved CSS selectors for more consistent styling
* Enhanced visual feedback for active reactions
* Optimized glow color propagation to parent elements
* Fixed inconsistent glow colors between different reaction states
* Improved transition effects for reaction type changes

= 0.3.9 - May 17, 2025 =
* Fixed reaction box positioning in activity modals
* Fixed reaction box positioning on single activity pages
* Added specialized positioning system for different contexts
* Created new JavaScript file for handling complex positioning scenarios
* Improved reaction box visibility in modal dialogs
* Enhanced z-index handling for better stacking context
* Added special CSS classes for different positioning contexts
* Implemented mutation observers to handle dynamically added content
* Improved positioning calculations for better accuracy
* Added debug logging for easier troubleshooting

= 0.3.8 - May 16, 2025 =
* Improved centered description alignment in admin interface
* Enhanced CSS for description elements to ensure proper centering
* Fixed potential alignment issues with description text

= 0.3.7 - May 16, 2025 =
* Added option to use darker glow color for tooltip background with adjustable darkness slider
* Added adjustable lightness slider for the lighter glow color tooltip background option
* Renamed "Sync tooltip background color" to "Use lighter glow color for tooltip background"
* Added new "Use darker glow color for tooltip background" option
* Improved tooltip preview to show both light and dark background color options
* Added helper functions to calculate both lighter and darker color variations
* Enhanced slider UI with value display and better styling
* Updated sanitization to handle new slider values and ensure proper option exclusivity
* Improved backward compatibility for existing settings
* Added detailed descriptions for slider options

= 0.3.6 - May 15, 2025 =
* Added option to set a custom text color for all custom reaction tooltips
* Implemented exclusive toggle behavior between sync tooltip text color and custom tooltip text color
* Added color picker for custom tooltip text color
* Enhanced tooltip preview to show custom text color
* Centered "Reaction Type Settings" heading and description text
* Improved tooltip styling with better text color handling
* Added expandable container for custom tooltip text color settings
* Updated sanitization to handle new tooltip text color settings
* Fixed tooltip text color application in the frontend
* Improved UI consistency with centered headings

= 0.3.5 - May 14, 2025 =
* Added option to set a custom background color for all custom reaction tooltips
* Implemented exclusive toggle behavior between sync tooltip background color and custom tooltip background color
* Added color picker for custom tooltip background color
* Enhanced tooltip preview to show custom background color
* Improved tooltip styling with better color handling
* Added expandable container for custom tooltip background color settings
* Updated sanitization to handle new tooltip background color settings
* Fixed tooltip background color application in the frontend
* Improved compatibility with Fluent Community theme variables

= 0.3.4 - May 13, 2025 =
* Updated delay time slider range to 0.5-5 seconds (minimum value increased from 0 to 0.5 seconds)
* Updated delay time slider description to be more clear
* Improved sanitization to enforce the new minimum delay time value
* Enhanced user experience by ensuring a minimum delay time for better usability

= 0.3.3 - May 12, 2025 =
* Fixed reaction box movement issue - now only moves down after the delay
* Added customizable delay time setting with slider in Basic Settings
* Added expandable container for delay time settings
* Implemented slider with range from 0 to 5 seconds in 0.25 second increments
* Added real-time slider value display
* Improved styling for slider and expandable container
* Added proper sanitization for delay time settings
* Improved transition animations for reaction box
* Fixed z-index issues with reaction box tooltips
* Added support for disabling delay completely (0 seconds)

= 0.3.2 - May 11, 2025 =
* Fixed issue with custom reaction type images not switching properly in the reaction box
* Improved icon element detection with multiple fallback methods
* Enhanced CSS selectors to target the specific .feed_footer .fcom_reaction .el-icon path
* Added forced background image clearing before applying new images
* Implemented !important flags to ensure styles override any existing ones
* Added additional CSS classes for better targeting of custom reaction elements
* Improved SVG hiding when custom images are applied
* Enhanced debugging with computed style logging
* Added small delay between clearing and setting background images for better transitions
* Improved compatibility with different Fluent Community layouts

= 0.3.1 - May 10, 2025 =
* Improved admin UI by moving save settings notice to the left side of the save button
* Enhanced notice styling with smoother animations and better visibility
* Consolidated notices into a dedicated container for better organization
* Added subtle entrance animation for notices to improve user experience
* Fixed duplicate success notice issue
* Improved responsive layout of the settings form footer
* Updated JavaScript to handle the new notice structure

= 0.3.0 - May 9, 2025 =
* Improved reaction type switching with instant background-image replacement
* Added support for switching between multiple reaction types without toggling off
* Implemented smarter reaction handling that only toggles off when clicking an already active reaction
* Enhanced animation effects for reaction type changes with smooth transitions
* Added subtle pulse animation for active reactions
* Improved performance with hardware acceleration and optimized animations
* Fixed issues with reaction type persistence when switching between types
* Added custom event for reaction type changes to improve integration with other scripts
* Improved debugging and error handling for reaction type switching
* Enhanced compatibility with different Fluent Community layouts

= 0.2.9 - May 8, 2025 =
* Improved integration of custom reaction type images in user lists
* Added API-based approach to determine reaction types for each user
* Implemented network request interception to capture reaction data
* Enhanced user experience with tooltips showing reaction type names
* Added smooth animations when reaction icons appear in user lists
* Improved dark mode support for reaction icons and tooltips
* Fixed positioning of reaction icons in user lists
* Added proper error handling and debugging for API integration
* Optimized performance by caching reaction data
* Enhanced compatibility with different Fluent Community layouts

= 0.2.8 - May 7, 2025 =
* Enhanced the option to hide the "Failed to load the list" text with multiple improvements
* Added advanced CSS selectors to target all possible empty state elements
* Implemented multiple layers of defense against the flashing text
* Added JavaScript-based empty state detection and hiding
* Enhanced loading spinner with pulse animation and subtle background
* Added fade-in animation for user list items when they appear
* Improved compatibility with different Fluent Community themes and layouts
* Added support for tooltip elements in addition to popovers
* Enhanced error handling and debugging
* Improved performance by optimizing CSS and JavaScript

= 0.2.7 - May 6, 2025 =
* Added option to hide "Failed to load the list" text that briefly flashes when loading reaction user lists
* Added loading spinner animation to replace the error text for a better user experience
* Improved CSS for user lists in reaction count popovers
* Enhanced body class handling for better CSS targeting
* Added support for both WordPress and Fluent Community portal environments
* Improved dark mode compatibility for the loading spinner

= 0.2.7 - May 6, 2025 =
* Added option to hide the "Failed to load the list" text that briefly flashes when loading reaction user lists
* Implemented a loading spinner to replace the error text for a better user experience
* Enhanced CSS implementation for the hide failed load text option
* Improved JavaScript implementation to ensure the option works in both WordPress and Fluent Community portal
* Added direct CSS injection for Fluent Community portal to ensure consistent behavior
* Refactored body class handling for better organization and maintainability
* Updated documentation and settings descriptions

= 0.2.6 - May 5, 2025 =
* Improved custom reaction icons in user lists with Fluent Community API integration
* Added dynamic reaction type detection for each user in the reactions list
* Implemented API-based approach to fetch accurate reaction types for each user
* Enhanced animation effects for reaction icons with smooth pop-in transitions
* Added hover effects to reaction icons in user lists
* Improved error handling with fallback to default reaction type
* Added support for both feed and comment reactions
* Enhanced debugging with detailed console logs
* Improved z-index handling to prevent visual issues

= 0.2.5 - May 4, 2025 =
* Added custom reaction icons to user lists in reaction count popovers
* Enhanced user experience by showing which reaction type each user selected
* Implemented automatic detection of reaction types for each activity
* Added tooltips to reaction icons in user lists
* Improved positioning of reaction icons in user list items
* Added dark mode support for reaction icons in popovers
* Enhanced mutation observer to detect dynamically added popovers
* Added detailed debug logging for easier troubleshooting

= 0.2.4 - May 3, 2025 =
* Enhanced custom reaction icon replacement with creative transitions
* Added activity-specific reaction icon updates to prevent interference between activities
* Implemented two methods for icon replacement: transition animation and overlay approach
* Added unique identifiers for each reaction to ensure targeted updates
* Improved visual feedback when selecting custom reaction types
* Added smooth scale and blur transitions for a more polished user experience
* Enhanced pseudo-element approach for better compatibility with Fluent Community
* Added detailed debug logging for easier troubleshooting

= 0.2.3 - May 2, 2025 =
* Fixed reaction preview image horizontal centering in WordPress admin
* Improved reaction image upload container styling
* Added proper centering for reaction preview images
* Enhanced image display with consistent margins
* Improved overall admin UI consistency

= 0.2.2 - May 1, 2025 =
* Fixed background image replacement not working when selecting custom reaction types
* Improved icon element selection with more specific selectors
* Added forced style application with !important to override conflicting styles
* Fixed reaction box positioning on single activity routes
* Added specific CSS for single activity routes to ensure proper positioning
* Increased z-index values to ensure reaction boxes are always visible
* Added debug logging to help identify issues with icon replacement
* Improved CSS for custom reaction icons to ensure proper display
* Added force repaint to ensure style changes take effect immediately
* Enhanced compatibility with Fluent Community's dynamic content loading

= 0.2.1 - April 30, 2025 =
* Added feature to replace reaction icon with selected custom reaction icon
* Implemented instant background-image replacement when a custom reaction is selected
* Added custom glow effect for reaction icons on hover with custom glow color
* Added custom glow effect for active reactions with custom glow color
* Improved styling for custom reaction icons
* Enhanced user experience with visual feedback for selected reactions
* Added proper CSS variables for consistent glow colors
* Fixed icon sizing and positioning for better visual appearance

= 0.2.0 - April 29, 2025 =
* Fixed "Remove Hover Background" option not working in Fluent Community portal
* Added multiple approaches to ensure hover background is removed
* Implemented JavaScript-based solution for dynamic elements
* Added MutationObserver to catch newly added elements
* Enhanced CSS selectors for better specificity
* Added direct style manipulation for maximum compatibility
* Improved cross-browser compatibility

= 0.1.9 - April 28, 2025 =
* Improved admin UI by moving error notice to the submit container
* Positioned error notice to the left of the save settings button
* Added inline styling for error notices in the submit container
* Enhanced error notice visibility and user experience
* Added automatic dismiss button for inline notices
* Improved responsive layout of the submit container
* Updated JavaScript to handle the new error notice position

= 0.1.8 - April 27, 2025 =
* Fixed "Remove Hover Background" CSS not loading properly in Fluent Community portal
* Added multiple methods to ensure CSS is properly applied in all contexts
* Added direct CSS injection for Fluent Community portal
* Added body class for more reliable CSS targeting
* Improved conditional loading to ensure CSS is applied regardless of other settings
* Enhanced compatibility with Fluent Community's portal architecture

= 0.1.7 - April 26, 2025 =
* Fixed hover delay issue for mqcrp-reaction-box
* Removed CSS transition-delay for appearing (no delay on hover)
* Implemented JavaScript-based delay for disappearing (1 second delay)
* Added timeout management to prevent race conditions
* Improved hover detection with better event handling
* Added timeout cancellation when re-entering elements
* Enhanced user experience with immediate appearance and delayed disappearance
* Fixed potential issues with multiple hover events
* Improved code organization and comments

= 0.1.6 - April 25, 2025 =
* Added new "Remove Hover Background" option to basic settings
* Option is enabled by default for better user experience
* Added CSS to remove background color when hovering over reaction buttons in feed
* Improved visual consistency in the Fluent Community feed
* Enhanced customization options for reaction button styling

= 0.1.5 - April 24, 2025 =
* Left-aligned the arrow indicator on the mqcrp-reaction-box
* Positioned the arrow at 20px from the left edge
* Ensured consistent arrow positioning in both light and dark modes
* Added specific CSS rule for dark mode arrow alignment

= 0.1.4 - April 23, 2025 =
* Updated tooltip background color to use --fcom-secondary-content-bg with increased brightness
* Changed tooltip font color to use --fcom-menu-text for better readability
* Repositioned reaction box to center above its parent container
* Set fixed dimensions for reaction button images (32px × 32px)
* Added min-width, min-height, max-width, and max-height to ensure consistent button image sizes
* Centered the arrow indicator below the reaction box
* Improved tooltip styling in both light and dark modes
* Enhanced positioning consistency across different screen sizes
* Fixed transform values to maintain proper centering during animations
* Added subtle box-shadow to tooltips for better visibility

= 0.1.3 - April 22, 2025 =
* Fixed delay issue so reaction box appears immediately on hover
* Ensured delay is only applied when the reaction box is disappearing
* Improved transition timing for more responsive appearance
* Explicitly set transition-delay: 0s when showing the reaction box
* Optimized CSS and JavaScript to prioritize immediate visibility
* Reordered style application to ensure no delay when appearing
* Maintained 1-second delay before disappearing for better UX
* Enhanced hover detection for more responsive interaction

= 0.1.2 - April 21, 2025 =
* Repositioned the reaction box arrow to align with the left side
* Fixed cutoff issues in containers with overflow: hidden
* Added fallback with position: fixed for extreme cases
* Added automatic detection of cutoff reaction boxes
* Implemented dynamic positioning based on viewport constraints
* Added specific CSS fixes for el-scrollbar and feed_layout containers
* Improved z-index handling to ensure reaction boxes appear above other elements
* Added cleanup for fixed positioning after hiding
* Enhanced overflow handling for parent containers
* Added intelligent positioning system that adapts to available space

= 0.1.1 - April 20, 2025 =
* Updated custom reaction types to scale from center on hover
* Removed vertical shift on hover for more consistent scaling
* Added smoother animation with slight bounce effect
* Improved transform-origin property to ensure centered scaling
* Enhanced transition timing for more natural hover effect
* Added will-change: transform for better performance
* Ensured consistent scaling behavior in both light and dark modes
* Optimized CSS transitions for smoother animations

= 0.1.0 - April 19, 2025 =
* Fixed mqcrp-reaction-box being partly cut off
* Changed animation to slide up/down instead of left/right
* Positioned the reaction box with its left side aligned with parent container
* Added 1-second delay before the reaction box disappears
* Ensured each reaction button's hover glow color syncs with its custom glow color
* Enhanced glow effect with stronger drop-shadow
* Added data-glow-color attribute for easier debugging
* Improved transition effects for smoother animations
* Fixed positioning issues with parent containers
* Enhanced dark mode support for custom glow colors
* Added explicit transform values for better animation control
* Improved hover detection with better event handling

= 0.0.9 - April 18, 2025 =
* Fixed mqcrp-reaction-box hover functionality not working on frontend
* Added global initialization function to ensure reaction boxes are created
* Added fallback hover detection using JavaScript event listeners
* Temporarily forced enable custom reactions for testing
* Added debug mode with visual indicators for reaction elements
* Added multiple initialization methods for better compatibility
* Improved hover detection with both CSS and JavaScript approaches
* Added periodic checks to ensure reaction boxes are created
* Enhanced CSS with !important flags to override conflicting styles
* Added display:flex to ensure reaction boxes are visible
* Added .hover class for JavaScript-based hover detection
* Improved error handling and debugging

= 0.0.8 - April 17, 2025 =
* Fixed mqcrp-reaction-box hover functionality not working
* Added direct JavaScript event listeners for hover effects
* Fixed CSS conflicts with multiple hover rules
* Improved pointer-events handling for better interaction
* Consolidated CSS rules for better maintainability
* Enhanced z-index handling to prevent cutoff issues
* Added fallback hover detection for better compatibility
* Improved debugging for hover-related issues

= 0.0.7 - April 16, 2025 =
* Improved theme integration with Fluent Community CSS variables
* Used --fcom-secondary-content-bg for reaction box background color
* Used --fcom-text-link for reaction box arrow border color
* Removed overflow-x: auto for cleaner appearance
* Enhanced dark mode support with theme variables
* Improved compatibility with Fluent Community themes
* Better visual consistency across light and dark modes
* Added fallbacks for CSS variables for backward compatibility
* Fixed issue with saving custom reaction types options
* Improved AJAX settings saving with forced update fallback
* Fixed tooltip labels being cut off when outside the reaction box
* Increased z-index for tooltips to ensure they're always visible
* Added special positioning for tooltips at the edges of the reaction box
* Improved tooltip width calculation based on text length

= 0.0.6 - April 15, 2025 =
* Enhanced tooltips for custom reaction types with dynamic colors
* Added direct event listeners for better tooltip behavior
* Improved tooltip styling with arrow indicators
* Added custom background colors based on reaction glow color
* Improved tooltip animations and transitions
* Enhanced dark mode support for tooltips
* Fixed tooltip positioning and visibility issues
* Added better hover effects for reaction buttons
* Fixed duplicate "Reactions Pro" menu item in WordPress admin
* Improved menu registration to prevent duplicate items
* Changed menu creation approach for better compatibility
* Fixed reaction box cutoff issues with improved positioning
* Increased z-index to ensure reaction box is always visible
* Added arrow indicator to reaction box for better usability
* Enhanced dark mode styling with better contrast and visibility
* Added special positioning for elements near the top of the page
* Improved mobile responsiveness with max-width and overflow handling
* Fixed reaction box cutoff issues caused by parent elements with overflow:hidden
* Added dynamic positioning for reaction boxes to escape overflow:hidden containers
* Implemented position:fixed fallback for elements inside scrollable containers
* Added detection of parent container overflow properties

= 0.0.5 - April 14, 2025 =
* Fixed logo alignment in admin header
* Improved admin UI layout and spacing
* Added Mind Qtrl menu integration
* Made MQ Reactions Pro a submenu item of Mind Qtrl menu
* Aligned save settings button to the right
* Improved compatibility with other Mind Qtrl plugins
* Fixed frontend code not working in Fluent Community portal
* Updated CSS selectors to match current Fluent Community DOM structure
* Improved JavaScript with better debugging and error handling
* Added double initialization to ensure plugin loads properly
* Added Fluent Community portal hooks for proper resource loading
* Simplified MutationObserver implementation for better performance
* Added more specific targeting for reaction elements
* Fixed duplicate menu item issue in WordPress admin
* Added AJAX form submission for better user experience
* Added success notification when settings are saved
* Added loading indicator during settings save
* Improved error handling for settings submission
* Fixed custom reaction types hover box not appearing in Fluent Community portal
* Improved CSS and JS loading in Fluent Community portal
* Added direct CSS/JS output for Fluent Community portal compatibility
* Fixed settings not saving properly in admin
* Added fallback method for saving settings when AJAX fails
* Improved AJAX handler registration
* Added comprehensive error logging for troubleshooting
* Fixed hover reaction box not working in Fluent Community portal
* Implemented proper reaction box positioning and styling
* Added multiple initialization attempts for better compatibility
* Fixed empty output_heart_replacement_script method

= 0.0.4 - April 13, 2025 =
* Fixed hover reaction box functionality in Fluent Community's frontend portal
* Improved custom like icon replacement to only target proper Fluent Community heart icons
* Added more specific CSS selectors to prevent affecting unrelated elements
* Enhanced JavaScript with better element filtering and targeting
* Added debugging support for easier troubleshooting
* Improved z-index handling for better stacking and visibility
* Added specific positioning for different Fluent Community contexts
* Enhanced reaction box visibility and interaction
* Added ability to add/delete custom reaction types (up to 7 custom types)
* Implemented drag-and-drop reordering for custom reaction types
* Added close icon to delete custom reaction types
* Centered reaction preview images horizontally
* Made MQ Reactions Pro a submenu item of the Mind QTRL menu
* Added compatibility to check for existing Mind QTRL menu

= 0.0.3 - April 12, 2025 =
* Improved Fluent Community integration using proper hooks and APIs
* Added support for Fluent Community's event system
* Optimized JavaScript to follow Fluent Community guidelines
* Improved performance by reducing DOM operations
* Enhanced compatibility with Fluent Community updates

= 0.0.2 - April 12, 2025 =
* Enhanced UI with Mind Qtrl dark theme styling
* Added custom image upload for each reaction type (32x32px recommended)
* Added ability to edit reaction type names with custom tooltips
* Implemented hover reaction box similar to BuddyPress Status
* Added hover reaction box that displays all available reaction types
* Improved hover effects and styling for reaction buttons
* Renamed "Color" to "Glow Color" for reaction types
* Synchronized hover color with the first reaction type's glow color when Custom Reaction Types are enabled
* Added better form styling and improved admin interface
* Fixed various UI issues and improved user experience

= 0.0.1 - April 12, 2025 =
* Initial release
* Basic functionality for replacing like icon with custom image (32x32px recommended)
* Support for custom reaction types (love, haha, wow, sad, angry)
* Admin interface for configuration
* Enhanced hover effects for reaction buttons
* Integration with Fluent Community
