# Mind Qtrl Community Reactions - Development Plan

This document analyzes the BuddyPress Status plugin's reaction functionality and outlines how similar features can be implemented in the Mind Qtrl Community Reactions plugin for Fluent Community.

## BuddyPress Status Reaction Features Analysis

### Core Reaction Functions

1. **`bpst_activity_reaction`**
   - Main reaction functionality handler
   - Processes user reactions to activities
   - Stores reaction data in activity meta
   - Updates user-specific reaction data
   - Triggers notification events

2. **`bpst_activity_reaction_remove_action`**
   - Removes reactions from activities
   - Updates activity meta to reflect removal
   - Cleans up user reaction data
   - Updates reaction counts

3. **`bpsts_add_reactions_html`**
   - Renders the reaction UI elements
   - Displays reaction buttons and icons
   - Shows reaction counts
   - Handles different themes and layouts

4. **`bpst_bp_activity_entry_content`**
   - Displays the list of users who reacted to an activity
   - Shows reaction type images next to usernames
   - Creates a hover effect to display reaction details
   - Formats the reaction display based on count

5. **`bpst_ajax_remove_content_emoji`**
   - Replaces the current activity reaction icon
   - Updates the UI when a user changes their reaction
   - Handles the visual transition between reaction types

### Activity Integration Functions

6. **`bpst_status_register_activity_actions`**
   - Registers activity stream actions for reactions/likes
   - Defines activity types and labels
   - Sets up action handlers for reaction events
   - Integrates with BuddyPress activity component

7. **`bpst_status_activity_comment_content`**
   - Adds reaction/like content to activity comments
   - Extends comment functionality with reaction support
   - Formats reaction data within comments
   - Ensures proper display in activity streams

8. **`bpst_status_update_html`**
   - Renders reaction/like HTML on post activity
   - Provides UI for adding reactions to new posts
   - Integrates with activity posting forms
   - Supports different themes and layouts

### UI and Styling Functions

9. **`bpst_activity_embed_add_inline_styles`**
   - Adds CSS styles for reaction elements
   - Ensures consistent styling across themes
   - Handles responsive design for reaction UI
   - Supports embedded activity content

10. **`bpst_bp_get_activity_reaction_notifications`**
    - Sets up notifications for activity reactions/likes
    - Formats notification content
    - Handles notification delivery
    - Manages notification preferences

## Fluent Community Integration Approach

Fluent Community uses a different architecture than BuddyPress, with a Vue.js frontend and REST API backend. The reaction system in Fluent Community is built around:

1. **Database Structure**:
   - Uses `fcom_post_reactions` table
   - Stores reaction type, user ID, object ID, and object type
   - Supports multiple reaction types

2. **API Endpoints**:
   - `/feeds/{feed_id}/reactions` for getting reactions
   - POST/DELETE methods for adding/removing reactions
   - Reaction counts stored in feed records

3. **Frontend Components**:
   - Vue.js components for reaction UI
   - `FCReactionBar` component for displaying reaction buttons
   - CSS classes like `.fcom_reaction`, `.fcom_reaction_list`

## Implementation Plan for Mind Qtrl Community Reactions

### Core Features to Implement

1. **Multiple Reaction Types**
   - Add support for additional reaction types (love, haha, wow, sad, angry, confused)
   - Create a reaction selector UI
   - Store and retrieve multiple reaction types

2. **Reaction Display**
   - Show who reacted to posts/activities by integrating with Fluent Community's el-popper component that is already used for reaction counts with custom reaction types images shown next to usernames
   - Display reaction counts
   - Create hover effects to show reaction details

3. **Notification Integration**
   - Trigger notifications for new reactions
   - Format notification content
   - Support different notification types

4. **API Integration**
   - Hook into Fluent Community's REST API
   - Extend reaction endpoints as needed
   - Maintain compatibility with Fluent Community updates

### Technical Implementation

1. **JavaScript Observers**
   - Use MutationObserver to detect new feed items
   - Apply custom styling to dynamically loaded content
   - Intercept reaction events for custom handling

2. **CSS Customization**
   - Target Fluent Community's reaction elements
   - Apply custom styling while maintaining responsiveness
   - Support both light and dark themes

3. **Backend Integration**
   - Use unique namespaces with mqcr prefix
   - Hook into Fluent Community's reaction system
   - Extend the reaction model as needed and use mqcr prefix for custom reactions
   - Maintain compatibility with core functionality

4. **Admin Interface**
   - Maintain but improve the modern dark admin UI with Mind Qtrl branding
   - Provide settings for customizing reactions
   - Allow replacing the custom reaction types with custom images
   - Allow replacing the custom reaction types with custom names for the frontend hover labels
   - Include preview functionality

## Feature Comparison with BuddyPress Status

| Feature | BuddyPress Status | Mind Qtrl Community Reactions |
|---------|-------------------|-------------------------------|
| Multiple reaction types | ✅ | ✅ |
| Custom reaction icons | ✅ | ✅ |
| User reaction lists | ✅ | ✅ |
| Notification support | ✅ | ✅ |
| Activity integration | ✅ | ✅ |
| Comment reactions | ✅ | ✅ |
| Mobile responsiveness | ⚠️ Limited | ✅ |
| Modern UI | ⚠️ Dated | ✅ |
| Performance | ⚠️ DOM-heavy | ✅ Optimized |
| Vue.js integration | ❌ | ✅ |

## Development Roadmap

1. **Phase 2: Enhanced Features**
   - Add multiple reaction types
   - Implement reaction selector UI
   - Create reaction display components

2. **Phase 3: Integration & Optimization**
   - Integrate with notifications
   - Optimize performance
   - Add advanced customization options

3. **Phase 4: Testing & Refinement**
   - Cross-browser testing
   - Mobile responsiveness
   - Performance benchmarking

## Conclusion

The Mind Qtrl Community Reactions plugin will provide a modern, performant alternative to BuddyPress Status reactions, specifically designed for Fluent Community. By leveraging Fluent Community's Vue.js architecture and REST API, we can create a seamless integration that enhances the user experience while maintaining compatibility with future updates.
