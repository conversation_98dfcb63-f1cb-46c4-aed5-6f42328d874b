<?php
/**
 * Fired during plugin activation.
 *
 * @since      0.1.1
 * @package    Mind_Qtrl_Community_Reactions_Pro_CE
 * @subpackage Mind_Qtrl_Community_Reactions_Pro_CE/includes
 */

class Mind_Qtrl_Community_Reactions_Pro_Activator {

    /**
     * Initialize plugin settings on activation.
     *
     * @since    0.1.1
     */
    public static function activate() {
        // Initialize default settings if they don't exist
        if (!get_option('mqcrpce_settings')) {
            $default_settings = array(
                'replace_like_icon' => true,
                'custom_like_icon' => '',
                'enable_custom_reactions' => true,
                'reaction_types' => array(
                    'like' => array(
                        'enabled' => true,
                        'icon' => 'thumbs-up',
                        'color' => '#3498db',
                        'name' => 'Like'
                    ),
                    'love' => array(
                        'enabled' => true,
                        'image' => '',
                        'color' => '#e74c3c',
                        'name' => 'Love'
                    ),
                    'haha' => array(
                        'enabled' => true,
                        'image' => '',
                        'color' => '#f39c12',
                        'name' => 'Haha'
                    ),
                    'wow' => array(
                        'enabled' => true,
                        'image' => '',
                        'color' => '#9b59b6',
                        'name' => 'Wow'
                    ),
                    'sad' => array(
                        'enabled' => true,
                        'image' => '',
                        'color' => '#3498db',
                        'name' => 'Sad'
                    ),
                    'angry' => array(
                        'enabled' => true,
                        'image' => '',
                        'color' => '#e74c3c',
                        'name' => 'Angry'
                    )
                ),
                'improve_hover_styling' => true,
                'hover_color' => '#3498db',
                'use_pure_ce' => false
            );

            update_option('mqcrpce_settings', $default_settings);
        }
    }
}
