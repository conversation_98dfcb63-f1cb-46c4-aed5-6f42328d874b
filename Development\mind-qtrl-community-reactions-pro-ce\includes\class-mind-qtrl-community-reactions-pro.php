<?php
/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      0.1.1
 * @package    Mind_Qtrl_Community_Reactions_Pro_CE
 * @subpackage Mind_Qtrl_Community_Reactions_Pro_CE/includes
 */

class Mind_Qtrl_Community_Reactions_Pro {

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    0.1.1
     * @access   protected
     * @var      Mind_Qtrl_Community_Reactions_Pro_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    0.1.1
     * @access   protected
     * @var      string    $plugin_name    The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    0.1.1
     * @access   protected
     * @var      string    $version    The current version of the plugin.
     */
    protected $version;

    /**
     * Define the core functionality of the plugin.
     *
     * @since    0.1.1
     */
    public function __construct() {
        if ( defined( 'MQCRPCE_VERSION' ) ) {
            $this->version = MQCRPCE_VERSION;
        } else {
            $this->version = '0.1.7';
        }
        $this->plugin_name = 'mind-qtrl-community-reactions-pro-ce';

        $this->load_dependencies();
        $this->define_admin_hooks();
        $this->define_public_hooks();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * @since    0.1.1
     * @access   private
     */
    private function load_dependencies() {
        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once MQCRPCE_PLUGIN_PATH . 'includes/class-mind-qtrl-community-reactions-pro-loader.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once MQCRPCE_PLUGIN_PATH . 'admin/class-mind-qtrl-community-reactions-pro-admin.php';

        /**
         * The class responsible for defining all actions that occur in the public-facing
         * side of the site.
         */
        require_once MQCRPCE_PLUGIN_PATH . 'public/class-mind-qtrl-community-reactions-pro-public.php';

        $this->loader = new Mind_Qtrl_Community_Reactions_Pro_Loader();
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    0.1.1
     * @updated  0.1.7
     * @access   private
     */
    private function define_admin_hooks() {
        $plugin_admin = new Mind_Qtrl_Community_Reactions_Pro_Admin( $this->get_plugin_name(), $this->get_version() );

        $this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
        $this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
        $this->loader->add_action( 'admin_menu', $plugin_admin, 'add_plugin_admin_menu' );
        $this->loader->add_action( 'admin_init', $plugin_admin, 'register_settings' );

        // Register AJAX handlers
        $this->loader->add_action( 'init', $plugin_admin, 'register_ajax_handlers' );
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since    0.1.1
     * @updated  0.1.7
     * @access   private
     */
    private function define_public_hooks() {
        $plugin_public = new Mind_Qtrl_Community_Reactions_Pro_Public( $this->get_plugin_name(), $this->get_version() );

        // Enqueue styles and scripts for both WordPress and Fluent Community portal
        $this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_styles' );
        $this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_scripts' );
        $this->loader->add_action( 'fluent_community/portal_head', $plugin_public, 'enqueue_styles' );
        $this->loader->add_action( 'fluent_community/portal_footer', $plugin_public, 'enqueue_scripts' );

        // Add hooks to replace the like icon and initialize the plugin
        $this->loader->add_action( 'init', $plugin_public, 'init' );
    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    0.1.1
     */
    public function run() {
        $this->loader->run();
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     0.1.1
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @since     0.1.1
     * @return    Mind_Qtrl_Community_Reactions_Pro_Loader    Orchestrates the hooks of the plugin.
     */
    public function get_loader() {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     0.1.1
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }
}


