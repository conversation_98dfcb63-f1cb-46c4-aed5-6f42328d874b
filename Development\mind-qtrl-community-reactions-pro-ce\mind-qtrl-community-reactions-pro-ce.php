<?php
/**
 * The plugin bootstrap file
 *
 * @link              https://mindqtrl.com/
 * @since             0.0.1
 * @package           Mind_Qtrl_Community_Reactions_Pro_CE
 *
 * @wordpress-plugin
 * Plugin Name:       Mind Qtrl | Community Reactions Pro CE
 * Plugin URI:        https://mindqtrl.com/
 * Description:       Enhanced Fluent Community reactions with custom reaction types using Web Components (Custom Elements).
 * Version:           0.1.8
 * Author:            Mind Qtrl
 * Author URI:        https://mindqtrl.com/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       mind-qtrl-community-reactions-pro-ce
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Currently plugin version and constants
 */
define('MQCRPCE_VERSION', '0.1.8');
define('MQCRPCE_PLUGIN_FILE', __FILE__);
define('MQCRPCE_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('MQCRPCE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MQCRPCE_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * The code that runs during plugin activation.
 */
function activate_mind_qtrl_community_reactions_pro_ce() {
    require_once MQCRPCE_PLUGIN_PATH . 'includes/class-mind-qtrl-community-reactions-pro-activator.php';
    Mind_Qtrl_Community_Reactions_Pro_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_mind_qtrl_community_reactions_pro_ce() {
    require_once MQCRPCE_PLUGIN_PATH . 'includes/class-mind-qtrl-community-reactions-pro-deactivator.php';
    Mind_Qtrl_Community_Reactions_Pro_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_mind_qtrl_community_reactions_pro_ce');
register_deactivation_hook(__FILE__, 'deactivate_mind_qtrl_community_reactions_pro_ce');

/**
 * The core plugin class
 */
require_once MQCRPCE_PLUGIN_PATH . 'includes/class-mind-qtrl-community-reactions-pro.php';

/**
 * Begins execution of the plugin.
 */
function run_mind_qtrl_community_reactions_pro_ce() {
    $plugin = new Mind_Qtrl_Community_Reactions_Pro();
    $plugin->run();
}

/**
 * Initialize the plugin on plugins_loaded to ensure Fluent Community is loaded
 */
function mind_qtrl_community_reactions_pro_ce_init() {
    // Check if Fluent Community is active
    if (!defined('FLUENT_COMMUNITY_PLUGIN_VERSION')) {
        add_action('admin_notices', function() {
            echo '<div class="error"><p>Mind Qtrl Community Reactions Pro CE requires Fluent Community plugin to be installed and activated.</p></div>';
        });
        return;
    }

    run_mind_qtrl_community_reactions_pro_ce();
}

add_action('plugins_loaded', 'mind_qtrl_community_reactions_pro_ce_init');


