/**
 * Mind Qtrl Community Reactions Pro CE - Pure CE Implementation
 *
 * This CSS file provides additional styling for the pure Custom Elements implementation.
 *
 * @since 0.1.6
 * @updated 0.1.7
 */

/* Global styles for custom elements */
mqcrp-reaction-button,
mqcrp-reaction-box,
mqcrp-reaction-type,
mqcrp-reaction-counter,
mqcrp-reaction-user-list {
    display: inline-block;
    font-family: var(--fcom-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif);
}

/* User list styles */
mqcrp-reaction-user-list {
    display: block;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
}

/* Feed item styles */
.feed_footer mqcrp-reaction-button {
    height: 32px;
    margin: 0 4px;
    vertical-align: middle;
}

/* Comment styles */
.comment_wrap mqcrp-reaction-button {
    height: 28px;
    margin: 0 2px;
    vertical-align: middle;
}

/* Modal styles */
.el-dialog.fcom_feed_modal mqcrp-reaction-button {
    height: 32px;
    margin: 0 4px;
    vertical-align: middle;
}

/* Reaction counter styles */
mqcrp-reaction-counter {
    margin-left: 8px;
    vertical-align: middle;
}

/* Dark mode adjustments */
.fcom_dark_mode mqcrp-reaction-button::part(button),
.fcom_dark_mode mqcrp-reaction-counter::part(counter) {
    color: var(--fcom-text-secondary-dark, #aaaaaa);
}

.fcom_dark_mode mqcrp-reaction-box::part(box) {
    background-color: var(--fcom-secondary-content-bg-dark, #2d2d2d);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

.fcom_dark_mode mqcrp-reaction-type::part(tooltip) {
    background-color: var(--fcom-secondary-content-bg-dark, #2d2d2d);
    color: var(--fcom-menu-text-dark, #dddddd);
}

/* Animations */
@keyframes mqcrp-fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes mqcrp-slide-up {
    from { transform: translateY(10px); }
    to { transform: translateY(0); }
}

@keyframes mqcrp-flip {
    from { transform: rotateY(180deg); }
    to { transform: rotateY(0deg); }
}

/* Animation classes */
.mqcrp-fade-in {
    animation: mqcrp-fade-in 0.3s ease forwards;
}

.mqcrp-slide-up {
    animation: mqcrp-slide-up 0.3s ease forwards;
}

.mqcrp-flip {
    animation: mqcrp-flip 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .feed_footer mqcrp-reaction-button,
    .el-dialog.fcom_feed_modal mqcrp-reaction-button {
        height: 28px;
        margin: 0 2px;
    }

    mqcrp-reaction-counter {
        margin-left: 4px;
    }
}
