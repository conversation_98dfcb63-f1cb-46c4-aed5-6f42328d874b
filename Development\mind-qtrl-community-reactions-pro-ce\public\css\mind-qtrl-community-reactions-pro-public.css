/**
 * Mind Qtrl Community Reactions Pro CE Public CSS
 */

/* Reaction container styling */
.mqcrp-reaction-parent {
    position: relative;
    display: inline-block;
    z-index: 100;
}

/* Reaction box styling */
.mqcrp-reaction-box {
    position: absolute;
    bottom: 100%;
    left: 0; /* Align with left side of parent */
    transform: translateY(-10px); /* Only vertical transform */
    background-color: var(--fcom-secondary-content-bg, #fff);
    border-radius: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 5px;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0.3s, opacity 0.3s, transform 0.3s;
    pointer-events: none;
    margin-bottom: 0px; /* Add space for the arrow */
    padding-top: 2px;
    padding-bottom: 2px;
}

/* Add arrow indicator to the reaction box */
.mqcrp-reaction-box::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 20px; /* Position arrow at the left side */
    border-width: 10px 10px 0 10px;
    border-style: solid;
    border-color: var(--fcom-secondary-content-bg, #fff) transparent transparent transparent;
    z-index: 1;
}

/* Show reaction box on hover */
.mqcrp-reaction-parent:hover .mqcrp-reaction-box,
.mqcrp-reaction-parent.hover .mqcrp-reaction-box,
.mqcrp-custom-icon:hover + .mqcrp-reaction-box,
.mqcrp-custom-icon.hover + .mqcrp-reaction-box {
    visibility: visible;
    opacity: 1;
    transform: translateY(0); /* Only vertical transform */
    pointer-events: auto;
}

/* Reaction button styling */
.mqcrp-reaction-button {
    background: none;
    border: none;
    cursor: pointer;
    margin: 0 5px;
    padding: 5px;
    border-radius: 50%;
    transition: transform 0.2s, filter 0.2s;
    position: relative;
    margin-left: 0px;
    margin-right: 0px;
    padding-left: 3px;
    padding-right: 3px;
}

.mqcrp-reaction-button:hover {
    transform: scale(1.15);
    filter: drop-shadow(0 0 3px var(--reaction-glow-color, #8770FF));
}

.mqcrp-reaction-button img {
    width: 24px;
    height: 24px;
    display: block;
}

/* Reaction tooltip styling */
.mqcrp-reaction-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--fcom-secondary-content-bg, #fff);
    filter: brightness(1.1); /* Slightly brighter background as per user preference */
    color: var(--fcom-menu-text, #333);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 5px 10px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 100000; /* Extremely high z-index to ensure visibility */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    pointer-events: none;
    margin-bottom: 5px;
    min-width: max-content; /* Ensure text doesn't get cut off */
    text-align: center;
}

/* Add arrow to tooltip */
.mqcrp-reaction-tooltip::after,
.mqcrp-reaction-tooltip-arrow {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid var(--fcom-secondary-content-bg, #fff);
    filter: brightness(1.1); /* Match the tooltip brightness */
}

/* First tooltip arrow alignment */
.mqcrp-reaction-button:first-child .mqcrp-reaction-tooltip::after,
.mqcrp-reaction-button:first-child .mqcrp-reaction-tooltip-arrow {
    left: 20%;
    transform: translateX(0);
}

/* Last tooltip arrow alignment */
.mqcrp-reaction-button:last-child .mqcrp-reaction-tooltip::after,
.mqcrp-reaction-button:last-child .mqcrp-reaction-tooltip-arrow {
    left: 80%;
    transform: translateX(0);
}

.mqcrp-reaction-button:hover .mqcrp-reaction-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Dark mode support */
.fcom_dark_mode .mqcrp-reaction-box {
    background-color: var(--fcom-secondary-content-bg, #1e1e2d);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    border: 1px solid var(--fcom-border-color, #2d2d3a);
}

.fcom_dark_mode .mqcrp-reaction-tooltip {
    background-color: var(--fcom-secondary-content-bg, #1e1e2d);
    color: var(--fcom-menu-text, #fff);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    border: 1px solid var(--fcom-border-color, #2d2d3a);
}

/* Dark mode arrow color */
.fcom_dark_mode .mqcrp-reaction-box::after {
    border-color: var(--fcom-secondary-content-bg, #1e1e2d) transparent transparent transparent;
}

/* Special positioning for reaction boxes in modals */
.mqcrp-modal-position {
    position: absolute !important;
    z-index: 9999999 !important; /* Extra high z-index for modal view */
}

/* Special positioning for reaction boxes on single activity pages */
.mqcrp-single-activity-position {
    position: fixed !important;
    z-index: 9999999 !important; /* Extra high z-index for single activity view */
}

/* Fix for parent elements with overflow:hidden */
.el-scrollbar .mqcrp-reaction-box,
.feed_layout .mqcrp-reaction-box {
    position: fixed !important; /* Use fixed positioning to escape overflow:hidden containers */
    transform: none !important; /* Reset transform */
}

/* Remove hover background from feed footer items when the option is enabled */
body.mqcrp-remove-hover-bg .feed_footer ul li:hover,
body.mqcrp-remove-hover-bg .feed_footer ul li.hover,
body.mqcrp-remove-hover-bg .feed_footer ul li:focus,
body.mqcrp-remove-hover-bg .feed_footer ul li:active,
body.mqcrp-remove-hover-bg mqcrp-reaction-button:hover,
body.mqcrp-remove-hover-bg mqcrp-reaction-button.hover,
body.mqcrp-remove-hover-bg mqcrp-reaction-button:focus,
body.mqcrp-remove-hover-bg mqcrp-reaction-button:active {
    background: transparent !important;
    background-color: transparent !important;
}

/* Custom element styling */
mqcrp-reaction-button {
    display: inline-block;
    position: relative;
}

/* Custom icon styling */
.mqcrp-custom-icon {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    cursor: pointer;
    z-index: 10;
}

/* Make sure custom icons can trigger the reaction box */
.mqcrp-custom-icon {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* Position reaction box for custom icons */
.mqcrp-custom-icon + .mqcrp-reaction-box {
    position: absolute;
    bottom: 100%;
    left: 0;
    transform: translateY(-10px);
    z-index: 1000;
}

/* Ensure the hover background is removed for custom elements */
body.mqcrp-remove-hover-bg .mqcrp-reaction-button:hover,
body.mqcrp-remove-hover-bg .mqcrp-reaction-button.hover,
body.mqcrp-remove-hover-bg .mqcrp-reaction-button:focus,
body.mqcrp-remove-hover-bg .mqcrp-reaction-button:active,
body.mqcrp-remove-hover-bg .mqcrp-custom-icon:hover,
body.mqcrp-remove-hover-bg .mqcrp-custom-icon.hover,
body.mqcrp-remove-hover-bg .mqcrp-custom-icon:focus,
body.mqcrp-remove-hover-bg .mqcrp-custom-icon:active {
    background: transparent !important;
    background-color: transparent !important;
}

/* Ensure the custom icon is visible and interactive */
.mqcrp-custom-icon {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* Style for custom icon images */
.mqcrp-icon-img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 2;
    position: relative;
}

/* Animation for icon transitions */
.mqcrp-icon-transition {
    transition: transform 0.3s;
}

.mqcrp-icon-transition img {
    animation: mqcrp-icon-flip 0.5s ease-in-out;
}

@keyframes mqcrp-icon-flip {
    0% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(180deg);
    }
    100% {
        transform: rotateY(360deg);
    }
}
