/**
 * Base Custom Element class for Mind Qtrl Community Reactions Pro CE
 *
 * This class provides common functionality for all custom elements in the plugin.
 * It handles shadow DOM creation, attribute observation, and lifecycle methods.
 *
 * @since 0.1.8
 */

// Ensure the global namespace for our custom elements
window.MQCRP = window.MQCRP || {};
window.MQCRP.elements = window.MQCRP.elements || {};

// Helper function to register custom elements
window.MQCRP.registerElement = function(name, elementClass) {
    try {
        if (!customElements.get(name)) {
            customElements.define(name, elementClass);
            console.log(`[MQCRP] Successfully registered custom element: ${name}`);
            return true;
        } else {
            console.log(`[MQCRP] Custom element already registered: ${name}`);
            return false;
        }
    } catch (error) {
        console.error(`[MQCRP] Error registering custom element ${name}:`, error);
        return false;
    }
};

export class MQCRPBaseElement extends HTMLElement {
    /**
     * Constructor for the base element
     */
    constructor() {
        super();

        // Create shadow DOM
        this.attachShadow({ mode: 'open' });

        // Initialize state
        this._state = {};

        // Debug mode
        this.debug = false;
    }

    /**
     * Called when the element is connected to the DOM
     */
    connectedCallback() {
        this.debugLog('Element connected to DOM');
        this.render();
    }

    /**
     * Called when the element is disconnected from the DOM
     */
    disconnectedCallback() {
        this.debugLog('Element disconnected from DOM');
    }

    /**
     * Called when attributes are changed
     *
     * @param {string} name - The name of the attribute
     * @param {string} oldValue - The old value of the attribute
     * @param {string} newValue - The new value of the attribute
     */
    attributeChangedCallback(name, oldValue, newValue) {
        this.debugLog(`Attribute ${name} changed from ${oldValue} to ${newValue}`);
        this.render();
    }

    /**
     * Render the element
     * This method should be overridden by subclasses
     */
    render() {
        this.debugLog('Base render method called');
    }

    /**
     * Set state and trigger re-render
     *
     * @param {Object} newState - The new state to merge with the current state
     */
    setState(newState) {
        this._state = { ...this._state, ...newState };
        this.render();
    }

    /**
     * Get the current state
     *
     * @returns {Object} The current state
     */
    getState() {
        return { ...this._state };
    }

    /**
     * Create a style element with the given CSS
     *
     * @param {string} css - The CSS to include in the style element
     * @returns {HTMLStyleElement} The created style element
     */
    createStyle(css) {
        const style = document.createElement('style');
        style.textContent = css;
        return style;
    }

    /**
     * Log debug messages if debug mode is enabled
     *
     * @param {string} message - The message to log
     * @param {any} data - Optional data to log
     */
    debugLog(message, data) {
        if (this.debug) {
            console.log(`[MQCRP-CE] ${this.constructor.name}: ${message}`, data || '');
        }
    }

    /**
     * Dispatch a custom event
     *
     * @param {string} name - The name of the event
     * @param {Object} detail - The detail object to include in the event
     * @param {boolean} bubbles - Whether the event should bubble
     * @param {boolean} composed - Whether the event should cross shadow DOM boundaries
     */
    dispatchCustomEvent(name, detail = {}, bubbles = true, composed = true) {
        const event = new CustomEvent(name, {
            detail,
            bubbles,
            composed
        });
        this.dispatchEvent(event);
        this.debugLog(`Dispatched event: ${name}`, detail);
    }
}
