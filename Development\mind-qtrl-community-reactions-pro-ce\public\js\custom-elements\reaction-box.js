/**
 * Reaction Box Custom Element for Mind Qtrl Community Reactions Pro CE
 *
 * This custom element creates a reaction box that displays available reaction types
 * and handles user interactions with those reaction types.
 *
 * @since 0.1.5
 */
import { MQCRPBaseElement } from './base-element.js';
import './reaction-type.js';

export class MQCRPReactionBox extends MQCRPBaseElement {
    /**
     * Observed attributes for this element
     */
    static get observedAttributes() {
        return ['active', 'glow-color', 'delay-time'];
    }

    /**
     * Constructor for the reaction box element
     */
    constructor() {
        super();

        // Initialize state
        this._state = {
            reactionTypes: [],
            activeType: null,
            isVisible: false,
            hideTimeout: null
        };

        // Bind methods
        this.handleMouseEnter = this.handleMouseEnter.bind(this);
        this.handleMouseLeave = this.handleMouseLeave.bind(this);
        this.handleReactionTypeSelected = this.handleReactionTypeSelected.bind(this);
    }

    /**
     * Called when the element is connected to the DOM
     */
    connectedCallback() {
        super.connectedCallback();

        // Add event listeners
        this.addEventListener('mouseenter', this.handleMouseEnter);
        this.addEventListener('mouseleave', this.handleMouseLeave);
        this.addEventListener('mqcrp-reaction-type-selected', this.handleReactionTypeSelected);

        // Load reaction types
        this.loadReactionTypes();
    }

    /**
     * Called when the element is disconnected from the DOM
     */
    disconnectedCallback() {
        super.disconnectedCallback();

        // Remove event listeners
        this.removeEventListener('mouseenter', this.handleMouseEnter);
        this.removeEventListener('mouseleave', this.handleMouseLeave);
        this.removeEventListener('mqcrp-reaction-type-selected', this.handleReactionTypeSelected);

        // Clear any pending timeouts
        if (this._state.hideTimeout) {
            clearTimeout(this._state.hideTimeout);
        }
    }

    /**
     * Load reaction types from the global configuration
     */
    loadReactionTypes() {
        // Check if the global configuration is available
        if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
            this.setState({ reactionTypes: window.mqcrpConfig.reactionTypes });
        } else {
            // Fallback to default reaction types
            this.setState({
                reactionTypes: [
                    { id: 'like', name: 'Like', image: '', glowColor: '#8770FF' },
                    { id: 'love', name: 'Love', image: '', glowColor: '#FF5757' }
                ]
            });
        }
    }

    /**
     * Handle mouse enter event
     */
    handleMouseEnter() {
        // Clear any pending hide timeout
        if (this._state.hideTimeout) {
            clearTimeout(this._state.hideTimeout);
            this._state.hideTimeout = null;
        }

        // Show the reaction box
        this.setState({ isVisible: true });
    }

    /**
     * Handle mouse leave event
     */
    handleMouseLeave() {
        // Get delay time from attribute or use default
        const delayTime = this.getAttribute('delay-time') || 1000;

        // Set timeout to hide the reaction box
        this._state.hideTimeout = setTimeout(() => {
            this.setState({ isVisible: false });
        }, delayTime);
    }

    /**
     * Handle reaction type selected event
     *
     * @param {CustomEvent} event - The custom event
     */
    handleReactionTypeSelected(event) {
        const { reactionType } = event.detail;

        // Update active type
        this.setState({ activeType: reactionType });

        // Dispatch event to notify parent
        this.dispatchCustomEvent('mqcrp-reaction-selected', {
            reactionType
        });
    }

    /**
     * Render the element
     */
    render() {
        // Clear shadow DOM
        this.shadowRoot.innerHTML = '';

        // Add styles
        this.shadowRoot.appendChild(this.createStyle(this.getStyles()));

        // Create container
        const container = document.createElement('div');
        container.className = 'mqcrp-reaction-box';

        // Add active class if visible
        if (this._state.isVisible) {
            container.classList.add('active');
        }

        // Check if this is from a custom icon
        if (this.parentElement && this.parentElement.classList.contains('from-custom-icon')) {
            container.classList.add('from-custom-icon');
        }

        // Add glow color if provided
        const glowColor = this.getAttribute('glow-color');
        if (glowColor) {
            container.style.setProperty('--glow-color', glowColor);
        }

        // Create reaction types container
        const typesContainer = document.createElement('div');
        typesContainer.className = 'mqcrp-reaction-types';

        // Add reaction types
        this._state.reactionTypes.forEach((type, index) => {
            const reactionType = document.createElement('mqcrp-reaction-type');
            reactionType.setAttribute('type-id', type.id);
            reactionType.setAttribute('name', type.name);

            if (type.image) {
                reactionType.setAttribute('image', type.image);
            }

            if (type.glowColor) {
                reactionType.setAttribute('glow-color', type.glowColor);
            }

            // Set active if this is the active type
            if (this._state.activeType && this._state.activeType.id === type.id) {
                reactionType.setAttribute('active', 'true');
            }

            // Add special classes for first and last elements to help with tooltip positioning
            if (index === 0) {
                reactionType.classList.add('first-reaction-type');
            } else if (index === this._state.reactionTypes.length - 1) {
                reactionType.classList.add('last-reaction-type');
            }

            typesContainer.appendChild(reactionType);
        });

        // Add arrow
        const arrow = document.createElement('div');
        arrow.className = 'mqcrp-reaction-box-arrow';

        // Add elements to container
        container.appendChild(typesContainer);
        container.appendChild(arrow);

        // Add container to shadow DOM
        this.shadowRoot.appendChild(container);
    }

    /**
     * Get the styles for the element
     *
     * @returns {string} The CSS styles
     */
    getStyles() {
        return `
            :host {
                display: block;
                position: relative;
                z-index: 9999;
            }

            .mqcrp-reaction-box {
                position: absolute;
                bottom: 100%;
                left: 0;
                background-color: var(--fcom-secondary-content-bg, #ffffff);
                border-radius: 8px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
                padding: 8px;
                transform: translateY(10px) scale(0.95);
                opacity: 0;
                pointer-events: none;
                transition: transform 0.2s ease, opacity 0.2s ease;
                z-index: 9999;
                min-width: 240px;
                margin-bottom: 10px;
                /* Ensure there's enough space for tooltips */
                padding-top: 40px;
                margin-top: -30px;
            }

            .mqcrp-reaction-box.active {
                transform: translateY(0) scale(1);
                opacity: 1;
                pointer-events: auto;
            }

            .mqcrp-reaction-types {
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                align-items: center;
            }

            .mqcrp-reaction-box-arrow {
                position: absolute;
                bottom: -8px;
                left: 20px;
                width: 16px;
                height: 8px;
                overflow: hidden;
            }

            .mqcrp-reaction-box-arrow:after {
                content: '';
                position: absolute;
                top: -8px;
                left: 0;
                width: 16px;
                height: 16px;
                background-color: var(--fcom-secondary-content-bg, #ffffff);
                transform: rotate(45deg);
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                .mqcrp-reaction-box {
                    min-width: 200px;
                }
            }

            /* Positioning for different contexts */
            :host(.modal-context) .mqcrp-reaction-box {
                bottom: 120%;
            }

            :host(.comment-context) .mqcrp-reaction-box {
                bottom: 120%;
                left: -50%;
            }

            /* First reaction in a list - align arrow to left */
            :host(.first-reaction) .mqcrp-reaction-box-arrow {
                left: 10px;
            }

            /* Last reaction in a list - align arrow to right */
            :host(.last-reaction) .mqcrp-reaction-box-arrow {
                left: auto;
                right: 10px;
            }
        `;
    }
}

// Define the custom element
customElements.define('mqcrp-reaction-box', MQCRPReactionBox);
