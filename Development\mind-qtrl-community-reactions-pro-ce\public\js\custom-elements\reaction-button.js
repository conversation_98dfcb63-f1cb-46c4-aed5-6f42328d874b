/**
 * Reaction Button Custom Element for Mind Qtrl Community Reactions Pro CE
 *
 * This custom element completely replaces the default Fluent Community reaction button
 * with a custom implementation that supports custom reaction types.
 *
 * @since 0.1.8
 */
import { MQCRPBaseElement } from './base-element.js';
import './reaction-box.js';
import './reaction-counter.js';

export class MQCRPReactionButton extends MQCRPBaseElement {
    /**
     * Observed attributes for this element
     */
    static get observedAttributes() {
        return ['activity-id', 'active', 'reaction-type', 'reaction-count', 'from-custom-icon'];
    }

    /**
     * Constructor for the reaction button element
     */
    constructor() {
        super();

        // Initialize state
        this._state = {
            isHovering: false,
            hideTimeout: null,
            originalElement: null
        };

        // Bind methods
        this.handleMouseEnter = this.handleMouseEnter.bind(this);
        this.handleMouseLeave = this.handleMouseLeave.bind(this);
        this.handleClick = this.handleClick.bind(this);
        this.handleReactionSelected = this.handleReactionSelected.bind(this);
    }

    /**
     * Called when the element is connected to the DOM
     */
    connectedCallback() {
        super.connectedCallback();

        // Add event listeners
        this.addEventListener('mouseenter', this.handleMouseEnter);
        this.addEventListener('mouseleave', this.handleMouseLeave);
        this.addEventListener('click', this.handleClick);
        this.addEventListener('mqcrp-reaction-selected', this.handleReactionSelected);

        // Store reference to original element if provided
        const originalElementId = this.getAttribute('original-element-id');
        if (originalElementId) {
            this._state.originalElement = document.getElementById(originalElementId);
        }
    }

    /**
     * Called when the element is disconnected from the DOM
     */
    disconnectedCallback() {
        super.disconnectedCallback();

        // Remove event listeners
        this.removeEventListener('mouseenter', this.handleMouseEnter);
        this.removeEventListener('mouseleave', this.handleMouseLeave);
        this.removeEventListener('click', this.handleClick);
        this.removeEventListener('mqcrp-reaction-selected', this.handleReactionSelected);

        // Clear any pending timeouts
        if (this._state.hideTimeout) {
            clearTimeout(this._state.hideTimeout);
        }
    }

    /**
     * Handle mouse enter event
     */
    handleMouseEnter() {
        // Clear any pending hide timeout
        if (this._state.hideTimeout) {
            clearTimeout(this._state.hideTimeout);
            this._state.hideTimeout = null;
        }

        // Show the reaction box
        this.setState({ isHovering: true });
    }

    /**
     * Handle mouse leave event
     */
    handleMouseLeave() {
        // Get delay time from configuration or use default
        const delayTime = window.mqcrpConfig?.delayTime || 1000;

        // Set timeout to hide the reaction box
        this._state.hideTimeout = setTimeout(() => {
            this.setState({ isHovering: false });
        }, delayTime);
    }

    /**
     * Handle click event
     */
    handleClick(event) {
        // Prevent default behavior
        event.preventDefault();
        event.stopPropagation();

        // Get current reaction type
        const currentType = this.getAttribute('reaction-type') || 'like';

        // Toggle reaction if already active
        if (this.hasAttribute('active')) {
            this.toggleReaction(currentType);
        } else {
            // Add reaction with current type
            this.addReaction(currentType);
        }
    }

    /**
     * Handle reaction selected event
     *
     * @param {CustomEvent} event - The custom event
     */
    handleReactionSelected(event) {
        const { reactionType } = event.detail;

        // Get current reaction type
        const currentType = this.getAttribute('reaction-type') || 'like';

        // If selecting the same type and already active, toggle it off
        if (reactionType.id === currentType && this.hasAttribute('active')) {
            this.toggleReaction(reactionType.id);
        } else {
            // Add reaction with selected type
            this.addReaction(reactionType.id);
        }
    }

    /**
     * Add a reaction to the activity
     *
     * @param {string} type - The reaction type
     */
    async addReaction(type) {
        const activityId = this.getAttribute('activity-id');

        if (!activityId) {
            console.error('Cannot add reaction: No activity ID provided');
            return;
        }

        try {
            // Update UI immediately for better user experience
            this.setAttribute('active', 'true');
            this.setAttribute('reaction-type', type);

            // Increment reaction count
            const currentCount = parseInt(this.getAttribute('reaction-count') || '0', 10);
            this.setAttribute('reaction-count', (currentCount + 1).toString());

            // Update the icon image to match the selected reaction type
            this.updateIconImage(type);

            // Call API to add reaction
            if (window.mqcrpAddReaction) {
                await window.mqcrpAddReaction(activityId, type);
            } else {
                // Fallback to direct API call
                await fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': window.FluentCommunityVars?.nonce || ''
                    },
                    body: JSON.stringify({
                        react_type: type
                    })
                });
            }

            // Update original element if available
            this.updateOriginalElement(true, type);

            // Dispatch event to notify other components
            this.dispatchCustomEvent('mqcrp-reaction-added', {
                activityId,
                reactionType: type
            });
        } catch (error) {
            console.error('Error adding reaction:', error);

            // Revert UI changes on error
            this.removeAttribute('active');
            this.setAttribute('reaction-type', 'like');

            // Decrement reaction count
            const currentCount = parseInt(this.getAttribute('reaction-count') || '0', 10);
            this.setAttribute('reaction-count', Math.max(0, currentCount - 1).toString());

            // Reset the icon image to the default like icon
            this.updateIconImage('like');
        }
    }

    /**
     * Update the icon image to match the selected reaction type
     *
     * @param {string} type - The reaction type
     */
    updateIconImage(type) {
        // Find the reaction type data
        if (!window.mqcrpConfig || !window.mqcrpConfig.reactionTypes) {
            console.error('Cannot update icon image: No reaction types configuration found');
            return;
        }

        // Find the reaction type data
        const reactionType = window.mqcrpConfig.reactionTypes.find(rt => rt.id === type);
        if (!reactionType) {
            console.error('Cannot update icon image: Reaction type not found:', type);
            return;
        }

        // Find the icon element in the shadow DOM
        const iconElement = this.shadowRoot.querySelector('.mqcrp-reaction-icon');
        if (!iconElement) {
            console.error('Cannot update icon image: Icon element not found');
            return;
        }

        // Update the icon image
        iconElement.src = reactionType.image;
        iconElement.alt = reactionType.name || 'Reaction';

        // Also update the original element if available
        if (this._state.originalElement) {
            const originalIconImg = this._state.originalElement.querySelector('.mqcrp-icon-img');
            if (originalIconImg) {
                originalIconImg.src = reactionType.image;
                originalIconImg.alt = reactionType.name || 'Reaction';
            }
        }
    }

    /**
     * Toggle (remove) a reaction from the activity
     *
     * @param {string} type - The reaction type
     */
    async toggleReaction(type) {
        const activityId = this.getAttribute('activity-id');

        if (!activityId) {
            console.error('Cannot toggle reaction: No activity ID provided');
            return;
        }

        try {
            // Update UI immediately for better user experience
            this.removeAttribute('active');
            this.setAttribute('reaction-type', 'like');

            // Decrement reaction count
            const currentCount = parseInt(this.getAttribute('reaction-count') || '0', 10);
            this.setAttribute('reaction-count', Math.max(0, currentCount - 1).toString());

            // Reset the icon image to the default like icon
            this.updateIconImage('like');

            // Call API to remove reaction
            if (window.mqcrpRemoveReaction) {
                await window.mqcrpRemoveReaction(activityId, type);
            } else {
                // Fallback to direct API call
                await fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': window.FluentCommunityVars?.nonce || ''
                    },
                    body: JSON.stringify({
                        react_type: type,
                        remove: true
                    })
                });
            }

            // Update original element if available
            this.updateOriginalElement(false);

            // Find and update any related elements in the DOM
            if (activityId) {
                document.querySelectorAll(`.fcom_reaction[data-activity-id="${activityId}"], .fcom_reaction_list[data-activity-id="${activityId}"]`).forEach(element => {
                    // Reset custom attributes
                    element.removeAttribute('data-active-reaction-type');
                    element.removeAttribute('data-reaction-glow-color');
                    element.classList.remove('mqcrp-has-custom-reaction');

                    // Find and reset the icon
                    const customIcon = element.querySelector('.mqcrp-custom-icon, .el-icon');
                    if (customIcon) {
                        // Find the default like icon
                        let defaultLikeIcon = '';
                        if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
                            const likeType = window.mqcrpConfig.reactionTypes.find(t => t.id === 'like');
                            if (likeType && likeType.image) {
                                defaultLikeIcon = likeType.image;
                            }
                        }

                        // Update the image if found
                        const imgElement = customIcon.querySelector('.mqcrp-icon-img');
                        if (imgElement && defaultLikeIcon) {
                            imgElement.src = defaultLikeIcon;
                        }
                    }
                });
            }

            // Dispatch event to notify other components
            this.dispatchCustomEvent('mqcrp-reaction-removed', {
                activityId,
                reactionType: type
            });
        } catch (error) {
            console.error('Error toggling reaction:', error);

            // Revert UI changes on error
            this.setAttribute('active', 'true');
            this.setAttribute('reaction-type', type);

            // Increment reaction count
            const currentCount = parseInt(this.getAttribute('reaction-count') || '0', 10);
            this.setAttribute('reaction-count', (currentCount + 1).toString());

            // Restore the icon image to the previous reaction type
            this.updateIconImage(type);
        }
    }

    /**
     * Update the original Fluent Community element
     *
     * @param {boolean} isActive - Whether the reaction is active
     * @param {string} type - The reaction type
     */
    updateOriginalElement(isActive, type = 'like') {
        if (!this._state.originalElement) {
            return;
        }

        // Update classes
        if (isActive) {
            this._state.originalElement.classList.add('react_active');
            this._state.originalElement.classList.add('fcom_is_liked');

            // Set custom attributes for active reaction
            this._state.originalElement.setAttribute('data-active-reaction-type', type);

            // Find reaction type data for glow color and image
            let glowColor = '';
            let reactionImage = '';
            if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
                const reactionType = window.mqcrpConfig.reactionTypes.find(t => t.id === type);
                if (reactionType) {
                    if (reactionType.glowColor) {
                        glowColor = reactionType.glowColor;
                        this._state.originalElement.setAttribute('data-reaction-glow-color', glowColor);
                    }
                    if (reactionType.image) {
                        reactionImage = reactionType.image;
                    }
                }
            }

            // Update the icon image
            if (reactionImage) {
                const customIcon = this._state.originalElement.querySelector('.mqcrp-custom-icon, .el-icon');
                if (customIcon) {
                    // Check if there's an existing mqcrp-icon-img element
                    let imgElement = customIcon.querySelector('.mqcrp-icon-img');

                    if (imgElement) {
                        // Update the existing image element
                        imgElement.src = reactionImage;
                        imgElement.alt = type;
                    } else {
                        // Create a new image element
                        customIcon.innerHTML = '';

                        imgElement = document.createElement('img');
                        imgElement.src = reactionImage;
                        imgElement.alt = type;
                        imgElement.className = 'mqcrp-icon-img';
                        imgElement.style.width = '100%';
                        imgElement.style.height = '100%';
                        imgElement.style.objectFit = 'contain';

                        // Add error handler
                        imgElement.onerror = () => {
                            console.error('[MQCRP Debug] Failed to load image for original element:', imgElement.src);
                            // Try to find a fallback image
                            if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
                                const likeType = window.mqcrpConfig.reactionTypes.find(t => t.id === 'like');
                                if (likeType && likeType.image) {
                                    imgElement.src = likeType.image;
                                }
                            }
                        };

                        customIcon.appendChild(imgElement);
                    }

                    // Add classes to indicate this element has a custom reaction
                    this._state.originalElement.classList.add('mqcrp-has-custom-reaction');
                    customIcon.classList.add('mqcrp-has-custom-image');
                    customIcon.classList.add('mqcrp-custom-icon');
                }
            }
        } else {
            // Remove classes and attributes for inactive reaction
            this._state.originalElement.classList.remove('react_active');
            this._state.originalElement.classList.remove('fcom_is_liked');
            this._state.originalElement.classList.remove('mqcrp-has-custom-reaction');
            this._state.originalElement.removeAttribute('data-active-reaction-type');
            this._state.originalElement.removeAttribute('data-reaction-glow-color');

            // Find and update the icon if needed
            const customIcon = this._state.originalElement.querySelector('.mqcrp-custom-icon, .el-icon');
            if (customIcon) {
                // Find the default like icon
                let defaultLikeIcon = '';
                if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
                    const likeType = window.mqcrpConfig.reactionTypes.find(t => t.id === 'like');
                    if (likeType && likeType.image) {
                        defaultLikeIcon = likeType.image;
                    }
                }

                // Update the image if found
                const imgElement = customIcon.querySelector('.mqcrp-icon-img');
                if (imgElement && defaultLikeIcon) {
                    imgElement.src = defaultLikeIcon;
                } else if (customIcon && defaultLikeIcon && !imgElement) {
                    // Create a new image element if it doesn't exist
                    customIcon.innerHTML = '';

                    const newImgElement = document.createElement('img');
                    newImgElement.src = defaultLikeIcon;
                    newImgElement.alt = 'like';
                    newImgElement.className = 'mqcrp-icon-img';
                    newImgElement.style.width = '100%';
                    newImgElement.style.height = '100%';
                    newImgElement.style.objectFit = 'contain';

                    customIcon.appendChild(newImgElement);
                }
            }
        }

        // Update data attributes
        this._state.originalElement.setAttribute('data-reaction-type', isActive ? type : 'like');
    }

    /**
     * Render the element
     */
    render() {
        // Clear shadow DOM
        this.shadowRoot.innerHTML = '';

        // Add styles
        this.shadowRoot.appendChild(this.createStyle(this.getStyles()));

        // Create container
        const container = document.createElement('div');
        container.className = 'mqcrp-reaction-button';

        // Add active class if active
        if (this.hasAttribute('active')) {
            container.classList.add('active');
        }

        // Check if this button is from a custom icon
        const isFromCustomIcon = this.hasAttribute('from-custom-icon');
        if (isFromCustomIcon) {
            container.classList.add('from-custom-icon');
        }

        // Get reaction type
        const reactionType = this.getAttribute('reaction-type') || 'like';
        container.setAttribute('data-reaction-type', reactionType);

        // Find reaction type data
        let reactionTypeData = null;
        if (window.mqcrpReactionTypes && window.mqcrpReactionTypes[reactionType]) {
            reactionTypeData = window.mqcrpReactionTypes[reactionType];
        } else if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
            reactionTypeData = window.mqcrpConfig.reactionTypes.find(type => type.id === reactionType);
        }

        // Create icon element
        const icon = document.createElement('img');
        icon.className = 'mqcrp-reaction-button-icon';
        icon.alt = reactionTypeData?.name || reactionType;

        // Set icon source
        if (reactionTypeData && reactionTypeData.image) {
            // Use absolute URL for the image to prevent relative URL issues
            icon.src = reactionTypeData.image;

            // Add error handler to load fallback image if the custom image fails to load
            icon.onerror = () => {
                console.error('[MQCRP Debug] Failed to load image:', icon.src);
                // Try to load a fallback image
                const defaultLikeType = window.mqcrpConfig?.reactionTypes?.find(type => type.id === 'like');
                if (defaultLikeType && defaultLikeType.image) {
                    icon.src = defaultLikeType.image;
                } else {
                    icon.src = `${window.mqcrpConfig?.pluginUrl || ''}/public/images/reactions/like.png`;
                }
            };
        } else {
            // Use default image based on type
            icon.src = `${window.mqcrpConfig?.pluginUrl || ''}/public/images/reactions/${reactionType}.png`;
        }

        // Set glow color if available
        if (reactionTypeData && reactionTypeData.glowColor) {
            container.style.setProperty('--glow-color', reactionTypeData.glowColor);
        }

        // Create reaction box if hovering
        if (this._state.isHovering) {
            const reactionBox = document.createElement('mqcrp-reaction-box');

            // Set attributes
            reactionBox.setAttribute('delay-time', window.mqcrpConfig?.delayTime || '1000');

            if (reactionTypeData && reactionTypeData.glowColor) {
                reactionBox.setAttribute('glow-color', reactionTypeData.glowColor);
            }

            // If this is from a custom icon, position the box differently
            if (isFromCustomIcon) {
                reactionBox.classList.add('from-custom-icon');
            }

            container.appendChild(reactionBox);
        }

        // Create reaction counter
        const counter = document.createElement('mqcrp-reaction-counter');
        counter.setAttribute('activity-id', this.getAttribute('activity-id') || '');
        counter.setAttribute('count', this.getAttribute('reaction-count') || '0');

        if (this.hasAttribute('active')) {
            counter.setAttribute('active', 'true');
        }

        // Add elements to container
        container.appendChild(icon);
        container.appendChild(counter);

        // Add container to shadow DOM
        this.shadowRoot.appendChild(container);

        // In pure CE mode, we always want the custom element to be visible
        // No need to hide it anymore
    }

    /**
     * Get the styles for the element
     *
     * @returns {string} The CSS styles
     */
    getStyles() {
        // Check if remove hover background is enabled
        const removeHoverBg = document.body.classList.contains('mqcrp-remove-hover-bg');

        return `
            :host {
                display: inline-flex;
                position: relative;
                align-items: center;
                font-family: var(--fcom-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif);
                font-size: var(--fcom-font-size, 14px);
                color: var(--fcom-text-secondary, #666);
            }

            .mqcrp-reaction-button {
                display: flex;
                align-items: center;
                cursor: pointer;
                padding: 4px 8px;
                border-radius: 4px;
                transition: all 0.2s ease;
                background-color: transparent;
            }

            .mqcrp-reaction-button:hover {
                ${removeHoverBg ? 'background-color: transparent !important;' : 'background-color: var(--fcom-hover-bg, rgba(0, 0, 0, 0.05));'}
            }

            .mqcrp-reaction-button.active {
                color: var(--glow-color, var(--fcom-primary, #8770FF));
            }

            .mqcrp-reaction-button-icon {
                width: 20px;
                height: 20px;
                margin-right: 4px;
                transition: transform 0.2s ease, filter 0.2s ease;
                object-fit: contain;
            }

            .mqcrp-reaction-button:hover .mqcrp-reaction-button-icon {
                transform: scale(1.1);
            }

            .mqcrp-reaction-button.active .mqcrp-reaction-button-icon {
                filter: drop-shadow(0 0 3px var(--glow-color, var(--fcom-primary, #8770FF)));
            }

            /* Match Fluent Community styles */
            :host(.fcom-style) {
                height: 32px;
                margin: 0 4px;
            }

            :host(.fcom-style) .mqcrp-reaction-button {
                height: 100%;
            }
        `;
    }
}

// Store the element class in the global namespace
window.MQCRP.elements.ReactionButton = MQCRPReactionButton;

// Register the custom element using our helper function
window.MQCRP.registerElement('mqcrp-reaction-button', MQCRPReactionButton);

// Fallback registration in case the helper function fails
try {
    if (!customElements.get('mqcrp-reaction-button')) {
        customElements.define('mqcrp-reaction-button', MQCRPReactionButton);
    }
} catch (error) {
    console.error('[MQCRP] Error in fallback registration of mqcrp-reaction-button:', error);
}
