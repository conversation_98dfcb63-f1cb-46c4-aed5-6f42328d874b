/**
 * Reaction Counter Custom Element for Mind Qtrl Community Reactions Pro CE
 *
 * This custom element displays the count of reactions for a specific activity
 * and shows a list of users who reacted when clicked. Enhanced for the pure CE
 * implementation with improved styling and user experience.
 *
 * @since 0.1.6
 */
import { MQCRPBaseElement } from './base-element.js';

export class MQCRPReactionCounter extends MQCRPBaseElement {
    /**
     * Observed attributes for this element
     */
    static get observedAttributes() {
        return ['activity-id', 'count', 'active'];
    }

    /**
     * Constructor for the reaction counter element
     */
    constructor() {
        super();

        // Initialize state
        this._state = {
            users: [],
            isLoading: false,
            isOpen: false
        };

        // Bind methods
        this.handleClick = this.handleClick.bind(this);
        this.handleOutsideClick = this.handleOutsideClick.bind(this);
    }

    /**
     * Called when the element is connected to the DOM
     */
    connectedCallback() {
        super.connectedCallback();

        // Add event listeners
        this.addEventListener('click', this.handleClick);
        document.addEventListener('click', this.handleOutsideClick);
    }

    /**
     * Called when the element is disconnected from the DOM
     */
    disconnectedCallback() {
        super.disconnectedCallback();

        // Remove event listeners
        this.removeEventListener('click', this.handleClick);
        document.removeEventListener('click', this.handleOutsideClick);
    }

    /**
     * Handle click event
     */
    handleClick(event) {
        event.stopPropagation();

        // Toggle user list
        if (this._state.isOpen) {
            this.setState({ isOpen: false });
        } else {
            this.setState({ isOpen: true, isLoading: true });
            this.loadUsers();
        }
    }

    /**
     * Handle outside click event
     */
    handleOutsideClick(event) {
        if (this._state.isOpen && !this.contains(event.target)) {
            this.setState({ isOpen: false });
        }
    }

    /**
     * Load users who reacted to this activity
     */
    async loadUsers() {
        const activityId = this.getAttribute('activity-id');

        if (!activityId) {
            this.setState({ isLoading: false });
            return;
        }

        try {
            // Check if we have a global fetch function for reactions
            if (window.mqcrpFetchReactions) {
                const users = await window.mqcrpFetchReactions(activityId);
                this.setState({ users, isLoading: false });
            } else {
                // Fallback to direct API call
                const response = await fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`);
                const data = await response.json();

                if (data.success && data.data) {
                    this.setState({ users: data.data, isLoading: false });
                } else {
                    this.setState({ users: [], isLoading: false });
                }
            }
        } catch (error) {
            console.error('Error loading reaction users:', error);
            this.setState({ users: [], isLoading: false });
        }
    }

    /**
     * Render the element
     */
    render() {
        // Clear shadow DOM
        this.shadowRoot.innerHTML = '';

        // Add styles
        this.shadowRoot.appendChild(this.createStyle(this.getStyles()));

        // Create container
        const container = document.createElement('div');
        container.className = 'mqcrp-reaction-counter';

        // Add active class if active
        if (this.hasAttribute('active')) {
            container.classList.add('active');
        }

        // Create count element
        const count = document.createElement('span');
        count.className = 'mqcrp-reaction-count';

        // Format the count with proper text
        const countValue = parseInt(this.getAttribute('count') || '0', 10);
        if (countValue === 1) {
            count.textContent = '1 Mindfame';
        } else {
            count.textContent = `${countValue} Mindfame`;
        }

        // Add a tooltip to show what this is
        count.title = 'Click to see who reacted';

        // Create user list popup
        const userList = document.createElement('div');
        userList.className = 'mqcrp-reaction-user-list';

        // Add open class if open
        if (this._state.isOpen) {
            userList.classList.add('open');
        }

        // Add loading or users
        if (this._state.isLoading) {
            const loading = document.createElement('div');
            loading.className = 'mqcrp-reaction-loading';
            loading.innerHTML = '<div class="mqcrp-reaction-loading-spinner"></div><span>Loading...</span>';
            userList.appendChild(loading);
        } else if (this._state.users.length > 0) {
            const userListItems = document.createElement('ul');
            userListItems.className = 'mqcrp-reaction-user-items';

            this._state.users.forEach(user => {
                const userItem = document.createElement('li');
                userItem.className = 'mqcrp-reaction-user-item';

                // Create user avatar
                const avatar = document.createElement('img');
                avatar.className = 'mqcrp-reaction-user-avatar';
                avatar.src = user.user.avatar || '';
                avatar.alt = user.user.name || 'User';

                // Create user name
                const name = document.createElement('span');
                name.className = 'mqcrp-reaction-user-name';
                name.textContent = user.user.name || 'User';

                // Create reaction icon if available
                if (user.type && window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
                    const reactionType = window.mqcrpConfig.reactionTypes.find(type => type.id === user.type);

                    if (reactionType && reactionType.image) {
                        const icon = document.createElement('img');
                        icon.className = 'mqcrp-reaction-user-icon';
                        icon.src = reactionType.image;
                        icon.alt = reactionType.name || user.type;
                        icon.title = reactionType.name || user.type;

                        userItem.appendChild(icon);
                    }
                }

                // Add elements to user item
                userItem.appendChild(avatar);
                userItem.appendChild(name);

                // Add user item to list
                userListItems.appendChild(userItem);
            });

            userList.appendChild(userListItems);
        } else {
            const empty = document.createElement('div');
            empty.className = 'mqcrp-reaction-empty';
            empty.textContent = 'No reactions yet';
            userList.appendChild(empty);
        }

        // Add elements to container
        container.appendChild(count);
        container.appendChild(userList);

        // Add container to shadow DOM
        this.shadowRoot.appendChild(container);
    }

    /**
     * Get the styles for the element
     *
     * @returns {string} The CSS styles
     */
    getStyles() {
        return `
            :host {
                display: inline-block;
                position: relative;
                font-family: var(--fcom-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif);
            }

            .mqcrp-reaction-counter {
                cursor: pointer;
                position: relative;
                transition: transform 0.2s ease;
            }

            .mqcrp-reaction-counter:hover {
                transform: translateY(-1px);
            }

            .mqcrp-reaction-count {
                display: inline-block;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 14px;
                color: var(--fcom-text-secondary, #666666);
                transition: color 0.2s ease, background-color 0.2s ease;
            }

            .mqcrp-reaction-counter:hover .mqcrp-reaction-count {
                color: var(--fcom-text-link, #8770FF);
                background-color: var(--fcom-hover-bg, rgba(0, 0, 0, 0.03));
            }

            .mqcrp-reaction-counter.active .mqcrp-reaction-count {
                color: var(--fcom-text-link, #8770FF);
                font-weight: 500;
            }

            .mqcrp-reaction-user-list {
                position: absolute;
                bottom: 100%;
                left: 0;
                background-color: var(--fcom-secondary-content-bg, #ffffff);
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                padding: 12px;
                transform: translateY(10px) scale(0.95);
                opacity: 0;
                pointer-events: none;
                transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                            opacity 0.2s ease;
                z-index: 9999;
                min-width: 240px;
                margin-bottom: 15px;
                max-height: 350px;
                overflow-y: auto;
            }

            .mqcrp-reaction-user-list::after {
                content: '';
                position: absolute;
                bottom: -8px;
                left: 20px;
                width: 16px;
                height: 16px;
                background-color: var(--fcom-secondary-content-bg, #ffffff);
                transform: rotate(45deg);
                box-shadow: 4px 4px 5px rgba(0, 0, 0, 0.07);
                z-index: -1;
            }

            .mqcrp-reaction-user-list.open {
                transform: translateY(0) scale(1);
                opacity: 1;
                pointer-events: auto;
            }

            .mqcrp-reaction-loading {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }

            .mqcrp-reaction-loading-spinner {
                width: 28px;
                height: 28px;
                border: 2px solid rgba(0, 0, 0, 0.1);
                border-top-color: var(--fcom-text-link, #8770FF);
                border-radius: 50%;
                animation: mqcrp-spin 1s linear infinite;
                margin-bottom: 10px;
            }

            @keyframes mqcrp-spin {
                to { transform: rotate(360deg); }
            }

            .mqcrp-reaction-user-items {
                list-style: none;
                margin: 0;
                padding: 0;
            }

            .mqcrp-reaction-user-item {
                display: flex;
                align-items: center;
                padding: 10px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                transition: background-color 0.2s ease;
            }

            .mqcrp-reaction-user-item:hover {
                background-color: var(--fcom-hover-bg, rgba(0, 0, 0, 0.03));
            }

            .mqcrp-reaction-user-item:last-child {
                border-bottom: none;
            }

            .mqcrp-reaction-user-avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                margin-right: 10px;
                object-fit: cover;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }

            .mqcrp-reaction-user-name {
                flex: 1;
                font-size: 14px;
                font-weight: 500;
                color: var(--fcom-text-primary, #333333);
            }

            .mqcrp-reaction-user-icon {
                width: 20px;
                height: 20px;
                margin-left: 10px;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                transition: transform 0.2s ease;
            }

            .mqcrp-reaction-user-item:hover .mqcrp-reaction-user-icon {
                transform: scale(1.1);
            }

            .mqcrp-reaction-empty {
                padding: 20px;
                text-align: center;
                color: var(--fcom-text-secondary, #666666);
                font-size: 14px;
                font-style: italic;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                .mqcrp-reaction-user-list {
                    min-width: 200px;
                    max-height: 300px;
                }

                .mqcrp-reaction-user-avatar {
                    width: 28px;
                    height: 28px;
                }
            }
        `;
    }
}

// Define the custom element
customElements.define('mqcrp-reaction-counter', MQCRPReactionCounter);
