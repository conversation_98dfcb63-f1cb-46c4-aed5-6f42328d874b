/**
 * Reaction Type Custom Element for Mind Qtrl Community Reactions Pro CE
 *
 * This custom element represents a single reaction type button that users can click
 * to select a reaction. Enhanced for the pure CE implementation with improved
 * animations and styling.
 *
 * @since 0.1.6
 */
import { MQCRPBaseElement } from './base-element.js';

export class MQCRPReactionType extends MQCRPBaseElement {
    /**
     * Observed attributes for this element
     */
    static get observedAttributes() {
        return ['type-id', 'name', 'image', 'glow-color', 'active'];
    }

    /**
     * Constructor for the reaction type element
     */
    constructor() {
        super();

        // Bind methods
        this.handleClick = this.handleClick.bind(this);
    }

    /**
     * Called when the element is connected to the DOM
     */
    connectedCallback() {
        super.connectedCallback();

        // Add event listeners
        this.addEventListener('click', this.handleClick);
    }

    /**
     * Called when the element is disconnected from the DOM
     */
    disconnectedCallback() {
        super.disconnectedCallback();

        // Remove event listeners
        this.removeEventListener('click', this.handleClick);
    }

    /**
     * Handle click event
     */
    handleClick() {
        // Get reaction type data
        const typeId = this.getAttribute('type-id');
        const name = this.getAttribute('name');
        const image = this.getAttribute('image');
        const glowColor = this.getAttribute('glow-color');

        // Dispatch event to notify parent
        this.dispatchCustomEvent('mqcrp-reaction-type-selected', {
            reactionType: {
                id: typeId,
                name,
                image,
                glowColor
            }
        });
    }

    /**
     * Render the element
     */
    render() {
        // Clear shadow DOM
        this.shadowRoot.innerHTML = '';

        // Add styles
        this.shadowRoot.appendChild(this.createStyle(this.getStyles()));

        // Create container
        const container = document.createElement('div');
        container.className = 'mqcrp-reaction-type';

        // Add active class if active
        if (this.hasAttribute('active')) {
            container.classList.add('active');
        }

        // Add glow color if provided
        const glowColor = this.getAttribute('glow-color');
        if (glowColor) {
            container.style.setProperty('--glow-color', glowColor);
        }

        // Create image element
        const image = document.createElement('img');
        image.className = 'mqcrp-reaction-type-image';
        image.alt = this.getAttribute('name') || 'Reaction';

        // Set image source if provided
        const imageSrc = this.getAttribute('image');
        if (imageSrc) {
            image.src = imageSrc;
        } else {
            // Use default image based on type
            const typeId = this.getAttribute('type-id') || 'like';
            image.src = `${window.mqcrpConfig?.pluginUrl || ''}/public/images/reactions/${typeId}.png`;
        }

        // Create tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'mqcrp-reaction-type-tooltip';
        tooltip.textContent = this.getAttribute('name') || 'Reaction';

        // Add elements to container
        container.appendChild(image);
        container.appendChild(tooltip);

        // Add container to shadow DOM
        this.shadowRoot.appendChild(container);
    }

    /**
     * Get the styles for the element
     *
     * @returns {string} The CSS styles
     */
    getStyles() {
        return `
            :host {
                display: inline-block;
                position: relative;
                margin: 0 4px;
                perspective: 1000px; /* For 3D flip effect */
            }

            .mqcrp-reaction-type {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                cursor: pointer;
                transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                            filter 0.3s ease,
                            box-shadow 0.3s ease;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                transform-style: preserve-3d;
                backface-visibility: hidden;
                background-color: transparent;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }

            .mqcrp-reaction-type:hover {
                transform: scale(1.2) rotateY(0deg);
                filter: drop-shadow(0 0 5px var(--glow-color, #8770FF));
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                z-index: 2;
            }

            .mqcrp-reaction-type.active {
                transform: scale(1.1) rotateY(0deg);
                filter: drop-shadow(0 0 7px var(--glow-color, #8770FF));
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                z-index: 1;
            }

            /* 3D flip animation when clicked */
            .mqcrp-reaction-type:active {
                transform: scale(1.1) rotateY(180deg);
                transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            }

            .mqcrp-reaction-type-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
                display: block;
                transition: transform 0.3s ease, filter 0.3s ease;
                border-radius: 50%;
                backface-visibility: hidden;
            }

            .mqcrp-reaction-type:hover .mqcrp-reaction-type-image {
                filter: brightness(1.1);
            }

            .mqcrp-reaction-type-tooltip {
                position: absolute;
                top: -35px;
                left: 50%;
                transform: translateX(-50%);
                background-color: var(--fcom-secondary-content-bg, #ffffff);
                filter: brightness(1.1); /* Slightly brighter background as per user preference */
                color: var(--fcom-menu-text, #333333);
                padding: 4px 10px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                white-space: nowrap;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.2s ease, transform 0.2s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                z-index: 100000; /* Extremely high z-index to ensure visibility */
                transform: translateX(-50%) translateY(5px);
                min-width: max-content; /* Ensure text doesn't get cut off */
                text-align: center;
            }

            .mqcrp-reaction-type-tooltip::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid var(--fcom-secondary-content-bg, #ffffff);
            }

            .mqcrp-reaction-type:hover .mqcrp-reaction-type-tooltip {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
                visibility: visible; /* Explicitly set visibility */
            }

            /* First reaction type tooltip alignment - align right as per user preference */
            :host(:first-child) .mqcrp-reaction-type-tooltip {
                transform: translateX(-20%) translateY(5px);
                left: 50%; /* Reset left position */
            }

            :host(:first-child) .mqcrp-reaction-type:hover .mqcrp-reaction-type-tooltip {
                transform: translateX(-20%) translateY(0);
                opacity: 1;
                visibility: visible;
            }

            :host(:first-child) .mqcrp-reaction-type-tooltip::after {
                left: 20%; /* Position arrow at 20% from left */
                transform: translateX(0); /* Reset transform */
            }

            /* Last reaction type tooltip alignment - align left as per user preference */
            :host(:last-child) .mqcrp-reaction-type-tooltip {
                transform: translateX(-80%) translateY(5px);
                left: 50%; /* Reset left position */
            }

            :host(:last-child) .mqcrp-reaction-type:hover .mqcrp-reaction-type-tooltip {
                transform: translateX(-80%) translateY(0);
                opacity: 1;
                visibility: visible;
            }

            :host(:last-child) .mqcrp-reaction-type-tooltip::after {
                left: 80%; /* Position arrow at 80% from left */
                transform: translateX(0); /* Reset transform */
            }
        `;
    }
}

// Define the custom element
customElements.define('mqcrp-reaction-type', MQCRPReactionType);
