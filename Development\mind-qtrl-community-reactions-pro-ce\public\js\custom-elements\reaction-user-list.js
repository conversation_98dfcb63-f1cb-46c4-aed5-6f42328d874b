/**
 * Reaction User List Custom Element for Mind Qtrl Community Reactions Pro CE
 *
 * This custom element replaces the default Fluent Community user list in poppers
 * with a custom implementation that displays custom reaction icons.
 *
 * @since 0.1.7
 */
import { MQCRPBaseElement } from './base-element.js';

export class MQ<PERSON>PReactionUserList extends MQCRPBaseElement {
    /**
     * Observed attributes for this element
     */
    static get observedAttributes() {
        return ['popper-id'];
    }

    /**
     * Constructor for the reaction user list element
     */
    constructor() {
        super();

        // Initialize state
        this._state = {
            users: [],
            originalElement: null
        };
    }

    /**
     * Called when the element is connected to the DOM
     */
    connectedCallback() {
        super.connectedCallback();

        // Find the original element if popper-id is provided
        const popperId = this.getAttribute('popper-id');
        if (popperId) {
            const popper = document.getElementById(popperId);
            if (popper) {
                this._state.originalElement = popper;
                this.processUserList();
            }
        }

        // Process any users that were already added
        this.render();
    }

    /**
     * Process the user list from the original element
     */
    processUserList() {
        if (!this._state.originalElement) {
            return;
        }

        // Find user lists
        const userLists = this._state.originalElement.querySelectorAll('.fcom_user_lists');
        if (userLists.length === 0) {
            return;
        }

        // Process the first user list
        const userList = userLists[0];
        const listItems = userList.querySelectorAll('li');

        // Process each list item
        const users = [];
        listItems.forEach(item => {
            // Find avatar and username elements
            const avatar = item.querySelector('.fcom_avatar_is_rounded');
            const userRoute = item.querySelector('.user_route');

            if (avatar && userRoute) {
                // Get reaction type from data attribute if available
                const reactionType = item.getAttribute('data-reaction-type') || 'like';
                const username = userRoute.textContent.trim();
                const avatarSrc = avatar.querySelector('img')?.src || '';

                // Add user to the list
                users.push({
                    username,
                    avatarSrc,
                    reactionType
                });
            }
        });

        // Update state and re-render
        this.setState({ users });
    }

    /**
     * Add a user to the list
     * 
     * @param {Object} user - The user to add
     * @param {string} user.username - The username
     * @param {string} user.avatarSrc - The avatar source URL
     * @param {string} user.reactionType - The reaction type
     */
    addUser(user) {
        const users = [...this._state.users];
        users.push(user);
        this.setState({ users });
    }

    /**
     * Render the element
     */
    render() {
        // Clear shadow DOM
        this.shadowRoot.innerHTML = '';

        // Add styles
        this.shadowRoot.appendChild(this.createStyle(this.getStyles()));

        // Create container
        const container = document.createElement('div');
        container.className = 'mqcrp-reaction-user-list';

        // Create user list
        const userList = document.createElement('ul');
        userList.className = 'mqcrp-user-list';

        // Add users
        this._state.users.forEach(user => {
            const listItem = document.createElement('li');
            listItem.className = 'mqcrp-user-list-item';

            // Create avatar
            const avatar = document.createElement('div');
            avatar.className = 'mqcrp-user-avatar';
            
            const avatarImg = document.createElement('img');
            avatarImg.src = user.avatarSrc;
            avatarImg.alt = user.username;
            avatar.appendChild(avatarImg);

            // Create reaction icon
            const reactionIcon = document.createElement('img');
            reactionIcon.className = 'mqcrp-user-reaction-icon';
            
            // Find reaction type data
            let reactionImage = '';
            if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
                const reactionType = window.mqcrpConfig.reactionTypes.find(rt => rt.id === user.reactionType);
                if (reactionType && reactionType.image) {
                    reactionImage = reactionType.image;
                } else {
                    // Default to like
                    const likeType = window.mqcrpConfig.reactionTypes.find(rt => rt.id === 'like');
                    if (likeType && likeType.image) {
                        reactionImage = likeType.image;
                    }
                }
            }

            if (reactionImage) {
                reactionIcon.src = reactionImage;
                reactionIcon.alt = user.reactionType;
                
                // Add error handler
                reactionIcon.onerror = () => {
                    console.error('[MQCRP Debug] Failed to load reaction icon:', reactionIcon.src);
                    // Try to find a fallback image
                    if (window.mqcrpConfig && window.mqcrpConfig.reactionTypes) {
                        const likeType = window.mqcrpConfig.reactionTypes.find(rt => rt.id === 'like');
                        if (likeType && likeType.image) {
                            reactionIcon.src = likeType.image;
                        }
                    }
                };
            }

            // Create username
            const username = document.createElement('span');
            username.className = 'mqcrp-username';
            username.textContent = user.username;

            // Add elements to list item
            listItem.appendChild(avatar);
            listItem.appendChild(reactionIcon);
            listItem.appendChild(username);

            // Add list item to user list
            userList.appendChild(listItem);
        });

        // Add user list to container
        container.appendChild(userList);

        // Add container to shadow DOM
        this.shadowRoot.appendChild(container);
    }

    /**
     * Get the styles for the element
     *
     * @returns {string} The CSS styles
     */
    getStyles() {
        return `
            :host {
                display: block;
                font-family: var(--fcom-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif);
                font-size: var(--fcom-font-size, 14px);
                color: var(--fcom-text-primary, #333);
            }

            .mqcrp-reaction-user-list {
                padding: 8px 0;
            }

            .mqcrp-user-list {
                list-style: none;
                margin: 0;
                padding: 0;
            }

            .mqcrp-user-list-item {
                display: flex;
                align-items: center;
                padding: 4px 8px;
                border-radius: 4px;
                transition: background-color 0.2s ease;
            }

            .mqcrp-user-list-item:hover {
                background-color: var(--fcom-hover-bg, rgba(0, 0, 0, 0.05));
            }

            .mqcrp-user-avatar {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                overflow: hidden;
                margin-right: 8px;
            }

            .mqcrp-user-avatar img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .mqcrp-user-reaction-icon {
                width: 16px;
                height: 16px;
                margin-right: 8px;
                object-fit: contain;
            }

            .mqcrp-username {
                font-weight: 500;
            }
        `;
    }
}

// Define the custom element
customElements.define('mqcrp-reaction-user-list', MQCRPReactionUserList);
