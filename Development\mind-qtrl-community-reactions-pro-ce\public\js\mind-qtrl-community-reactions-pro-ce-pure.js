/**
 * Main JavaScript file for Mind Qtrl Community Reactions Pro CE (Pure CE Implementation)
 *
 * This file initializes the custom elements and completely replaces Fluent Community
 * reaction elements with custom elements.
 *
 * @since 0.1.8
 */

// Custom elements are now loaded individually via separate script tags
// This ensures they're loaded in the correct order and properly registered
// See public/class-mind-qtrl-community-reactions-pro-public.php

/**
 * Mind Qtrl Community Reactions Pro CE - Pure CE Implementation
 */
(function() {
    'use strict';

    // Store original fetch function
    const originalFetch = window.fetch;

    /**
     * Initialize the plugin
     */
    function init() {
        // Check if Fluent Community is loaded
        if (typeof window.FluentCommunityApp !== 'undefined' || document.querySelector('.fcom_reaction')) {
            setupMutationObserver();
            processExistingElements();
            setupFetchInterceptor();
            setupPopperObserver();
        } else {
            // Retry after a delay
            setTimeout(init, 1000);
        }
    }

    /**
     * Set up observer for poppers to handle user lists
     */
    function setupPopperObserver() {
        const observer = new MutationObserver(function(mutations) {
            let shouldProcess = false;

            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    // Check if any added nodes are poppers or contain poppers
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && node.classList.contains('el-popper')) {
                                shouldProcess = true;
                                processPopper(node);
                            } else if (node.querySelector && node.querySelector('.el-popper')) {
                                shouldProcess = true;
                                const poppers = node.querySelectorAll('.el-popper');
                                poppers.forEach(processPopper);
                            }
                        }
                    });
                }
            });

            if (shouldProcess) {
                // Process all poppers after a short delay to ensure they're fully rendered
                setTimeout(processAllPoppers, 100);
            }
        });

        // Start observing the document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Also process on clicks to reaction counts
        document.addEventListener('click', function(e) {
            // Check if the clicked element is a reaction count or contains one
            let target = e.target;
            while (target && target !== document.body) {
                if (target.classList &&
                    (target.classList.contains('fcom_reactions_count') ||
                     target.classList.contains('fcom_reaction') ||
                     target.classList.contains('fcom_reaction_list'))) {
                    // Process all poppers after a short delay to ensure they're opened
                    setTimeout(processAllPoppers, 300);
                    break;
                }
                target = target.parentElement;
            }
        });

        // Initial processing
        processAllPoppers();
    }

    /**
     * Process all poppers on the page
     */
    function processAllPoppers() {
        const poppers = document.querySelectorAll('.el-popper');
        poppers.forEach(processPopper);
    }

    /**
     * Process a single popper
     *
     * @param {Element} popper - The popper element to process
     */
    function processPopper(popper) {
        // Skip if already processed
        if (popper.hasAttribute('data-mqcrp-processed') ||
            popper.closest('[data-mqcrp-processed]')) {
            return;
        }

        // Mark as processed
        popper.setAttribute('data-mqcrp-processed', 'true');

        // Find user lists
        const userLists = popper.querySelectorAll('.fcom_user_lists');
        if (userLists.length === 0) {
            return;
        }

        // Generate a unique ID for the popper
        const popperId = 'mqcrp-popper-' + Math.random().toString(36).substring(2, 15);
        popper.id = popperId;

        // Create custom element
        const customElement = document.createElement('mqcrp-reaction-user-list');
        customElement.setAttribute('popper-id', popperId);

        // Replace the original user list with the custom element
        const firstUserList = userLists[0];
        firstUserList.style.display = 'none'; // Hide the original element
        firstUserList.parentNode.insertBefore(customElement, firstUserList);
    }

    /**
     * Set up mutation observer to watch for new elements
     */
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Process new nodes
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            processElement(node);

                            // Process child elements
                            const elements = node.querySelectorAll('.fcom_reaction, .feed_actions button');
                            elements.forEach(processElement);
                        }
                    });
                }
            });
        });

        // Start observing the document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Process existing elements on the page
     */
    function processExistingElements() {
        // Find all reaction elements
        const elements = document.querySelectorAll('.fcom_reaction, .feed_actions button');
        elements.forEach(processElement);
    }

    /**
     * Process a single element
     *
     * @param {Element} element - The element to process
     */
    function processElement(element) {
        // Skip if already processed or is a custom element
        if (element.hasAttribute('data-mqcrp-processed') ||
            element.tagName.toLowerCase().startsWith('mqcrp-') ||
            element.closest('[data-mqcrp-processed]') ||
            element.closest('mqcrp-reaction-button')) {
            return;
        }

        // Mark as processed
        element.setAttribute('data-mqcrp-processed', 'true');

        // Check if this is a reaction element
        if (element.classList.contains('fcom_reaction') ||
            (element.parentElement && element.parentElement.classList.contains('feed_actions'))) {

            // Get activity ID
            let activityId = '';
            let reactionContainer = element.closest('.feed_footer, .feed_actions, .comment_wrap');

            if (reactionContainer) {
                const feedItem = reactionContainer.closest('.feed_outer, .feed_list_item, .feed');
                if (feedItem) {
                    activityId = feedItem.getAttribute('data-id') || '';
                }

                if (!activityId) {
                    // Try to find ID in comment
                    const comment = reactionContainer.closest('.comment_item');
                    if (comment) {
                        activityId = comment.getAttribute('data-id') || '';
                    }
                }
            }

            if (!activityId) {
                // Try to get from URL if on single activity page
                const urlMatch = window.location.pathname.match(/\/feed\/(\d+)/);
                if (urlMatch) {
                    activityId = urlMatch[1];
                }
            }

            if (!activityId) {
                return; // Skip if no activity ID found
            }

            // Determine reaction type
            let reactionType = 'like';
            if (element.getAttribute('data-type')) {
                reactionType = element.getAttribute('data-type');
            }

            // Check if reaction is active
            const isActive = element.classList.contains('react_active') ||
                            element.classList.contains('fcom_is_liked');

            // Generate a unique ID for the original element
            const originalElementId = 'mqcrp-original-' + Math.random().toString(36).substring(2, 15);
            element.id = originalElementId;

            // Get reaction count
            let reactionCount = '0';
            const countElement = reactionContainer.querySelector('.fcom_reactions_count');
            if (countElement) {
                reactionCount = countElement.textContent.trim().split(' ')[0];
                if (reactionCount === '-1') reactionCount = '0'; // Fix for negative counts
            }

            // Create custom element
            const customElement = document.createElement('mqcrp-reaction-button');
            customElement.setAttribute('activity-id', activityId);
            customElement.setAttribute('reaction-type', reactionType);
            customElement.setAttribute('reaction-count', reactionCount);
            customElement.setAttribute('original-element-id', originalElementId);

            if (isActive) {
                customElement.setAttribute('active', 'true');
            }

            // Replace the original element with the custom element
            element.style.display = 'none'; // Hide the original element
            element.parentNode.insertBefore(customElement, element);
        }
    }

    /**
     * Set up fetch interceptor to handle reaction API calls
     */
    function setupFetchInterceptor() {
        window.fetch = async function(input, init) {
            // Check if this is a reaction API call
            if (typeof input === 'string' && input.includes('/feeds/') && input.includes('/reactions')) {
                // Get the activity ID from the URL
                const match = input.match(/\/feeds\/(\d+)\/reactions/);
                if (match) {
                    const activityId = match[1];

                    // Check if this is a POST request (add/remove reaction)
                    if (init && init.method === 'POST') {
                        try {
                            // Parse the request body
                            const body = JSON.parse(init.body);
                            const reactionType = body.react_type || 'like';
                            const isRemove = body.remove || false;

                            // Update all custom elements for this activity
                            document.querySelectorAll(`mqcrp-reaction-button[activity-id="${activityId}"]`).forEach(element => {
                                if (isRemove) {
                                    element.removeAttribute('active');
                                } else {
                                    element.setAttribute('active', 'true');
                                    element.setAttribute('reaction-type', reactionType);
                                }
                            });
                        } catch (e) {
                            console.error('Error parsing reaction request:', e);
                        }
                    }
                }
            }

            // Call the original fetch function
            return originalFetch.apply(window, arguments);
        };
    }

    /**
     * Add a reaction to an activity
     *
     * @param {string} activityId - The activity ID
     * @param {string} type - The reaction type
     * @returns {Promise<Object>} A promise that resolves with the API response
     */
    async function addReaction(activityId, type) {
        const response = await fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': window.FluentCommunityVars?.nonce || ''
            },
            body: JSON.stringify({
                react_type: type
            })
        });

        return response.json();
    }

    /**
     * Remove a reaction from an activity
     *
     * @param {string} activityId - The activity ID
     * @param {string} type - The reaction type
     * @returns {Promise<Object>} A promise that resolves with the API response
     */
    async function removeReaction(activityId, type) {
        const response = await fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': window.FluentCommunityVars?.nonce || ''
            },
            body: JSON.stringify({
                react_type: type,
                remove: true
            })
        });

        return response.json();
    }

    /**
     * Fetch reactions for an activity
     *
     * @param {string} activityId - The activity ID
     * @returns {Promise<Array>} A promise that resolves with the reactions
     */
    async function fetchReactions(activityId) {
        const response = await fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`);
        const data = await response.json();

        if (data.success && data.data) {
            return data.data;
        }

        return [];
    }

    // Expose global functions
    window.mqcrpAddReaction = addReaction;
    window.mqcrpRemoveReaction = removeReaction;
    window.mqcrpFetchReactions = fetchReactions;

    // Track initialization state
    let initialized = false;
    let initAttempts = 0;
    const MAX_INIT_ATTEMPTS = 5;

    /**
     * Ensure all custom elements are defined
     */
    function ensureCustomElementsDefined() {
        // Check if custom elements are already defined
        const elementNames = [
            'mqcrp-reaction-button',
            'mqcrp-reaction-box',
            'mqcrp-reaction-type',
            'mqcrp-reaction-counter',
            'mqcrp-reaction-user-list'
        ];

        let allDefined = true;

        elementNames.forEach(elementName => {
            if (!customElements.get(elementName)) {
                allDefined = false;
                console.warn(`[MQCRP] Custom element ${elementName} is not defined yet`);
            }
        });

        return allDefined;
    }

    /**
     * Initialize with retry logic
     */
    function initWithRetry() {
        // Check if already initialized
        if (initialized) {
            console.log('[MQCRP] Already initialized, processing new elements only');
            processExistingElements();
            return;
        }

        // Check if custom elements are defined
        if (!ensureCustomElementsDefined()) {
            if (initAttempts < MAX_INIT_ATTEMPTS) {
                initAttempts++;
                console.log(`[MQCRP] Custom elements not defined yet, retrying (${initAttempts}/${MAX_INIT_ATTEMPTS})...`);
                setTimeout(initWithRetry, 500);
                return;
            } else {
                console.warn('[MQCRP] Max initialization attempts reached, proceeding anyway');
            }
        }

        // Add the pure-ce class to the document element
        document.documentElement.classList.add('mqcrp-pure-ce');

        // Initialize the plugin
        init();

        // Mark as initialized
        initialized = true;
        console.log('[MQCRP] Pure CE implementation initialized successfully');
    }

    // Initialize when document is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWithRetry);
    } else {
        initWithRetry();
    }

    // Also initialize when Fluent Community's app is loaded
    document.addEventListener('fluent_community/app_loaded', function() {
        console.log('[MQCRP] Fluent Community app loaded event detected');
        // Reset initialization state to force reprocessing
        initialized = false;
        initAttempts = 0;
        initWithRetry();
    });

    // Initialize periodically to catch any missed elements
    setTimeout(initWithRetry, 2000);
})();
