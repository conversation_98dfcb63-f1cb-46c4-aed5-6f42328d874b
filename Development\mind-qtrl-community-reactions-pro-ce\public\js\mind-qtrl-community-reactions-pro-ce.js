/**
 * Main JavaScript file for Mind Qtrl Community Reactions Pro CE
 *
 * This file initializes the custom elements and integrates them with Fluent Community.
 *
 * @since 0.1.0
 */

// Import custom elements
import './custom-elements/reaction-button.js';
import './custom-elements/reaction-box.js';
import './custom-elements/reaction-type.js';
import './custom-elements/reaction-counter.js';

/**
 * Mind Qtrl Community Reactions Pro CE
 */
(function() {
    'use strict';

    // Store original fetch function
    const originalFetch = window.fetch;

    /**
     * Initialize the plugin
     */
    function init() {
        // Check if Fluent Community is loaded
        if (typeof window.FluentCommunityApp !== 'undefined' || document.querySelector('.fcom_reaction')) {
            setupMutationObserver();
            processExistingElements();
            setupFetchInterceptor();
        } else {
            // Retry after a delay
            setTimeout(init, 1000);
        }
    }

    /**
     * Set up mutation observer to watch for new elements
     */
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Process new nodes
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            processElement(node);

                            // Process child elements
                            const elements = node.querySelectorAll('.fcom_reaction, .feed_actions button, .mqcrp-custom-icon');
                            elements.forEach(processElement);
                        }
                    });
                }
            });
        });

        // Start observing the document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Process existing elements on the page
     */
    function processExistingElements() {
        // Find all reaction elements
        const elements = document.querySelectorAll('.fcom_reaction, .feed_actions button, .mqcrp-custom-icon');
        elements.forEach(processElement);
    }

    /**
     * Process a single element
     *
     * @param {Element} element - The element to process
     */
    function processElement(element) {
        // Skip if already processed
        if (element.hasAttribute('data-mqcrp-processed') || element.closest('mqcrp-reaction-button')) {
            return;
        }

        // Check if this is a reaction element or custom icon
        if (element.classList.contains('fcom_reaction') ||
            (element.parentElement && element.parentElement.classList.contains('feed_actions')) ||
            element.classList.contains('mqcrp-custom-icon')) {

            // Mark as processed
            element.setAttribute('data-mqcrp-processed', 'true');

            // Get activity ID
            let activityId = '';
            let reactionContainer = null;

            // If this is a custom icon, find its parent reaction container
            if (element.classList.contains('mqcrp-custom-icon')) {
                reactionContainer = element.closest('.fcom_reaction, .fcom_reaction_list');
                if (reactionContainer) {
                    // Try to get activity ID from the reaction container
                    if (reactionContainer.hasAttribute('data-id')) {
                        activityId = reactionContainer.getAttribute('data-id');
                    } else {
                        // Try to find activity ID from parent elements
                        const feedItem = reactionContainer.closest('.feed_item');
                        if (feedItem && feedItem.hasAttribute('data-id')) {
                            activityId = feedItem.getAttribute('data-id');
                        }
                    }
                }
            } else {
                // This is a regular reaction element
                reactionContainer = element;

                // Try to get activity ID from data attribute
                if (element.hasAttribute('data-id')) {
                    activityId = element.getAttribute('data-id');
                } else {
                    // Try to find activity ID from parent elements
                    const feedItem = element.closest('.feed_item');
                    if (feedItem && feedItem.hasAttribute('data-id')) {
                        activityId = feedItem.getAttribute('data-id');
                    }
                }
            }

            // Skip if no activity ID found or no reaction container
            if (!activityId || !reactionContainer) {
                return;
            }

            // Create a unique ID for the original element
            const originalElementId = 'mqcrp-original-' + activityId + '-' + Math.random().toString(36).substring(2, 9);

            // Set ID on the reaction container
            reactionContainer.id = originalElementId;

            // Get reaction type
            let reactionType = 'like';
            if (reactionContainer.hasAttribute('data-type')) {
                reactionType = reactionContainer.getAttribute('data-type');
            } else if (reactionContainer.hasAttribute('data-active-reaction-type')) {
                reactionType = reactionContainer.getAttribute('data-active-reaction-type');
            }

            // Check if reaction is active
            const isActive = reactionContainer.classList.contains('react_active') ||
                            reactionContainer.classList.contains('fcom_is_liked');

            // Get reaction count
            let reactionCount = '0';
            const countElement = reactionContainer.querySelector('.fcom_reactions_count');
            if (countElement) {
                reactionCount = countElement.textContent.trim();
            }

            // Create custom element
            const customElement = document.createElement('mqcrp-reaction-button');
            customElement.setAttribute('activity-id', activityId);
            customElement.setAttribute('reaction-type', reactionType);
            customElement.setAttribute('reaction-count', reactionCount);
            customElement.setAttribute('original-element-id', originalElementId);

            if (isActive) {
                customElement.setAttribute('active', 'true');
            }

            // If this is a custom icon, add a special attribute
            if (element.classList.contains('mqcrp-custom-icon')) {
                customElement.setAttribute('from-custom-icon', 'true');

                // Add the custom element after the custom icon
                element.parentNode.insertBefore(customElement, element.nextSibling);

                // Add hover event to show reaction box
                element.addEventListener('mouseenter', function() {
                    // Trigger mouseenter on the custom element
                    const event = new MouseEvent('mouseenter', {
                        bubbles: true,
                        cancelable: true
                    });
                    customElement.dispatchEvent(event);
                });

                element.addEventListener('mouseleave', function() {
                    // Trigger mouseleave on the custom element
                    const event = new MouseEvent('mouseleave', {
                        bubbles: true,
                        cancelable: true
                    });
                    customElement.dispatchEvent(event);
                });
            } else {
                // Replace original element with custom element
                reactionContainer.style.display = 'none';
                reactionContainer.parentNode.insertBefore(customElement, reactionContainer.nextSibling);
            }
        }
    }

    /**
     * Set up fetch interceptor to capture reaction data
     */
    function setupFetchInterceptor() {
        // Replace global fetch with our interceptor
        window.fetch = async function(url, ...args) {
            // Check if this is a reaction-related API call
            if (typeof url === 'string' && url.includes('reactions')) {
                try {
                    // Call the original fetch
                    const response = await originalFetch.apply(this, [url, ...args]);

                    // Clone the response to avoid consuming it
                    const clonedResponse = response.clone();

                    // Process the response data
                    clonedResponse.json().then(data => {
                        processReactionAPIResponse(url, data);
                    }).catch(error => {
                        console.error('Error parsing reaction API response:', error);
                    });

                    return response;
                } catch (error) {
                    console.error('Error in fetch interception:', error);
                    throw error;
                }
            }

            // Pass through to original fetch for non-reaction requests
            return originalFetch.apply(this, arguments);
        };
    }

    /**
     * Process reaction API response data
     *
     * @param {string} url - The API URL
     * @param {Object} data - The response data
     */
    function processReactionAPIResponse(url, data) {
        // Store reaction data in global object
        if (!window.mqcrpReactionData) {
            window.mqcrpReactionData = {};
        }

        // Extract post ID from URL
        let postId = null;
        const matches = url.match(/feeds\/(\d+)\/reactions/);
        if (matches && matches[1]) {
            postId = matches[1];

            // Initialize data for this post
            if (!window.mqcrpReactionData[postId]) {
                window.mqcrpReactionData[postId] = {};
            }
        }

        // Process reaction data
        if (data.success && data.data && Array.isArray(data.data)) {
            data.data.forEach(reaction => {
                if (reaction.user && reaction.user.id) {
                    const userId = reaction.user.id;
                    const reactionType = reaction.type || 'like';

                    // Store by post ID and user ID
                    if (postId) {
                        window.mqcrpReactionData[postId][userId] = {
                            type: reactionType,
                            user: reaction.user
                        };
                    }

                    // Also store by user ID for cases where we don't have post ID
                    if (!window.mqcrpReactionData.users) {
                        window.mqcrpReactionData.users = {};
                    }

                    window.mqcrpReactionData.users[userId] = {
                        type: reactionType,
                        user: reaction.user
                    };
                }
            });
        }
    }

    /**
     * Add reaction to an activity
     *
     * @param {string} activityId - The activity ID
     * @param {string} type - The reaction type
     * @returns {Promise} A promise that resolves when the reaction is added
     */
    async function addReaction(activityId, type) {
        return fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': window.FluentCommunityVars?.nonce || ''
            },
            body: JSON.stringify({
                react_type: type
            })
        });
    }

    /**
     * Remove reaction from an activity
     *
     * @param {string} activityId - The activity ID
     * @param {string} type - The reaction type
     * @returns {Promise} A promise that resolves when the reaction is removed
     */
    async function removeReaction(activityId, type) {
        return fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': window.FluentCommunityVars?.nonce || ''
            },
            body: JSON.stringify({
                react_type: type,
                remove: true
            })
        });
    }

    /**
     * Fetch reactions for an activity
     *
     * @param {string} activityId - The activity ID
     * @returns {Promise<Array>} A promise that resolves with the reactions
     */
    async function fetchReactions(activityId) {
        const response = await fetch(`/wp-json/fluent-community/v1/feeds/${activityId}/reactions`);
        const data = await response.json();

        if (data.success && data.data) {
            return data.data;
        }

        return [];
    }

    // Expose global functions
    window.mqcrpAddReaction = addReaction;
    window.mqcrpRemoveReaction = removeReaction;
    window.mqcrpFetchReactions = fetchReactions;

    // Initialize when document is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Also initialize when Fluent Community's app is loaded
    document.addEventListener('fluent_community/app_loaded', init);

    // Initialize periodically to catch any missed elements
    setTimeout(init, 2000);
})();
