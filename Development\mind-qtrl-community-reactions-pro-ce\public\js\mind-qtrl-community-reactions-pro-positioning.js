/**
 * Mind Qtrl Community Reactions Pro CE - Reaction Box Positioning
 * Handles special positioning cases for reaction boxes
 */
(function($) {
    'use strict';

    // Debug mode
    const debug = true;

    // Log debug messages if debug mode is enabled
    function debugLog(message) {
        if (debug) {
            console.log('[MQCRPCE Positioning]', message);
        }
    }

    /**
     * Initialize the positioning functionality
     */
    function initPositioning() {
        debugLog('Initializing reaction box positioning');

        // Set up observers for different scenarios
        setupModalObserver();
        setupSingleActivityObserver();

        // Set up observer for reaction boxes to adjust margin based on number of reaction types
        setupReactionBoxObserver();

        // Process any existing elements
        processExistingElements();
    }

    /**
     * Process existing elements that might need special positioning
     */
    function processExistingElements() {
        debugLog('Processing existing elements');

        // Check if we're on a single activity page
        if (document.querySelector('[data-route="single_activity"]')) {
            debugLog('Detected single activity page');
            handleSingleActivityPositioning();
        }

        // Check for existing modals
        const modals = document.querySelectorAll('.el-dialog.fcom_feed_modal');
        if (modals.length) {
            debugLog(`Found ${modals.length} existing modals`);
            modals.forEach(modal => {
                handleModalPositioning(modal);
            });
        }

        // Process existing reaction boxes to adjust margins
        const reactionBoxes = document.querySelectorAll('.mqcrp-reaction-box');
        if (reactionBoxes.length) {
            debugLog(`Found ${reactionBoxes.length} existing reaction boxes`);
            reactionBoxes.forEach(reactionBox => {
                adjustReactionBoxMargin(reactionBox);
            });
        }
    }

    /**
     * Set up observer for modals
     */
    function setupModalObserver() {
        debugLog('Setting up modal observer');

        // Create a mutation observer to watch for new modals
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length) {
                    // Check if any of the added nodes are modals or contain modals
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            // Check if this is a modal
                            if (node.classList && node.classList.contains('el-dialog') && node.classList.contains('fcom_feed_modal')) {
                                handleModalPositioning(node);
                            }

                            // Check for modals within this node
                            const modals = node.querySelectorAll('.el-dialog.fcom_feed_modal');
                            if (modals.length) {
                                modals.forEach(handleModalPositioning);
                            }
                        }
                    }
                }
            });
        });

        // Start observing the document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        debugLog('Modal observer set up');
    }

    /**
     * Handle positioning for modals
     */
    function handleModalPositioning(modal) {
        debugLog('Handling modal positioning');

        // Process immediately and then again after a short delay to ensure all elements are loaded
        processModalReactionElements(modal);

        // Process again after a delay to catch any elements that might be added dynamically
        setTimeout(function() {
            processModalReactionElements(modal);
        }, 500);
    }

    /**
     * Process reaction elements in a modal
     */
    function processModalReactionElements(modal) {
        debugLog('Processing reaction elements in modal');

        // Find all reaction parents in the modal
        const reactionParents = modal.querySelectorAll('.mqcrp-reaction-parent');
        if (reactionParents.length) {
            debugLog(`Found ${reactionParents.length} reaction parents in modal`);

            reactionParents.forEach(function(parent) {
                // Find the reaction box
                const reactionBox = parent.querySelector('.mqcrp-reaction-box');
                if (reactionBox) {
                    // Add a special class for modal positioning
                    reactionBox.classList.add('mqcrp-modal-position');

                    // Ensure the reaction box is properly positioned relative to the modal
                    positionReactionBoxInModal(parent, reactionBox, modal);

                    // Also adjust the margin based on the number of reaction types
                    adjustReactionBoxMargin(reactionBox);
                }
            });
        }
    }

    /**
     * Position a reaction box within a modal
     */
    function positionReactionBoxInModal(parent, reactionBox, modal) {
        // Get the position of the parent relative to the modal
        const parentRect = parent.getBoundingClientRect();
        const modalRect = modal.getBoundingClientRect();

        // Calculate the position relative to the modal
        const top = parentRect.top - modalRect.top;
        const left = parentRect.left - modalRect.left;

        // Set the position - improved vertical positioning
        reactionBox.style.position = 'absolute';
        reactionBox.style.bottom = 'auto';

        // Improved vertical positioning - position the box higher above the parent
        const verticalOffset = 65; // Increased from 45px to 65px
        reactionBox.style.top = (top - verticalOffset) + 'px';

        // Set the left position to align with the parent's left edge
        reactionBox.style.left = left + 'px';

        // Use transform for vertical positioning only
        reactionBox.style.transform = 'translateY(0)';

        // Add a subtle animation for smoother appearance
        reactionBox.style.transition = 'opacity 0.2s ease, transform 0.2s ease';

        debugLog(`Positioned reaction box in modal with vertical offset of ${verticalOffset}px`);
    }

    /**
     * Set up observer for single activity pages
     */
    function setupSingleActivityObserver() {
        debugLog('Setting up single activity observer');

        // Create a mutation observer to watch for route changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-route') {
                    // Check if we're now on a single activity page
                    if (mutation.target.getAttribute('data-route') === 'single_activity') {
                        handleSingleActivityPositioning();
                    }
                }
            });
        });

        // Start observing the document body for data-route changes
        const appElement = document.querySelector('#app');
        if (appElement) {
            observer.observe(appElement, {
                attributes: true,
                attributeFilter: ['data-route']
            });
        }

        debugLog('Single activity observer set up');
    }

    /**
     * Handle positioning for single activity pages
     */
    function handleSingleActivityPositioning() {
        debugLog('Handling single activity positioning');

        // Process immediately and then again after a short delay to ensure all elements are loaded
        processSingleActivityReactionElements();

        // Process again after a delay to catch any elements that might be added dynamically
        setTimeout(function() {
            processSingleActivityReactionElements();
        }, 500);
    }

    /**
     * Process reaction elements on a single activity page
     */
    function processSingleActivityReactionElements() {
        debugLog('Processing reaction elements on single activity page');

        // Find all reaction parents on the page
        const reactionParents = document.querySelectorAll('.mqcrp-reaction-parent');
        if (reactionParents.length) {
            debugLog(`Found ${reactionParents.length} reaction parents on single activity page`);

            reactionParents.forEach(function(parent) {
                // Find the reaction box
                const reactionBox = parent.querySelector('.mqcrp-reaction-box');
                if (reactionBox) {
                    // Add a special class for single activity positioning
                    reactionBox.classList.add('mqcrp-single-activity-position');

                    // Ensure the reaction box is properly positioned
                    positionReactionBoxInSingleActivity(parent, reactionBox);

                    // Also adjust the margin based on the number of reaction types
                    adjustReactionBoxMargin(reactionBox);
                }
            });
        }
    }

    /**
     * Position a reaction box on a single activity page
     */
    function positionReactionBoxInSingleActivity(parent, reactionBox) {
        // Get the position of the parent
        const parentRect = parent.getBoundingClientRect();

        // Calculate the position
        const top = parentRect.top;
        const left = parentRect.left;

        // Set the position with improved vertical positioning
        reactionBox.style.position = 'fixed';
        reactionBox.style.bottom = 'auto';

        // Improved vertical positioning - position the box higher above the parent
        const verticalOffset = 65; // Increased from 45px to 65px
        reactionBox.style.top = (top - verticalOffset) + 'px';

        // Set the left position to align with the parent's left edge
        reactionBox.style.left = left + 'px';

        // Use transform for vertical positioning only
        reactionBox.style.transform = 'translateY(0)';

        // Add a subtle animation for smoother appearance
        reactionBox.style.transition = 'opacity 0.2s ease, transform 0.2s ease';

        // Add a subtle shadow for better visibility
        reactionBox.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';

        debugLog(`Positioned reaction box on single activity page with vertical offset of ${verticalOffset}px`);
    }

    /**
     * Set up observer for reaction boxes
     */
    function setupReactionBoxObserver() {
        debugLog('Setting up reaction box observer');

        // Create a mutation observer to watch for new reaction boxes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length) {
                    // Check if any of the added nodes are reaction boxes or contain them
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            // Check if this is a reaction box
                            if (node.classList && node.classList.contains('mqcrp-reaction-box')) {
                                adjustReactionBoxMargin(node);
                            }

                            // Check for reaction boxes within this node
                            const reactionBoxes = node.querySelectorAll('.mqcrp-reaction-box');
                            if (reactionBoxes.length) {
                                reactionBoxes.forEach(adjustReactionBoxMargin);
                            }
                        }
                    }
                }
            });
        });

        // Start observing the document
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        debugLog('Reaction box observer set up');
    }

    /**
     * Adjust the margin-left of a reaction box based on the number of reaction types
     */
    function adjustReactionBoxMargin(reactionBox) {
        // Count the number of reaction buttons
        const reactionButtons = reactionBox.querySelectorAll('.mqcrp-reaction-button');
        const buttonCount = reactionButtons.length;

        debugLog(`Adjusting margin for reaction box with ${buttonCount} buttons`);

        // Ensure the transform property doesn't override our horizontal positioning
        const currentTransform = window.getComputedStyle(reactionBox).transform;
        if (currentTransform && currentTransform !== 'none') {
            // If there's a transform that includes translateX, we need to override it
            const currentTransformY = reactionBox.style.transform.includes('translateY') ?
                                      reactionBox.style.transform.match(/translateY\(([^)]+)\)/)?.[1] :
                                      '0';

            // Set transform to only include translateY
            reactionBox.style.transform = `translateY(${currentTransformY || '0'})`;
        }

        // Log additional information about the reaction box type
        let boxType = 'standard';
        if (reactionBox.classList.contains('mqcrp-modal-position')) {
            boxType = 'modal';
        } else if (reactionBox.classList.contains('mqcrp-single-activity-position')) {
            boxType = 'single-activity';
        }

        debugLog(`Adjusted transform for ${boxType} reaction box with ${buttonCount} buttons`);
    }

    /**
     * Handle window resize events to reposition reaction boxes
     */
    function setupResizeHandler() {
        debugLog('Setting up resize handler');

        // Use a debounced resize handler to avoid performance issues
        let resizeTimeout;
        $(window).on('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                debugLog('Window resized, repositioning reaction boxes');

                // Reposition modal reaction boxes
                const modals = document.querySelectorAll('.el-dialog.fcom_feed_modal');
                if (modals.length) {
                    modals.forEach(modal => {
                        processModalReactionElements(modal);
                    });
                }

                // Reposition single activity reaction boxes
                if (document.querySelector('[data-route="single_activity"]')) {
                    processSingleActivityReactionElements();
                }
            }, 100); // 100ms debounce
        });
    }

    // Initialize when document is ready
    $(document).ready(function() {
        debugLog('Document ready, initializing positioning');
        initPositioning();

        // Set up event handlers for reaction box visibility
        setupReactionBoxVisibilityHandlers();

        // Set up resize handler
        setupResizeHandler();
    });

    /**
     * Set up event handlers for reaction box visibility
     */
    function setupReactionBoxVisibilityHandlers() {
        // Use event delegation to handle mouseover/mouseout on reaction containers
        $(document).on('mouseover', '.fcom_reaction, .fcom_reaction_list, .mqcrp-custom-icon', function() {
            // Find the reaction box associated with this container
            const reactionParent = $(this).closest('.mqcrp-reaction-parent');
            if (reactionParent.length) {
                const reactionBox = reactionParent.find('.mqcrp-reaction-box');
                if (reactionBox.length) {
                    // Adjust the margin when the reaction box becomes visible
                    setTimeout(function() {
                        adjustReactionBoxMargin(reactionBox[0]);
                    }, 10);
                }
            } else {
                // Check if this is a custom icon with a reaction box sibling
                const customIcon = $(this).hasClass('mqcrp-custom-icon') ? $(this) : null;
                if (customIcon) {
                    const reactionBox = customIcon.next('.mqcrp-reaction-box');
                    if (reactionBox.length) {
                        // Make the reaction box visible
                        reactionBox.css({
                            'visibility': 'visible',
                            'opacity': '1',
                            'transform': 'translateY(0)'
                        });

                        // Adjust the margin
                        setTimeout(function() {
                            adjustReactionBoxMargin(reactionBox[0]);
                        }, 10);
                    }
                }
            }
        });

        // Also handle mouseout to hide the reaction box with a delay
        $(document).on('mouseout', '.mqcrp-custom-icon', function() {
            const reactionBox = $(this).next('.mqcrp-reaction-box');
            if (reactionBox.length) {
                // Hide the reaction box after a delay
                setTimeout(function() {
                    reactionBox.css({
                        'visibility': 'hidden',
                        'opacity': '0',
                        'transform': 'translateY(-10px)'
                    });
                }, 1000); // 1 second delay
            }
        });
    }

})(jQuery);
