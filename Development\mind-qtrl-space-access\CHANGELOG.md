# Changelog

All notable changes to the Mind Qtrl Space Access plugin will be documented in this file.

## 1.2.0 - 2025-04-10

### Added
- Modular JavaScript structure for better maintainability and performance
- Request cancellation using AbortController
- Request caching for membership data
- Operation locking to prevent concurrent operations
- Cleanup functions for when navigating away from spaces
- Event delegation for handling restricted actions

### Fixed
- Timeout issues when quickly switching between spaces
- Race conditions in AJAX requests
- Memory leaks from uncancelled operations
- Performance issues from redundant DOM operations

### Changed
- Split frontend.js into multiple module files:
  - utils.js: Utility functions
  - api.js: API requests and responses
  - restrictions.js: Space restrictions
  - vue-integration.js: Vue.js and Vuex integration
  - frontend-modular.js: Main initialization
- Optimized DOM operations by using a single style element for each restriction type
- Improved error handling throughout the codebase

## 1.1.8 - 2024-06-10

### Fixed
- Fixed issue with join space button still being clickable
- Added missing name attribute to the hide_join_buttons checkbox
- Improved join button prevention using methods from fcom_mlive_integration.md
- Enhanced CSS selectors for hiding join buttons
- Added support for FluentCommunityApp.$toast for better toast messages
- Improved API call interception for join, request_join, and accept_request endpoints

## 1.1.7 - 2024-06-10

### Fixed
- Fixed issue with "Restrict Likes" checkbox not being remembered in the admin UI
- Added missing name attributes to the restrict_like checkbox and like_message textarea

## 1.1.6 - 2024-06-10

### Improved
- Enhanced integration with Fluent Community using methods from fcom_mlive_integration.md
- Improved space join prevention with more reliable Vuex store methods
- Enhanced space likes prevention with better event interception
- Added support for FluentCommunityApp.$toast for better toast messages
- Improved backend integration with additional API filters
- Updated unjoined member restrictions to apply to all unjoined members regardless of any set requirement

## 1.1.5 - 2024-06-05

### Fixed
- Fixed issue with Restrict Likes settings checkboxes not being remembered
- Added proper name attributes to hide_like_buttons and restrict_like_unjoined checkboxes
- Updated all "Apply to all unjoined members" text to "ONLY Apply to Unjoined Members" for consistency
- Improved settings saving and loading for like restriction options

## 1.1.4 - 2024-06-05

### Fixed
- Fixed hide join space button option in the backend UI and Fluent Community portal
- Fixed hide reactions/likes button feature in Fluent Community portal
- Fixed "restrict likes" option not being properly saved
- Changed "Apply to all unjoined members" to "ONLY Apply to unjoined members" for clarity
- Improved space restriction status indicator visibility with larger size and better contrast
- Added white border and stronger glow effect to status indicators

## 1.1.3 - 2024-06-05

### Fixed
- Space restrictions status indicators now visible regardless of mouse hover
- Added multiple layers of CSS fixes to ensure indicators are always visible
- Improved z-index handling for indicators to prevent them from being hidden
- Added data attribute approach as a fallback for themes that might hide pseudo-elements

## 1.1.2 - 2024-06-05

### Improved
- Enhanced admin UI with a professional design while maintaining the same color scheme
- Added collapsible options for hiding join buttons under the "Restrict Joining" feature
- Added collapsible options for hiding like/reaction buttons under the "Restrict Likes" feature
- Improved form controls with modern toggle switches
- Added card-based layout for better organization of settings
- Enhanced visual feedback with improved notifications

## 1.1.1 - 2024-06-05

### Improved
- Added clear distinction between comments and likes/reactions in Fluent Community
- Improved documentation and code comments to clarify that:
  - Comments are text replies to activities
  - Likes/Reactions are emoji expressions (like, love, etc.)
- Fixed incorrect selector in comment restriction function that was targeting reaction elements
- Enhanced CSS comments to better explain which elements are being targeted

## 1.1.0 - 2024-05-15

### Added
- New "Restrict Likes" feature to prevent unjoined users from liking content in spaces
- Option to hide like buttons for restricted users
- Custom messages for like restriction notifications

### Improved
- Enhanced join button detection and hiding
- Better space route detection with multiple fallback methods
- More comprehensive CSS selectors for hiding UI elements

## 1.0.0 - 2024-04-01

### Initial Release
- Space access control based on user badges, CRM tags, and leaderboard levels
- Restriction options for viewing, joining, posting, and commenting
- Integration with Fluent Community and Fluent CRM
- Admin interface for managing space access requirements
