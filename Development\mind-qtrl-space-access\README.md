# Mind Qtrl | Space Access Control

Advanced access control for Fluent Community spaces based on user badges, CRM tags, and leaderboard levels.

## Description

Mind Qtrl | Space Access Control provides advanced membership access control for Fluent Community spaces. This plugin allows site administrators to restrict access to spaces based on various criteria such as user badges, Fluent CRM tags, and leaderboard levels.

### Key Features

* **Granular Space Access Control:** Control who can view, join, post, comment, and like in each Fluent Community space
* **Multiple Access Control Methods:**
  - Restrict access based on Fluent CRM tags
  - Control access using user badges
  - Set minimum leaderboard levels
  - Simple checkbox controls for quick restrictions
  - Option to restrict unjoined members regardless of other requirements
* **Per-Space Settings:** Configure different access requirements for each space
* **Rich Text Custom Messages:** Set custom restriction messages with a rich text editor
* **Advanced Debugging:** Comprehensive logging system for troubleshooting
* **Modern Dark Admin UI:** User-friendly single-page dark-themed administration interface
* **Fluent Community Frontend Portal Integration:** Access settings directly from the Fluent Community portal
* **Modular JavaScript Structure:** Improved maintainability and performance with a modular code structure
* **Optimized Performance:** Request cancellation, caching, and efficient DOM operations for better performance

## Installation

1. Upload the plugin files to `/wp-content/plugins/mind-qtrl-space-access`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Configure space access settings under 'Fluent Community > Space Access'

## Configuration

## Technical Details

### Modular JavaScript Structure (New in v1.2.0)

The plugin now uses a modular JavaScript structure for better maintainability and performance:

1. **Utils Module (`modules/utils.js`)**
   - Utility functions used across the plugin
   - Debounce function to limit how often a function can be called
   - Functions to check if a route is a space route
   - Functions to get space ID from the current route
   - Unified message display function

2. **API Module (`modules/api.js`)**
   - Handles API requests and responses
   - Membership checking with request cancellation and caching
   - API interception for restricted actions
   - Functions to clear active requests and cache

3. **Restrictions Module (`modules/restrictions.js`)**
   - Implements the different types of space restrictions
   - View restrictions
   - Join restrictions
   - Posting restrictions
   - Commenting restrictions
   - Like restrictions
   - Event delegation for handling restricted actions
   - DOM manipulation functions

4. **Vue Integration Module (`modules/vue-integration.js`)**
   - Integrates with Vue.js and Vuex
   - Vue router watcher setup
   - Space access control application
   - Vuex store permission updates

5. **Main Frontend Module (`frontend-modular.js`)**
   - Initializes the plugin and coordinates the modules
   - Vue app initialization with timeout prevention
   - Module coordination
   - Cleanup functions

This modular approach provides several benefits:

- **Improved Code Organization**: Each module has a single responsibility, making the code easier to understand and maintain.
- **Better Performance**: The modular structure allows for more efficient code execution and better resource management.
- **Easier Debugging**: Issues can be isolated to specific modules, making debugging easier.
- **Enhanced Extensibility**: New features can be added by extending existing modules or creating new ones without modifying the core functionality.

### Timeout Issue Fixes (New in v1.2.0)

Version 1.2.0 addresses the timeout issues that occurred when quickly switching between spaces in the Fluent Community portal:

1. **Request Cancellation**: The API module implements request cancellation using AbortController to cancel previous requests when a new navigation occurs, preventing race conditions and memory leaks.

2. **Request Caching**: A simple cache stores membership data for spaces the user has already visited, reducing redundant requests and improving performance. The cache has a configurable expiration time (default: 5 minutes).

3. **Operation Locking**: A flag prevents multiple instances of space access control from running concurrently, eliminating race conditions that could cause unpredictable behavior.

4. **Optimized DOM Operations**: DOM operations are optimized by using a single style element for each restriction type, reducing DOM manipulation and improving rendering performance.

5. **Cleanup on Navigation**: A cleanup function cancels pending operations and removes temporary DOM elements when navigating away from a space, preventing memory leaks and zombie processes.

6. **Event Delegation**: Instead of attaching multiple event listeners to individual elements, a unified event delegation approach is used, significantly reducing the number of event listeners and improving performance.

These improvements result in a smoother user experience when navigating between spaces, especially when doing so rapidly.

### General Settings

1. Navigate to **Fluent Community > Space Access** in your WordPress admin dashboard
2. In the **General Settings** tab, you can:
   - Enable/disable the plugin globally
   - Configure default messages for access restrictions
   - Set up debugging options

### Space-Specific Settings

1. Go to the **Space Settings** tab
2. Select a space from the list on the left
3. Configure the following settings for the selected space:
   - **Enable Restrictions:** Turn on/off access control for this specific space
   - **Access Requirements:** Choose the type of requirements (CRM Tags, Badges, Leaderboard)
   - **Restriction Types:** Select what to restrict (viewing, joining, posting, commenting, liking)
   - **Unjoined Member Restrictions:** Option to apply restrictions to all unjoined members
   - **Custom Messages:** Set custom messages for each restriction type

### Access Requirement Types

#### Fluent CRM Tags

If you select "CRM Tags" as the access requirement:
1. The "CRM Tags" dropdown will appear
2. Select one or more tags from your Fluent CRM
3. Users must have at least one of the selected tags to access the space

#### Badges

If you select "Badges" as the access requirement:
1. The "Badges" dropdown will appear
2. Select one or more badges from your badge system
3. Users must have at least one of the selected badges to access the space

#### Leaderboard Levels

If you select "Leaderboard" as the access requirement:
1. The "Leaderboard Levels" dropdown will appear
2. Select one or more leaderboard levels
3. Users must have at least the minimum selected level to access the space

### Frontend Portal Integration

You can also access space settings from the Fluent Community frontend portal:

1. Log in to your Fluent Community portal
2. Navigate to the admin settings (gear icon in the footer)
3. Click on "Space Access" in the settings menu
4. You'll see a link to the full settings page in the WordPress admin

## Frequently Asked Questions

### Does this plugin work with the free version of Fluent Community?

Yes, this plugin works with both the free and pro versions of Fluent Community.

### Can I set different access rules for different spaces?

Yes, you can configure access rules individually for each Fluent Community space.

### Will restrictions apply to existing space members?

No, restrictions only apply to non-members. Once a user is a member of a space, they will retain access regardless of the restriction settings.

### How does the CRM tag integration work?

The plugin checks if a user has any of the required Fluent CRM tags. If they have at least one of the specified tags, they will be granted access based on your settings.

### Can I use multiple requirement types together?

Currently, you can only select one requirement type per space (CRM Tags, Badges, or Leaderboard). However, within each type, you can select multiple options (e.g., multiple CRM tags).

### How do I troubleshoot access issues?

1. Go to the "Debug" tab in the plugin settings
2. Enable detailed logging
3. Attempt the action that's causing issues
4. Check the debug log for detailed information about what's happening

## Support

For support, please contact <NAME_EMAIL> or visit our website at [mindqtrl.com](https://mindqtrl.com/).

## Changelog

### 1.2.0
* Implemented modular JavaScript structure for better maintainability and performance
* Fixed timeout issues when quickly switching between spaces
* Added request cancellation using AbortController
* Implemented request caching for membership data
* Added operation locking to prevent concurrent operations
* Optimized DOM operations
* Added cleanup functions for when navigating away from spaces

### 1.1.8
* Fixed issue with join space button still being clickable
* Added missing name attribute to the hide_join_buttons checkbox
* Improved join button prevention using methods from fcom_mlive_integration.md
* Enhanced CSS selectors for hiding join buttons
* Added support for FluentCommunityApp.$toast for better toast messages
* Improved API call interception for join, request_join, and accept_request endpoints

### 1.1.7
* Fixed issue with "Restrict Likes" checkbox not being remembered in the admin UI
* Added missing name attributes to the restrict_like checkbox and like_message textarea

### 1.1.6
* Enhanced integration with Fluent Community using recommended methods
* Improved space join prevention with more reliable Vuex store methods
* Enhanced space likes prevention with better event interception
* Added support for FluentCommunityApp.$toast for better toast messages
* Improved backend integration with additional API filters
* Updated unjoined member restrictions to apply to all unjoined members regardless of any set requirement

### 1.1.0
* Added "Restrict Likes" feature to prevent users from liking activities
* Improved space join prevention with multiple methods
* Enhanced route detection for better Fluent Community integration
* Added option to hide join buttons in space menu
* Improved event interception for better restriction enforcement
* Added option to apply restrictions to all unjoined space members
* Fixed issues with dynamically added elements
* Improved error handling and messaging
* Enhanced Vue.js store integration
* Added support for more Fluent Community selectors

### 1.0.0
* Complete rewrite with improved integration
* Added rich text editor for custom messages
* Added per-space granular access controls
* Improved frontend restriction handling
* Enhanced debugging and logging system
* New single-page dark admin UI with tabs
* Added Fluent CRM tag integration
* Added badge-based access control
* Added leaderboard level restrictions
* Improved frontend Vue.js integration
* Added comprehensive event logging
* Added detailed error tracking
* Added access control statistics
* Added Fluent Community frontend portal integration

### 0.0.2
* Added basic access control features
* Initial frontend restrictions
* Basic admin UI

### 0.0.1
* Initial release
