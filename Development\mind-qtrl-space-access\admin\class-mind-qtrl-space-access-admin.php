<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://mindqtrl.com/
 * @since      0.0.1
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the admin area including
 * the settings page, AJAX handlers, and admin UI.
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin
 */
class Mind_Qtrl_Space_Access_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @since    0.0.1
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    0.0.1
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * The options for this plugin.
	 *
	 * @since    0.0.1
	 * @access   private
	 * @var      array    $options    The options for this plugin.
	 */
	private $options;

	/**
	 * The debug log file path.
	 *
	 * @since    0.0.1
	 * @access   private
	 * @var      string    $log_file    The path to the debug log file.
	 */
	private $log_file;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    0.0.1
	 * @param    string    $plugin_name       The name of this plugin.
	 * @param    string    $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;
		$this->options = get_option('mqsa_settings', array());

		// Set log file path to the log directory within the plugin
		$log_dir = plugin_dir_path( dirname( __FILE__ ) ) . 'log/';
		$this->log_file = $log_dir . 'debug.log';
	}

	/**
	 * Register the stylesheets for the admin area.
	 *
	 * @since    0.0.1
	 */
	public function enqueue_styles() {
		$screen = get_current_screen();

		// Only load on our plugin's settings page
		if ( $screen && $this->is_plugin_admin_page($screen->id) ) {
			wp_enqueue_style(
				$this->plugin_name,
				plugin_dir_url( __FILE__ ) . 'css/mind-qtrl-space-access-admin.css',
				array(),
				$this->version,
				'all'
			);
		}
	}

	/**
	 * Register the JavaScript for the admin area.
	 *
	 * @since    0.0.1
	 */
	public function enqueue_scripts() {
		$screen = get_current_screen();

		// Only load on our plugin's settings page
		if ( $screen && $this->is_plugin_admin_page($screen->id) ) {
			// Enqueue jQuery UI for the admin interface
			wp_enqueue_script('jquery-ui-core');
			wp_enqueue_script('jquery-ui-tabs');
			wp_enqueue_script('jquery-ui-accordion');
			wp_enqueue_script('jquery-ui-dialog');
			wp_enqueue_script('jquery-ui-sortable');
			wp_enqueue_script('jquery-ui-autocomplete');

			// Enqueue Select2 for enhanced select boxes
			wp_enqueue_style('select2', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css', array(), '4.1.0-rc.0');
			wp_enqueue_script('select2', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), '4.1.0-rc.0', true);

			// Enqueue our admin script
			wp_enqueue_script(
				$this->plugin_name,
				plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/mind-qtrl-space-access-admin.js',
				array('jquery', 'jquery-ui-core', 'select2'),
				time(), // Use current timestamp to force cache refresh
				true
			);

			// Enqueue space settings script
			wp_enqueue_script(
				$this->plugin_name . '-space-settings',
				plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/space-settings.js',
				array('jquery', $this->plugin_name),
				time(), // Use current timestamp to force cache refresh
				true
			);

			// Enqueue status indicator fix script
			wp_enqueue_script(
				$this->plugin_name . '-status-fix',
				plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/status-indicator-fix.js',
				array('jquery', $this->plugin_name, $this->plugin_name . '-space-settings'),
				time(), // Use current timestamp to force cache refresh
				true
			);

			// Pass variables to the script
			wp_localize_script(
				$this->plugin_name,
				'mqsaAdmin',
				array(
					'ajaxurl' => admin_url('admin-ajax.php'),
					'nonce' => wp_create_nonce('mqsa_admin_nonce'),
					'plugin_url' => MQSA_PLUGIN_URL,
					'debug_enabled' => isset($this->options['enable_debug_logging']) ? $this->options['enable_debug_logging'] : 'yes',
					'strings' => array(
						'save_success' => __('Settings saved successfully.', 'mind-qtrl-space-access'),
						'save_error' => __('Error saving settings.', 'mind-qtrl-space-access'),
						'confirm_clear_log' => __('Are you sure you want to clear the debug log?', 'mind-qtrl-space-access'),
						'log_cleared' => __('Debug log cleared successfully.', 'mind-qtrl-space-access'),
						'log_clear_error' => __('Error clearing debug log.', 'mind-qtrl-space-access'),
						'confirm_delete' => __('Are you sure you want to delete this item?', 'mind-qtrl-space-access'),
						'loading' => __('Loading...', 'mind-qtrl-space-access'),
						'select_space' => __('Select a space...', 'mind-qtrl-space-access'),
						'select_tags' => __('Select CRM tags...', 'mind-qtrl-space-access'),
						'select_badges' => __('Select badges...', 'mind-qtrl-space-access'),
						'select_levels' => __('Select leaderboard levels...', 'mind-qtrl-space-access'),
					)
				)
			);
		}
	}

	/**
	 * Add the admin menu for this plugin.
	 *
	 * @since    0.0.1
	 */
	public function add_admin_menu() {
		add_menu_page(
			__('Mind Qtrl | Space Access Control', 'mind-qtrl-space-access'),
			__('Space Access', 'mind-qtrl-space-access'),
			'manage_options',
			$this->plugin_name,
			array($this, 'display_admin_page'),
			'dashicons-lock',
			30
		);

		// Add submenus
		add_submenu_page(
			$this->plugin_name,
			__('Settings', 'mind-qtrl-space-access'),
			__('Settings', 'mind-qtrl-space-access'),
			'manage_options',
			$this->plugin_name,
			array($this, 'display_admin_page')
		);

		add_submenu_page(
			$this->plugin_name,
			__('Space Rules', 'mind-qtrl-space-access'),
			__('Space Rules', 'mind-qtrl-space-access'),
			'manage_options',
			$this->plugin_name . '-rules',
			array($this, 'display_rules_page')
		);

		add_submenu_page(
			$this->plugin_name,
			__('Debug Log', 'mind-qtrl-space-access'),
			__('Debug Log', 'mind-qtrl-space-access'),
			'manage_options',
			$this->plugin_name . '-debug',
			array($this, 'display_debug_page')
		);
	}

	/**
	 * Register plugin settings.
	 *
	 * @since    0.0.1
	 */
	public function register_settings() {
		register_setting(
			'mqsa_settings_group',
			'mqsa_settings',
			array($this, 'sanitize_settings')
		);

		// General Settings Section
		add_settings_section(
			'mqsa_general_section',
			__('General Settings', 'mind-qtrl-space-access'),
			array($this, 'render_general_section'),
			$this->plugin_name
		);

		// Add settings fields
		add_settings_field(
			'enable_space_access_control',
			__('Enable Space Access Control', 'mind-qtrl-space-access'),
			array($this, 'render_enable_space_access_control_field'),
			$this->plugin_name,
			'mqsa_general_section'
		);

		add_settings_field(
			'enable_debug_logging',
			__('Enable Debug Logging', 'mind-qtrl-space-access'),
			array($this, 'render_enable_debug_logging_field'),
			$this->plugin_name,
			'mqsa_general_section'
		);

		// Default Messages Section
		add_settings_section(
			'mqsa_messages_section',
			__('Default Messages', 'mind-qtrl-space-access'),
			array($this, 'render_messages_section'),
			$this->plugin_name
		);

		add_settings_field(
			'default_access_message',
			__('Default Access Denied Message', 'mind-qtrl-space-access'),
			array($this, 'render_default_access_message_field'),
			$this->plugin_name,
			'mqsa_messages_section'
		);

		add_settings_field(
			'default_join_message',
			__('Default Join Denied Message', 'mind-qtrl-space-access'),
			array($this, 'render_default_join_message_field'),
			$this->plugin_name,
			'mqsa_messages_section'
		);

		add_settings_field(
			'default_post_message',
			__('Default Post Denied Message', 'mind-qtrl-space-access'),
			array($this, 'render_default_post_message_field'),
			$this->plugin_name,
			'mqsa_messages_section'
		);

		add_settings_field(
			'default_comment_message',
			__('Default Comment/React Denied Message', 'mind-qtrl-space-access'),
			array($this, 'render_default_comment_message_field'),
			$this->plugin_name,
			'mqsa_messages_section'
		);

		add_settings_field(
			'default_like_message',
			__('Default Like Denied Message', 'mind-qtrl-space-access'),
			array($this, 'render_default_like_message_field'),
			$this->plugin_name,
			'mqsa_messages_section'
		);
	}

	/**
	 * Sanitize settings before saving.
	 *
	 * @since    0.0.1
	 * @param    array    $input    The settings to sanitize.
	 * @return   array              The sanitized settings.
	 */
	public function sanitize_settings( $input ) {
		$sanitized = array();

		// Sanitize yes/no options
		$yes_no_options = array(
			'enable_space_access_control',
			'enable_debug_logging'
		);

		foreach ( $yes_no_options as $option ) {
			$sanitized[$option] = isset( $input[$option] ) ? sanitize_text_field( $input[$option] ) : 'no';
		}

		// Sanitize text fields
		$text_fields = array(
			'default_access_message',
			'default_join_message',
			'default_post_message',
			'default_comment_message'
		);

		foreach ( $text_fields as $field ) {
			$sanitized[$field] = isset( $input[$field] ) ? wp_kses_post( $input[$field] ) : '';
		}

		return $sanitized;
	}

	/**
	 * Render the general section description.
	 *
	 * @since    0.0.1
	 */
	public function render_general_section() {
		echo '<p>' . __('Configure the general settings for Space Access Control.', 'mind-qtrl-space-access') . '</p>';
	}

	/**
	 * Render the messages section description.
	 *
	 * @since    0.0.1
	 */
	public function render_messages_section() {
		echo '<p>' . __('Configure the default messages shown to users when access is denied.', 'mind-qtrl-space-access') . '</p>';
	}

	/**
	 * Render the enable space access control field.
	 *
	 * @since    0.0.1
	 */
	public function render_enable_space_access_control_field() {
		$value = isset( $this->options['enable_space_access_control'] ) ? $this->options['enable_space_access_control'] : 'yes';
		?>
		<select name="mqsa_settings[enable_space_access_control]" id="enable_space_access_control">
			<option value="yes" <?php selected( $value, 'yes' ); ?>><?php _e('Yes', 'mind-qtrl-space-access'); ?></option>
			<option value="no" <?php selected( $value, 'no' ); ?>><?php _e('No', 'mind-qtrl-space-access'); ?></option>
		</select>
		<p class="description"><?php _e('Enable or disable space access control functionality.', 'mind-qtrl-space-access'); ?></p>
		<?php
	}

	/**
	 * Render the enable debug logging field.
	 *
	 * @since    0.0.1
	 */
	public function render_enable_debug_logging_field() {
		$value = isset( $this->options['enable_debug_logging'] ) ? $this->options['enable_debug_logging'] : 'yes';
		?>
		<select name="mqsa_settings[enable_debug_logging]" id="enable_debug_logging">
			<option value="yes" <?php selected( $value, 'yes' ); ?>><?php _e('Yes', 'mind-qtrl-space-access'); ?></option>
			<option value="no" <?php selected( $value, 'no' ); ?>><?php _e('No', 'mind-qtrl-space-access'); ?></option>
		</select>
		<p class="description"><?php _e('Enable or disable debug logging.', 'mind-qtrl-space-access'); ?></p>
		<?php
	}

	/**
	 * Render the default access message field.
	 *
	 * @since    0.0.1
	 */
	public function render_default_access_message_field() {
		$value = isset( $this->options['default_access_message'] ) ? $this->options['default_access_message'] : 'You do not have access to this space. Please contact the administrator for more information.';
		?>
		<textarea name="mqsa_settings[default_access_message]" id="default_access_message" rows="3" cols="50"><?php echo esc_textarea( $value ); ?></textarea>
		<p class="description"><?php _e('Message shown when a user tries to access a space they don\'t have permission to view.', 'mind-qtrl-space-access'); ?></p>
		<?php
	}

	/**
	 * Render the default join message field.
	 *
	 * @since    0.0.1
	 */
	public function render_default_join_message_field() {
		$value = isset( $this->options['default_join_message'] ) ? $this->options['default_join_message'] : 'You do not have permission to join this space. Please contact the administrator for more information.';
		?>
		<textarea name="mqsa_settings[default_join_message]" id="default_join_message" rows="3" cols="50"><?php echo esc_textarea( $value ); ?></textarea>
		<p class="description"><?php _e('Message shown when a user tries to join a space they don\'t have permission to join.', 'mind-qtrl-space-access'); ?></p>
		<?php
	}

	/**
	 * Render the default post message field.
	 *
	 * @since    0.0.1
	 */
	public function render_default_post_message_field() {
		$value = isset( $this->options['default_post_message'] ) ? $this->options['default_post_message'] : 'You do not have permission to post in this space. Please contact the administrator for more information.';
		?>
		<textarea name="mqsa_settings[default_post_message]" id="default_post_message" rows="3" cols="50"><?php echo esc_textarea( $value ); ?></textarea>
		<p class="description"><?php _e('Message shown when a user tries to post in a space they don\'t have permission to post in.', 'mind-qtrl-space-access'); ?></p>
		<?php
	}

	/**
	 * Render the default comment message field.
	 *
	 * @since    0.0.1
	 */
	public function render_default_comment_message_field() {
		$value = isset( $this->options['default_comment_message'] ) ? $this->options['default_comment_message'] : 'You do not have permission to comment or react in this space. Please contact the administrator for more information.';
		?>
		<textarea name="mqsa_settings[default_comment_message]" id="default_comment_message" rows="3" cols="50"><?php echo esc_textarea( $value ); ?></textarea>
		<p class="description"><?php _e('Message shown when a user tries to comment or react in a space they don\'t have permission to interact with.', 'mind-qtrl-space-access'); ?></p>
		<?php
	}

	/**
	 * Render the default like message field.
	 *
	 * @since    0.0.1
	 */
	public function render_default_like_message_field() {
		$value = isset( $this->options['default_like_message'] ) ? $this->options['default_like_message'] : 'You do not have permission to like activities in this space. Please contact the administrator for more information.';
		?>
		<textarea name="mqsa_settings[default_like_message]" id="default_like_message" rows="3" cols="50"><?php echo esc_textarea( $value ); ?></textarea>
		<p class="description"><?php _e('Message shown when a user tries to like activities in a space they don\'t have permission to interact with.', 'mind-qtrl-space-access'); ?></p>
		<?php
	}

	/**
	 * Display the admin settings page.
	 *
	 * @since    0.0.1
	 */
	public function display_admin_page() {
		// Include the admin display template
		require_once plugin_dir_path( __FILE__ ) . 'partials/mind-qtrl-space-access-admin-display.php';
	}

	/**
	 * Display the space rules page.
	 *
	 * @since    0.0.1
	 */
	public function display_rules_page() {
		// Include the rules display template
		require_once plugin_dir_path( __FILE__ ) . 'partials/mind-qtrl-space-access-rules-display.php';
	}

	/**
	 * Display the debug log page.
	 *
	 * @since    0.0.1
	 */
	public function display_debug_page() {
		// Include the debug display template
		require_once plugin_dir_path( __FILE__ ) . 'partials/mind-qtrl-space-access-debug-display.php';
	}

	/**
	 * AJAX handler for getting the debug log.
	 *
	 * @since    0.0.1
	 */
	public function ajax_get_debug_log() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Get the log content
		if ( file_exists( $this->log_file ) ) {
			$log_content = file_get_contents( $this->log_file );
			wp_send_json_success( array( 'log' => $log_content ) );
		} else {
			wp_send_json_error( array( 'message' => 'Log file not found' ) );
		}
	}

	/**
	 * AJAX handler for clearing the debug log.
	 *
	 * @since    0.0.1
	 */
	public function ajax_clear_debug_log() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Clear the log file
		if ( file_exists( $this->log_file ) ) {
			file_put_contents( $this->log_file, '' );
			wp_send_json_success( array( 'message' => 'Log cleared successfully' ) );
		} else {
			wp_send_json_error( array( 'message' => 'Log file not found' ) );
		}
	}


	/**
	 * AJAX handler for saving space settings.
	 *
	 * @since    0.0.1
	 */
	public function ajax_save_space_settings() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Get the space ID and settings
		if ( ! isset( $_POST['space_id'] ) || ! isset( $_POST['settings'] ) ) {
			wp_send_json_error( array( 'message' => 'Missing required parameters' ) );
		}

		$space_id = intval( $_POST['space_id'] );
		$settings = json_decode( stripslashes( $_POST['settings'] ), true );

		// Sanitize settings
		$sanitized_settings = array();

		// Sanitize access control settings
		$sanitized_settings['enabled'] = isset( $settings['enabled'] ) ? sanitize_text_field( $settings['enabled'] ) : 'no';

		// Sanitize hide_like_buttons setting
		$sanitized_settings['hide_like_buttons'] = isset( $settings['hide_like_buttons'] ) ? sanitize_text_field( $settings['hide_like_buttons'] ) : 'no';

		// Sanitize restrict_like_unjoined setting
		$sanitized_settings['restrict_like_unjoined'] = isset( $settings['restrict_like_unjoined'] ) ? sanitize_text_field( $settings['restrict_like_unjoined'] ) : 'no';

		// Sanitize access types
		$access_types = array('view', 'join', 'post', 'comment', 'like');
		foreach ( $access_types as $type ) {
			$sanitized_settings[$type] = array();

			// Sanitize enabled status
			$sanitized_settings[$type]['enabled'] = isset( $settings[$type]['enabled'] ) ? sanitize_text_field( $settings[$type]['enabled'] ) : 'no';

			// Sanitize message
			$sanitized_settings[$type]['message'] = isset( $settings[$type]['message'] ) ? wp_kses_post( $settings[$type]['message'] ) : '';

			// Sanitize criteria
			$sanitized_settings[$type]['criteria'] = array();

			// Sanitize badges
			if ( isset( $settings[$type]['criteria']['badges'] ) && is_array( $settings[$type]['criteria']['badges'] ) ) {
				$sanitized_settings[$type]['criteria']['badges'] = array_map( 'intval', $settings[$type]['criteria']['badges'] );
			} else {
				$sanitized_settings[$type]['criteria']['badges'] = array();
			}

			// Sanitize CRM tags
			if ( isset( $settings[$type]['criteria']['crm_tags'] ) && is_array( $settings[$type]['criteria']['crm_tags'] ) ) {
				$sanitized_settings[$type]['criteria']['crm_tags'] = array_map( 'intval', $settings[$type]['criteria']['crm_tags'] );
			} else {
				$sanitized_settings[$type]['criteria']['crm_tags'] = array();
			}

			// Sanitize leaderboard levels
			if ( isset( $settings[$type]['criteria']['leaderboard_levels'] ) && is_array( $settings[$type]['criteria']['leaderboard_levels'] ) ) {
				$sanitized_settings[$type]['criteria']['leaderboard_levels'] = array_map( 'intval', $settings[$type]['criteria']['leaderboard_levels'] );
			} else {
				$sanitized_settings[$type]['criteria']['leaderboard_levels'] = array();
			}
		}

		// Save the settings
		$space_settings = get_option( 'mqsa_space_settings', array() );
		$space_settings[$space_id] = $sanitized_settings;
		update_option( 'mqsa_space_settings', $space_settings );

		// Log the action
		$this->log_debug( sprintf( 'Space settings saved for space ID %d', $space_id ) );

		wp_send_json_success( array( 'message' => 'Space settings saved successfully' ) );
	}

	/**
	 * AJAX handler for getting space settings.
	 *
	 * @since    0.0.1
	 */
	public function ajax_get_space_settings() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Get the space ID
		if ( ! isset( $_POST['space_id'] ) ) {
			wp_send_json_error( array( 'message' => 'Missing space ID' ) );
		}

		$space_id = intval( $_POST['space_id'] );

		// Get the space settings
		$space_settings = get_option( 'mqsa_space_settings', array() );

		if ( isset( $space_settings[$space_id] ) ) {
			wp_send_json_success( array( 'settings' => $space_settings[$space_id] ) );
		} else {
			// Return default settings
			$default_settings = $this->get_default_space_settings();
			wp_send_json_success( array( 'settings' => $default_settings ) );
		}
	}

	/**
	 * AJAX handler for getting spaces.
	 *
	 * @since    0.0.1
	 */
	public function ajax_get_spaces() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Check if Fluent Community is active
		if ( ! class_exists( '\FluentCommunity\App\Models\Space' ) ) {
			wp_send_json_error( array( 'message' => 'Fluent Community is not active' ) );
		}

		// Get all spaces
		$spaces = \FluentCommunity\App\Models\Space::orderBy('title', 'ASC')->get();

		$formatted_spaces = array();
		foreach ( $spaces as $space ) {
			$formatted_spaces[] = array(
				'id' => $space->id,
				'title' => $space->title,
				'slug' => $space->slug,
				'privacy' => $space->privacy
			);
		}

		wp_send_json_success( array( 'spaces' => $formatted_spaces ) );
	}

	/**
	 * AJAX handler for getting CRM tags.
	 *
	 * @since    0.0.1
	 */
	public function ajax_get_crm_tags() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Check if FluentCRM is active
		if ( ! defined( 'FLUENTCRM' ) ) {
			wp_send_json_error( array( 'message' => 'FluentCRM is not active' ) );
		}

		// Get all tags
		$tags = array();

		if ( class_exists( '\FluentCrm\App\Models\Tag' ) ) {
			$crm_tags = \FluentCrm\App\Models\Tag::orderBy('title', 'ASC')->get();

			foreach ( $crm_tags as $tag ) {
				$tags[] = array(
					'id' => $tag->id,
					'title' => $tag->title,
					'slug' => $tag->slug
				);
			}
		}

		wp_send_json_success( array( 'tags' => $tags ) );
	}

	/**
	 * AJAX handler for getting badges.
	 *
	 * @since    0.0.1
	 */
	public function ajax_get_badges() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Check if Fluent Community Pro is active
		if ( ! class_exists( '\FluentCommunityPro\App\Models\Badge' ) ) {
			wp_send_json_error( array( 'message' => 'Fluent Community Pro is not active or badges are not available' ) );
		}

		// Get all badges
		$badges = array();

		try {
			$fc_badges = \FluentCommunityPro\App\Models\Badge::orderBy('title', 'ASC')->get();

			foreach ( $fc_badges as $badge ) {
				$badges[] = array(
					'id' => $badge->id,
					'title' => $badge->title,
					'description' => $badge->description
				);
			}
		} catch ( \Exception $e ) {
			// Log the error
			$this->log_debug( 'Error getting badges: ' . $e->getMessage() );
		}

		wp_send_json_success( array( 'badges' => $badges ) );
	}

	/**
	 * AJAX handler for getting leaderboard levels.
	 *
	 * @since    0.0.1
	 */
	public function ajax_get_leaderboard_levels() {
		// Check nonce
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'mqsa_admin_nonce' ) ) {
			wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
		}

		// Check permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Permission denied' ) );
		}

		// Check if Fluent Community Pro is active
		if ( ! class_exists( '\FluentCommunityPro\App\Models\LeaderboardLevel' ) ) {
			wp_send_json_error( array( 'message' => 'Fluent Community Pro is not active or leaderboard levels are not available' ) );
		}

		// Get all leaderboard levels
		$levels = array();

		try {
			$fc_levels = \FluentCommunityPro\App\Models\LeaderboardLevel::orderBy('points', 'ASC')->get();

			foreach ( $fc_levels as $level ) {
				$levels[] = array(
					'id' => $level->id,
					'title' => $level->title,
					'points' => $level->points
				);
			}
		} catch ( \Exception $e ) {
			// Log the error
			$this->log_debug( 'Error getting leaderboard levels: ' . $e->getMessage() );
		}

		wp_send_json_success( array( 'levels' => $levels ) );
	}

	/**
	 * Get default space settings.
	 *
	 * @since    0.0.1
	 * @return   array    The default space settings.
	 */
	private function get_default_space_settings() {
		// Get default messages from global settings
		$default_access_message = isset( $this->options['default_access_message'] ) ? $this->options['default_access_message'] : 'You do not have access to this space. Please contact the administrator for more information.';
		$default_join_message = isset( $this->options['default_join_message'] ) ? $this->options['default_join_message'] : 'You do not have permission to join this space. Please contact the administrator for more information.';
		$default_post_message = isset( $this->options['default_post_message'] ) ? $this->options['default_post_message'] : 'You do not have permission to post in this space. Please contact the administrator for more information.';
		$default_comment_message = isset( $this->options['default_comment_message'] ) ? $this->options['default_comment_message'] : 'You do not have permission to comment or react in this space. Please contact the administrator for more information.';
		$default_like_message = isset( $this->options['default_like_message'] ) ? $this->options['default_like_message'] : 'You do not have permission to like activities in this space. Please contact the administrator for more information.';

		return array(
			'enabled' => 'no',
			'view' => array(
				'enabled' => 'no',
				'message' => $default_access_message,
				'criteria' => array(
					'badges' => array(),
					'crm_tags' => array(),
					'leaderboard_levels' => array()
				)
			),
			'join' => array(
				'enabled' => 'no',
				'message' => $default_join_message,
				'criteria' => array(
					'badges' => array(),
					'crm_tags' => array(),
					'leaderboard_levels' => array()
				)
			),
			'post' => array(
				'enabled' => 'no',
				'message' => $default_post_message,
				'criteria' => array(
					'badges' => array(),
					'crm_tags' => array(),
					'leaderboard_levels' => array()
				)
			),
			'comment' => array(
				'enabled' => 'no',
				'message' => $default_comment_message,
				'criteria' => array(
					'badges' => array(),
					'crm_tags' => array(),
					'leaderboard_levels' => array()
				)
			),
			'like' => array(
				'enabled' => 'no',
				'message' => $default_like_message,
				'criteria' => array(
					'badges' => array(),
					'crm_tags' => array(),
					'leaderboard_levels' => array()
				),
				'hide_buttons' => 'no'
			),
			'hide_like_buttons' => 'no',
			'restrict_like_unjoined' => 'no'
		);
	}

	/**
	 * AJAX handler for toggling debug logging.
	 *
	 * @since    0.0.4
	 * @return   void
	 */
	public function ajax_toggle_debug() {
		// Check nonce
		if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqsa_admin_nonce')) {
			wp_send_json_error(array('message' => __('Invalid security token', 'mind-qtrl-space-access')));
		}

		// Check permissions
		if (!current_user_can('manage_options')) {
			wp_send_json_error(array('message' => __('Permission denied', 'mind-qtrl-space-access')));
		}

		// Get the enabled status
		if (!isset($_POST['enabled'])) {
			// If no enabled parameter, toggle based on current setting
			$options = get_option('mqsa_settings', array());
			$current_value = isset($options['enable_debug_logging']) ? $options['enable_debug_logging'] : 'yes';
			$enabled = ($current_value === 'yes') ? 'no' : 'yes';
		} else {
			$enabled = sanitize_text_field($_POST['enabled']);
		}

		// Validate enabled value
		if (!in_array($enabled, array('yes', 'no'))) {
			wp_send_json_error(array('message' => __('Invalid enabled value', 'mind-qtrl-space-access')));
		}

		// Update the option
		$options = get_option('mqsa_settings', array());
		$options['enable_debug_logging'] = $enabled;
		$updated = update_option('mqsa_settings', $options);

		if (!$updated) {
			wp_send_json_error(array('message' => __('Failed to update settings', 'mind-qtrl-space-access')));
		}

		// Update the instance options
		$this->options = $options;

		// Log the action
		$this->log_debug(sprintf(
			__('Debug logging %s by user ID: %d', 'mind-qtrl-space-access'),
			($enabled === 'yes' ? 'enabled' : 'disabled'),
			get_current_user_id()
		));

		wp_send_json_success(array(
			'message' => sprintf(
				__('Debug logging has been %s', 'mind-qtrl-space-access'),
				($enabled === 'yes' ? 'enabled' : 'disabled')
			),
			'status' => $enabled
		));
	}

	/**
	 * Log debug message.
	 *
	 * @since    0.0.1
	 * @param    string    $message    The message to log.
	 */
	public function log_debug( $message ) {
		// Check if debug logging is enabled
		if ( isset( $this->options['enable_debug_logging'] ) && $this->options['enable_debug_logging'] === 'yes' ) {
			// Make sure the log directory exists
			$log_dir = dirname( $this->log_file );
			if ( ! file_exists( $log_dir ) ) {
				wp_mkdir_p( $log_dir );
			}

			// Format the message
			$timestamp = current_time( 'mysql' );
			$formatted_message = sprintf( "[%s] %s\n", $timestamp, $message );

			// Append to the log file
			file_put_contents( $this->log_file, $formatted_message, FILE_APPEND );
		}
	}

	/**
	 * Check if we're on a plugin admin page
	 *
	 * @since    0.0.1
	 * @return   boolean    True if on plugin admin page
	 */
	private function is_plugin_page() {
		$current_screen = get_current_screen();
		$screen_id = $current_screen ? $current_screen->id : '';

		// Ensure we have a string to check against
		if (empty($screen_id)) {
			return false;
		}

		return strpos($screen_id, $this->plugin_name) !== false;
	}

	/**
	 * Check if we're on the plugin settings page
	 *
	 * @since    0.0.1
	 * @return   boolean    True if on plugin settings page
	 */
	private function is_settings_page() {
		$current_screen = get_current_screen();
		$screen_id = $current_screen ? $current_screen->id : '';

		// Ensure we have a string to check against
		if (empty($screen_id)) {
			return false;
		}

		return strpos($screen_id, $this->plugin_name . '_settings') !== false;
	}

	/**
	 * Check if current page is a plugin admin page
	 *
	 * @since    0.0.1
	 * @param    string    $screen_id    The current screen ID
	 * @return   boolean
	 */
	private function is_plugin_admin_page($screen_id) {
		if (empty($screen_id)) {
			return false;
		}

		$plugin_pages = array(
			'toplevel_page_' . $this->plugin_name,
			$this->plugin_name . '_page_' . $this->plugin_name . '-rules',
			$this->plugin_name . '_page_' . $this->plugin_name . '-debug'
		);

		return in_array($screen_id, $plugin_pages);
	}
}
