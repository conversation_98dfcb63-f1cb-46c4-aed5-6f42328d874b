<?php
class MQSA_Admin {
    private $plugin_name;
    private $version;

    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        add_action('admin_enqueue_scripts', [$this, 'enqueue_styles']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('admin_menu', [$this, 'add_menu']);
        add_action('admin_init', [$this, 'register_settings']);

        // Add AJAX handler for saving settings
        add_action('wp_ajax_mqsa_save_settings', [$this, 'ajax_save_settings']);
    }

    public function enqueue_styles() {
        wp_enqueue_style(
            $this->plugin_name . '-admin',
            plugin_dir_url(__FILE__) . 'css/admin.css',
            [],
            $this->version
        );
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            $this->plugin_name . '-admin',
            plugin_dir_url(__FILE__) . 'js/admin.js',
            ['jquery'],
            time(), // Use current timestamp to force cache refresh
            true
        );

        wp_localize_script($this->plugin_name . '-admin', 'mqsaAdmin', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mqsa-admin'),
            'strings' => [
                'saveSuccess' => __('Settings saved successfully.', 'mind-qtrl-space-access'),
                'saveError' => __('An error occurred while saving settings.', 'mind-qtrl-space-access')
            ]
        ]);
    }

    public function add_menu() {
        // Add top-level Mind Qtrl menu
        add_menu_page(
            __('Mind Qtrl', 'mind-qtrl-space-access'),
            __('Mind Qtrl', 'mind-qtrl-space-access'),
            'manage_options',
            'mind-qtrl-admin',
            [$this, 'render_admin_page'],
            plugin_dir_url(__FILE__) . 'images/mind-qtrl-icon.png',  // 20px x 20px icon
            30
        );

        // Add Space Access as submenu under Mind Qtrl
        add_submenu_page(
            'mind-qtrl-admin',  // Parent slug for Mind Qtrl
            __('Space Access', 'mind-qtrl-space-access'),
            __('Space Access', 'mind-qtrl-space-access'),
            'manage_options',
            'mind-qtrl-admin',  // Same as parent to make it the default page
            [$this, 'render_admin_page']
        );

        // Add Settings submenu under Mind Qtrl
        add_submenu_page(
            'mind-qtrl-admin',  // Parent slug for Mind Qtrl
            __('Settings', 'mind-qtrl-space-access'),
            __('Settings', 'mind-qtrl-space-access'),
            'manage_options',
            'mind-qtrl-admin-settings',
            [$this, 'render_admin_page']
        );

        // Add as submenu to Fluent Community if active
        if (function_exists('FluentCommunity') || class_exists('\\FluentCommunity\\App\\App')) {
            add_submenu_page(
                'fluent-community',  // Parent slug for Fluent Community
                __('Space Access', 'mind-qtrl-space-access'),
                __('Space Access', 'mind-qtrl-space-access'),
                'manage_options',
                'mind-qtrl-space-access-fluent',
                [$this, 'render_admin_page']
            );
        }
    }

    public function render_admin_page() {
        // Get current tab
        $tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : '';

        // Get current page
        $page = isset($_GET['page']) ? sanitize_text_field($_GET['page']) : '';

        // Set page title and default tab based on page slug
        $page_title = __('Mind Qtrl | Space Access Control', 'mind-qtrl-space-access');
        if ($page === 'mind-qtrl-admin') {
            $page_title = __('Mind Qtrl | Space Access Control', 'mind-qtrl-space-access');
            // Default to rules tab if no tab specified
            if (empty($tab)) {
                $tab = 'rules';
            }
        } elseif ($page === 'mind-qtrl-admin-settings') {
            $page_title = __('Mind Qtrl | Settings', 'mind-qtrl-space-access');
            // Default to settings tab
            if (empty($tab)) {
                $tab = 'settings';
            }
        } elseif ($page === 'mind-qtrl-space-access-fluent') {
            $page_title = __('Space Access Control', 'mind-qtrl-space-access');
        }

        // Add admin header
        echo '<div class="wrap mqsa-admin-wrap">';
        echo '<div class="mqsa-header">';
        echo '<img src="' . plugin_dir_url(__FILE__) . 'images/mind-qtrl-logo.png' . '" alt="Mind Qtrl Logo" class="mqsa-logo">';
        echo '<h1 class="mqsa-title">' . esc_html($page_title) . '</h1>';
        echo '</div>';

        // Add tabs navigation
        echo '<div class="mqsa-nav-tabs">';

        // Determine the base URL for tabs based on the current page
        $base_url = admin_url('admin.php?page=' . $page);

        // Settings tab
        echo '<a href="' . esc_url(add_query_arg('tab', 'settings', $base_url)) . '" class="mqsa-nav-tab ' . ($tab === 'settings' ? 'active' : '') . '">';
        echo esc_html__('Settings', 'mind-qtrl-space-access');
        echo '</a>';

        // Space Rules tab
        echo '<a href="' . esc_url(add_query_arg('tab', 'rules', $base_url)) . '" class="mqsa-nav-tab ' . ($tab === 'rules' ? 'active' : '') . '">';
        echo esc_html__('Space Rules', 'mind-qtrl-space-access');
        echo '</a>';

        // Debug Log tab
        echo '<a href="' . esc_url(add_query_arg('tab', 'debug', $base_url)) . '" class="mqsa-nav-tab ' . ($tab === 'debug' ? 'active' : '') . '">';
        echo esc_html__('Debug Log', 'mind-qtrl-space-access');
        echo '</a>';

        echo '</div>';

        // Add content container
        echo '<div class="mqsa-content">';

        // Include the appropriate tab file
        switch ($tab) {
            case 'rules':
                // Try to load space-settings.php from tabs directory
                if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/space-settings.php')) {
                    include_once plugin_dir_path(__FILE__) . 'partials/tabs/space-settings.php';
                } else if (file_exists(plugin_dir_path(__FILE__) . 'partials/mind-qtrl-space-access-rules-display.php')) {
                    // Fallback to old file
                    include_once plugin_dir_path(__FILE__) . 'partials/mind-qtrl-space-access-rules-display.php';
                } else {
                    echo '<div class="mqsa-notice mqsa-notice-error">';
                    echo esc_html__('Rules display file not found.', 'mind-qtrl-space-access');
                    echo '</div>';
                    // Fallback to settings tab
                    if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/settings.php')) {
                        include_once plugin_dir_path(__FILE__) . 'partials/tabs/settings.php';
                    }
                }
                break;

            case 'debug':
                // Try to load debug-log.php from tabs directory
                if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/debug-log.php')) {
                    include_once plugin_dir_path(__FILE__) . 'partials/tabs/debug-log.php';
                } else if (file_exists(plugin_dir_path(__FILE__) . 'partials/mind-qtrl-space-access-debug-display.php')) {
                    // Fallback to old file
                    include_once plugin_dir_path(__FILE__) . 'partials/mind-qtrl-space-access-debug-display.php';
                } else {
                    echo '<div class="mqsa-notice mqsa-notice-error">';
                    echo esc_html__('Debug display file not found.', 'mind-qtrl-space-access');
                    echo '</div>';
                    // Fallback to settings tab
                    if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/settings.php')) {
                        include_once plugin_dir_path(__FILE__) . 'partials/tabs/settings.php';
                    }
                }
                break;

            case 'settings':
                // Try to load settings.php from tabs directory
                if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/settings.php')) {
                    include_once plugin_dir_path(__FILE__) . 'partials/tabs/settings.php';
                } else if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/general-settings.php')) {
                    // Fallback to general-settings.php
                    include_once plugin_dir_path(__FILE__) . 'partials/tabs/general-settings.php';
                } else {
                    // Fallback to inline form
                    echo '<form method="post" action="options.php" class="mqsa-form">';
                    settings_fields('mqsa_settings');
                    do_settings_sections('mind-qtrl-space-access');
                    submit_button(__('Save Settings', 'mind-qtrl-space-access'), 'primary mqsa-button');
                    echo '</form>';
                }
                break;

            default:
                // Default to settings tab
                if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/settings.php')) {
                    include_once plugin_dir_path(__FILE__) . 'partials/tabs/settings.php';
                } else if (file_exists(plugin_dir_path(__FILE__) . 'partials/tabs/general-settings.php')) {
                    // Fallback to general-settings.php
                    include_once plugin_dir_path(__FILE__) . 'partials/tabs/general-settings.php';
                } else {
                    // Fallback to inline form
                    echo '<form method="post" action="options.php" class="mqsa-form">';
                    settings_fields('mqsa_settings');
                    do_settings_sections('mind-qtrl-space-access');
                    submit_button(__('Save Settings', 'mind-qtrl-space-access'), 'primary mqsa-button');
                    echo '</form>';
                }
                break;
        }

        echo '</div>'; // Close mqsa-content
        echo '</div>'; // Close mqsa-admin-wrap
    }

    /**
     * Register plugin settings
     */
    public function register_settings() {
        // Register settings
        register_setting('mqsa_settings', 'mqsa_settings', [
            'sanitize_callback' => [$this, 'sanitize_settings']
        ]);

        // Add settings section
        add_settings_section(
            'mqsa_general_section',
            __('General Settings', 'mind-qtrl-space-access'),
            [$this, 'render_general_section'],
            'mind-qtrl-space-access'
        );

        // Add settings fields
        add_settings_field(
            'mqsa_enable_plugin',
            __('Enable Plugin', 'mind-qtrl-space-access'),
            [$this, 'render_enable_plugin_field'],
            'mind-qtrl-space-access',
            'mqsa_general_section'
        );

        add_settings_field(
            'mqsa_debug_mode',
            __('Debug Mode', 'mind-qtrl-space-access'),
            [$this, 'render_debug_mode_field'],
            'mind-qtrl-space-access',
            'mqsa_general_section'
        );
    }

    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = [];

        // Sanitize enable plugin
        $sanitized['enable_plugin'] = isset($input['enable_plugin']) ? 'yes' : 'no';

        // Sanitize debug mode
        $sanitized['debug_mode'] = isset($input['debug_mode']) ? 'yes' : 'no';

        // Update logger debug mode
        if (function_exists('mqsa_logger')) {
            mqsa_logger()->set_debug_mode($sanitized['debug_mode'] === 'yes');
        }

        return $sanitized;
    }

    /**
     * Render general section
     */
    public function render_general_section() {
        echo '<p>' . __('Configure general settings for the Space Access plugin.', 'mind-qtrl-space-access') . '</p>';
    }

    /**
     * Render enable plugin field
     */
    public function render_enable_plugin_field() {
        $settings = get_option('mqsa_settings', []);
        $enabled = isset($settings['enable_plugin']) ? $settings['enable_plugin'] : 'yes';

        echo '<label><input type="checkbox" name="mqsa_settings[enable_plugin]" value="1" ' . checked('yes', $enabled, false) . ' /> ' .
             __('Enable space access control', 'mind-qtrl-space-access') . '</label>';
    }

    /**
     * Render debug mode field
     */
    public function render_debug_mode_field() {
        $settings = get_option('mqsa_settings', []);
        $debug_mode = isset($settings['debug_mode']) ? $settings['debug_mode'] : 'no';

        echo '<label><input type="checkbox" name="mqsa_settings[debug_mode]" value="1" ' . checked('yes', $debug_mode, false) . ' /> ' .
             __('Enable debug logging', 'mind-qtrl-space-access') . '</label>';
    }

    /**
     * AJAX handler for saving settings
     */
    public function ajax_save_settings() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqsa-admin')) {
            wp_send_json_error([
                'message' => __('Security check failed.', 'mind-qtrl-space-access')
            ]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error([
                'message' => __('You do not have permission to perform this action.', 'mind-qtrl-space-access')
            ]);
        }

        // Parse form data
        $form_data = [];
        parse_str($_POST['formData'], $form_data);

        // Save settings
        if (isset($form_data['mqsa_settings'])) {
            // Get the settings
            $settings = $form_data['mqsa_settings'];

            // Sanitize settings
            $sanitized = $this->sanitize_settings($settings);

            // Update settings
            update_option('mqsa_settings', $sanitized);

            // Log settings update
            if (function_exists('mqsa_logger')) {
                mqsa_logger()->info('Settings updated via AJAX');
            }

            // Clear cache
            if (function_exists('mqsa_cache')) {
                mqsa_cache()->clear();
            }

            wp_send_json_success([
                'message' => __('Settings saved successfully.', 'mind-qtrl-space-access')
            ]);
        } else {
            wp_send_json_error([
                'message' => __('No settings data received.', 'mind-qtrl-space-access')
            ]);
        }
    }
}
