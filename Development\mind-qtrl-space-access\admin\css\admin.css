/* Main Admin Wrapper */
.mqsa-admin-wrap {
    background: #1a1a1a;
    color: #e0e0e0;
    padding: 25px;
    margin: 20px;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

a:hover, a:active {
    color: #59CBD2;
}

/* Header Styles */
.mqsa-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #333;
    position: relative;
}

.mqsa-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #59CBD2, #3a8a8f);
    border-radius: 3px;
}

.mqsa-logo {
    width: 48px;
    height: 48px;
    margin-right: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transition: transform 0.3s ease;
}

.mqsa-logo:hover {
    transform: scale(1.05);
}

.mqsa-title {
    color: #e0e0e0;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Navigation Tabs */
.mqsa-nav-tabs {
    display: flex;
    margin-bottom: 25px;
    border-bottom: 1px solid #333;
    padding-bottom: 1px;
}

.mqsa-nav-tab {
    padding: 12px 20px;
    margin-right: 8px;
    background: #262626;
    color: #e0e0e0;
    text-decoration: none;
    border-radius: 8px 8px 0 0;
    border: 1px solid #333;
    border-bottom: none;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    top: 1px;
}

.mqsa-nav-tab:hover {
    background: #2d2d2d;
    color: #59CBD2;
}

.mqsa-nav-tab.active {
    background: #333;
    color: #59CBD2;
    border-bottom: 1px solid #333;
    position: relative;
    font-weight: 600;
}

.mqsa-nav-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: #59CBD2;
    border-radius: 3px 3px 0 0;
}

/* Content Areas */
.mqsa-content {
    background: #262626;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #333;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mqsa-tab-content {
    background: #262626;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mqsa-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #333;
    border-radius: 8px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
    border-left: 3px solid #59CBD2;
}

/* Form Styles */
.mqsa-form {
    max-width: 800px;
}

.mqsa-form label {
    display: block;
    margin-bottom: 12px;
    font-weight: 500;
}

.mqsa-form input[type="text"],
.mqsa-form input[type="number"],
.mqsa-form select,
.mqsa-form textarea {
    width: 100%;
    padding: 10px 12px;
    background: #1a1a1a;
    border: 1px solid #444;
    color: #e0e0e0;
    border-radius: 6px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.mqsa-form input[type="text"]:focus,
.mqsa-form input[type="number"]:focus,
.mqsa-form select:focus,
.mqsa-form textarea:focus {
    border-color: #59CBD2;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(89, 203, 210, 0.2);
    outline: none;
}

.mqsa-form input[type="checkbox"] {
    margin-right: 10px;
}

.mqsa-form-group {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.mqsa-form-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* Button Styles */
.mqsa-button {
    background: linear-gradient(to bottom, #59CBD2, #3a8a8f);
    color: #fff;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.mqsa-button:hover {
    background: linear-gradient(to bottom, #6ad4db, #4a9a9f);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.mqsa-button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mqsa-button:disabled {
    background: #555;
    cursor: not-allowed;
    opacity: 0.7;
}

.mqsa-loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Notification Styles */
.mqsa-notice {
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    border-left: 4px solid transparent;
}

.mqsa-notice::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 15px;
    background-size: contain;
    background-repeat: no-repeat;
}

.mqsa-notice-success {
    background: rgba(70, 180, 80, 0.15);
    color: #e0e0e0;
    border-color: #46b450;
}

.mqsa-notice-success::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='%2346b450' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
}

.mqsa-notice-error {
    background: rgba(220, 50, 50, 0.15);
    color: #e0e0e0;
    border-color: #dc3232;
}

.mqsa-notice-error::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='%23dc3232' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
}

/* Sub-option and Collapsible Styles */
.mqsa-sub-option {
    margin-left: 30px;
    padding: 15px;
    border-left: 2px solid #444;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 0 6px 6px 0;
    margin-top: 10px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.mqsa-sub-option.hidden {
    display: none;
}

.mqsa-sub-option p.description {
    margin-top: 8px;
    margin-left: 25px;
    font-size: 13px;
    color: #aaa;
    line-height: 1.5;
}

/* Collapsible section styles */
.mqsa-collapsible {
    margin-top: 10px;
}

.mqsa-collapsible-header {
    padding: 12px 15px;
    background: #2a2a2a;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
    border-left: 3px solid #444;
}

.mqsa-collapsible-header:hover {
    background: #333;
    border-left-color: #59CBD2;
}

.mqsa-collapsible-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

.mqsa-collapsible-header .mqsa-toggle-icon {
    transition: transform 0.3s ease;
}

.mqsa-collapsible-header.active .mqsa-toggle-icon {
    transform: rotate(180deg);
}

.mqsa-collapsible-content {
    padding: 15px;
    background: #222;
    border-radius: 0 0 6px 6px;
    margin-top: -5px;
    display: none;
    border-left: 3px solid #444;
    border-bottom: 1px solid #444;
    border-right: 1px solid #444;
}

.mqsa-collapsible-content.active {
    display: block;
}

/* Card Styles */
.mqsa-card {
    background: #262626;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    margin-bottom: 25px;
    overflow: hidden;
    border: 1px solid #333;
}

.mqsa-card-header {
    background: #333;
    padding: 15px 20px;
    border-bottom: 1px solid #444;
    position: relative;
}

.mqsa-card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: #59CBD2;
}

.mqsa-card-header h3 {
    margin: 0;
    color: #e0e0e0;
    font-size: 18px;
    font-weight: 600;
}

.mqsa-card-body {
    padding: 20px;
}

.mqsa-card-footer {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.1);
    border-top: 1px solid #333;
    text-align: right;
}

/* Toggle Switch Styles */
.mqsa-form-checkbox {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    float: right;
}

.mqsa-form-checkbox input {
    opacity: 0;
    width: 0;
    height: 0;
}

.mqsa-form-checkbox .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #444;
    transition: .4s;
    border-radius: 24px;
}

.mqsa-form-checkbox .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.mqsa-form-checkbox input:checked + .slider {
    background-color: #59CBD2;
}

.mqsa-form-checkbox input:checked + .slider:before {
    transform: translateX(26px);
}

.mqsa-form-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    margin-bottom: 15px;
}

/* Space item indicator styles */
.mqsa-space-item {
    position: relative !important;
    padding-left: 30px !important; /* Make room for the indicator */
    overflow: visible !important;
    font-weight: 500 !important;
}

/* Indicator for all space items - red by default */
.mqsa-space-item::before {
    content: '' !important;
    display: block !important;
    position: absolute !important;
    left: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 14px !important; /* Slightly larger */
    height: 14px !important; /* Slightly larger */
    border-radius: 50% !important;
    background-color: #dc3545 !important; /* Red color by default */
    opacity: 1 !important; /* Full opacity */
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.8) !important; /* More visible shadow */
    animation: mqsa-pulse-red 2s infinite !important;
    z-index: 9999 !important; /* Higher z-index to ensure visibility */
    pointer-events: none !important;
    visibility: visible !important; /* Always show the indicator */
    display: block !important; /* Force display */
    border: 1px solid rgba(255, 255, 255, 0.5) !important; /* White border for contrast */
}

/* Green indicator only for spaces with active restrictions */
.mqsa-space-item.restrictions-active::before {
    background-color: #28a745 !important; /* Green color */
    opacity: 1 !important;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.8) !important; /* More visible shadow */
    animation: mqsa-pulse-green 2s infinite !important;
    border: 1px solid rgba(255, 255, 255, 0.5) !important; /* White border for contrast */
}

/* Ensure indicators are visible even on hover */
.mqsa-space-item:hover::before {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 10000 !important; /* Even higher z-index on hover */
    width: 16px !important; /* Slightly larger on hover */
    height: 16px !important; /* Slightly larger on hover */
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.8) !important; /* Brighter glow on hover */
}

/* Ensure the indicator is visible even when the space item has other pseudo-elements */
.mqsa-space-item::after {
    z-index: 99 !important;
}

/* Green pulse animation */
@keyframes mqsa-pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Red pulse animation */
@keyframes mqsa-pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);
    }
    70% {
        box-shadow: 0 0 0 7px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* Ensure the indicator is visible in all themes */
.mqsa-space-item.has-restrictions {
    position: relative !important;
    padding-left: 30px !important;
}

/* Active space item styling */
.mqsa-space-item.active {
    background-color: #333 !important;
    color: #fff !important;
    font-weight: bold !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
}
