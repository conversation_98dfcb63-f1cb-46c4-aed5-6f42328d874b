/**
 * Admin CSS for Mind Qtrl | Space Access Control
 * This file implements the Modern dark Mind Qtrl UI template for the admin interface.
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.0
 */

/* Modern dark Mind Qtrl UI template */

.toplevel_page_mind-qtrl-admin #wpwrap,
.mind-qtrl_page_mind-qtrl-admin-settings #wpwrap,
.mind-qtrl_page_mind-qtrl-space-access-fluent #wpwrap {
    background: #1B1B1E !important;
}

/* Admin Wrap */
.mqsa-admin-wrap {
    background: #1e1e2d;
    color: #ffffff;
    padding: 30px;
    border-radius: 12px;
    margin: 20px 20px 20px 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    max-width: 1200px;
}

/* Header */
.mqsa-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #333344;
}

.mqsa-logo {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.mqsa-header h1 {
    color: #8770FF;
    font-size: 24px;
    margin: 0;
    padding: 0;
}

/* Tabs */
.mqsa-nav-tabs {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0 0 30px 0;
    border-bottom: 2px solid #333344;
    justify-content: flex-start;
    gap: 10px;
}

.mqsa-nav-tab {
    padding: 12px 24px;
    cursor: pointer;
    background: #2a2a3c;
    border-radius: 8px 8px 0 0;
    border: 2px solid transparent;
    border-bottom: none;
    color: #8770FF;
    transition: all 0.3s ease;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
}

.mqsa-nav-tab:hover {
    background: #333344;
    transform: translateY(-2px);
}

.mqsa-nav-tab.active {
    background: #8770FF;
    color: #ffffff;
    border-color: #8770FF;
    box-shadow: 0 4px 8px rgba(135, 112, 255, 0.3);
}

/* Content */
.mqsa-content {
    background: #2a2a3c;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Sections */
.mqsa-section {
    background: #1e1e2d;
    padding: 20px;
    margin-bottom: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.mqsa-section h2,
.mqsa-content h2 {
    color: #8770FF;
    font-size: 20px;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333344;
}

.mqsa-section h3,
.mqsa-content h3 {
    color: #8770FF;
    font-size: 18px;
    margin-top: 0;
    margin-bottom: 15px;
}

/* Forms */
.mqsa-form {
    max-width: 800px;
}

.mqsa-form .form-table {
    background: transparent;
    border-collapse: collapse;
}

.mqsa-form .form-table th {
    color: #ffffff;
    font-weight: 500;
    padding: 15px;
    text-align: left;
}

.mqsa-form .form-table td {
    padding: 15px;
}

.mqsa-form input[type="text"],
.mqsa-form input[type="number"],
.mqsa-form input[type="email"],
.mqsa-form input[type="password"],
.mqsa-form select,
.mqsa-form textarea {
    background: #1e1e2d;
    border: 1px solid #333344;
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    width: 100%;
    max-width: 300px;
}

.mqsa-form input[type="checkbox"] {
    border: 1px solid #333344;
    background: #1e1e2d;
}

.mqsa-form .description {
    color: #aaaabc;
    font-style: italic;
    margin-top: 5px;
}

/* Submit button */
.mqsa-form .button-primary,
.mqsa-button {
    background: #8770FF;
    border-color: #6550dd;
    color: #ffffff;
    padding: 8px 20px;
    height: auto;
    font-size: 14px;
    transition: all 0.3s ease;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.mqsa-form .button-primary:hover,
.mqsa-form .button-primary:focus,
.mqsa-button:hover,
.mqsa-button:focus {
    background: #6550dd;
    border-color: #5440cc;
    color: #ffffff;
}

/* Spaces */
.mqsa-spaces-wrapper {
    display: flex;
    margin-top: 20px;
    gap: 30px;
}

.mqsa-spaces-sidebar {
    width: 250px;
    background: #1e1e2d;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.mqsa-spaces-sidebar h3 {
    color: #8770FF;
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #333344;
}

.mqsa-space-item {
    display: block;
    padding: 10px 15px;
    margin-bottom: 8px;
    background: #2a2a3c;
    border-radius: 4px;
    text-decoration: none;
    color: #ffffff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.mqsa-space-item:hover {
    background: #333344;
    transform: translateX(5px);
}

.mqsa-space-item.active {
    background: #8770FF;
    color: #ffffff;
    position: relative;
}

/* Ensure active space item with restrictions has proper padding */
.mqsa-space-item.active.has-restrictions {
    padding-left: 25px !important; /* Force padding for indicator */
}

.mqsa-space-item.has-restrictions {
    padding-left: 25px; /* Make room for the indicator */
    position: relative;
}

/* Indicator for all space items - red by default */
.mqsa-space-item::before {
    content: '' !important;
    position: absolute !important;
    left: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 10px !important;
    height: 10px !important;
    border-radius: 50% !important;
    background-color: #dc3545 !important; /* Red color by default */
    opacity: 0.8 !important;
    box-shadow: 0 0 0 rgba(220, 53, 69, 0.4) !important;
    animation: mqsa-pulse-red 2s infinite !important;
    z-index: 999 !important; /* Ensure it's visible above everything */
    display: block !important; /* Always show the indicator */
    visibility: visible !important; /* Ensure visibility */
    pointer-events: none !important; /* Don't interfere with clicks */
}

/* Green indicator only for spaces with active restrictions */
.mqsa-space-item.restrictions-active::before {
    background-color: #28a745 !important; /* Green color */
    opacity: 1 !important;
    box-shadow: 0 0 0 rgba(40, 167, 69, 0.4) !important;
    animation: mqsa-pulse-green 2s infinite !important;
}

/* Ensure indicators are visible even on hover */
.mqsa-space-item:hover::before {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Green pulse animation */
@keyframes mqsa-pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Red pulse animation */
@keyframes mqsa-pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);
    }
    70% {
        box-shadow: 0 0 0 7px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

.mqsa-space-settings {
    flex: 1;
    background: #1e1e2d;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Space Settings Form */
.mqsa-space-settings-form {
    animation: fadeIn 0.3s ease;
}

.mqsa-space-settings-form h3 {
    color: #8770FF;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333344;
    font-size: 20px;
}

.mqsa-form-group {
    margin-bottom: 20px;
}

.mqsa-submit-container {
    display: flex;
    align-items: center;
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid #333344;
}

.mqsa-submit-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.mqsa-success-notice-container {
    flex: 1;
    margin-left: 15px;
}

.mqsa-success-notice-container .mqsa-notice-success {
    margin: 0;
    padding: 8px 12px;
    display: inline-block;
    animation: mqsa-fade-in 0.3s ease;
}

@keyframes mqsa-fade-in {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

.mqsa-form-group label {
    display: block;
    margin-bottom: 8px;
    color: #ffffff;
    font-weight: 500;
}

.mqsa-form-group select,
.mqsa-form-group textarea {
    width: 100%;
    padding: 10px;
    background: #2a2a3c;
    border: 1px solid #333344;
    border-radius: 4px;
    color: #ffffff;
}

.mqsa-form-group select[multiple] {
    height: 120px;
}

.mqsa-form-group .description {
    margin-top: 5px;
    color: #aaaabc;
    font-style: italic;
    font-size: 12px;
}

.mqsa-checkbox-group {
    margin-top: 10px;
}

.mqsa-checkbox-group label {
    display: block;
    margin-bottom: 10px;
}

.mqsa-restriction-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333344;
}

.mqsa-restriction-row:last-child {
    border-bottom: none;
}

.mqsa-restriction-row label {
    margin-bottom: 0;
    margin-right: 20px;
}

.mqsa-unjoined-option {
    color: #aaaabc;
    font-style: italic;
}

.mqsa-loading {
    padding: 20px;
    text-align: center;
    color: #aaaabc;
    font-style: italic;
}

.mqsa-loading-mini {
    text-align: center;
    padding: 5px;
    color: #8770FF;
    font-style: italic;
    font-size: 12px;
    margin-top: 10px;
    background: rgba(135, 112, 255, 0.1);
    border-radius: 4px;
}

/* Debug Log */
.mqsa-debug-log {
    margin-top: 30px;
}

.mqsa-clear-log-form {
    margin-bottom: 20px;
}

.mqsa-clear-log-form .button {
    background: #8770FF;
    border-color: #6550dd;
    color: #ffffff;
    padding: 8px 16px;
    height: auto;
    font-size: 14px;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.mqsa-clear-log-form .button:hover {
    background: #6550dd;
    border-color: #5440cc;
}

.mqsa-log-entries {
    background: #1e1e2d;
    border: 1px solid #333344;
    border-radius: 8px;
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2);
}

.mqsa-log-entries table {
    width: 100%;
    border-collapse: collapse;
}

.mqsa-log-entries table th {
    background: #2a2a3c;
    color: #8770FF;
    padding: 10px;
    text-align: left;
    border-bottom: 2px solid #333344;
}

.mqsa-log-entries table td {
    padding: 8px 10px;
    border-bottom: 1px solid #333344;
    color: #e0e0e0;
}

.mqsa-log-entries table tr:hover td {
    background: #2a2a3c;
}

/* Notices */
.mqsa-notice {
    padding: 15px;
    margin: 15px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.mqsa-notice-success {
    background: #28a745;
    color: #ffffff;
    border-left: 4px solid #1e7e34;
}

/* Success message next to save button */
.mqsa-form-group .mqsa-notice-success {
    display: inline-block;
    margin: 0 0 0 15px;
    padding: 8px 15px;
    vertical-align: middle;
    animation: fadeInRight 0.3s ease;
}

@keyframes fadeInRight {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

.mqsa-notice-error {
    background: #dc3545;
    color: #ffffff;
    border-left: 4px solid #bd2130;
}

.mqsa-notice-warning {
    background: #ffc107;
    color: #212529;
    border-left: 4px solid #d39e00;
}

.mqsa-notice-info {
    background: #17a2b8;
    color: #ffffff;
    border-left: 4px solid #138496;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .mqsa-spaces-wrapper {
        flex-direction: column;
    }

    .mqsa-spaces-sidebar {
        width: 100%;
        margin-bottom: 20px;
    }

    .mqsa-nav-tabs {
        flex-wrap: wrap;
    }

    .mqsa-nav-tab {
        flex: 1 1 auto;
        text-align: center;
    }
}

/* WordPress Admin Menu Icon */
#adminmenu .toplevel_page_mind-qtrl-admin .wp-menu-image img {
    width: 20px;
    height: 20px;
    padding: 7px 0 0;
}

/* Override WordPress default styles */
.mqsa-admin-wrap .wp-core-ui .button-secondary {
    background: #2a2a3c;
    border-color: #333344;
    color: #ffffff;
}

.mqsa-admin-wrap .wp-core-ui .button-secondary:hover,
.mqsa-admin-wrap .wp-core-ui .button-secondary:focus {
    background: #333344;
    border-color: #444455;
    color: #ffffff;
}

/* Dark mode for WordPress color picker */
.mqsa-admin-wrap .wp-picker-container .wp-color-result.button {
    background: #1e1e2d;
    border-color: #333344;
}

.mqsa-admin-wrap .wp-picker-container .wp-color-result-text {
    background: #2a2a3c;
    color: #ffffff;
}

/* Footer */
.mqsa-admin-footer {
    margin-top: 30px;
    text-align: center;
    color: #aaaabc;
    font-style: italic;
    padding-top: 20px;
    border-top: 1px solid #333344;
}

/* Animation for tab switching */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
