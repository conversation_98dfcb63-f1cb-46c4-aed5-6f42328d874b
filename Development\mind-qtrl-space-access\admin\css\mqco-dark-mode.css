/**
 * Mind Qtrl Community Options Pro - Dark Mode CSS
 * This file implements the dark mode styling specifically for Mind Qtrl Community Options Pro plugin pages.
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.0
 */

/* Target only Mind Qtrl Community Options Pro plugin pages */
.mqco-admin-page #wpcontent,
.toplevel_page_mind-qtrl-admin #wpcontent,
.mind-qtrl_page_mind-qtrl-admin-settings #wpcontent,
.mind-qtrl_page_mind-qtrl-space-access-fluent #wpcontent {
    background: #1B1B1E;
}

/* Dark mode for WordPress admin menu */
.mqco-admin-page #adminmenu,
.mqco-admin-page #adminmenuback,
.mqco-admin-page #adminmenuwrap {
    background: #1e1e2d;
}

.mqco-admin-page #adminmenu a {
    color: #e0e0e0;
}

.mqco-admin-page #adminmenu div.wp-menu-image:before {
    color: #8770FF;
}

.mqco-admin-page #adminmenu li.menu-top:hover,
.mqco-admin-page #adminmenu li.opensub > a.menu-top,
.mqco-admin-page #adminmenu li > a.menu-top:focus {
    background-color: #2a2a3c;
    color: #8770FF;
}

.mqco-admin-page #adminmenu li.menu-top:hover div.wp-menu-image:before,
.mqco-admin-page #adminmenu li.opensub > a.menu-top div.wp-menu-image:before {
    color: #8770FF;
}

/* Dark mode for WordPress admin bar */
.mqco-admin-page #wpadminbar {
    background: #1e1e2d;
}

.mqco-admin-page #wpadminbar .ab-item, 
.mqco-admin-page #wpadminbar a.ab-item, 
.mqco-admin-page #wpadminbar > #wp-toolbar span.ab-label {
    color: #e0e0e0;
}

.mqco-admin-page #wpadminbar .ab-icon, 
.mqco-admin-page #wpadminbar .ab-icon:before, 
.mqco-admin-page #wpadminbar .ab-item:before, 
.mqco-admin-page #wpadminbar .ab-item:after {
    color: #8770FF;
}

/* Dark mode for WordPress admin notices */
.mqco-admin-page .notice {
    background: #2a2a3c;
    border-left-color: #8770FF;
    color: #e0e0e0;
}

.mqco-admin-page .notice-success {
    border-left-color: #28a745;
}

.mqco-admin-page .notice-error {
    border-left-color: #dc3545;
}

.mqco-admin-page .notice-warning {
    border-left-color: #ffc107;
}

.mqco-admin-page .notice-info {
    border-left-color: #17a2b8;
}

/* Dark mode for WordPress form elements */
.mqco-admin-page input[type="text"],
.mqco-admin-page input[type="password"],
.mqco-admin-page input[type="checkbox"],
.mqco-admin-page input[type="color"],
.mqco-admin-page input[type="date"],
.mqco-admin-page input[type="datetime"],
.mqco-admin-page input[type="datetime-local"],
.mqco-admin-page input[type="email"],
.mqco-admin-page input[type="month"],
.mqco-admin-page input[type="number"],
.mqco-admin-page input[type="search"],
.mqco-admin-page input[type="radio"],
.mqco-admin-page input[type="tel"],
.mqco-admin-page input[type="time"],
.mqco-admin-page input[type="url"],
.mqco-admin-page input[type="week"],
.mqco-admin-page select,
.mqco-admin-page textarea {
    background-color: #1e1e2d;
    border-color: #333344;
    color: #e0e0e0;
}

.mqco-admin-page input[type="text"]:focus,
.mqco-admin-page input[type="password"]:focus,
.mqco-admin-page input[type="color"]:focus,
.mqco-admin-page input[type="date"]:focus,
.mqco-admin-page input[type="datetime"]:focus,
.mqco-admin-page input[type="datetime-local"]:focus,
.mqco-admin-page input[type="email"]:focus,
.mqco-admin-page input[type="month"]:focus,
.mqco-admin-page input[type="number"]:focus,
.mqco-admin-page input[type="search"]:focus,
.mqco-admin-page input[type="tel"]:focus,
.mqco-admin-page input[type="time"]:focus,
.mqco-admin-page input[type="url"]:focus,
.mqco-admin-page input[type="week"]:focus,
.mqco-admin-page select:focus,
.mqco-admin-page textarea:focus {
    border-color: #8770FF;
    box-shadow: 0 0 0 1px #8770FF;
}

/* Dark mode for WordPress buttons */
.mqco-admin-page .wp-core-ui .button,
.mqco-admin-page .wp-core-ui .button-secondary {
    background: #2a2a3c;
    border-color: #333344;
    color: #e0e0e0;
}

.mqco-admin-page .wp-core-ui .button:hover,
.mqco-admin-page .wp-core-ui .button-secondary:hover {
    background: #333344;
    border-color: #444455;
    color: #ffffff;
}

.mqco-admin-page .wp-core-ui .button-primary {
    background: #8770FF;
    border-color: #6550dd;
    color: #ffffff;
}

.mqco-admin-page .wp-core-ui .button-primary:hover,
.mqco-admin-page .wp-core-ui .button-primary:focus {
    background: #6550dd;
    border-color: #5440cc;
    color: #ffffff;
}

/* Dark mode for WordPress tables */
.mqco-admin-page .wp-list-table {
    background-color: #2a2a3c;
    border-color: #333344;
}

.mqco-admin-page .wp-list-table th {
    background-color: #1e1e2d;
    color: #8770FF;
}

.mqco-admin-page .wp-list-table td {
    color: #e0e0e0;
}

.mqco-admin-page .wp-list-table tr:nth-child(odd) {
    background-color: #2a2a3c;
}

.mqco-admin-page .wp-list-table tr:nth-child(even) {
    background-color: #333344;
}

.mqco-admin-page .wp-list-table tr:hover td {
    background-color: #444455;
}

/* Dark mode for WordPress metaboxes */
.mqco-admin-page .postbox {
    background: #2a2a3c;
    border-color: #333344;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.mqco-admin-page .postbox .hndle,
.mqco-admin-page .postbox .handlediv {
    border-color: #333344;
    color: #8770FF;
}

.mqco-admin-page .postbox .inside {
    color: #e0e0e0;
}

/* Dark mode for WordPress tabs */
.mqco-admin-page .nav-tab-wrapper {
    border-bottom-color: #333344;
}

.mqco-admin-page .nav-tab {
    background: #1e1e2d;
    border-color: #333344;
    color: #e0e0e0;
}

.mqco-admin-page .nav-tab:hover {
    background: #2a2a3c;
    color: #8770FF;
}

.mqco-admin-page .nav-tab-active,
.mqco-admin-page .nav-tab-active:hover {
    background: #8770FF;
    border-color: #8770FF;
    color: #ffffff;
}

/* Dark mode for WordPress help tabs */
.mqco-admin-page .contextual-help-tabs .active {
    background: #2a2a3c;
    border-color: #333344;
    color: #8770FF;
}

.mqco-admin-page .contextual-help-tabs .active a {
    color: #8770FF;
}

.mqco-admin-page .contextual-help-tabs-wrap {
    background: #2a2a3c;
    border-color: #333344;
    color: #e0e0e0;
}

/* Dark mode for WordPress admin footer */
.mqco-admin-page #wpfooter {
    color: #aaaabc;
}

.mqco-admin-page #wpfooter a {
    color: #8770FF;
}

/* Remove primary color part from dark mode options */
.mqco-admin-page .fcom_color_mode_core {
    display: none !important;
}
