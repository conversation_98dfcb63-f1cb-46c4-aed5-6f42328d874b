/**
 * Admin JavaScript for Mind Qtrl | Space Access Control
 *
 * @since      1.0.0
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Initialize admin UI
        initAdminUI();
    });

    /**
     * Initialize admin UI
     */
    function initAdminUI() {
        // Initialize collapsible sections
        initCollapsibleSections();

        // Handle settings form submission
        $('.mqsa-form').on('submit', function(e) {
            // If AJAX is already handling this in the PHP template, don't do anything
            if ($(this).hasClass('mqsa-ajax-form')) {
                return;
            }

            e.preventDefault();

            const $form = $(this);
            const $submitButton = $form.find('.mqsa-button');

            $form.addClass('mqsa-loading');
            $submitButton.prop('disabled', true);

            $.ajax({
                url: mqsaAdmin.ajaxurl,
                type: 'POST',
                data: {
                    action: 'mqsa_save_settings',
                    nonce: mqsaAdmin.nonce,
                    formData: $form.serialize()
                },
                success: function(response) {
                    $form.removeClass('mqsa-loading');
                    $submitButton.prop('disabled', false);

                    if (response.success) {
                        // Show success message
                        showNotice('success', response.data.message || mqsaAdmin.strings.saveSuccess);
                    } else {
                        // Show error message
                        showNotice('error', response.data.message || mqsaAdmin.strings.saveError);
                    }
                },
                error: function() {
                    $form.removeClass('mqsa-loading');
                    $submitButton.prop('disabled', false);

                    // Show error message
                    showNotice('error', mqsaAdmin.strings.saveError);
                }
            });
        });
    }

    /**
     * Initialize collapsible sections
     */
    function initCollapsibleSections() {
        // Handle collapsible section toggling
        $('.mqsa-collapsible-header').on('click', function() {
            const targetId = $(this).data('target');
            const content = $('#' + targetId);

            // Toggle active class on header
            $(this).toggleClass('active');

            // Toggle content visibility with slide animation
            content.slideToggle(200, function() {
                $(this).toggleClass('active');
            });
        });

        // Handle checkbox dependencies
        $('#mqsa-restrict-join').on('change', function() {
            if ($(this).is(':checked')) {
                $('#join-options').closest('.mqsa-collapsible').show();
            } else {
                $('#join-options').closest('.mqsa-collapsible').hide();
            }
        });

        $('#mqsa-restrict-like').on('change', function() {
            if ($(this).is(':checked')) {
                $('#like-options').closest('.mqsa-collapsible').show();
            } else {
                $('#like-options').closest('.mqsa-collapsible').hide();
            }
        });

        // Initial state
        if (!$('#mqsa-restrict-join').is(':checked')) {
            $('#join-options').closest('.mqsa-collapsible').hide();
        }

        if (!$('#mqsa-restrict-like').is(':checked')) {
            $('#like-options').closest('.mqsa-collapsible').hide();
        }
    }

    /**
     * Show a notice
     *
     * @param {string} type The notice type (success, error)
     * @param {string} message The notice message
     */
    function showNotice(type, message) {
        const $notice = $('<div class="mqsa-notice mqsa-notice-' + type + '">')
            .text(message);

        // Find the form
        const $form = $('.mqsa-form').first();

        // Insert before the form
        $notice.insertBefore($form);

        // Remove after 3 seconds
        setTimeout(function() {
            $notice.fadeOut(500, function() {
                $(this).remove();
            });
        }, 3000);
    }

})(jQuery);
