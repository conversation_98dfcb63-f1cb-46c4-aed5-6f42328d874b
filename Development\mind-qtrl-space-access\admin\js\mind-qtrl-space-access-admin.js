/**
 * Admin JavaScript for Mind Qtrl | Space Access Control
 *
 * This file handles all the admin interface functionality:
 * - Tab navigation
 * - Space settings
 * - Debug log management
 * - AJAX calls to save settings
 * - UI interactions
 *
 * @link       https://mindqtrl.com/
 * @since      0.0.2
 */

(function($) {
    'use strict';

    // Initialize when DOM is ready
    $(document).ready(function() {
        // Initialize tabs
        initTabs();

        // Initialize space settings
        initSpaceSettings();

        // Initialize debug log features
        initDebugLog();

        // Initialize form components
        initFormComponents();
    });

    /**
     * Initialize tab navigation
     */
    function initTabs() {
        // Handle tab clicks
        $('.mqsa-tab').on('click', function() {
            const tabId = $(this).data('tab');

            // Update active tab
            $('.mqsa-tab').removeClass('active');
            $(this).addClass('active');

            // Show corresponding tab content
            $('.mqsa-tab-content').removeClass('active');
            $(`#${tabId}`).addClass('active');

            // Save active tab to localStorage
            localStorage.setItem('mqsa_active_tab', tabId);
        });

        // Restore active tab from localStorage if available
        const activeTab = localStorage.getItem('mqsa_active_tab');
        if (activeTab) {
            $(`.mqsa-tab[data-tab="${activeTab}"]`).trigger('click');
        } else {
            // Default to first tab
            $('.mqsa-tab:first').trigger('click');
        }
    }

    /**
     * Initialize space settings functionality
     */
    function initSpaceSettings() {
        // Load spaces on page load
        loadSpaces();

        // Handle space selection
        $(document).on('click', '.mqsa-space-item', function() {
            const spaceId = $(this).data('space-id');
            selectSpace(spaceId);
        });

        // Handle save space settings button
        $(document).on('click', '#mqsa-save-space-settings', function() {
            saveSpaceSettings();
        });

        // Handle access requirement type change
        $(document).on('change', '#mqsa-access-requirements', function() {
            const requirementType = $(this).val();
            toggleRequirementsFields(requirementType);
        });
    }

    /**
     * Initialize debug log functionality
     */
    function initDebugLog() {
        // Load debug log on tab change
        $('.mqsa-tab[data-tab="mqsa-tab-debug"]').on('click', function() {
            loadDebugLog();
        });

        // Handle refresh log button
        $(document).on('click', '#mqsa-refresh-log', function() {
            loadDebugLog();
        });

        // Handle clear log button
        $(document).on('click', '#mqsa-clear-log', function() {
            clearDebugLog();
        });
    }

    /**
     * Initialize form components like Select2
     */
    function initFormComponents() {
        // Initialize Select2 dropdowns with search
        if ($.fn.select2) {
            $('.mqsa-select2').select2({
                width: '100%',
                dropdownParent: $('.mqsa-admin-wrap')
            });

            // Load options for CRM tags, badges, and leaderboard levels
            loadCrmTags();
            loadBadges();
            loadLeaderboardLevels();
        }

        // Handle toggle switches
        $('.mqsa-form-checkbox input').on('change', function() {
            const target = $(this).data('target');
            if (target) {
                $(target).toggleClass('hidden', !this.checked);
            }
        });

        // Trigger change event for pre-checked checkboxes
        $('.mqsa-form-checkbox input:checked').trigger('change');
    }

    /**
     * Load spaces from server
     */
    function loadSpaces() {
        showLoading('.mqsa-spaces-list');

        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_spaces',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    renderSpacesList(response.data.spaces);
                } else {
                    showError(response.data.message || 'Error loading spaces');
                }
            },
            error: function() {
                showError('Server error while loading spaces');
            },
            complete: function() {
                hideLoading('.mqsa-spaces-list');
            }
        });
    }

    /**
     * Render spaces list in the UI
     *
     * @param {Array} spaces List of spaces to render
     */
    function renderSpacesList(spaces) {
        const $spacesList = $('.mqsa-spaces-list');

        if (!spaces || spaces.length === 0) {
            $spacesList.html('<div class="mqsa-alert mqsa-alert-info">No spaces found. Please create spaces in Fluent Community first.</div>');
            return;
        }

        let html = '';
        spaces.forEach(function(space) {
            const initial = space.name.charAt(0).toUpperCase();

            html += `
                <div class="mqsa-space-item" data-space-id="${space.id}">
                    <div class="mqsa-space-icon">${initial}</div>
                    <div class="mqsa-space-info">
                        <h4 class="mqsa-space-name">${space.name}</h4>
                        <div class="mqsa-space-meta">ID: ${space.id} | Privacy: ${space.privacy}</div>
                    </div>
                </div>
            `;
        });

        $spacesList.html(html);
    }

    /**
     * Select a space and load its settings
     *
     * @param {number} spaceId The space ID to select
     */
    function selectSpace(spaceId) {
        // Update UI to show selected space
        $('.mqsa-space-item').removeClass('active');
        $(`.mqsa-space-item[data-space-id="${spaceId}"]`).addClass('active');

        // Show space settings form
        $('#mqsa-space-settings-form').removeClass('hidden');

        // Show loading
        showLoading('#mqsa-space-settings-form');

        // Load space settings
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_space_settings',
                nonce: mqsaAdmin.nonce,
                space_id: spaceId
            },
            success: function(response) {
                if (response.success) {
                    fillSpaceSettingsForm(response.data.settings, spaceId);
                } else {
                    showError(response.data.message || 'Error loading space settings');
                }
            },
            error: function() {
                showError('Server error while loading space settings');
            },
            complete: function() {
                hideLoading('#mqsa-space-settings-form');
            }
        });
    }

    /**
     * Fill space settings form with data
     *
     * @param {Object} settings The space settings
     * @param {number} spaceId The space ID
     */
    function fillSpaceSettingsForm(settings, spaceId) {
        // Set space ID
        $('#mqsa-space-id').val(spaceId);

        // Fill form fields
        $('#mqsa-enable-restrictions').prop('checked', settings.enable_restrictions === 'yes');
        $('#mqsa-access-requirements').val(settings.access_requirements || 'none');
        $('#mqsa-restrict-view').prop('checked', settings.restrict_view === 'yes');
        $('#mqsa-restrict-join').prop('checked', settings.restrict_join === 'yes');
        $('#mqsa-restrict-post').prop('checked', settings.restrict_post === 'yes');
        $('#mqsa-restrict-comment').prop('checked', settings.restrict_comment === 'yes');
        $('#mqsa-restrict-like').prop('checked', settings.restrict_like === 'yes');

        // Set messages
        $('#mqsa-view-message').val(settings.view_message || '');
        $('#mqsa-join-message').val(settings.join_message || '');
        $('#mqsa-post-message').val(settings.post_message || '');
        $('#mqsa-comment-message').val(settings.comment_message || '');
        $('#mqsa-like-message').val(settings.like_message || '');

        // Set requirement fields
        if (settings.crm_tags && $.fn.select2) {
            $('#mqsa-crm-tags').val(settings.crm_tags.split(','));
            $('#mqsa-crm-tags').trigger('change');
        }

        if (settings.badges && $.fn.select2) {
            $('#mqsa-badges').val(settings.badges.split(','));
            $('#mqsa-badges').trigger('change');
        }

        if (settings.leaderboard_levels && $.fn.select2) {
            $('#mqsa-leaderboard-levels').val(settings.leaderboard_levels.split(','));
            $('#mqsa-leaderboard-levels').trigger('change');
        }

        // Toggle requirement fields based on selected type
        toggleRequirementsFields(settings.access_requirements || 'none');

        // Trigger change events for checkboxes to show/hide dependent fields
        $('.mqsa-form-checkbox input').trigger('change');
    }

    /**
     * Toggle visibility of requirement type specific fields
     *
     * @param {string} requirementType The selected requirement type
     */
    function toggleRequirementsFields(requirementType) {
        // Hide all requirement fields
        $('.mqsa-requirement-field').addClass('hidden');

        // Show fields based on requirement type
        switch (requirementType) {
            case 'crm_tags':
                $('.mqsa-requirement-field-crm-tags').removeClass('hidden');
                break;
            case 'badges':
                $('.mqsa-requirement-field-badges').removeClass('hidden');
                break;
            case 'leaderboard_levels':
                $('.mqsa-requirement-field-leaderboard-levels').removeClass('hidden');
                break;
            case 'all':
                $('.mqsa-requirement-field').removeClass('hidden');
                break;
        }
    }

    /**
     * Save space settings
     */
    function saveSpaceSettings() {
        const spaceId = $('#mqsa-space-id').val();

        if (!spaceId) {
            showError('No space selected');
            return;
        }

        // Show loading
        showLoading('#mqsa-space-settings-form');
        $('#mqsa-save-space-settings').prop('disabled', true);

        // Prepare form data
        const formData = {
            space_id: spaceId,
            enable_restrictions: $('#mqsa-enable-restrictions').is(':checked') ? 'yes' : 'no',
            access_requirements: $('#mqsa-access-requirements').val(),
            restrict_view: $('#mqsa-restrict-view').is(':checked') ? 'yes' : 'no',
            restrict_join: $('#mqsa-restrict-join').is(':checked') ? 'yes' : 'no',
            restrict_post: $('#mqsa-restrict-post').is(':checked') ? 'yes' : 'no',
            restrict_comment: $('#mqsa-restrict-comment').is(':checked') ? 'yes' : 'no',
            restrict_like: $('#mqsa-restrict-like').is(':checked') ? 'yes' : 'no',
            view_message: $('#mqsa-view-message').val(),
            join_message: $('#mqsa-join-message').val(),
            post_message: $('#mqsa-post-message').val(),
            comment_message: $('#mqsa-comment-message').val(),
            like_message: $('#mqsa-like-message').val(),
            crm_tags: $('#mqsa-crm-tags').val() ? $('#mqsa-crm-tags').val().join(',') : '',
            badges: $('#mqsa-badges').val() ? $('#mqsa-badges').val().join(',') : '',
            leaderboard_levels: $('#mqsa-leaderboard-levels').val() ? $('#mqsa-leaderboard-levels').val().join(',') : ''
        };

        // Send AJAX request
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_save_space_settings',
                nonce: mqsaAdmin.nonce,
                settings: formData
            },
            success: function(response) {
                if (response.success) {
                    showSuccess('Space settings saved successfully');
                } else {
                    showError(response.data.message || 'Error saving space settings');
                }
            },
            error: function() {
                showError('Server error while saving space settings');
            },
            complete: function() {
                hideLoading('#mqsa-space-settings-form');
                $('#mqsa-save-space-settings').prop('disabled', false);
            }
        });
    }

    /**
     * Load CRM tags from server
     */
    function loadCrmTags() {
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_crm_tags',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    fillSelectOptions('#mqsa-crm-tags', response.data.tags);
                }
            }
        });
    }

    /**
     * Load badges from server
     */
    function loadBadges() {
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_badges',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    fillSelectOptions('#mqsa-badges', response.data.badges);
                }
            }
        });
    }

    /**
     * Load leaderboard levels from server
     */
    function loadLeaderboardLevels() {
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_leaderboard_levels',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    fillSelectOptions('#mqsa-leaderboard-levels', response.data.levels);
                }
            }
        });
    }

    /**
     * Fill select options for dropdowns
     *
     * @param {string} selector The selector for the dropdown
     * @param {Array} options Array of options { id, name }
     */
    function fillSelectOptions(selector, options) {
        const $select = $(selector);

        if (!options || options.length === 0) {
            $select.html('<option value="">No options available</option>');
            return;
        }

        let html = '';
        options.forEach(function(option) {
            html += `<option value="${option.id}">${option.name}</option>`;
        });

        $select.html(html);
    }

    /**
     * Load debug log from server
     */
    function loadDebugLog() {
        const $logContainer = $('.mqsa-log-container');

        showLoading('.mqsa-log-container');

        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_debug_log',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    renderDebugLog(response.data.log);
                } else {
                    $logContainer.html('<div class="mqsa-alert mqsa-alert-warning">Failed to load debug log: ' + (response.data.message || 'Unknown error') + '</div>');
                }
            },
            error: function() {
                $logContainer.html('<div class="mqsa-alert mqsa-alert-danger">Server error while loading debug log</div>');
            },
            complete: function() {
                hideLoading('.mqsa-log-container');
            }
        });
    }

    /**
     * Render debug log in the UI
     *
     * @param {Array} logEntries Array of log entries
     */
    function renderDebugLog(logEntries) {
        const $logContainer = $('.mqsa-log-container');

        if (!logEntries || logEntries.length === 0) {
            $logContainer.html('<div class="mqsa-alert mqsa-alert-info">No log entries found. This is normal if debug logging was just enabled or if no plugin activity has occurred yet.</div>');
            return;
        }

        let html = '';
        logEntries.forEach(function(entry) {
            const levelClass = 'mqsa-log-level-' + (entry.level || 'info');

            html += `
                <div class="mqsa-log-entry">
                    <span class="mqsa-log-timestamp">${entry.timestamp}</span>
                    <span class="mqsa-log-level ${levelClass}">${entry.level || 'info'}</span>
                    <span class="mqsa-log-message">${entry.message}</span>
                </div>
            `;
        });

        $logContainer.html(html);

        // Scroll to bottom of log
        $logContainer.scrollTop($logContainer[0].scrollHeight);
    }

    /**
     * Clear debug log on server
     */
    function clearDebugLog() {
        if (!confirm(mqsaAdmin.strings.confirm_clear_log)) {
            return;
        }

        showLoading('.mqsa-log-container');

        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_clear_debug_log',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSuccess(mqsaAdmin.strings.log_cleared);
                    loadDebugLog();
                } else {
                    showError(mqsaAdmin.strings.log_clear_error);
                }
            },
            error: function() {
                showError(mqsaAdmin.strings.log_clear_error);
            },
            complete: function() {
                hideLoading('.mqsa-log-container');
            }
        });
    }

    /**
     * Show loading spinner in a container
     *
     * @param {string} selector The selector for the container
     */
    function showLoading(selector) {
        const $container = $(selector);

        // Only add loading overlay if not already present
        if ($container.find('.mqsa-loading-overlay').length === 0) {
            $container.append('<div class="mqsa-loading-overlay"><div class="mqsa-spinner"></div></div>');
        }
    }

    /**
     * Hide loading spinner
     *
     * @param {string} selector The selector for the container
     */
    function hideLoading(selector) {
        $(selector).find('.mqsa-loading-overlay').remove();
    }

    /**
     * Show success message toast
     *
     * @param {string} message The success message
     */
    function showSuccess(message) {
        showToast(message, 'success');
    }

    /**
     * Show error message toast
     *
     * @param {string} message The error message
     */
    function showError(message) {
        showToast(message, 'error');
    }

    /**
     * Show toast message
     *
     * @param {string} message The message to show
     * @param {string} type The type of toast (success, error)
     */
    function showToast(message, type) {
        // Remove existing toasts
        $('.mqsa-toast').remove();

        // Create toast element
        const $toast = $(`
            <div class="mqsa-toast mqsa-toast-${type}">
                ${message}
            </div>
        `);

        // Add to body
        $('body').append($toast);

        // Show toast
        setTimeout(function() {
            $toast.addClass('show');
        }, 10);

        // Hide toast after 3 seconds
        setTimeout(function() {
            $toast.removeClass('show');

            // Remove after fade out
            setTimeout(function() {
                $toast.remove();
            }, 300);
        }, 3000);
    }
})(jQuery);