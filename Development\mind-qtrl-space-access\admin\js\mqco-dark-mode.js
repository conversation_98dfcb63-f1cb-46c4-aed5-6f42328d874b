/**
 * Mind Qtrl Community Options Pro - Dark Mode JavaScript
 * This file enhances the dark mode experience for Mind Qtrl Community Options Pro plugin pages.
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.0
 */

(function($) {
    'use strict';

    // Initialize when DOM is ready
    $(document).ready(function() {
        initDarkMode();
    });

    /**
     * Initialize dark mode
     */
    function initDarkMode() {
        // Add mqco-admin-page class to body if not already added by PHP
        if (!$('body').hasClass('mqco-admin-page')) {
            $('body').addClass('mqco-admin-page');
        }

        // Hide Fluent Community color mode toggle
        $('.fcom_color_mode_core').hide();

        // Apply smooth transitions
        $('body').append('<style>.mqco-admin-page * { transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease; }</style>');

        // Add animation effects to admin elements
        animateAdminElements();

        // Enhance form elements
        enhanceFormElements();
    }

    /**
     * Add animation effects to admin elements
     */
    function animateAdminElements() {
        // Add hover effects to buttons
        $('.mqco-admin-page .button').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
            }
        );

        // Add fade-in animation to content
        $('.mqco-admin-page .mqsa-content').css({
            'opacity': '0',
            'transform': 'translateY(10px)'
        }).animate({
            'opacity': '1',
            'transform': 'translateY(0)'
        }, 300);

        // Add subtle pulse animation to logo
        $('.mqco-admin-page .mqsa-logo').css({
            'animation': 'pulse 2s infinite'
        });

        // Add pulse animation
        $('body').append(`
            <style>
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            </style>
        `);
    }

    /**
     * Enhance form elements
     */
    function enhanceFormElements() {
        // Add focus effects to form elements
        $('.mqco-admin-page input[type="text"], .mqco-admin-page input[type="password"], .mqco-admin-page textarea, .mqco-admin-page select').focus(function() {
            $(this).css({
                'border-color': '#8770FF',
                'box-shadow': '0 0 0 1px #8770FF'
            });
        }).blur(function() {
            $(this).css({
                'border-color': '#333344',
                'box-shadow': 'none'
            });
        });

        // Style checkboxes
        $('.mqco-admin-page input[type="checkbox"]').each(function() {
            const $checkbox = $(this);
            const $label = $checkbox.closest('label');
            
            // Add custom styling
            $checkbox.css({
                'position': 'relative',
                'appearance': 'none',
                '-webkit-appearance': 'none',
                'width': '18px',
                'height': '18px',
                'border': '2px solid #333344',
                'border-radius': '3px',
                'background': '#1e1e2d',
                'cursor': 'pointer',
                'margin-right': '8px',
                'vertical-align': 'middle'
            });
            
            // Add checked state
            $checkbox.on('change', function() {
                if ($(this).is(':checked')) {
                    $(this).css({
                        'background': '#8770FF',
                        'border-color': '#8770FF'
                    }).after('<span class="checkmark"></span>');
                } else {
                    $(this).css({
                        'background': '#1e1e2d',
                        'border-color': '#333344'
                    }).next('.checkmark').remove();
                }
            });
            
            // Add checkmark style
            $('body').append(`
                <style>
                    .checkmark {
                        position: absolute;
                        top: 2px;
                        left: 6px;
                        width: 6px;
                        height: 12px;
                        border: solid white;
                        border-width: 0 2px 2px 0;
                        transform: rotate(45deg);
                        pointer-events: none;
                    }
                </style>
            `);
            
            // Trigger change to apply initial state
            $checkbox.trigger('change');
        });
    }

})(jQuery);
