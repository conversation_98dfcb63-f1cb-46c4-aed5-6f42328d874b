/**
 * Space Settings JavaScript for Mind Qtrl | Space Access Control
 *
 * This file handles the space settings functionality in the admin interface.
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.0
 */

(function($) {
    'use strict';

    // Initialize when DOM is ready
    $(document).ready(function() {
        initSpaceSettings();
    });

    /**
     * Initialize space settings
     */
    function initSpaceSettings() {
        // Initialize all space item indicators
        initAllSpaceItemIndicators();

        // Handle space item click
        $('.mqsa-space-item').on('click', function(e) {
            e.preventDefault();

            // Get space ID
            const spaceId = $(this).data('space-id');

            // Set active class
            $('.mqsa-space-item').removeClass('active');
            $(this).addClass('active');

            // Load space settings
            loadSpaceSettings(spaceId);
        });
    }

    /**
     * Initialize all space item indicators by loading their restriction status
     */
    function initAllSpaceItemIndicators() {
        // Get all space items
        const $spaceItems = $('.mqsa-space-item');

        // If no space items, return
        if ($spaceItems.length === 0) return;

        // Show loading indicator
        $('.mqsa-spaces-sidebar').append('<div class="mqsa-loading-mini"><p>Loading...</p></div>');

        // Get all space IDs
        const spaceIds = [];
        $spaceItems.each(function() {
            spaceIds.push($(this).data('space-id'));
        });

        // Get all space settings via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_all_space_settings',
                space_ids: spaceIds,
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                // Remove loading indicator
                $('.mqsa-loading-mini').remove();

                if (response.success) {
                    // Update all space item indicators
                    const allSettings = response.data;

                    // Loop through all space items
                    $spaceItems.each(function() {
                        const spaceId = $(this).data('space-id');
                        const settings = allSettings[spaceId] || {};

                        // Remove old classes first
                        $(this).removeClass('has-restrictions restrictions-active restrictions-inactive');

                        // Ensure proper padding for the indicator
                        if ($(this).css('padding-left') === '15px' || $(this).css('padding-left') === '10px') {
                            $(this).css('padding-left', '30px');
                        }

                        // Check if restrictions are ACTIVE (not just if they exist)
                        const restrictionsActive = settings.enable_restrictions === 'yes' &&
                            (settings.restrict_view === 'yes' ||
                             settings.restrict_join === 'yes' ||
                             settings.restrict_post === 'yes' ||
                             settings.restrict_comment === 'yes' ||
                             settings.restrict_like === 'yes' ||
                             settings.restrict_view_unjoined === 'yes' ||
                             settings.restrict_join_unjoined === 'yes' ||
                             settings.restrict_post_unjoined === 'yes' ||
                             settings.restrict_comment_unjoined === 'yes' ||
                             settings.restrict_like_unjoined === 'yes');

                        // Store restriction status in data attribute for later use
                        $(this).attr('data-restrictions-enabled', restrictionsActive ? 'true' : 'false');

                        // For backward compatibility
                        $(this).attr('data-has-restrictions', restrictionsActive ? 'true' : 'false');

                        // Add the appropriate class based on restrictions status
                        // By default, all items will have a red indicator (no class needed)
                        // Only add the green indicator class if restrictions are ACTIVE
                        if (restrictionsActive) {
                            $(this).addClass('restrictions-active');
                            console.log('MQSA: Space', spaceId, 'has ACTIVE restrictions (init)');
                        } else {
                            console.log('MQSA: Space', spaceId, 'has INACTIVE restrictions (init)');
                        }
                    });
                }
            },
            error: function() {
                // Remove loading indicator
                $('.mqsa-loading-mini').remove();
            }
        });
    }

    /**
     * Load space settings
     *
     * @param {number} spaceId The space ID
     */
    function loadSpaceSettings(spaceId) {
        // Show loading indicator
        $('.mqsa-space-settings').html('<div class="mqsa-loading"><p>Loading space settings...</p></div>');

        // Get space settings via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_space_settings',
                space_id: spaceId,
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Render space settings form
                    renderSpaceSettingsForm(spaceId, response.data);

                    // Update space item indicator if restrictions are enabled
                    updateSpaceItemIndicator(spaceId, response.data);
                } else {
                    // Show error message
                    $('.mqsa-space-settings').html('<div class="mqsa-notice mqsa-notice-error"><p>' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                // Show error message
                $('.mqsa-space-settings').html('<div class="mqsa-notice mqsa-notice-error"><p>An error occurred while loading space settings.</p></div>');
            }
        });
    }

    /**
     * Update space item indicator based on restriction settings
     *
     * @param {number} spaceId The space ID
     * @param {object} settings The space settings
     */
    function updateSpaceItemIndicator(spaceId, settings) {
        // Get the space item element
        const $spaceItem = $('.mqsa-space-item[data-space-id="' + spaceId + '"]');

        if (!$spaceItem.length) {
            console.log('MQSA: Space item not found for ID', spaceId);
            return;
        }

        // Remove old classes first
        $spaceItem.removeClass('has-restrictions restrictions-active restrictions-inactive');

        // Ensure proper padding for the indicator
        if ($spaceItem.css('padding-left') === '15px' || $spaceItem.css('padding-left') === '10px') {
            $spaceItem.css('padding-left', '30px');
        }

        // Check if restrictions are ACTIVE (not just if they exist)
        const restrictionsActive = settings.enable_restrictions === 'yes' &&
            (settings.restrict_view === 'yes' ||
             settings.restrict_join === 'yes' ||
             settings.restrict_post === 'yes' ||
             settings.restrict_comment === 'yes' ||
             settings.restrict_like === 'yes' ||
             settings.restrict_view_unjoined === 'yes' ||
             settings.restrict_join_unjoined === 'yes' ||
             settings.restrict_post_unjoined === 'yes' ||
             settings.restrict_comment_unjoined === 'yes' ||
             settings.restrict_like_unjoined === 'yes');

        // Store restriction status in data attribute for later use
        $spaceItem.attr('data-restrictions-enabled', restrictionsActive ? 'true' : 'false');

        // For backward compatibility
        $spaceItem.attr('data-has-restrictions', restrictionsActive ? 'true' : 'false');

        // Add the appropriate class based on restrictions status
        // By default, all items will have a red indicator (no class needed)
        // Only add the green indicator class if restrictions are ACTIVE
        if (restrictionsActive) {
            $spaceItem.addClass('restrictions-active');
            console.log('MQSA: Space', spaceId, 'has ACTIVE restrictions');
        } else {
            console.log('MQSA: Space', spaceId, 'has INACTIVE restrictions');
        }
    }

    /**
     * Render space settings form
     *
     * @param {number} spaceId The space ID
     * @param {object} settings The space settings
     */
    function renderSpaceSettingsForm(spaceId, settings) {
        // Create form HTML
        let html = '<div class="mqsa-space-settings-form">';

        // Add space title
        html += '<h3>' + settings.space_title + ' Settings</h3>';

        // Add form
        html += '<form method="post" class="mqsa-form mqsa-space-form">';
        html += '<input type="hidden" name="space_id" value="' + spaceId + '">';

        // Enable restrictions
        html += '<div class="mqsa-form-group">';
        html += '<label><input type="checkbox" name="enable_restrictions" value="1" ' + (settings.enable_restrictions === 'yes' ? 'checked' : '') + '> Enable access restrictions for this space</label>';
        html += '<p class="description">When enabled, access to this space will be restricted based on the settings below.</p>';
        html += '</div>';

        // Access requirements
        html += '<div class="mqsa-form-group">';
        html += '<label for="access_requirements">Access Requirements</label>';
        html += '<select name="access_requirements" id="access_requirements">';
        html += '<option value="none" ' + (settings.access_requirements === 'none' ? 'selected' : '') + '>No requirements</option>';
        html += '<option value="crm_tags" ' + (settings.access_requirements === 'crm_tags' ? 'selected' : '') + '>Fluent CRM Tags</option>';
        html += '<option value="badges" ' + (settings.access_requirements === 'badges' ? 'selected' : '') + '>User Badges</option>';
        html += '<option value="leaderboard" ' + (settings.access_requirements === 'leaderboard' ? 'selected' : '') + '>Leaderboard Level</option>';
        html += '</select>';
        html += '<p class="description">Select the type of requirements users must meet to access this space.</p>';
        html += '</div>';

        // CRM Tags (conditionally shown)
        html += '<div class="mqsa-form-group mqsa-crm-tags-group" ' + (settings.access_requirements !== 'crm_tags' ? 'style="display: none;"' : '') + '>';
        html += '<label for="crm_tags">CRM Tags</label>';
        html += '<select name="crm_tags[]" id="crm_tags" multiple>';

        // Add CRM tags options
        if (settings.crm_tags_options) {
            settings.crm_tags_options.forEach(function(tag) {
                const selected = settings.crm_tags && settings.crm_tags.includes(tag.id.toString()) ? 'selected' : '';
                html += '<option value="' + tag.id + '" ' + selected + '>' + tag.title + '</option>';
            });
        }

        html += '</select>';
        html += '<p class="description">Select the CRM tags that users must have to access this space.</p>';
        html += '</div>';

        // Badges (conditionally shown)
        html += '<div class="mqsa-form-group mqsa-badges-group" ' + (settings.access_requirements !== 'badges' ? 'style="display: none;"' : '') + '>';
        html += '<label for="badges">Badges</label>';
        html += '<select name="badges[]" id="badges" multiple>';

        // Add badges options
        if (settings.badges_options) {
            settings.badges_options.forEach(function(badge) {
                const selected = settings.badges && settings.badges.includes(badge.id.toString()) ? 'selected' : '';
                html += '<option value="' + badge.id + '" ' + selected + '>' + badge.title + '</option>';
            });
        }

        html += '</select>';
        html += '<p class="description">Select the badges that users must have to access this space.</p>';
        html += '</div>';

        // Leaderboard (conditionally shown)
        html += '<div class="mqsa-form-group mqsa-leaderboard-group" ' + (settings.access_requirements !== 'leaderboard' ? 'style="display: none;"' : '') + '>';
        html += '<label for="leaderboard_levels">Leaderboard Levels</label>';
        html += '<select name="leaderboard_levels[]" id="leaderboard_levels" multiple>';

        // Add leaderboard options
        if (settings.leaderboard_options) {
            settings.leaderboard_options.forEach(function(level) {
                const selected = settings.leaderboard_levels && settings.leaderboard_levels.includes(level.id.toString()) ? 'selected' : '';
                html += '<option value="' + level.id + '" ' + selected + '>' + level.title + '</option>';
            });
        }

        html += '</select>';
        html += '<p class="description">Select the leaderboard levels that users must have to access this space.</p>';
        html += '</div>';

        // Restriction types
        html += '<div class="mqsa-form-group">';
        html += '<label>Restriction Types</label>';
        html += '<div class="mqsa-checkbox-group">';

        // Viewing restrictions
        html += '<div class="mqsa-restriction-row">';
        html += '<label><input type="checkbox" name="restrict_view" value="1" ' + (settings.restrict_view === 'yes' ? 'checked' : '') + '> Restrict Viewing</label>';
        html += '<label class="mqsa-unjoined-option"><input type="checkbox" name="restrict_view_unjoined" value="1" ' + (settings.restrict_view_unjoined === 'yes' ? 'checked' : '') + '> ONLY Apply to Unjoined Members</label>';
        html += '</div>';

        // Joining restrictions
        html += '<div class="mqsa-restriction-row">';
        html += '<label><input type="checkbox" name="restrict_join" value="1" ' + (settings.restrict_join === 'yes' ? 'checked' : '') + '> Restrict Joining</label>';
        html += '<label class="mqsa-unjoined-option"><input type="checkbox" name="restrict_join_unjoined" value="1" ' + (settings.restrict_join_unjoined === 'yes' ? 'checked' : '') + '> ONLY Apply to Unjoined Members</label>';
        html += '</div>';

        // Posting restrictions
        html += '<div class="mqsa-restriction-row">';
        html += '<label><input type="checkbox" name="restrict_post" value="1" ' + (settings.restrict_post === 'yes' ? 'checked' : '') + '> Restrict Posting</label>';
        html += '<label class="mqsa-unjoined-option"><input type="checkbox" name="restrict_post_unjoined" value="1" ' + (settings.restrict_post_unjoined === 'yes' ? 'checked' : '') + '> ONLY Apply to Unjoined Members</label>';
        html += '</div>';

        // Commenting restrictions
        html += '<div class="mqsa-restriction-row">';
        html += '<label><input type="checkbox" name="restrict_comment" value="1" ' + (settings.restrict_comment === 'yes' ? 'checked' : '') + '> Restrict Commenting</label>';
        html += '<label class="mqsa-unjoined-option"><input type="checkbox" name="restrict_comment_unjoined" value="1" ' + (settings.restrict_comment_unjoined === 'yes' ? 'checked' : '') + '> ONLY Apply to Unjoined Members</label>';
        html += '</div>';

        // Liking restrictions
        html += '<div class="mqsa-restriction-row">';
        html += '<label><input type="checkbox" name="restrict_like" value="1" ' + (settings.restrict_like === 'yes' ? 'checked' : '') + '> Restrict Likes</label>';
        html += '<label class="mqsa-unjoined-option"><input type="checkbox" name="restrict_like_unjoined" value="1" ' + (settings.restrict_like_unjoined === 'yes' ? 'checked' : '') + '> ONLY Apply to Unjoined Members</label>';
        html += '</div>';

        // Hide like buttons option
        html += '<div class="mqsa-restriction-row mqsa-sub-option' + (settings.restrict_like !== 'yes' ? ' hidden' : '') + '">';
        html += '<label><input type="checkbox" name="hide_like_buttons" value="1" ' + (settings.hide_like_buttons === 'yes' ? 'checked' : '') + '> Hide Like Buttons</label>';
        html += '<p class="description">When enabled, like/reaction buttons will be hidden for users who don\'t meet the requirements.</p>';
        html += '</div>';

        html += '</div>';
        html += '<p class="description">Select what actions should be restricted for users who don\'t meet the requirements. The "ONLY Apply to Unjoined Members" option will restrict the action ONLY for users who haven\'t joined the space, and joined members will not be affected regardless of requirements.</p>';
        html += '</div>';

        // Custom messages
        html += '<div class="mqsa-form-group">';
        html += '<label for="view_message">View Restriction Message</label>';
        html += '<textarea name="view_message" id="view_message" rows="3">' + (settings.view_message || '') + '</textarea>';
        html += '<p class="description">Message shown to users who don\'t have permission to view this space.</p>';
        html += '</div>';

        html += '<div class="mqsa-form-group">';
        html += '<label for="join_message">Join Restriction Message</label>';
        html += '<textarea name="join_message" id="join_message" rows="3">' + (settings.join_message || '') + '</textarea>';
        html += '<p class="description">Message shown to users who don\'t have permission to join this space.</p>';
        html += '</div>';

        html += '<div class="mqsa-form-group">';
        html += '<label for="post_message">Post Restriction Message</label>';
        html += '<textarea name="post_message" id="post_message" rows="3">' + (settings.post_message || '') + '</textarea>';
        html += '<p class="description">Message shown to users who don\'t have permission to post in this space.</p>';
        html += '</div>';

        html += '<div class="mqsa-form-group">';
        html += '<label for="comment_message">Comment Restriction Message</label>';
        html += '<textarea name="comment_message" id="comment_message" rows="3">' + (settings.comment_message || '') + '</textarea>';
        html += '<p class="description">Message shown to users who don\'t have permission to comment in this space.</p>';
        html += '</div>';

        html += '<div class="mqsa-form-group">';
        html += '<label for="like_message">Like Restriction Message</label>';
        html += '<textarea name="like_message" id="like_message" rows="3">' + (settings.like_message || '') + '</textarea>';
        html += '<p class="description">Message shown to users who don\'t have permission to like activities in this space.</p>';
        html += '</div>';

        // Submit button with success notice container
        html += '<div class="mqsa-form-group mqsa-submit-container">';
        html += '<div class="mqsa-submit-row">';
        html += '<button type="submit" class="button button-primary mqsa-button">Save Space Settings</button>';
        html += '<div class="mqsa-success-notice-container"></div>';
        html += '</div>';
        html += '</div>';

        html += '</form>';
        html += '</div>';

        // Add HTML to container
        $('.mqsa-space-settings').html(html);

        // Initialize form events
        initSpaceSettingsForm();
    }

    /**
     * Initialize space settings form
     */
    function initSpaceSettingsForm() {
        // Handle access requirements change
        $('#access_requirements').on('change', function() {
            const value = $(this).val();

            // Hide all requirement groups
            $('.mqsa-crm-tags-group, .mqsa-badges-group, .mqsa-leaderboard-group').hide();

            // Show selected requirement group
            if (value === 'crm_tags') {
                $('.mqsa-crm-tags-group').show();
            } else if (value === 'badges') {
                $('.mqsa-badges-group').show();
            } else if (value === 'leaderboard') {
                $('.mqsa-leaderboard-group').show();
            }
        });

        // Handle restrict like checkbox change
        $('input[name="restrict_like"]').on('change', function() {
            const isChecked = $(this).is(':checked');
            $('.mqsa-sub-option').toggleClass('hidden', !isChecked);
        });

        // Handle form submission
        $('.mqsa-space-form').on('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = $(this).serialize();

            // Show loading indicator
            $(this).find('.mqsa-button').prop('disabled', true).text('Saving...');

            // Save space settings via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'mqsa_save_space_settings',
                    form_data: formData,
                    nonce: mqsaAdmin.nonce
                },
                success: function(response) {
                    // Re-enable button
                    $('.mqsa-space-form .mqsa-button').prop('disabled', false).text('Save Space Settings');

                    if (response.success) {
                        // Get the space ID from the form
                        const spaceId = $('#space_id').val();

                        // Update space item indicator based on form data
                        const formSettings = {
                            enable_restrictions: $('input[name="enable_restrictions"]').is(':checked') ? 'yes' : 'no',
                            restrict_view: $('input[name="restrict_view"]').is(':checked') ? 'yes' : 'no',
                            restrict_join: $('input[name="restrict_join"]').is(':checked') ? 'yes' : 'no',
                            restrict_post: $('input[name="restrict_post"]').is(':checked') ? 'yes' : 'no',
                            restrict_comment: $('input[name="restrict_comment"]').is(':checked') ? 'yes' : 'no',
                            restrict_like: $('input[name="restrict_like"]').is(':checked') ? 'yes' : 'no',
                            hide_like_buttons: $('input[name="hide_like_buttons"]').is(':checked') ? 'yes' : 'no',
                            restrict_view_unjoined: $('input[name="restrict_view_unjoined"]').is(':checked') ? 'yes' : 'no',
                            restrict_join_unjoined: $('input[name="restrict_join_unjoined"]').is(':checked') ? 'yes' : 'no',
                            restrict_post_unjoined: $('input[name="restrict_post_unjoined"]').is(':checked') ? 'yes' : 'no',
                            restrict_comment_unjoined: $('input[name="restrict_comment_unjoined"]').is(':checked') ? 'yes' : 'no',
                            restrict_like_unjoined: $('input[name="restrict_like_unjoined"]').is(':checked') ? 'yes' : 'no'
                        };
                        updateSpaceItemIndicator(spaceId, formSettings);

                        // Create success message element
                        const successMessage = $('<div class="mqsa-notice mqsa-notice-success"><p>' + response.data.message + '</p></div>');

                        // Add success message to the container next to the save button
                        $('.mqsa-success-notice-container').html(successMessage);

                        // Remove message after 5 seconds
                        setTimeout(function() {
                            successMessage.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 5000);
                    } else {
                        // Show error message
                        $('.mqsa-space-settings-form').prepend('<div class="mqsa-notice mqsa-notice-error"><p>' + response.data.message + '</p></div>');
                    }
                },
                error: function() {
                    // Re-enable button
                    $('.mqsa-space-form .mqsa-button').prop('disabled', false).text('Save Space Settings');

                    // Show error message
                    $('.mqsa-space-settings-form').prepend('<div class="mqsa-notice mqsa-notice-error"><p>An error occurred while saving space settings.</p></div>');
                }
            });
        });
    }

})(jQuery);
