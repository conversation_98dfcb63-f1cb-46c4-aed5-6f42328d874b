/**
 * Status Indicator Fix for Mind Qtrl | Space Access Control
 *
 * This file fixes the issue with space restriction status indicators (red/green lights)
 * not appearing on the active space item.
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.0
 */

(function($) {
    'use strict';

    // Initialize when DOM is ready
    $(document).ready(function() {
        // Wait a short time to ensure other scripts have run
        setTimeout(fixStatusIndicators, 500);

        // Also run when clicking on space items
        $(document).on('click', '.mqsa-space-item', function() {
            // Wait for the active class to be applied
            setTimeout(fixStatusIndicators, 100);
        });

        // Add custom CSS to ensure indicators are visible
        addCustomCSS();
    });

    /**
     * Fix status indicators for space items
     */
    function fixStatusIndicators() {
        console.log('MQSA: Fixing status indicators');

        // Get all space items
        const $spaceItems = $('.mqsa-space-item');

        if ($spaceItems.length === 0) {
            console.log('MQSA: No space items found');
            return;
        }

        console.log('MQSA: Found ' + $spaceItems.length + ' space items');

        // Loop through each space item
        $spaceItems.each(function() {
            const $item = $(this);
            const spaceId = $item.data('space-id');

            // Check if space restrictions are ACTIVE (not just if they exist)
            // Check for data-restrictions-enabled first, fall back to data-has-restrictions
            let restrictionsEnabled = false;

            // First check if restrictions are enabled in the space settings
            if ($item.attr('data-restrictions-enabled') === 'true') {
                restrictionsEnabled = true;
            } else if ($item.attr('data-has-restrictions') === 'true') {
                // For backward compatibility
                restrictionsEnabled = true;
            }

            console.log('MQSA: Processing space item', spaceId, 'restrictions ACTIVE:', restrictionsEnabled);

            // Store current active state (selected tab)
            const wasActive = $item.hasClass('active');

            // Remove only the restriction indicator classes, preserve the active class
            $item.removeClass('has-restrictions restrictions-active restrictions-inactive');

            // Ensure the item has proper padding for the indicator
            if ($item.css('padding-left') === '15px' || $item.css('padding-left') === '10px') {
                $item.css('padding-left', '30px');
                console.log('MQSA: Fixed padding for space item', spaceId);
            }

            // Add the appropriate class based on restrictions status
            // By default, all items will have a red indicator (no class needed)
            // Only add the green indicator class if restrictions are ACTIVE
            if (restrictionsEnabled) {
                $item.addClass('restrictions-active');
                console.log('MQSA: Added restrictions-active class to space item', spaceId);
            }

            // Restore active state if it was active before
            if (wasActive && !$item.hasClass('active')) {
                $item.addClass('active');
                console.log('MQSA: Restored active class to space item', spaceId);
            }

            // Force the indicator to be visible
            // This is a workaround for themes that might hide pseudo-elements
            $item.attr('data-mqsa-indicator', 'visible');
        });

        // Add an additional style to force indicators to be visible
        if (!$('#mqsa-force-indicators').length) {
            const forceStyle = `
                .mqsa-space-item[data-mqsa-indicator="visible"]::before {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
            `;
            $('<style id="mqsa-force-indicators">').text(forceStyle).appendTo('head');
            console.log('MQSA: Added force indicator styles');
        }
    }

    /**
     * Add custom CSS to ensure indicators are visible
     */
    function addCustomCSS() {
        const css = `
            /* Space item indicator styles */
            .mqsa-space-item {
                position: relative !important;
                padding-left: 30px !important; /* Make room for the indicator */
                overflow: visible !important;
            }

            /* Indicator for all space items - red by default */
            .mqsa-space-item::before {
                content: '' !important;
                display: block !important;
                position: absolute !important;
                left: 10px !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
                width: 14px !important; /* Slightly larger */
                height: 14px !important; /* Slightly larger */
                border-radius: 50% !important;
                background-color: #dc3545 !important; /* Red color by default */
                box-shadow: 0 0 5px rgba(220, 53, 69, 0.8) !important; /* More visible shadow */
                animation: mqsa-pulse-red 2s infinite !important;
                z-index: 9999 !important; /* Higher z-index to ensure visibility */
                pointer-events: none !important;
                visibility: visible !important; /* Always show the indicator */
                opacity: 1 !important; /* Full opacity */
                display: block !important; /* Force display */
                border: 1px solid rgba(255, 255, 255, 0.5) !important; /* White border for contrast */
            }

            /* Green indicator only for spaces with active restrictions */
            .mqsa-space-item.restrictions-active::before {
                background-color: #28a745 !important; /* Green color */
                opacity: 1 !important;
                box-shadow: 0 0 5px rgba(40, 167, 69, 0.8) !important; /* More visible shadow */
                animation: mqsa-pulse-green 2s infinite !important;
                border: 1px solid rgba(255, 255, 255, 0.5) !important; /* White border for contrast */
            }

            /* Ensure indicators are visible even on hover */
            .mqsa-space-item:hover::before {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                z-index: 10000 !important; /* Even higher z-index on hover */
                width: 16px !important; /* Slightly larger on hover */
                height: 16px !important; /* Slightly larger on hover */
                box-shadow: 0 0 8px rgba(255, 255, 255, 0.8) !important; /* Brighter glow on hover */
            }

            /* Active space item styling */
            .mqsa-space-item.active {
                background-color: #333 !important;
                color: #fff !important;
                font-weight: bold !important;
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
            }

            /* Ensure the indicator is visible even when the space item has other pseudo-elements */
            .mqsa-space-item::after {
                z-index: 99 !important;
            }

            /* Green pulse animation */
            @keyframes mqsa-pulse-green {
                0% {
                    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
                }
                70% {
                    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
                }
            }

            /* Red pulse animation */
            @keyframes mqsa-pulse-red {
                0% {
                    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);
                }
                70% {
                    box-shadow: 0 0 0 7px rgba(220, 53, 69, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
                }
            }
        `;

        // Add the CSS to the head
        $('<style id="mqsa-status-indicator-styles">').text(css).appendTo('head');
        console.log('MQSA: Added custom CSS for status indicators');
    }

})(jQuery);
