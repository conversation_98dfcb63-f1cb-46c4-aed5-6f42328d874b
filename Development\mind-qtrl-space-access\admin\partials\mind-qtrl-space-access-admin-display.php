<?php
/**
 * Admin page template
 *
 * @since      0.0.1
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}
?>

<div class="wrap mqsa-admin-wrap">
    <div class="mqsa-header">
        <img src="<?php echo plugin_dir_url(dirname(__FILE__)) . 'images/mind-qtrl-logo.png'; ?>" alt="Mind Qtrl Logo" class="mqsa-logo">
        <h1 class="mqsa-title"><?php echo esc_html(get_admin_page_title()); ?></h1>
    </div>

    <div class="mqsa-nav-tabs">
        <a href="?page=mind-qtrl-space-access" class="mqsa-nav-tab <?php echo empty($_GET['tab']) ? 'active' : ''; ?>">
            <?php _e('Settings', 'mind-qtrl-space-access'); ?>
        </a>
        <a href="?page=mind-qtrl-space-access&tab=rules" class="mqsa-nav-tab <?php echo isset($_GET['tab']) && $_GET['tab'] === 'rules' ? 'active' : ''; ?>">
            <?php _e('Space Rules', 'mind-qtrl-space-access'); ?>
        </a>
        <a href="?page=mind-qtrl-space-access&tab=debug" class="mqsa-nav-tab <?php echo isset($_GET['tab']) && $_GET['tab'] === 'debug' ? 'active' : ''; ?>">
            <?php _e('Debug Log', 'mind-qtrl-space-access'); ?>
        </a>
    </div>

    <div class="mqsa-content">
        <form method="post" action="options.php" class="mqsa-form">
            <?php
            settings_fields('mqsa_settings');
            do_settings_sections('mind-qtrl-space-access');
            submit_button(__('Save Settings', 'mind-qtrl-space-access'), 'mqsa-button');
            ?>
        </form>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Save settings via AJAX
    $('.mqsa-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitButton = $form.find('.mqsa-button');
        
        $form.addClass('mqsa-loading');
        $submitButton.prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_save_settings',
                nonce: '<?php echo wp_create_nonce('mqsa_admin_nonce'); ?>',
                settings: $form.serialize()
            },
            success: function(response) {
                if (response.success) {
                    showNotice('success', response.data.message);
                } else {
                    showNotice('error', response.data.message);
                }
            },
            error: function() {
                showNotice('error', '<?php _e('An error occurred while saving settings.', 'mind-qtrl-space-access'); ?>');
            },
            complete: function() {
                $form.removeClass('mqsa-loading');
                $submitButton.prop('disabled', false);
            }
        });
    });
    
    function showNotice(type, message) {
        const $notice = $('<div>')
            .addClass(`mqsa-notice ${type}`)
            .text(message)
            .hide();
            
        $('.mqsa-content').prepend($notice);
        $notice.slideDown().delay(3000).slideUp(function() {
            $(this).remove();
        });
    }
});
</div>
