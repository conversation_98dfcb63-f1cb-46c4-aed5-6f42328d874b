<?php
/**
 * Debug log display template for Mind Qtrl | Space Access Control
 *
 * @link       https://mindqtrl.com/
 * @since      0.0.2
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

// Get plugin options
$options = get_option('mqsa_settings', array());
?>

<div class="wrap mqsa-admin-wrap">
    <div class="mqsa-admin-header">
        <div class="mqsa-admin-logo">
            <h1 class="mqsa-admin-title"><?php echo esc_html( get_admin_page_title() ); ?> <span class="mqsa-admin-version">v<?php echo MQSA_VERSION; ?></span></h1>
        </div>
    </div>
    
    <div class="mqsa-admin-content">
        <div class="mqsa-card">
            <div class="mqsa-card-header">
                <h2><?php _e('Debug Log', 'mind-qtrl-space-access'); ?></h2>
            </div>
            <div class="mqsa-card-body">
                <div class="mqsa-debug-controls">
                    <button id="mqsa-refresh-log" class="mqsa-btn mqsa-btn-secondary"><?php _e('Refresh Log', 'mind-qtrl-space-access'); ?></button>
                    <button id="mqsa-clear-log" class="mqsa-btn mqsa-btn-danger"><?php _e('Clear Log', 'mind-qtrl-space-access'); ?></button>
                    
                    <div class="mqsa-form-group mqsa-debug-toggle">
                        <label class="mqsa-form-label">
                            <?php _e('Enable Debug Logging', 'mind-qtrl-space-access'); ?>
                            <div class="mqsa-form-checkbox">
                                <input type="checkbox" id="mqsa-enable-debug" value="yes" <?php checked( isset($options['enable_debug_logging']) ? $options['enable_debug_logging'] : 'yes', 'yes' ); ?>>
                                <span class="slider"></span>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="mqsa-debug-log-container">
                    <pre id="mqsa-debug-log" class="mqsa-debug-log"><?php _e('Loading debug log...', 'mind-qtrl-space-access'); ?></pre>
                </div>
                
                <div class="mqsa-debug-info">
                    <h3><?php _e('Debug Information', 'mind-qtrl-space-access'); ?></h3>
                    <table class="mqsa-debug-table">
                        <tr>
                            <th><?php _e('Plugin Version', 'mind-qtrl-space-access'); ?></th>
                            <td><?php echo MQSA_VERSION; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('WordPress Version', 'mind-qtrl-space-access'); ?></th>
                            <td><?php echo get_bloginfo('version'); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('PHP Version', 'mind-qtrl-space-access'); ?></th>
                            <td><?php echo phpversion(); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Fluent Community', 'mind-qtrl-space-access'); ?></th>
                            <td><?php echo class_exists('\FluentCommunity\App\App') ? __('Active', 'mind-qtrl-space-access') : __('Inactive', 'mind-qtrl-space-access'); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Fluent Community Pro', 'mind-qtrl-space-access'); ?></th>
                            <td><?php echo class_exists('\FluentCommunityPro\App\App') ? __('Active', 'mind-qtrl-space-access') : __('Inactive', 'mind-qtrl-space-access'); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('FluentCRM', 'mind-qtrl-space-access'); ?></th>
                            <td><?php echo defined('FLUENTCRM') ? __('Active', 'mind-qtrl-space-access') : __('Inactive', 'mind-qtrl-space-access'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Load debug log on page load
    loadDebugLog();
    
    // Handle refresh log button
    $('#mqsa-refresh-log').on('click', function() {
        loadDebugLog();
    });
    
    // Handle clear log button
    $('#mqsa-clear-log').on('click', function() {
        if (confirm(mqsaAdmin.strings.confirm_clear_log)) {
            clearDebugLog();
        }
    });
    
    // Handle enable debug toggle
    $('#mqsa-enable-debug').on('change', function() {
        const enabled = $(this).is(':checked') ? 'yes' : 'no';
        
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_toggle_debug',
                nonce: mqsaAdmin.nonce,
                enabled: enabled
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(response.data.message);
                } else {
                    // Show error message
                    alert(response.data.message || mqsaAdmin.strings.save_error);
                }
            },
            error: function() {
                alert(mqsaAdmin.strings.save_error);
            }
        });
    });
    
    /**
     * Load debug log
     */
    function loadDebugLog() {
        $('#mqsa-debug-log').html(mqsaAdmin.strings.loading);
        
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_debug_log',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.log) {
                        $('#mqsa-debug-log').html(response.data.log);
                    } else {
                        $('#mqsa-debug-log').html('<?php _e("Debug log is empty.", "mind-qtrl-space-access"); ?>');
                    }
                } else {
                    $('#mqsa-debug-log').html(response.data.message || '<?php _e("Error loading debug log.", "mind-qtrl-space-access"); ?>');
                }
            },
            error: function() {
                $('#mqsa-debug-log').html('<?php _e("Error loading debug log.", "mind-qtrl-space-access"); ?>');
            }
        });
    }
    
    /**
     * Clear debug log
     */
    function clearDebugLog() {
        $.ajax({
            url: mqsaAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_clear_debug_log',
                nonce: mqsaAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Show success message and reload log
                    alert(mqsaAdmin.strings.log_cleared);
                    loadDebugLog();
                } else {
                    // Show error message
                    alert(response.data.message || mqsaAdmin.strings.log_clear_error);
                }
            },
            error: function() {
                alert(mqsaAdmin.strings.log_clear_error);
            }
        });
    }
});
</script>