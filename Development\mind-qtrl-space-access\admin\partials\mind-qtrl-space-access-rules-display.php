<?php
/**
 * Space rules page template
 *
 * @since      0.0.1
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get all spaces
$spaces = array();
if (class_exists('\FluentCommunity\App\Models\Space')) {
    $spaces = \FluentCommunity\App\Models\Space::orderBy('title', 'ASC')->get();
}
?>

<div class="wrap mqsa-admin-wrap">
    <div class="mqsa-header">
        <img src="<?php echo plugin_dir_url(dirname(__FILE__)) . 'images/mind-qtrl-logo.png'; ?>" alt="Mind Qtrl Logo" class="mqsa-logo">
        <h1 class="mqsa-title"><?php _e('Space Access Rules', 'mind-qtrl-space-access'); ?></h1>
    </div>

    <div class="mqsa-content">
        <?php if (empty($spaces)): ?>
            <div class="mqsa-notice error">
                <?php _e('No spaces found. Please create spaces in Fluent Community first.', 'mind-qtrl-space-access'); ?>
            </div>
        <?php else: ?>
            <table class="mqsa-table">
                <thead>
                    <tr>
                        <th><?php _e('Space', 'mind-qtrl-space-access'); ?></th>
                        <th><?php _e('Access Control', 'mind-qtrl-space-access'); ?></th>
                        <th><?php _e('Actions', 'mind-qtrl-space-access'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($spaces as $space): ?>
                        <tr data-space-id="<?php echo esc_attr($space->id); ?>">
                            <td><?php echo esc_html($space->title); ?></td>
                            <td class="status-cell">
                                <?php
                                $settings = get_option('mqsa_space_settings', array());
                                $enabled = isset($settings[$space->id]['enabled']) ? $settings[$space->id]['enabled'] : 'no';
                                echo $enabled === 'yes' ? 
                                    '<span class="mqsa-badge success">' . __('Enabled', 'mind-qtrl-space-access') . '</span>' : 
                                    '<span class="mqsa-badge">' . __('Disabled', 'mind-qtrl-space-access') . '</span>';
                                ?>
                            </td>
                            <td>
                                <button class="mqsa-button edit-rules" data-space-id="<?php echo esc_attr($space->id); ?>">
                                    <?php _e('Edit Rules', 'mind-qtrl-space-access'); ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Rules Modal Template -->
<div id="mqsa-rules-modal" class="mqsa-modal" style="display: none;">
    <div class="mqsa-modal-content">
        <span class="mqsa-modal-close">&times;</span>
        <h2 class="mqsa-modal-title"></h2>
        <form id="mqsa-rules-form" class="mqsa-form">
            <input type="hidden" name="space_id" id="space_id">
            
            <div class="mqsa-form-group">
                <label class="mqsa-label">
                    <input type="checkbox" name="enabled" value="yes" class="mqsa-checkbox">
                    <?php _e('Enable Access Control', 'mind-qtrl-space-access'); ?>
                </label>
            </div>
            
            <!-- Access Rules Sections -->
            <?php
            $rule_types = array(
                'view' => __('View Access', 'mind-qtrl-space-access'),
                'join' => __('Join Access', 'mind-qtrl-space-access'),
                'post' => __('Post Access', 'mind-qtrl-space-access'),
                'comment' => __('Comment Access', 'mind-qtrl-space-access')
            );
            
            foreach ($rule_types as $type => $label):
            ?>
            <div class="mqsa-rule-section" data-type="<?php echo esc_attr($type); ?>">
                <h3><?php echo esc_html($label); ?></h3>
                
                <div class="mqsa-form-group">
                    <label class="mqsa-label">
                        <input type="checkbox" name="<?php echo esc_attr($type); ?>[enabled]" value="yes" class="mqsa-checkbox">
                        <?php printf(__('Enable %s Control', 'mind-qtrl-space-access'), $label); ?>
                    </label>
                </div>
                
                <div class="mqsa-form-group">
                    <label class="mqsa-label"><?php _e('Custom Message', 'mind-qtrl-space-access'); ?></label>
                    <textarea name="<?php echo esc_attr($type); ?>[message]" class="mqsa-textarea" rows="3"></textarea>
                </div>
                
                <div class="mqsa-form-group">
                    <label class="mqsa-label"><?php _e('Required Badges', 'mind-qtrl-space-access'); ?></label>
                    <select name="<?php echo esc_attr($type); ?>[criteria][badges][]" class="mqsa-select" multiple>
                        <?php
                        // Add badge options here
                        ?>
                    </select>
                </div>
                
                <div class="mqsa-form-group">
                    <label class="mqsa-label"><?php _e('Required CRM Tags', 'mind-qtrl-space-access'); ?></label>
                    <select name="<?php echo esc_attr($type); ?>[criteria][crm_tags][]" class="mqsa-select" multiple>
                        <?php
                        // Add CRM tag options here
                        ?>
                    </select>
                </div>
                
                <div class="mqsa-form-group">
                    <label class="mqsa-label"><?php _e('Required Leaderboard Levels', 'mind-qtrl-space-access'); ?></label>
                    <select name="<?php echo esc_attr($type); ?>[criteria][leaderboard_levels][]" class="mqsa-select" multiple>
                        <?php
                        // Add leaderboard level options here
                        ?>
                    </select>
                </div>
            </div>
            <?php endforeach; ?>
            
            <div class="mqsa-form-actions">
                <button type="submit" class="mqsa-button"><?php _e('Save Rules', 'mind-qtrl-space-access'); ?></button>
                <button type="button" class="mqsa-button secondary mqsa-modal-close"><?php _e('Cancel', 'mind-qtrl-space-access'); ?></button>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Modal handling
    const $modal = $('#mqsa-rules-modal');
    const $form = $('#mqsa-rules-form');
    
    $('.edit-rules').on('click', function() {
        const spaceId = $(this).data('space-id');
        loadSpaceRules(spaceId);
    });
    
    $('.mqsa-modal-close').on('click', function() {
        $modal.hide();
    });
    
    // Load space rules
    function loadSpaceRules(spaceId) {
        $modal.addClass('mqsa-loading');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_get_space_settings',
                nonce: '<?php echo wp_create_nonce('mqsa_admin_nonce'); ?>',
                space_id: spaceId
            },
            success: function(response) {
                if (response.success) {
                    populateForm(spaceId, response.data.settings);
                    $modal.show();
                } else {
                    showNotice('error', response.data.message);
                }
            },
            error: function() {
                showNotice('error', '<?php _e('Failed to load space rules.', 'mind-qtrl-space-access'); ?>');
            },
            complete: function() {
                $modal.removeClass('mqsa-loading');
            }
        });
    }
    
    // Populate form with space rules
    function populateForm(spaceId, settings) {
        $('#space_id').val(spaceId);
        $form.find('input[name="enabled"]').prop('checked', settings.enabled === 'yes');
        
        // Populate each rule type section
        ['view', 'join', 'post', 'comment'].forEach(function(type) {
            const typeSettings = settings[type] || {};
            const $section = $(`.mqsa-rule-section[data-type="${type}"]`);
            
            $section.find(`input[name="${type}[enabled]"]`).prop('checked', typeSettings.enabled === 'yes');
            $section.find(`textarea[name="${type}[message]"]`).val(typeSettings.message || '');
            
            // Populate criteria selects
            if (typeSettings.criteria) {
                Object.keys(typeSettings.criteria).forEach(function(criteriaType) {
                    $section.find(`select[name="${type}[criteria][${criteriaType}][]"]`).val(typeSettings.criteria[criteriaType] || []);
                });
            }
        });
    }
    
    // Show notice function
    function showNotice(type, message) {
        const $notice = $('<div class="mqsa-notice ' + type + '">' + message + '</div>');
        $modal.find('.mqsa-modal-content').prepend($notice);
        setTimeout(function() {
            $notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }
});
</script>
</div>><?php
