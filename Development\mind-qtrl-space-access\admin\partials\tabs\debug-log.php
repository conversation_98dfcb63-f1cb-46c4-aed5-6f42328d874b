<?php
/**
 * Debug Log tab content
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin/partials/tabs
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get plugin options
$options = get_option('mqsa_settings', array());
?>

<div class="mqsa-card">
    <div class="mqsa-card-header">
        <h3><?php _e('Debug Log', 'mind-qtrl-space-access'); ?></h3>
        <div class="mqsa-card-actions">
            <button type="button" id="mqsa-refresh-log" class="mqsa-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.3"/>
                </svg>
                <?php _e('Refresh', 'mind-qtrl-space-access'); ?>
            </button>
            <button type="button" id="mqsa-clear-log" class="mqsa-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                </svg>
                <?php _e('Clear Log', 'mind-qtrl-space-access'); ?>
            </button>
        </div>
    </div>
    <div class="mqsa-card-body">
        <div class="mqsa-form-group">
            <label class="mqsa-form-label">
                <?php _e('Enable Debug Logging', 'mind-qtrl-space-access'); ?>
                <div class="mqsa-form-checkbox">
                    <input type="checkbox" id="mqsa-enable-debug" value="yes" 
                        <?php checked(isset($options['enable_debug_logging']) ? $options['enable_debug_logging'] : 'yes', 'yes'); ?>>
                    <span class="slider"></span>
                </div>
            </label>
            <p class="mqsa-form-description"><?php _e('Enable logging for debugging purposes. Log files are stored in the plugin\'s logs directory.', 'mind-qtrl-space-access'); ?></p>
        </div>

        <div class="mqsa-debug-log-container">
            <pre id="mqsa-debug-log" class="mqsa-debug-log"><?php _e('Loading debug log...', 'mind-qtrl-space-access'); ?></pre>
        </div>
    </div>
</div>