<?php
/**
 * General Settings tab content
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin/partials/tabs
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Get plugin options
$options = get_option('mqsa_settings', array());
?>

<form method="post" action="options.php" class="mqsa-settings-form">
    <?php settings_fields('mqsa_settings_group'); ?>

    <div class="mqsa-card">
        <div class="mqsa-card-header">
            <h3><?php _e('Global Settings', 'mind-qtrl-space-access'); ?></h3>
        </div>
        <div class="mqsa-card-body">
            <div class="mqsa-form-group">
                <label class="mqsa-form-label">
                    <?php _e('Enable Space Access Control', 'mind-qtrl-space-access'); ?>
                    <div class="mqsa-form-checkbox">
                        <input type="checkbox" name="mqsa_settings[enable_space_access_control]" value="yes" 
                            <?php checked(isset($options['enable_space_access_control']) ? $options['enable_space_access_control'] : 'yes', 'yes'); ?>>
                        <span class="slider"></span>
                    </div>
                </label>
                <p class="mqsa-form-description"><?php _e('Enable or disable all space access control features.', 'mind-qtrl-space-access'); ?></p>
            </div>

            <div class="mqsa-form-group">
                <label class="mqsa-form-label">
                    <?php _e('Enable Debug Logging', 'mind-qtrl-space-access'); ?>
                    <div class="mqsa-form-checkbox">
                        <input type="checkbox" name="mqsa_settings[enable_debug_logging]" value="yes" 
                            <?php checked(isset($options['enable_debug_logging']) ? $options['enable_debug_logging'] : 'yes', 'yes'); ?>>
                        <span class="slider"></span>
                    </div>
                </label>
                <p class="mqsa-form-description"><?php _e('Enable logging for debugging purposes.', 'mind-qtrl-space-access'); ?></p>
            </div>
        </div>
    </div>

    <div class="mqsa-card">
        <div class="mqsa-card-header">
            <h3><?php _e('Default Messages', 'mind-qtrl-space-access'); ?></h3>
        </div>
        <div class="mqsa-card-body">
            <div class="mqsa-form-group">
                <label class="mqsa-form-label" for="mqsa-default-access-message">
                    <?php _e('Access Denied Message', 'mind-qtrl-space-access'); ?>
                </label>
                <textarea id="mqsa-default-access-message" name="mqsa_settings[default_access_message]" 
                    class="mqsa-form-control mqsa-form-textarea"><?php echo esc_textarea(isset($options['default_access_message']) ? $options['default_access_message'] : ''); ?></textarea>
            </div>

            <div class="mqsa-form-group">
                <label class="mqsa-form-label" for="mqsa-default-join-message">
                    <?php _e('Join Restricted Message', 'mind-qtrl-space-access'); ?>
                </label>
                <textarea id="mqsa-default-join-message" name="mqsa_settings[default_join_message]" 
                    class="mqsa-form-control mqsa-form-textarea"><?php echo esc_textarea(isset($options['default_join_message']) ? $options['default_join_message'] : ''); ?></textarea>
            </div>

            <div class="mqsa-form-group">
                <label class="mqsa-form-label" for="mqsa-default-post-message">
                    <?php _e('Post Restricted Message', 'mind-qtrl-space-access'); ?>
                </label>
                <textarea id="mqsa-default-post-message" name="mqsa_settings[default_post_message]" 
                    class="mqsa-form-control mqsa-form-textarea"><?php echo esc_textarea(isset($options['default_post_message']) ? $options['default_post_message'] : ''); ?></textarea>
            </div>

            <div class="mqsa-form-group">
                <label class="mqsa-form-label" for="mqsa-default-comment-message">
                    <?php _e('Comment Restricted Message', 'mind-qtrl-space-access'); ?>
                </label>
                <textarea id="mqsa-default-comment-message" name="mqsa_settings[default_comment_message]" 
                    class="mqsa-form-control mqsa-form-textarea"><?php echo esc_textarea(isset($options['default_comment_message']) ? $options['default_comment_message'] : ''); ?></textarea>
            </div>
        </div>
        <div class="mqsa-card-footer">
            <button type="submit" class="mqsa-btn mqsa-btn-primary">
                <?php _e('Save Settings', 'mind-qtrl-space-access'); ?>
            </button>
        </div>
    </div>
</form>