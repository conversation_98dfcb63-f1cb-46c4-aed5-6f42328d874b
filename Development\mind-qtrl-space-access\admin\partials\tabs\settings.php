<?php
/**
 * Settings tab template
 *
 * @since      1.0.0
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin/partials/tabs
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}
?>

<div class="mqsa-settings-tab">
    <h2><?php _e('General Settings', 'mind-qtrl-space-access'); ?></h2>
    
    <p><?php _e('Configure general settings for the Space Access plugin.', 'mind-qtrl-space-access'); ?></p>
    
    <form method="post" action="options.php" class="mqsa-form">
        <?php
        settings_fields('mqsa_settings');
        do_settings_sections('mind-qtrl-space-access');
        submit_button(__('Save Settings', 'mind-qtrl-space-access'), 'primary mqsa-button');
        ?>
    </form>
</div>

<div class="mqsa-section">
    <h3><?php _e('Plugin Information', 'mind-qtrl-space-access'); ?></h3>
    
    <p><?php _e('Mind Qtrl Space Access Control provides advanced membership access control for Fluent Community spaces.', 'mind-qtrl-space-access'); ?></p>
    
    <table class="form-table">
        <tr>
            <th scope="row"><?php _e('Version', 'mind-qtrl-space-access'); ?></th>
            <td><?php echo MQSA_VERSION; ?></td>
        </tr>
        <tr>
            <th scope="row"><?php _e('Support', 'mind-qtrl-space-access'); ?></th>
            <td><a href="https://mindqtrl.com/support" target="_blank"><?php _e('Get Support', 'mind-qtrl-space-access'); ?></a></td>
        </tr>
        <tr>
            <th scope="row"><?php _e('Documentation', 'mind-qtrl-space-access'); ?></th>
            <td><a href="https://mindqtrl.com/docs/space-access" target="_blank"><?php _e('View Documentation', 'mind-qtrl-space-access'); ?></a></td>
        </tr>
    </table>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Save settings via AJAX
    $('.mqsa-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitButton = $form.find('.mqsa-button');
        
        $form.addClass('mqsa-loading');
        $submitButton.prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'mqsa_save_settings',
                nonce: '<?php echo wp_create_nonce('mqsa-admin'); ?>',
                formData: $form.serialize()
            },
            success: function(response) {
                $form.removeClass('mqsa-loading');
                $submitButton.prop('disabled', false);
                
                if (response.success) {
                    // Show success message
                    $('<div class="mqsa-notice mqsa-notice-success">')
                        .text(response.data.message)
                        .insertBefore($form)
                        .delay(3000)
                        .fadeOut(500, function() {
                            $(this).remove();
                        });
                } else {
                    // Show error message
                    $('<div class="mqsa-notice mqsa-notice-error">')
                        .text(response.data.message)
                        .insertBefore($form)
                        .delay(3000)
                        .fadeOut(500, function() {
                            $(this).remove();
                        });
                }
            },
            error: function() {
                $form.removeClass('mqsa-loading');
                $submitButton.prop('disabled', false);
                
                // Show error message
                $('<div class="mqsa-notice mqsa-notice-error">')
                    .text('<?php _e('An error occurred while saving settings.', 'mind-qtrl-space-access'); ?>')
                    .insertBefore($form)
                    .delay(3000)
                    .fadeOut(500, function() {
                        $(this).remove();
                    });
            }
        });
    });
});
</script>
