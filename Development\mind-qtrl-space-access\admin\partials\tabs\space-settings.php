<?php
/**
 * Space Settings tab content
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/admin/partials/tabs
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}
?>

<div class="mqsa-spaces-container">
    <div class="mqsa-spaces-sidebar">
        <div class="mqsa-spaces-search">
            <input type="text" id="mqsa-space-search" class="mqsa-form-control" placeholder="<?php _e('Search spaces...', 'mind-qtrl-space-access'); ?>">
        </div>
        <div class="mqsa-spaces-list" id="mqsa-spaces-list">
            <div class="mqsa-loading">
                <?php _e('Loading spaces...', 'mind-qtrl-space-access'); ?>
            </div>
        </div>
    </div>

    <div class="mqsa-space-settings">
        <div class="mqsa-space-settings-placeholder" id="mqsa-space-settings-placeholder">
            <div class="mqsa-placeholder-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="8" y1="12" x2="16" y2="12"></line>
                    <line x1="12" y1="8" x2="12" y2="16"></line>
                </svg>
            </div>
            <h3><?php _e('Select a Space', 'mind-qtrl-space-access'); ?></h3>
            <p><?php _e('Select a space from the list to configure access control settings.', 'mind-qtrl-space-access'); ?></p>
        </div>

        <div class="mqsa-space-settings-form" id="mqsa-space-settings-form" style="display: none;">
            <input type="hidden" id="mqsa-space-id">
            
            <div class="mqsa-card">
                <div class="mqsa-card-header">
                    <h3><?php _e('Access Control Settings', 'mind-qtrl-space-access'); ?></h3>
                </div>
                <div class="mqsa-card-body">
                    <div class="mqsa-form-group">
                        <label class="mqsa-form-label">
                            <?php _e('Enable Restrictions', 'mind-qtrl-space-access'); ?>
                            <div class="mqsa-form-checkbox">
                                <input type="checkbox" id="mqsa-enable-restrictions">
                                <span class="slider"></span>
                            </div>
                        </label>
                    </div>

                    <div class="mqsa-form-group">
                        <label class="mqsa-form-label" for="mqsa-access-requirements">
                            <?php _e('Access Requirements', 'mind-qtrl-space-access'); ?>
                        </label>
                        <select id="mqsa-access-requirements" class="mqsa-form-select">
                            <option value="none"><?php _e('None', 'mind-qtrl-space-access'); ?></option>
                            <option value="crm_tags"><?php _e('CRM Tags', 'mind-qtrl-space-access'); ?></option>
                            <option value="badges"><?php _e('User Badges', 'mind-qtrl-space-access'); ?></option>
                            <option value="leaderboard"><?php _e('Leaderboard Level', 'mind-qtrl-space-access'); ?></option>
                        </select>
                    </div>

                    <div class="mqsa-restrictions-container">
                        <div class="mqsa-form-group">
                            <label class="mqsa-form-label">
                                <?php _e('Restrict Viewing', 'mind-qtrl-space-access'); ?>
                                <div class="mqsa-form-checkbox">
                                    <input type="checkbox" id="mqsa-restrict-view">
                                    <span class="slider"></span>
                                </div>
                            </label>
                            <textarea id="mqsa-view-message" class="mqsa-form-control mqsa-form-textarea" placeholder="<?php _e('Custom message for restricted viewing...', 'mind-qtrl-space-access'); ?>"></textarea>
                        </div>

                        <div class="mqsa-form-group">
                            <label class="mqsa-form-label">
                                <?php _e('Restrict Joining', 'mind-qtrl-space-access'); ?>
                                <div class="mqsa-form-checkbox">
                                    <input type="checkbox" id="mqsa-restrict-join">
                                    <span class="slider"></span>
                                </div>
                            </label>
                            <textarea id="mqsa-join-message" class="mqsa-form-control mqsa-form-textarea" placeholder="<?php _e('Custom message for restricted joining...', 'mind-qtrl-space-access'); ?>"></textarea>
                        </div>

                        <div class="mqsa-form-group">
                            <label class="mqsa-form-label">
                                <?php _e('Restrict Posting', 'mind-qtrl-space-access'); ?>
                                <div class="mqsa-form-checkbox">
                                    <input type="checkbox" id="mqsa-restrict-post">
                                    <span class="slider"></span>
                                </div>
                            </label>
                            <textarea id="mqsa-post-message" class="mqsa-form-control mqsa-form-textarea" placeholder="<?php _e('Custom message for restricted posting...', 'mind-qtrl-space-access'); ?>"></textarea>
                        </div>

                        <div class="mqsa-form-group">
                            <label class="mqsa-form-label">
                                <?php _e('Restrict Comments', 'mind-qtrl-space-access'); ?>
                                <div class="mqsa-form-checkbox">
                                    <input type="checkbox" id="mqsa-restrict-comment">
                                    <span class="slider"></span>
                                </div>
                            </label>
                            <textarea id="mqsa-comment-message" class="mqsa-form-control mqsa-form-textarea" placeholder="<?php _e('Custom message for restricted commenting...', 'mind-qtrl-space-access'); ?>"></textarea>
                        </div>
                    </div>
                </div>
                <div class="mqsa-card-footer">
                    <button type="button" id="mqsa-save-space-settings" class="mqsa-btn mqsa-btn-primary">
                        <?php _e('Save Space Settings', 'mind-qtrl-space-access'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
