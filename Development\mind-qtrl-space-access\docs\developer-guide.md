# Mind Qtrl | Space Access Control - Developer Guide

This guide provides technical information for developers who want to extend or customize the Mind Qtrl Space Access Control plugin.

## Table of Contents

1. [Plugin Architecture](#plugin-architecture)
2. [Hooks and Filters](#hooks-and-filters)
3. [Database Structure](#database-structure)
4. [Integration Points](#integration-points)
5. [Frontend JavaScript API](#frontend-javascript-api)
6. [Performance Considerations](#performance-considerations)

## Plugin Architecture

The plugin follows a modular architecture with the following main components:

- **Core Plugin Class** (`Mind_Qtrl_Space_Access`): Main plugin initialization and hooks
- **Access Controller** (`MQSA_Access_Controller`): Handles access control logic
- **Admin Interface** (`MQSA_Admin`): Manages the admin dashboard UI
- **Public Interface** (`Mind_Qtrl_Space_Access_Public`): Handles frontend functionality
- **Loader** (`MQSA_Loader`): Registers all hooks and filters

### File Structure

```
mind-qtrl-space-access/
├── admin/                  # Admin-related files
│   ├── css/                # Admin CSS files
│   ├── js/                 # Admin JavaScript files
│   ├── partials/           # Admin page templates
│   └── class-mqsa-admin.php # Admin class
├── includes/               # Core plugin files
│   ├── class-mqsa-loader.php # Hook loader
│   ├── class-mqsa-access-controller.php # Access control logic
│   └── class-mind-qtrl-space-access.php # Main plugin class
├── public/                 # Public-facing files
│   ├── css/                # Public CSS files
│   ├── js/                 # Public JavaScript files
│   └── class-mind-qtrl-space-access-public.php # Public class
├── docs/                   # Documentation
├── languages/              # Translation files
├── mind-qtrl-space-access.php # Plugin bootstrap file
└── README.md               # Plugin documentation
```

## Hooks and Filters

### Actions

| Action Name | Description | Parameters |
|-------------|-------------|------------|
| `mqsa_before_check_access` | Fires before checking space access | `$space_id`, `$user_id` |
| `mqsa_after_check_access` | Fires after checking space access | `$space_id`, `$user_id`, `$has_access` |
| `mqsa_log_event` | Logs an event to the debug log | `$event_type`, `$message`, `$context` |
| `mqsa_space_settings_saved` | Fires after space settings are saved | `$space_id`, `$settings` |

### Filters

| Filter Name | Description | Parameters |
|-------------|-------------|------------|
| `mqsa_space_settings` | Filters space settings before they're used | `$settings`, `$space_id` |
| `mqsa_user_meets_requirements` | Filters whether a user meets the requirements | `$meets_requirements`, `$user_id`, `$space_id`, `$settings` |
| `mqsa_access_requirement_types` | Filters the available access requirement types | `$types` |
| `mqsa_default_messages` | Filters the default restriction messages | `$messages` |
| `mqsa_debug_log_retention` | Filters the debug log retention period (in days) | `$days` |

### Integration with Fluent Community

The plugin hooks into the following Fluent Community filters:

| Filter Name | Description | Parameters |
|-------------|-------------|------------|
| `fluent_community/can_view_space` | Controls whether a user can view a space | `$can_view`, `$space_id`, `$user_id` |
| `fluent_community/can_join_space` | Controls whether a user can join a space | `$can_join`, `$space_id`, `$user_id` |
| `fluent_community/can_post_in_space` | Controls whether a user can post in a space | `$can_post`, `$space_id`, `$user_id` |
| `fluent_community/can_react_in_space` | Controls whether a user can react in a space | `$can_react`, `$space_id`, `$user_id` |
| `fluent_community/can_comment_in_space` | Controls whether a user can comment in a space | `$can_comment`, `$space_id`, `$user_id` |
| `fluent_community/space_data` | Modifies space data | `$space`, `$user_id` |
| `fluent_community/space_access` | Controls overall space access | `$has_access`, `$space_id`, `$user_id` |
| `fluent_community/portal_settings_menu_items` | Adds items to the portal settings menu | `$menu_items` |

## Database Structure

The plugin stores its settings in the WordPress options table:

- `mqsa_settings`: Global plugin settings
- `mqsa_space_settings`: Per-space access control settings
- `mqsa_debug_log`: Debug log entries (if enabled)

### Space Settings Structure

```php
[
    'space_id' => [
        'enable_restrictions' => 'yes', // 'yes' or 'no'
        'access_requirements' => 'crm_tags', // 'none', 'crm_tags', 'badges', or 'leaderboard'
        'crm_tags' => '1,2,3', // Comma-separated list of tag IDs
        'badges' => '4,5,6', // Comma-separated list of badge IDs
        'leaderboard_levels' => '7,8,9', // Comma-separated list of level IDs
        'restrict_view' => 'yes', // 'yes' or 'no'
        'restrict_join' => 'yes', // 'yes' or 'no'
        'restrict_post' => 'yes', // 'yes' or 'no'
        'restrict_comment' => 'yes', // 'yes' or 'no'
        'view_message' => 'Custom message for view restriction',
        'join_message' => 'Custom message for join restriction',
        'post_message' => 'Custom message for post restriction',
        'comment_message' => 'Custom message for comment restriction'
    ],
    // Additional spaces...
]
```

## Integration Points

### FluentCRM Integration

The plugin integrates with FluentCRM to check if a user has specific tags:

```php
/**
 * Check if user has required CRM tags
 *
 * @param int $user_id The user ID
 * @param array $space_settings The space settings
 * @return bool Whether the user has the required tags
 */
private function check_crm_tags($user_id, $space_settings) {
    // Check if FluentCRM is active
    if (!defined('FLUENTCRM')) {
        return false;
    }
    
    // Get required tags
    $required_tags = !empty($space_settings['crm_tags']) ? explode(',', $space_settings['crm_tags']) : [];
    if (empty($required_tags)) {
        return true;
    }
    
    // Get user's email
    $user = get_userdata($user_id);
    if (!$user) {
        return false;
    }
    
    // Get contact
    $contact = FluentCrmApi('contacts')->getContact($user->user_email);
    if (!$contact) {
        return false;
    }
    
    // Get contact's tags
    $contact_tags = $contact->tags;
    if (empty($contact_tags)) {
        return false;
    }
    
    // Extract tag IDs
    $contact_tag_ids = array_map(function($tag) {
        return $tag->id;
    }, $contact_tags);
    
    // Check if user has any of the required tags
    foreach ($required_tags as $tag_id) {
        if (in_array($tag_id, $contact_tag_ids)) {
            return true;
        }
    }
    
    return false;
}
```

### Badge System Integration

The plugin can integrate with badge systems like GamiPress:

```php
/**
 * Check if user has required badges
 *
 * @param int $user_id The user ID
 * @param array $space_settings The space settings
 * @return bool Whether the user has the required badges
 */
private function check_badges($user_id, $space_settings) {
    // Get required badges
    $required_badges = !empty($space_settings['badges']) ? explode(',', $space_settings['badges']) : [];
    if (empty($required_badges)) {
        return true;
    }
    
    // Check if GamiPress is active
    if (!function_exists('gamipress_get_user_achievements')) {
        return false;
    }
    
    // Get user's badges
    $user_badges = gamipress_get_user_achievements([
        'user_id' => $user_id,
        'achievement_type' => 'badge'
    ]);
    
    if (empty($user_badges)) {
        return false;
    }
    
    // Extract badge IDs
    $user_badge_ids = array_map(function($badge) {
        return $badge->ID;
    }, $user_badges);
    
    // Check if user has any of the required badges
    foreach ($required_badges as $badge_id) {
        if (in_array($badge_id, $user_badge_ids)) {
            return true;
        }
    }
    
    return false;
}
```

### Leaderboard Integration

The plugin can integrate with point systems like myCRED:

```php
/**
 * Check if user meets leaderboard level requirements
 *
 * @param int $user_id The user ID
 * @param array $space_settings The space settings
 * @return bool Whether the user meets the leaderboard requirements
 */
private function check_leaderboard($user_id, $space_settings) {
    // Get required leaderboard levels
    $required_levels = !empty($space_settings['leaderboard_levels']) ? explode(',', $space_settings['leaderboard_levels']) : [];
    if (empty($required_levels)) {
        return true;
    }
    
    // Check if myCRED is active
    if (!function_exists('mycred_get_users_rank')) {
        return false;
    }
    
    // Get user's rank
    $user_rank = mycred_get_users_rank($user_id);
    if (!$user_rank) {
        return false;
    }
    
    // Check if user's rank is in the required levels
    return in_array($user_rank->post_id, $required_levels);
}
```

## Frontend JavaScript API

The plugin provides a JavaScript API for frontend integration:

### Global Objects

- `mqsaSettings`: Plugin settings and selectors
- `mqsaSpaceSettings`: Per-space access control settings
- `mqsaPublic`: Public-facing messages and utilities

### Methods

```javascript
/**
 * Check if user is a member of the space
 * 
 * @param {number} spaceId The space ID
 * @returns {Promise<boolean>} Whether user is a member
 */
async function checkSpaceMembership(spaceId) {
    try {
        const response = await fetch(`${mqsaSettings.ajaxurl}?action=mqsa_check_membership&space_id=${spaceId}&nonce=${mqsaSettings.nonce}`);
        const data = await response.json();
        
        if (data.success && data.data) {
            return data.data.is_member;
        }
    } catch (error) {
        console.error('MQSA: Error checking membership', error);
    }
    
    return false;
}
```

### Events

The plugin triggers the following custom events:

- `mqsa:restrictions_applied`: Fired when restrictions are applied to a space
- `mqsa:space_access_checked`: Fired when space access is checked

## Performance Considerations

### Caching

The plugin implements caching for expensive operations:

- User membership status is cached for the duration of the request
- Access check results are cached for the duration of the request
- FluentCRM tag checks are cached for 1 hour (can be filtered)

### Database Queries

- Space settings are loaded once and cached
- User capability checks are batched where possible
- The plugin avoids unnecessary database queries by checking requirements in order of complexity

### Memory Usage

- The plugin uses a lazy loading approach for settings
- Debug logs are only written when debug mode is enabled
- Large data structures are cleaned up after use

### Extending the Plugin

When extending the plugin, consider the following best practices:

1. Use the provided hooks and filters instead of modifying core files
2. Cache expensive operations, especially API calls
3. Clean up after yourself by removing hooks and filters when they're no longer needed
4. Follow WordPress coding standards and security best practices
5. Test your extensions thoroughly with different user roles and access scenarios
