# Mind Qtrl | Space Access Control - User Guide

This guide will help you configure and use the Mind Qtrl Space Access Control plugin to manage access to your Fluent Community spaces.

## Table of Contents

1. [Plugin Overview](#plugin-overview)
2. [General Settings](#general-settings)
3. [Space Settings](#space-settings)
4. [Access Requirements](#access-requirements)
5. [Frontend Portal Integration](#frontend-portal-integration)
6. [Troubleshooting](#troubleshooting)

## Plugin Overview

Mind Qtrl Space Access Control allows you to restrict access to Fluent Community spaces based on various criteria such as:

- Fluent CRM tags
- User badges
- Leaderboard levels

You can control who can view, join, post, and comment in each space individually.

## General Settings

To access the plugin settings:

1. Navigate to **Fluent Community > Space Access** in your WordPress admin dashboard
2. The settings page has several tabs:
   - **General**: Global plugin settings
   - **Space Settings**: Configure access for individual spaces
   - **Debug**: Troubleshooting tools

### General Settings Tab

In this tab, you can configure:

- **Enable Plugin**: Turn the plugin on or off globally
- **Default Messages**: Set default messages for access restrictions
- **Debug Mode**: Enable/disable detailed logging
- **Log Retention**: How long to keep debug logs

## Space Settings

The Space Settings tab allows you to configure access control for individual spaces:

1. Select a space from the list on the left
2. Configure the following settings for the selected space:

### Basic Settings

- **Enable Restrictions**: Turn on/off access control for this specific space
- **Access Requirements**: Choose the type of requirements (CRM Tags, Badges, Leaderboard)

### Restriction Types

- **Restrict Viewing**: Prevent non-qualified users from viewing the space
- **Restrict Joining**: Prevent non-qualified users from joining the space
- **Restrict Posting**: Prevent non-qualified users from creating posts
- **Restrict Commenting**: Prevent non-qualified users from commenting or reacting

### Custom Messages

- **View Restriction Message**: Shown when a user can't view a space
- **Join Restriction Message**: Shown when a user can't join a space
- **Post Restriction Message**: Shown when a user can't post in a space
- **Comment Restriction Message**: Shown when a user can't comment in a space

## Access Requirements

Depending on the access requirement type you select, different options will be available:

### Fluent CRM Tags

If you select "CRM Tags" as the access requirement:

1. The "CRM Tags" dropdown will appear
2. Select one or more tags from your Fluent CRM
3. Users must have at least one of the selected tags to access the space

**Note**: This requires Fluent CRM to be installed and activated.

### Badges

If you select "Badges" as the access requirement:

1. The "Badges" dropdown will appear
2. Select one or more badges from your badge system
3. Users must have at least one of the selected badges to access the space

**Note**: This requires a badge system like GamiPress to be installed and activated.

### Leaderboard Levels

If you select "Leaderboard" as the access requirement:

1. The "Leaderboard Levels" dropdown will appear
2. Select one or more leaderboard levels
3. Users must have at least the minimum selected level to access the space

**Note**: This requires a point system like myCRED to be installed and activated.

## Frontend Portal Integration

You can also access space settings from the Fluent Community frontend portal:

1. Log in to your Fluent Community portal
2. Navigate to the admin settings (gear icon in the footer)
3. Click on "Space Access" in the settings menu
4. You'll see a link to the full settings page in the WordPress admin

## Troubleshooting

If you encounter issues with space access control:

1. Go to the "Debug" tab in the plugin settings
2. Enable detailed logging
3. Attempt the action that's causing issues
4. Check the debug log for detailed information about what's happening

### Common Issues

#### Users can still access restricted spaces

- Check if the user is already a member of the space (restrictions don't apply to existing members)
- Verify that restrictions are enabled for the specific space
- Ensure the user doesn't meet the access requirements

#### CRM tag restrictions not working

- Verify that Fluent CRM is installed and activated
- Check that the user has a contact record in Fluent CRM
- Confirm that the correct tags are selected in the space settings

#### Badge restrictions not working

- Verify that your badge system (e.g., GamiPress) is installed and activated
- Check that the user has earned the required badges
- Confirm that the correct badges are selected in the space settings

#### Leaderboard restrictions not working

- Verify that your point system (e.g., myCRED) is installed and activated
- Check that the user has reached the required rank
- Confirm that the correct leaderboard levels are selected in the space settings

### Getting Support

If you need further assistance, please contact <NAME_EMAIL> or visit our website at [mindqtrl.com](https://mindqtrl.com/).
