# Fluent Community Integration Guide

This document provides detailed information about integrating with Fluent Community, with a focus on space access control and restriction methods.

## Best Methods to Prevent Users from Joining Fluent Community Spaces

There are multiple layers of protection that can be implemented to prevent unauthorized users from joining Fluent Community spaces:

### 1. Frontend UI Approach (CSS and DOM Manipulation)

This approach hides join buttons and prevents click events on them:

```javascript
// Hide join buttons with CSS
const style = document.createElement('style');
style.textContent = `
    /* Hide all join buttons */
    .fcom_space_join_btn,
    .btn_join_space,
    .fcom_space_request_btn,
    .menu_actions .fcom_primary_button,
    .menu_actions .el-button:first-child,
    .fcom_space_header_actions button,
    .fcom_space_header_actions .el-button,
    .fcom_space_actions button,
    .fcom_space_actions .el-button,
    [data-action="join"],
    button[title*="Join"],
    button[title*="join"],
    .el-button[title*="Join"],
    .el-button[title*="join"] {
        display: none !important;
    }
`;
document.head.appendChild(style);

// Intercept click events on join buttons
document.addEventListener('click', function(event) {
    let target = event.target;
    let isJoinButton = false;

    // Check up to 5 levels of parent elements
    for (let i = 0; i < 5; i++) {
        if (!target) break;

        if (target.classList && (
            target.classList.contains('fcom_space_join_btn') ||
            target.classList.contains('btn_join_space') ||
            target.classList.contains('fcom_space_request_btn') ||
            (target.classList.contains('fcom_primary_button') &&
             target.closest('.menu_actions')) ||
            (target.closest('.fcom_space_header_actions') && 
             (target.tagName === 'BUTTON' || target.classList.contains('el-button'))) ||
            target.getAttribute('data-action') === 'join' ||
            (target.title && (target.title.includes('Join') || target.title.includes('join')))
        )) {
            isJoinButton = true;
            break;
        }

        target = target.parentElement;
    }

    if (isJoinButton) {
        event.preventDefault();
        event.stopPropagation();
        
        // Show message if needed
        if (typeof mqsaToast === 'function') {
            mqsaToast({
                message: 'You do not have permission to join this space',
                type: 'warning',
                duration: 5000
            });
        }
    }
}, true); // Use capture phase to intercept events before they reach their targets
```

### 2. API Interception Approach

This approach intercepts network requests to join spaces:

```javascript
// Intercept API calls to join spaces
const originalFetch = window.fetch;
window.fetch = function(url, options) {
    // Check if this is a join space API call
    if (typeof url === 'string' && url.includes('/join') && options?.method === 'POST') {
        // Show message if needed
        if (typeof mqsaToast === 'function') {
            mqsaToast({
                message: 'You do not have permission to join this space',
                type: 'warning',
                duration: 5000
            });
        }
        
        // Return a fake successful response
        return Promise.resolve({
            ok: false,
            status: 403,
            json: () => Promise.resolve({ message: 'Access restricted' })
        });
    }
    return originalFetch.apply(this, arguments);
};
```

### 3. Vue Store Approach

This approach modifies the Vue store to prevent joining at the data level:

```javascript
// Update permissions in the Vue store
if (vueApp && vueApp.$store) {
    try {
        // Get current permissions
        const currentPermissions = vueApp.$store.state.spaces.currentSpacePermissions || {};

        // Update permissions to prevent joining
        vueApp.$store.commit('spaces/setCurrentSpacePermissions', {
            ...currentPermissions,
            can_join: false,
            can_request: false
        });
        
        // Intercept store mutations related to joining
        const originalCommit = vueApp.$store.commit;
        vueApp.$store.commit = function(type, payload) {
            // Block mutations related to joining spaces
            if (type.includes('join') || type.includes('JOIN')) {
                console.log('MQSA: Blocked store mutation:', type);
                return;
            }
            return originalCommit.call(this, type, payload);
        };
    } catch (e) {
        console.error('Error updating Vue store permissions', e);
    }
}
```

### 4. Dynamic Element Monitoring

This approach uses MutationObserver to handle dynamically added join buttons:

```javascript
// Monitor for dynamically added join buttons
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length) {
            for (let i = 0; i < mutation.addedNodes.length; i++) {
                const node = mutation.addedNodes[i];
                if (node.nodeType === 1) { // Element node
                    // Check if this is a join button
                    if (node.classList && (
                        node.classList.contains('fcom_space_join_btn') ||
                        node.classList.contains('btn_join_space') ||
                        node.classList.contains('fcom_space_request_btn') ||
                        node.getAttribute('data-action') === 'join'
                    )) {
                        node.style.display = 'none';
                    }
                    
                    // Check children for join buttons
                    const joinButtons = node.querySelectorAll('.fcom_space_join_btn, .btn_join_space, .fcom_space_request_btn, [data-action="join"], .menu_actions .fcom_primary_button, .menu_actions .el-button:first-child');
                    joinButtons.forEach(button => {
                        button.style.display = 'none';
                    });
                }
            }
        }
    });
});

// Start observing the document
observer.observe(document.body, {
    childList: true,
    subtree: true
});
```

## Methods to Prevent Likes in Fluent Community

> **Important Distinction:** In Fluent Community, likes/reactions are completely separate from comments:
> - **Likes/Reactions:** Allow users to express appreciation for content with emoji reactions (like, love, etc.)
> - **Comments:** Allow users to respond with text replies to activities
>
> This section focuses specifically on preventing the like/reaction functionality, not comments.

Preventing users from liking content in Fluent Community spaces can be implemented using similar multi-layered approaches:

### 1. CSS Approach to Hide Like/Reaction Buttons

```javascript
// Hide like/reaction elements with CSS
const style = document.createElement('style');
style.textContent = `
    /* Hide all reaction elements */
    .fcom_reaction,
    .fcom_reaction_list,
    .fcom_reaction_btn,
    .fcom_reaction_icon,
    .fcom_reaction_count,
    .fcom_like_btn,
    .fcom_like_icon,
    .fcom_like_count,
    .el-button[data-action="like"],
    button[data-action="like"],
    .fcom_feed_item_footer .fcom_reaction,
    /* Additional selectors for better coverage */
    .feed_footer .fcom_reaction,
    .feed_actions .fcom_reaction,
    .fcom_feed_item .fcom_reaction,
    .fcom_feed_actions .fcom_reaction,
    .fcom_feed_item_actions .fcom_reaction,
    .fcom_feed_item_footer .feed_actions button[data-type="like"],
    .fcom_feed_item_footer .feed_actions .fcom_reaction_list,
    .feed_footer .fcom_reaction_list,
    /* Target custom reaction elements */
    .mqcr-reaction,
    .mqcr-reaction-like,
    /* Target Vue component elements */
    [data-v-component="FCReactionBar"],
    .fcom_reaction_wrapper,
    .fcom_feed_reaction {
        display: none !important;
    }
`;
document.head.appendChild(style);
```

### 2. Event Interception for Like/Reaction Actions

```javascript
// Add event listener to intercept clicks on reaction buttons
document.addEventListener('click', function(event) {
    // Check if the clicked element or its parent is a reaction button
    let target = event.target;
    let isReactionButton = false;

    // Check up to 5 levels of parent elements
    for (let i = 0; i < 5; i++) {
        if (!target) break;

        // Check if this element is a reaction button
        if (target.classList && (
            target.classList.contains('fcom_reaction') ||
            target.classList.contains('fcom_reaction_btn') ||
            target.classList.contains('fcom_like_btn') ||
            target.classList.contains('mqcr-reaction') ||
            target.hasAttribute('data-action') && target.getAttribute('data-action') === 'like' ||
            target.hasAttribute('data-type') && target.getAttribute('data-type') === 'like'
        )) {
            isReactionButton = true;
            break;
        }

        // Move up to parent element
        target = target.parentElement;
    }

    // If this is a reaction button, prevent the default action and stop propagation
    if (isReactionButton) {
        event.preventDefault();
        event.stopPropagation();

        // Show custom message if provided
        if (typeof mqsaToast === 'function') {
            mqsaToast({
                message: 'You do not have permission to like content in this space',
                type: 'warning',
                duration: 5000
            });
        }

        return false;
    }
}, true); // Use capture phase to intercept events before they reach their targets
```

### 3. Vue Store Permission Updates for Likes/Reactions

```javascript
// Update permissions in the Vue store to prevent liking
if (vueApp && vueApp.$store) {
    try {
        // Get current permissions
        const currentPermissions = vueApp.$store.state.spaces.currentSpacePermissions || {};

        // Update permissions to prevent liking and reacting
        vueApp.$store.commit('spaces/setCurrentSpacePermissions', {
            ...currentPermissions,
            can_like: false,
            can_react: false
        });
    } catch (e) {
        console.error('Error updating Vue store permissions', e);
    }
}
```

### 4. Dynamic Monitoring for Like/Reaction Elements

```javascript
// Monitor for dynamically added like/reaction elements
const likeElementsObserver = new MutationObserver(function() {
    const selectors = [
        '.fcom_reaction',
        '.fcom_reaction_list',
        '.fcom_reaction_btn',
        '.fcom_reaction_icon',
        '.fcom_reaction_count',
        '.fcom_like_btn',
        '.fcom_like_icon',
        '.fcom_like_count',
        '.el-button[data-action="like"]',
        'button[data-action="like"]',
        '.fcom_feed_item_footer .fcom_reaction',
        '.feed_footer .fcom_reaction',
        '.feed_actions .fcom_reaction',
        '.fcom_feed_item .fcom_reaction',
        '.fcom_feed_actions .fcom_reaction',
        '.fcom_feed_item_actions .fcom_reaction',
        '.fcom_feed_item_footer .feed_actions button[data-type="like"]',
        '.fcom_feed_item_footer .feed_actions .fcom_reaction_list',
        '.feed_footer .fcom_reaction_list',
        '.mqcr-reaction',
        '.mqcr-reaction-like',
        '[data-v-component="FCReactionBar"]',
        '.fcom_reaction_wrapper',
        '.fcom_feed_reaction'
    ];

    const elements = document.querySelectorAll(selectors.join(','));
    elements.forEach(function(element) {
        element.style.display = 'none';
    });
});

// Observe the entire document for added elements
likeElementsObserver.observe(document.body, {
    childList: true,
    subtree: true
});
```

## Methods to Prevent Comments in Fluent Community

> **Important Distinction:** In Fluent Community, comments are completely separate from likes/reactions:
> - **Comments:** Allow users to respond with text replies to activities
> - **Likes/Reactions:** Allow users to express appreciation for content with emoji reactions (like, love, etc.)
>
> This section focuses specifically on preventing the comment functionality, not likes/reactions.

### 1. CSS Approach to Hide Comment Elements

```javascript
// Hide comment elements with CSS
const style = document.createElement('style');
style.textContent = `
    /* Hide all comment elements */
    .fcom_comment_btn_wrap,
    .fcom_comment_form,
    .fcom_comment_button,
    .fcom_comment_btn,
    .fcom_comment_action,
    .fcom_comment_icon,
    .fcom_comment_count,
    .fcom_comment_area,
    .fcom_comment_list,
    .fcom_comment_input,
    .fcom_comment_submit,
    .el-button[data-action="comment"],
    button[data-action="comment"],
    .fcom_feed_item_footer .fcom_comment_btn_wrap {
        display: none !important;
    }
`;
document.head.appendChild(style);
```

### 2. Event Interception for Comment Actions

```javascript
// Add event listener to intercept clicks on comment buttons
document.addEventListener('click', function(event) {
    // Check if the clicked element or its parent is a comment button
    let target = event.target;
    let isCommentButton = false;

    // Check up to 5 levels of parent elements
    for (let i = 0; i < 5; i++) {
        if (!target) break;

        // Check if this element is a comment button
        if (target.classList && (
            target.classList.contains('fcom_comment_btn_wrap') ||
            target.classList.contains('fcom_comment_btn') ||
            target.classList.contains('fcom_comment_action') ||
            target.hasAttribute('data-action') && target.getAttribute('data-action') === 'comment'
        )) {
            isCommentButton = true;
            break;
        }

        // Move up to parent element
        target = target.parentElement;
    }

    // If this is a comment button, prevent the default action and stop propagation
    if (isCommentButton) {
        event.preventDefault();
        event.stopPropagation();

        // Show custom message if provided
        if (typeof mqsaToast === 'function') {
            mqsaToast({
                message: 'You do not have permission to comment in this space',
                type: 'warning',
                duration: 5000
            });
        }

        return false;
    }
}, true); // Use capture phase to intercept events before they reach their targets
```

## Space Route Detection Methods

To accurately detect when a user is on a Fluent Community space page, use these methods:

```javascript
function isSpaceRoute(route) {
    // Check route name pattern
    if (route.name && (route.name.startsWith('space_') || route.name.includes('space'))) {
        return true;
    }

    // Check route path pattern
    if (route.path && (route.path.includes('/space/') || route.path.includes('/spaces/'))) {
        return true;
    }

    // Check for space-related data attributes in DOM
    if (document.querySelector('[data-route="space"], [data-space-id], [data-space], .fcom_space_header, .fcom_space_content_wrap')) {
        return true;
    }

    // Check for space-specific elements
    if (document.querySelector('.fcom_space_header, .fcom_space_content, .fcom_space_sidebar, .fcom_space_feed')) {
        return true;
    }

    // Check URL pattern as fallback
    const url = window.location.href;
    return url.includes('/community/space/') || url.includes('/spaces/') || url.includes('/space/');
}
```

## Getting Space ID from Route

To get the current space ID, use this enhanced method:

```javascript
function getSpaceIdFromRoute() {
    // Check if we're on a space route
    const route = vueApp.$route;
    if (!isSpaceRoute(route)) return null;
    
    // Try to get space ID from route params
    if (route.params) {
        // Check different possible param names
        if (route.params.space_id) {
            return parseInt(route.params.space_id);
        }
        
        if (route.params.id) {
            return parseInt(route.params.id);
        }
        
        // Some routes use slug instead of ID
        if (route.params.space) {
            // Try to get space ID from Vuex store
            const space = vueApp.$store.state.spaces.currentSpace;
            if (space && space.id) {
                return parseInt(space.id);
            }
        }
    }
    
    // Fallback: try to get space ID from Vuex store
    const space = vueApp.$store.state.spaces.currentSpace;
    if (space && space.id) {
        return parseInt(space.id);
    }
    
    return null;
}
```
