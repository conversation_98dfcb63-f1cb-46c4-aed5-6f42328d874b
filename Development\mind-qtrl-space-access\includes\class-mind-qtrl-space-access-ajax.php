<?php
class Mind_Qtrl_Space_Access_Ajax {

    /**
     * Check space membership status
     */
    public function check_membership() {
        check_ajax_referer('mqsa_frontend');

        $space_id = isset($_GET['space_id']) ? intval($_GET['space_id']) : 0;
        if (!$space_id) {
            wp_send_json_error();
        }

        $user_id = get_current_user_id();
        $is_member = false;

        // Check membership using Fluent Community's methods
        if (class_exists('\FluentCommunity\App\Models\SpaceUserPivot')) {
            $membership = \FluentCommunity\App\Models\SpaceUserPivot::where('space_id', $space_id)
                ->where('user_id', $user_id)
                ->first();
            
            $is_member = !empty($membership);
        }

        wp_send_json_success([
            'is_member' => $is_member
        ]);
    }
}