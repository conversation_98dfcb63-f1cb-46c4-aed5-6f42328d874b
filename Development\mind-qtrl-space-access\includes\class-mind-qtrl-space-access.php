<?php

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      0.0.1
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      0.0.1
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/includes
 */
class Mind_Qtrl_Space_Access {

	/**
	 * The loader that's responsible for maintaining and registering all hooks that power
	 * the plugin.
	 *
	 * @since    0.0.1
	 * @access   protected
	 * @var      Mind_Qtrl_Space_Access_Loader    $loader    Maintains and registers all hooks for the plugin.
	 */
	protected $loader;

	/**
	 * The unique identifier of this plugin.
	 *
	 * @since    0.0.1
	 * @access   protected
	 * @var      string    $plugin_name    The string used to uniquely identify this plugin.
	 */
	protected $plugin_name;

	/**
	 * The current version of the plugin.
	 *
	 * @since    0.0.1
	 * @access   protected
	 * @var      string    $version    The current version of the plugin.
	 */
	protected $version;

	/**
	 * Define the core functionality of the plugin.
	 *
	 * Set the plugin name and the plugin version that can be used throughout the plugin.
	 * Load the dependencies, define the locale, and set the hooks for the admin area and
	 * the public-facing side of the site.
	 *
	 * @since    0.0.1
	 */
	public function __construct() {
		// Load dependencies before any output is sent
		$this->load_dependencies();
		$this->set_locale();
		$this->define_admin_hooks();
		$this->define_public_hooks();
	}

	/**
	 * Load the required dependencies for this plugin.
	 *
	 * Include the following files that make up the plugin:
	 *
	 * - Mind_Qtrl_Space_Access_Loader. Orchestrates the hooks of the plugin.
	 * - Mind_Qtrl_Space_Access_i18n. Defines internationalization functionality.
	 * - Mind_Qtrl_Space_Access_Admin. Defines all hooks for the admin area.
	 * - Mind_Qtrl_Space_Access_Public. Defines all hooks for the public side of the site.
	 *
	 * Create an instance of the loader which will be used to register the hooks
	 * with WordPress.
	 *
	 * @since    0.0.1
	 * @access   private
	 */
	private function load_dependencies() {
		// Core plugin loader
		require_once MQSA_PLUGIN_PATH . 'includes/class-mind-qtrl-space-access-loader.php';
		
		// The class responsible for defining internationalization
		require_once MQSA_PLUGIN_PATH . 'includes/class-mind-qtrl-space-access-i18n.php';
		
		// Admin and public facing classes
		require_once MQSA_PLUGIN_PATH . 'admin/class-mind-qtrl-space-access-admin.php';
		require_once MQSA_PLUGIN_PATH . 'public/class-mind-qtrl-space-access-public.php';

		$this->loader = new Mind_Qtrl_Space_Access_Loader();
	}

	/**
	 * Define the locale for this plugin for internationalization.
	 *
	 * Uses the Mind_Qtrl_Space_Access_i18n class in order to set the domain and to register the hook
	 * with WordPress.
	 *
	 * @since    0.0.1
	 * @access   private
	 */
	private function set_locale() {

		$plugin_i18n = new Mind_Qtrl_Space_Access_i18n();

		$this->loader->add_action( 'plugins_loaded', $plugin_i18n, 'load_plugin_textdomain' );

	}

	/**
	 * Register all of the hooks related to the admin area functionality
	 * of the plugin.
	 *
	 * @since    0.0.1
	 * @access   private
	 */
	private function define_admin_hooks() {

		$plugin_admin = new Mind_Qtrl_Space_Access_Admin( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
		
		// Add admin menu
		$this->loader->add_action( 'admin_menu', $plugin_admin, 'add_admin_menu' );
		
		// Register settings
		$this->loader->add_action( 'admin_init', $plugin_admin, 'register_settings' );
		
		// Add AJAX handlers
		$this->loader->add_action( 'wp_ajax_mqsa_get_debug_log', $plugin_admin, 'ajax_get_debug_log' );
		$this->loader->add_action( 'wp_ajax_mqsa_clear_debug_log', $plugin_admin, 'ajax_clear_debug_log' );
		$this->loader->add_action( 'wp_ajax_mqsa_save_space_settings', $plugin_admin, 'ajax_save_space_settings' );
		$this->loader->add_action( 'wp_ajax_mqsa_get_space_settings', $plugin_admin, 'ajax_get_space_settings' );
		$this->loader->add_action( 'wp_ajax_mqsa_get_spaces', $plugin_admin, 'ajax_get_spaces' );
		$this->loader->add_action( 'wp_ajax_mqsa_get_crm_tags', $plugin_admin, 'ajax_get_crm_tags' );
		$this->loader->add_action( 'wp_ajax_mqsa_get_badges', $plugin_admin, 'ajax_get_badges' );
		$this->loader->add_action( 'wp_ajax_mqsa_get_leaderboard_levels', $plugin_admin, 'ajax_get_leaderboard_levels' );
	}

	/**
	 * Register all of the hooks related to the public-facing functionality
	 * of the plugin.
	 *
	 * @since    0.0.1
	 * @access   private
	 */
	private function define_public_hooks() {

		$plugin_public = new Mind_Qtrl_Space_Access_Public( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_styles' );
		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_scripts' );
		
		// Add hooks for Fluent Community integration
		$this->loader->add_action( 'fluent_community/portal_head', $plugin_public, 'inject_frontend_scripts' );
		
		// Add filters for space access control
		$this->loader->add_filter( 'fluent_community/space/join_status_for_private', $plugin_public, 'filter_space_join_status', 10, 3 );
		$this->loader->add_filter( 'fluent_community/space', $plugin_public, 'filter_space_access', 10, 1 );
	}

	/**
	 * Run the loader to execute all of the hooks with WordPress.
	 *
	 * @since    0.0.1
	 */
	public function run() {
		$this->loader->run();
	}

	/**
	 * The name of the plugin used to uniquely identify it within the context of
	 * WordPress and to define internationalization functionality.
	 *
	 * @since     0.0.1
	 * @return    string    The name of the plugin.
	 */
	public function get_plugin_name() {
		return $this->plugin_name;
	}

	/**
	 * The reference to the class that orchestrates the hooks with the plugin.
	 *
	 * @since     0.0.1
	 * @return    Mind_Qtrl_Space_Access_Loader    Orchestrates the hooks of the plugin.
	 */
	public function get_loader() {
		return $this->loader;
	}

	/**
	 * Retrieve the version number of the plugin.
	 *
	 * @since     0.0.1
	 * @return    string    The version number of the plugin.
	 */
	public function get_version() {
		return $this->version;
	}

}
