<?php
/**
 * Space Access Controller
 *
 * This class handles the access control logic for Fluent Community spaces.
 *
 * @since      1.0.0
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/includes
 */

class MQSA_Access_Controller {
    private static $instance = null;

    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->init_hooks();
    }

    private function init_hooks() {
        // Use Fluent Community's permission system
        add_filter('fluent_community/user/space/permissions', [$this, 'modify_space_permissions'], 10, 4);

        // Keep these filters for backward compatibility
        add_filter('fluent_community/can_view_space', [$this, 'check_view_access'], 10, 3);
        add_filter('fluent_community/can_join_space', [$this, 'check_join_access'], 10, 3);
        add_filter('fluent_community/can_post_in_space', [$this, 'check_post_access'], 10, 3);
        add_filter('fluent_community/can_react_in_space', [$this, 'check_reaction_access'], 10, 3);
        add_filter('fluent_community/can_comment_in_space', [$this, 'check_comment_access'], 10, 3);

        // Modify space data to include restriction messages
        add_filter('fluent_community/space_data', [$this, 'modify_space_data'], 10, 2);

        // Add filter for space access
        add_filter('fluent_community/space_access', [$this, 'check_space_access'], 10, 3);

        // Add filter for API endpoints
        add_filter('fluent_community/api/space/join', [$this, 'check_join_access'], 10, 3);

        // Add filter for portal settings menu
        add_filter('fluent_community/portal_settings_menu_items', [$this, 'add_portal_settings_menu_item'], 10, 1);
    }

    /**
     * Check if user can view a space
     *
     * @param bool $can_view Whether the user can view the space
     * @param int $space_id The space ID
     * @param int $user_id The user ID
     * @return bool Whether the user can view the space
     */
    public function check_view_access($can_view, $space_id, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space_id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Check if view restriction is enabled
            if (isset($space_settings['restrict_view']) && $space_settings['restrict_view'] === 'yes') {
                // Check if user meets the requirements
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    return false;
                }
            }

            // Check if unjoined member restriction is enabled
            if (isset($space_settings['restrict_view_unjoined']) && $space_settings['restrict_view_unjoined'] === 'yes') {
                // Check if user is a member of the space
                if (!$this->is_space_member($user_id, $space_id)) {
                    return false;
                }
            }
        }

        return $can_view;
    }

    /**
     * Check if user can join a space
     *
     * @param bool $can_join Whether the user can join the space
     * @param int $space_id The space ID
     * @param int $user_id The user ID
     * @return bool Whether the user can join the space
     */
    public function check_join_access($can_join, $space_id, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space_id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Check if join restriction is enabled
            if (isset($space_settings['restrict_join']) && $space_settings['restrict_join'] === 'yes') {
                // Check if user meets the requirements
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    return false;
                }
            }

            // Check if unjoined member restriction is enabled
            // Note: This is a bit redundant for join access since the user is trying to join,
            // but we include it for consistency and in case the logic is needed in the future
            if (isset($space_settings['restrict_join_unjoined']) && $space_settings['restrict_join_unjoined'] === 'yes') {
                // Check if user is a member of the space
                if (!$this->is_space_member($user_id, $space_id)) {
                    return false;
                }
            }
        }

        return $can_join;
    }

    /**
     * Check if user can post in a space
     *
     * @param bool $can_post Whether the user can post in the space
     * @param int $space_id The space ID
     * @param int $user_id The user ID
     * @return bool Whether the user can post in the space
     */
    public function check_post_access($can_post, $space_id, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space_id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Check if post restriction is enabled
            if (isset($space_settings['restrict_post']) && $space_settings['restrict_post'] === 'yes') {
                // Check if user meets the requirements
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    return false;
                }
            }

            // Check if unjoined member restriction is enabled
            if (isset($space_settings['restrict_post_unjoined']) && $space_settings['restrict_post_unjoined'] === 'yes') {
                // Check if user is a member of the space
                if (!$this->is_space_member($user_id, $space_id)) {
                    return false;
                }
            }
        }

        return $can_post;
    }

    /**
     * Check if user can react in a space
     *
     * @param bool $can_react Whether the user can react in the space
     * @param int $space_id The space ID
     * @param int $user_id The user ID
     * @return bool Whether the user can react in the space
     */
    public function check_reaction_access($can_react, $space_id, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space_id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Check if comment restriction is enabled (reactions are part of comments)
            if (isset($space_settings['restrict_comment']) && $space_settings['restrict_comment'] === 'yes') {
                // Check if user meets the requirements
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    return false;
                }
            }

            // Check if unjoined member restriction is enabled
            if (isset($space_settings['restrict_comment_unjoined']) && $space_settings['restrict_comment_unjoined'] === 'yes') {
                // Check if user is a member of the space
                if (!$this->is_space_member($user_id, $space_id)) {
                    return false;
                }
            }
        }

        return $can_react;
    }

    /**
     * Check if user can comment in a space
     *
     * @param bool $can_comment Whether the user can comment in the space
     * @param int $space_id The space ID
     * @param int $user_id The user ID
     * @return bool Whether the user can comment in the space
     */
    public function check_comment_access($can_comment, $space_id, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space_id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Check if comment restriction is enabled
            if (isset($space_settings['restrict_comment']) && $space_settings['restrict_comment'] === 'yes') {
                // Check if user meets the requirements
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    return false;
                }
            }

            // Check if unjoined member restriction is enabled
            if (isset($space_settings['restrict_comment_unjoined']) && $space_settings['restrict_comment_unjoined'] === 'yes') {
                // Check if user is a member of the space
                if (!$this->is_space_member($user_id, $space_id)) {
                    return false;
                }
            }
        }

        return $can_comment;
    }

    /**
     * Modify space permissions using Fluent Community's permission system
     *
     * @param array $permissions The current permissions
     * @param object $space The space object
     * @param string $role The user's role in the space
     * @param object $user The user object
     * @return array The modified permissions
     */
    public function modify_space_permissions($permissions, $space, $role, $user) {
        // Get space settings
        $space_settings = $this->get_space_settings($space->id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            $user_id = $user->ID;

            // Skip restrictions for space admins and moderators
            if (in_array($role, ['admin', 'moderator'])) {
                return $permissions;
            }

            // Apply view restrictions
            if (isset($space_settings['restrict_view']) && $space_settings['restrict_view'] === 'yes') {
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    $permissions['can_view_posts'] = false;
                    $permissions['can_view_info'] = false;
                }
            }

            // Apply unjoined member view restrictions
            if (isset($space_settings['restrict_view_unjoined']) && $space_settings['restrict_view_unjoined'] === 'yes' && !$permissions['is_member']) {
                $permissions['can_view_posts'] = false;
                $permissions['can_view_info'] = false;
            }

            // Apply post restrictions
            if (isset($space_settings['restrict_post']) && $space_settings['restrict_post'] === 'yes') {
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    $permissions['can_create_post'] = false;
                }
            }

            // Apply unjoined member post restrictions
            if (isset($space_settings['restrict_post_unjoined']) && $space_settings['restrict_post_unjoined'] === 'yes' && !$permissions['is_member']) {
                $permissions['can_create_post'] = false;
            }

            // Apply comment restrictions
            if (isset($space_settings['restrict_comment']) && $space_settings['restrict_comment'] === 'yes') {
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    // Set can_comment permission if it exists
                    if (isset($permissions['can_comment'])) {
                        $permissions['can_comment'] = false;
                    }
                    // Also restrict reactions which are part of comments
                    if (isset($permissions['can_react'])) {
                        $permissions['can_react'] = false;
                    }
                }
            }

            // Apply unjoined member comment restrictions
            if (isset($space_settings['restrict_comment_unjoined']) && $space_settings['restrict_comment_unjoined'] === 'yes' && !$permissions['is_member']) {
                // Set can_comment permission if it exists
                if (isset($permissions['can_comment'])) {
                    $permissions['can_comment'] = false;
                }
                // Also restrict reactions which are part of comments
                if (isset($permissions['can_react'])) {
                    $permissions['can_react'] = false;
                }
            }
        }

        return $permissions;
    }

    /**
     * Modify space data
     *
     * @param object $space The space object
     * @param int $user_id The user ID
     * @return object The modified space object
     */
    public function modify_space_data($space, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space->id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Add restriction settings to space object for frontend use
            $space->mqsa_settings = [
                'enable_restrictions' => $space_settings['enable_restrictions'],
                'restrict_view' => $space_settings['restrict_view'] ?? 'no',
                'restrict_join' => $space_settings['restrict_join'] ?? 'no',
                'restrict_post' => $space_settings['restrict_post'] ?? 'no',
                'restrict_comment' => $space_settings['restrict_comment'] ?? 'no',
                'restrict_view_unjoined' => $space_settings['restrict_view_unjoined'] ?? 'no',
                'restrict_join_unjoined' => $space_settings['restrict_join_unjoined'] ?? 'no',
                'restrict_post_unjoined' => $space_settings['restrict_post_unjoined'] ?? 'no',
                'restrict_comment_unjoined' => $space_settings['restrict_comment_unjoined'] ?? 'no',
                'view_message' => $space_settings['view_message'] ?? '',
                'join_message' => $space_settings['join_message'] ?? '',
                'post_message' => $space_settings['post_message'] ?? '',
                'comment_message' => $space_settings['comment_message'] ?? ''
            ];

            // Check if user meets requirements
            $space->mqsa_user_meets_requirements = $this->user_meets_requirements($user_id, $space_settings);
        }

        return $space;
    }

    /**
     * Check space access
     *
     * @param bool $has_access Whether the user has access to the space
     * @param int $space_id The space ID
     * @param int $user_id The user ID
     * @return bool Whether the user has access to the space
     */
    public function check_space_access($has_access, $space_id, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space_id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Check if view restriction is enabled
            if (isset($space_settings['restrict_view']) && $space_settings['restrict_view'] === 'yes') {
                // Check if user meets the requirements
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    return false;
                }
            }
        }

        return $has_access;
    }

    /**
     * Add menu item to Fluent Community portal admin settings
     *
     * @param array $menu_items The current menu items
     * @return array The filtered menu items
     */
    public function add_portal_settings_menu_item($menu_items) {
        $menu_items['mqsa_settings'] = [
            'label' => __('Space Access', 'mind-qtrl-space-access'),
            'route' => 'mqsa-settings',
            'icon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>'
        ];

        return $menu_items;
    }

    /**
     * Get space settings
     *
     * @param int $space_id The space ID
     * @return array The space settings
     */
    private function get_space_settings($space_id) {
        // Use cache if available
        if (function_exists('mqsa_cache')) {
            $cache_key = 'space_settings_' . $space_id;
            $cached_settings = mqsa_cache()->get($cache_key);

            if ($cached_settings !== null) {
                return $cached_settings;
            }
        }

        // Try to get settings from Fluent Community's custom meta storage first
        $fc_settings = $this->get_fc_space_meta($space_id, 'mqsa_settings');
        if (!empty($fc_settings)) {
            // Cache the settings
            if (function_exists('mqsa_cache')) {
                $cache_key = 'space_settings_' . $space_id;
                mqsa_cache()->set($cache_key, $fc_settings);
            }
            return $fc_settings;
        }

        // Fall back to WordPress options table
        $all_settings = get_option('mqsa_space_settings', []);
        $settings = isset($all_settings[$space_id]) ? $all_settings[$space_id] : [];

        // If settings exist in WP options, migrate them to Fluent Community's storage
        if (!empty($settings)) {
            $this->update_fc_space_meta($space_id, 'mqsa_settings', $settings);
        }

        // Cache the settings
        if (function_exists('mqsa_cache')) {
            $cache_key = 'space_settings_' . $space_id;
            mqsa_cache()->set($cache_key, $settings);
        }

        return $settings;
    }

    /**
     * Get space meta from Fluent Community's storage
     *
     * @param int $space_id The space ID
     * @param string $key The meta key
     * @param mixed $default The default value
     * @return mixed The meta value
     */
    private function get_fc_space_meta($space_id, $key, $default = []) {
        // Check if Fluent Community's Helper class exists
        if (class_exists('\FluentCommunity\App\Services\Helper')) {
            try {
                return \FluentCommunity\App\Services\Helper::getSpaceMeta($space_id, $key, $default);
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error getting space meta: ' . $e->getMessage());
                }
            }
        }

        // If Space model exists, try to use it directly
        if (class_exists('\FluentCommunity\App\Models\Space')) {
            try {
                $space = \FluentCommunity\App\Models\Space::find($space_id);
                if ($space) {
                    return $space->getCustomMeta($key, $default);
                }
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error getting space meta from model: ' . $e->getMessage());
                }
            }
        }

        return $default;
    }

    /**
     * Update space meta in Fluent Community's storage
     *
     * @param int $space_id The space ID
     * @param string $key The meta key
     * @param mixed $value The meta value
     * @return bool Whether the update was successful
     */
    private function update_fc_space_meta($space_id, $key, $value) {
        // Check if Fluent Community's Helper class exists
        if (class_exists('\FluentCommunity\App\Services\Helper')) {
            try {
                \FluentCommunity\App\Services\Helper::updateSpaceMeta($space_id, $key, $value);
                return true;
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error updating space meta: ' . $e->getMessage());
                }
            }
        }

        // If Space model exists, try to use it directly
        if (class_exists('\FluentCommunity\App\Models\Space')) {
            try {
                $space = \FluentCommunity\App\Models\Space::find($space_id);
                if ($space) {
                    $space->updateCustomMeta($key, $value);
                    return true;
                }
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error updating space meta from model: ' . $e->getMessage());
                }
            }
        }

        return false;
    }

    /**
     * Check if user meets the requirements
     *
     * @param int $user_id The user ID
     * @param array $space_settings The space settings
     * @return bool Whether the user meets the requirements
     */
    private function user_meets_requirements($user_id, $space_settings) {
        // If no requirements are set, user meets requirements
        if (empty($space_settings['access_requirements']) || $space_settings['access_requirements'] === 'none') {
            return true;
        }

        // Check based on requirement type
        switch ($space_settings['access_requirements']) {
            case 'crm_tags':
                return $this->check_crm_tags($user_id, $space_settings);

            case 'badges':
                return $this->check_badges($user_id, $space_settings);

            case 'leaderboard':
                return $this->check_leaderboard($user_id, $space_settings);

            default:
                return true;
        }
    }

    /**
     * Check if user is a member of a space
     *
     * @param int $user_id The user ID
     * @param int $space_id The space ID
     * @return bool Whether the user is a member of the space
     */
    private function is_space_member($user_id, $space_id) {
        // Check if Fluent Community's SpaceMember model exists
        if (class_exists('\\FluentCommunity\\App\\Models\\SpaceMember')) {
            try {
                // Check if user is a member of the space using Fluent Community's model
                $is_member = \FluentCommunity\App\Models\SpaceMember::where('user_id', $user_id)
                    ->where('space_id', $space_id)
                    ->where('status', 'active')
                    ->exists();

                return $is_member;
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error checking space membership from Fluent Community: ' . $e->getMessage());
                }
                return false;
            }
        } else {
            // Fallback to direct database query if Fluent Community model is not available
            global $wpdb;
            $space_members_table = $wpdb->prefix . 'fc_space_members';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$space_members_table'") === $space_members_table;

            if ($table_exists) {
                // Check if user is a member of the space
                $is_member = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $space_members_table WHERE user_id = %d AND space_id = %d AND status = 'active'",
                    $user_id,
                    $space_id
                ));

                return (int)$is_member > 0;
            }

            // No valid way to check membership
            return false;
        }
    }

    /**
     * Check if user has required CRM tags
     *
     * @param int $user_id The user ID
     * @param array $space_settings The space settings
     * @return bool Whether the user has the required tags
     */
    private function check_crm_tags($user_id, $space_settings) {
        // Check if FluentCRM is active
        if (!defined('FLUENTCRM')) {
            return false;
        }

        // Get required tags
        $required_tags = !empty($space_settings['crm_tags']) ? explode(',', $space_settings['crm_tags']) : [];
        if (empty($required_tags)) {
            return true;
        }

        // Get user's email
        $user = get_userdata($user_id);
        if (!$user) {
            return false;
        }

        // Get contact
        $contact = FluentCrmApi('contacts')->getContact($user->user_email);
        if (!$contact) {
            return false;
        }

        // Get contact's tags
        $contact_tags = $contact->tags;
        if (empty($contact_tags)) {
            return false;
        }

        // Extract tag IDs
        $contact_tag_ids = array_map(function($tag) {
            return $tag->id;
        }, $contact_tags);

        // Check if user has any of the required tags
        foreach ($required_tags as $tag_id) {
            if (in_array($tag_id, $contact_tag_ids)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has required badges
     *
     * @param int $user_id The user ID
     * @param array $space_settings The space settings
     * @return bool Whether the user has the required badges
     */
    private function check_badges($user_id, $space_settings) {
        // Get required badges
        $required_badges = !empty($space_settings['badges']) ? explode(',', $space_settings['badges']) : [];
        if (empty($required_badges)) {
            return true;
        }

        // First try to use the Fluent Community API if available
        if (function_exists('FluentCommunity')) {
            try {
                // Get user's badges using Fluent Community's API
                $api = FluentCommunity('api');
                if ($api && method_exists($api, 'getUserBadges')) {
                    $user_badges = $api->getUserBadges($user_id, ['status' => 1]);

                    if (!empty($user_badges)) {
                        // Extract badge IDs - handle different API response formats
                        $user_badge_ids = array_map(function($badge) {
                            // Different versions might return different structures
                            if (isset($badge['badge_id'])) {
                                return $badge['badge_id'];
                            } elseif (isset($badge->badge_id)) {
                                return $badge->badge_id;
                            } elseif (isset($badge['id'])) {
                                return $badge['id'];
                            } elseif (isset($badge->id)) {
                                return $badge->id;
                            } else {
                                return 0; // Fallback
                            }
                        }, $user_badges);

                        // Check if user has any of the required badges
                        foreach ($required_badges as $badge_id) {
                            if (in_array($badge_id, $user_badge_ids)) {
                                return true;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error checking user badges from Fluent Community API: ' . $e->getMessage());
                }
            }

            // Try alternative API methods if the primary one failed
            try {
                // Try alternative method names that might exist in different versions
                $methodsToTry = ['getAllUserBadges', 'listUserBadges', 'fetchUserBadges', 'getUserBadgesByUserId'];

                foreach ($methodsToTry as $method) {
                    if (method_exists($api, $method)) {
                        $user_badges = $api->$method($user_id, ['status' => 1]);

                        if (!empty($user_badges)) {
                            // Extract badge IDs - handle different API response formats
                            $user_badge_ids = array_map(function($badge) {
                                // Different versions might return different structures
                                if (isset($badge['badge_id'])) {
                                    return $badge['badge_id'];
                                } elseif (isset($badge->badge_id)) {
                                    return $badge->badge_id;
                                } elseif (isset($badge['id'])) {
                                    return $badge['id'];
                                } elseif (isset($badge->id)) {
                                    return $badge->id;
                                } else {
                                    return 0; // Fallback
                                }
                            }, $user_badges);

                            // Check if user has any of the required badges
                            foreach ($required_badges as $badge_id) {
                                if (in_array($badge_id, $user_badge_ids)) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error checking user badges from Fluent Community alternative API methods: ' . $e->getMessage());
                }
            }
        }

        // If API check failed, try using the model directly
        // Try different model namespaces that might exist in different versions
        $modelClasses = [
            '\\FluentCommunity\\App\\Models\\UserBadge',
            '\\FluentCommunityPro\\App\\Models\\UserBadge',
            '\\FluentCommunity\\App\\Modules\\UserBadge\\Models\\UserBadge',
            '\\FluentCommunityPro\\App\\Modules\\UserBadge\\Models\\UserBadge'
        ];

        foreach ($modelClasses as $modelClass) {
            if (class_exists($modelClass)) {
                try {
                    // Get user's badges using Fluent Community's model
                    $user_badges = $modelClass::where('user_id', $user_id)
                        ->where('status', 1)
                        ->get();

                    if (!empty($user_badges)) {
                        // Try to use pluck method if available (Laravel/Eloquent style)
                        if (method_exists($user_badges, 'pluck')) {
                            $user_badge_ids = $user_badges->pluck('badge_id')->toArray();
                        } else {
                            // Fallback to manual extraction
                            $user_badge_ids = [];
                            foreach ($user_badges as $badge) {
                                if (isset($badge->badge_id)) {
                                    $user_badge_ids[] = $badge->badge_id;
                                } elseif (isset($badge['badge_id'])) {
                                    $user_badge_ids[] = $badge['badge_id'];
                                }
                            }
                        }

                        // Check if user has any of the required badges
                        foreach ($required_badges as $badge_id) {
                            if (in_array($badge_id, $user_badge_ids)) {
                                return true;
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Log error if available
                    if (function_exists('mqsa_logger')) {
                        mqsa_logger()->error('Error checking user badges from Fluent Community model ' . $modelClass . ': ' . $e->getMessage());
                    }
                }
            }
        }

        // Fallback to direct database query if previous methods failed
        global $wpdb;

        // Try different possible table names based on different Fluent Community versions
        $possible_tables = [
            $wpdb->prefix . 'fc_user_badges',
            $wpdb->prefix . 'fcom_user_badges',
            $wpdb->prefix . 'fluent_community_user_badges'
        ];

        foreach ($possible_tables as $user_badges_table) {
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$user_badges_table'") === $user_badges_table;

            if ($table_exists) {
                // Check table structure to determine correct column names
                $columns = $wpdb->get_results("SHOW COLUMNS FROM $user_badges_table");
                $column_names = array_map(function($col) { return $col->Field; }, $columns);

                $badge_id_column = in_array('badge_id', $column_names) ? 'badge_id' : 'id';
                $status_column = in_array('status', $column_names) ? 'status' : 'is_active';

                // Get user's badges
                $query = $wpdb->prepare(
                    "SELECT $badge_id_column FROM $user_badges_table WHERE user_id = %d AND $status_column = 1",
                    $user_id
                );

                $user_badge_ids = $wpdb->get_col($query);

                if (!empty($user_badge_ids)) {
                    // Check if user has any of the required badges
                    foreach ($required_badges as $badge_id) {
                        if (in_array($badge_id, $user_badge_ids)) {
                            return true;
                        }
                    }
                }
            }
        }

        // Try checking user meta for badges as a last resort
        $user_meta_badges = get_user_meta($user_id, 'fcom_user_badges', true);
        if (!empty($user_meta_badges) && is_array($user_meta_badges)) {
            $user_badge_ids = array_map(function($badge) {
                if (is_array($badge) && isset($badge['badge_id'])) {
                    return $badge['badge_id'];
                } elseif (is_object($badge) && isset($badge->badge_id)) {
                    return $badge->badge_id;
                } elseif (is_scalar($badge)) {
                    return $badge;
                }
                return 0;
            }, $user_meta_badges);

            // Check if user has any of the required badges
            foreach ($required_badges as $badge_id) {
                if (in_array($badge_id, $user_badge_ids)) {
                    return true;
                }
            }
        }

        // If we got here, the user doesn't have any of the required badges
        return false;
    }

    /**
     * Check if user meets leaderboard level requirements
     *
     * @param int $user_id The user ID
     * @param array $space_settings The space settings
     * @return bool Whether the user meets the leaderboard requirements
     */
    private function check_leaderboard($user_id, $space_settings) {
        // Get required leaderboard levels
        $required_levels = !empty($space_settings['leaderboard_levels']) ? explode(',', $space_settings['leaderboard_levels']) : [];
        if (empty($required_levels)) {
            return true;
        }

        // First try to use the Fluent Community API if available
        if (function_exists('FluentCommunity')) {
            try {
                // Get user's leader badges using Fluent Community's API
                $api = FluentCommunity('api');
                if ($api && method_exists($api, 'getUserLeaderBadges')) {
                    $user_leader_badges = $api->getUserLeaderBadges($user_id, ['status' => 1]);

                    if (!empty($user_leader_badges)) {
                        // Extract leader badge IDs - handle different API response formats
                        $user_leader_badge_ids = array_map(function($badge) {
                            // Different versions might return different structures
                            if (isset($badge['badge_id'])) {
                                return $badge['badge_id'];
                            } elseif (isset($badge->badge_id)) {
                                return $badge->badge_id;
                            } elseif (isset($badge['id'])) {
                                return $badge['id'];
                            } elseif (isset($badge->id)) {
                                return $badge->id;
                            } elseif (isset($badge['slug'])) {
                                return $badge['slug'];
                            } elseif (isset($badge->slug)) {
                                return $badge->slug;
                            } else {
                                return 0; // Fallback
                            }
                        }, $user_leader_badges);

                        // Check if user has any of the required leader badges
                        foreach ($required_levels as $badge_id) {
                            if (in_array($badge_id, $user_leader_badge_ids)) {
                                return true;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error checking user leader badges from Fluent Community API: ' . $e->getMessage());
                }
            }

            // Try alternative API methods if the primary one failed
            try {
                // Try alternative method names that might exist in different versions
                $methodsToTry = ['getAllUserLeaderBadges', 'listUserLeaderBadges', 'fetchUserLeaderBadges', 'getUserLeaderLevel', 'getUserLevel'];

                foreach ($methodsToTry as $method) {
                    if (method_exists($api, $method)) {
                        $user_leader_badges = $api->$method($user_id, ['status' => 1]);

                        if (!empty($user_leader_badges)) {
                            // Handle different return types - some methods might return a single level object
                            if (!is_array($user_leader_badges) && !is_object($user_leader_badges)) {
                                continue;
                            }

                            // If it's a single object, convert to array
                            if (is_object($user_leader_badges) && !method_exists($user_leader_badges, 'toArray')) {
                                if (isset($user_leader_badges->slug)) {
                                    // Check if user's level is in the required levels
                                    foreach ($required_levels as $level_id) {
                                        if ($level_id == $user_leader_badges->slug) {
                                            return true;
                                        }
                                    }
                                }
                                continue;
                            }

                            // Extract leader badge IDs - handle different API response formats
                            $user_leader_badge_ids = array_map(function($badge) {
                                // Different versions might return different structures
                                if (isset($badge['badge_id'])) {
                                    return $badge['badge_id'];
                                } elseif (isset($badge->badge_id)) {
                                    return $badge->badge_id;
                                } elseif (isset($badge['id'])) {
                                    return $badge['id'];
                                } elseif (isset($badge->id)) {
                                    return $badge->id;
                                } elseif (isset($badge['slug'])) {
                                    return $badge['slug'];
                                } elseif (isset($badge->slug)) {
                                    return $badge->slug;
                                } else {
                                    return 0; // Fallback
                                }
                            }, is_array($user_leader_badges) ? $user_leader_badges : [$user_leader_badges]);

                            // Check if user has any of the required leader badges
                            foreach ($required_levels as $badge_id) {
                                if (in_array($badge_id, $user_leader_badge_ids)) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error checking user leader badges from Fluent Community alternative API methods: ' . $e->getMessage());
                }
            }
        }

        // If API check failed, try using the model directly
        // Try different model namespaces that might exist in different versions
        $modelClasses = [
            '\\FluentCommunity\\App\\Models\\UserLeaderBadge',
            '\\FluentCommunityPro\\App\\Models\\UserLeaderBadge',
            '\\FluentCommunity\\App\\Modules\\LeaderBoard\\Models\\UserLeaderBadge',
            '\\FluentCommunityPro\\App\\Modules\\LeaderBoard\\Models\\UserLeaderBadge'
        ];

        foreach ($modelClasses as $modelClass) {
            if (class_exists($modelClass)) {
                try {
                    // Get user's leader badges using Fluent Community's model
                    $user_leader_badges = $modelClass::where('user_id', $user_id)
                        ->where('status', 1)
                        ->get();

                    if (!empty($user_leader_badges)) {
                        // Try to use pluck method if available (Laravel/Eloquent style)
                        if (method_exists($user_leader_badges, 'pluck')) {
                            $user_leader_badge_ids = $user_leader_badges->pluck('badge_id')->toArray();
                        } else {
                            // Fallback to manual extraction
                            $user_leader_badge_ids = [];
                            foreach ($user_leader_badges as $badge) {
                                if (isset($badge->badge_id)) {
                                    $user_leader_badge_ids[] = $badge->badge_id;
                                } elseif (isset($badge['badge_id'])) {
                                    $user_leader_badge_ids[] = $badge['badge_id'];
                                } elseif (isset($badge->slug)) {
                                    $user_leader_badge_ids[] = $badge->slug;
                                } elseif (isset($badge['slug'])) {
                                    $user_leader_badge_ids[] = $badge['slug'];
                                }
                            }
                        }

                        // Check if user has any of the required leader badges
                        foreach ($required_levels as $badge_id) {
                            if (in_array($badge_id, $user_leader_badge_ids)) {
                                return true;
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Log error if available
                    if (function_exists('mqsa_logger')) {
                        mqsa_logger()->error('Error checking user leader badges from Fluent Community model ' . $modelClass . ': ' . $e->getMessage());
                    }
                }
            }
        }

        // Try checking XProfile for user level
        if (class_exists('\\FluentCommunity\\App\\Models\\XProfile')) {
            try {
                $xprofile = \FluentCommunity\App\Models\XProfile::find($user_id);
                if ($xprofile && isset($xprofile->meta) && is_array($xprofile->meta)) {
                    if (isset($xprofile->meta['leader_level'])) {
                        $level_slug = $xprofile->meta['leader_level'];
                        foreach ($required_levels as $level_id) {
                            if ($level_id == $level_slug) {
                                return true;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Log error if available
                if (function_exists('mqsa_logger')) {
                    mqsa_logger()->error('Error checking XProfile for leader level: ' . $e->getMessage());
                }
            }
        }

        // Fallback to direct database query if previous methods failed
        global $wpdb;

        // Try different possible table names based on different Fluent Community versions
        $possible_tables = [
            $wpdb->prefix . 'fc_user_leader_badges',
            $wpdb->prefix . 'fcom_user_leader_badges',
            $wpdb->prefix . 'fluent_community_user_leader_badges'
        ];

        foreach ($possible_tables as $user_leader_badges_table) {
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$user_leader_badges_table'") === $user_leader_badges_table;

            if ($table_exists) {
                // Check table structure to determine correct column names
                $columns = $wpdb->get_results("SHOW COLUMNS FROM $user_leader_badges_table");
                $column_names = array_map(function($col) { return $col->Field; }, $columns);

                $badge_id_column = in_array('badge_id', $column_names) ? 'badge_id' :
                                  (in_array('level_id', $column_names) ? 'level_id' : 'id');
                $status_column = in_array('status', $column_names) ? 'status' : 'is_active';

                // Get user's leader badges
                $query = $wpdb->prepare(
                    "SELECT $badge_id_column FROM $user_leader_badges_table WHERE user_id = %d AND $status_column = 1",
                    $user_id
                );

                $user_leader_badge_ids = $wpdb->get_col($query);

                if (!empty($user_leader_badge_ids)) {
                    // Check if user has any of the required leader badges
                    foreach ($required_levels as $badge_id) {
                        if (in_array($badge_id, $user_leader_badge_ids)) {
                            return true;
                        }
                    }
                }
            }
        }

        // Try checking user meta for leader badges as a last resort
        $user_meta_leader_level = get_user_meta($user_id, 'fcom_leader_level', true);
        if (!empty($user_meta_leader_level)) {
            foreach ($required_levels as $level_id) {
                if ($level_id == $user_meta_leader_level) {
                    return true;
                }
            }
        }

        // Try checking usermeta for leaderboard data
        $user_meta_leaderboard = get_user_meta($user_id, 'fcom_leaderboard_data', true);
        if (!empty($user_meta_leaderboard) && is_array($user_meta_leaderboard)) {
            if (isset($user_meta_leaderboard['level']) && !empty($user_meta_leaderboard['level'])) {
                $level = $user_meta_leaderboard['level'];
                $level_slug = is_array($level) ? (isset($level['slug']) ? $level['slug'] : '') : $level;

                foreach ($required_levels as $level_id) {
                    if ($level_id == $level_slug) {
                        return true;
                    }
                }
            }
        }

        // If we got here, the user doesn't have any of the required leader badges
        return false;
    }
}

