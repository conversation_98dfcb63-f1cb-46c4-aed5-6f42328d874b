<?php
/**
 * Cache helper class for Mind Qtrl Space Access
 *
 * This class provides caching functionality for the plugin to improve performance.
 *
 * @since      1.0.0
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/includes
 */

class MQSA_Cache {
    /**
     * The single instance of the class.
     *
     * @since    1.0.0
     * @access   private
     * @var      MQSA_Cache    $instance    The single instance of the class.
     */
    private static $instance = null;

    /**
     * The cache data.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $cache    The cache data.
     */
    private $cache = [];

    /**
     * The transient prefix.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $transient_prefix    The transient prefix.
     */
    private $transient_prefix = 'mqsa_cache_';

    /**
     * Main Cache Instance.
     *
     * Ensures only one instance of the cache is loaded or can be loaded.
     *
     * @since    1.0.0
     * @return   MQSA_Cache    Main instance.
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor.
     *
     * @since    1.0.0
     */
    private function __construct() {
        // Add cleanup action
        add_action('mqsa_cleanup_cache', [$this, 'cleanup_transients']);
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('mqsa_cleanup_cache')) {
            wp_schedule_event(time(), 'daily', 'mqsa_cleanup_cache');
        }
    }

    /**
     * Get a value from the cache.
     *
     * @since    1.0.0
     * @param    string    $key      The cache key.
     * @param    mixed     $default  The default value if the key doesn't exist.
     * @return   mixed     The cached value or default.
     */
    public function get($key, $default = null) {
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }
        
        return $default;
    }

    /**
     * Set a value in the cache.
     *
     * @since    1.0.0
     * @param    string    $key      The cache key.
     * @param    mixed     $value    The value to cache.
     */
    public function set($key, $value) {
        $this->cache[$key] = $value;
    }

    /**
     * Check if a key exists in the cache.
     *
     * @since    1.0.0
     * @param    string    $key    The cache key.
     * @return   bool      Whether the key exists.
     */
    public function has($key) {
        return isset($this->cache[$key]);
    }

    /**
     * Remove a value from the cache.
     *
     * @since    1.0.0
     * @param    string    $key    The cache key.
     */
    public function delete($key) {
        if (isset($this->cache[$key])) {
            unset($this->cache[$key]);
        }
    }

    /**
     * Clear the entire cache.
     *
     * @since    1.0.0
     */
    public function clear() {
        $this->cache = [];
    }

    /**
     * Remember a value in the cache.
     *
     * If the key doesn't exist in the cache, the callback will be executed
     * and its return value will be stored in the cache and returned.
     *
     * @since    1.0.0
     * @param    string    $key       The cache key.
     * @param    callable  $callback  The callback to execute if the key doesn't exist.
     * @return   mixed     The cached value.
     */
    public function remember($key, $callback) {
        if (!$this->has($key)) {
            $value = call_user_func($callback);
            $this->set($key, $value);
        }
        
        return $this->get($key);
    }

    /**
     * Get a value from the transient cache.
     *
     * @since    1.0.0
     * @param    string    $key      The cache key.
     * @param    mixed     $default  The default value if the key doesn't exist.
     * @return   mixed     The cached value or default.
     */
    public function get_transient($key, $default = null) {
        $value = get_transient($this->transient_prefix . $key);
        
        if ($value === false) {
            return $default;
        }
        
        return $value;
    }

    /**
     * Set a value in the transient cache.
     *
     * @since    1.0.0
     * @param    string    $key      The cache key.
     * @param    mixed     $value    The value to cache.
     * @param    int       $expiration  The expiration time in seconds.
     */
    public function set_transient($key, $value, $expiration = 3600) {
        set_transient($this->transient_prefix . $key, $value, $expiration);
    }

    /**
     * Delete a value from the transient cache.
     *
     * @since    1.0.0
     * @param    string    $key    The cache key.
     */
    public function delete_transient($key) {
        delete_transient($this->transient_prefix . $key);
    }

    /**
     * Remember a value in the transient cache.
     *
     * If the key doesn't exist in the transient cache, the callback will be executed
     * and its return value will be stored in the transient cache and returned.
     *
     * @since    1.0.0
     * @param    string    $key         The cache key.
     * @param    int       $expiration  The expiration time in seconds.
     * @param    callable  $callback    The callback to execute if the key doesn't exist.
     * @return   mixed     The cached value.
     */
    public function remember_transient($key, $expiration, $callback) {
        $value = $this->get_transient($key);
        
        if ($value === null) {
            $value = call_user_func($callback);
            $this->set_transient($key, $value, $expiration);
        }
        
        return $value;
    }

    /**
     * Clean up expired transients.
     *
     * @since    1.0.0
     */
    public function cleanup_transients() {
        global $wpdb;
        
        // Get all transients with our prefix
        $transients = $wpdb->get_col(
            $wpdb->prepare(
                "SELECT option_name FROM $wpdb->options WHERE option_name LIKE %s",
                '_transient_' . $this->transient_prefix . '%'
            )
        );
        
        // Get all timeout transients with our prefix
        $timeout_transients = $wpdb->get_col(
            $wpdb->prepare(
                "SELECT option_name FROM $wpdb->options WHERE option_name LIKE %s",
                '_transient_timeout_' . $this->transient_prefix . '%'
            )
        );
        
        // Count of deleted transients
        $deleted = 0;
        
        // Check each timeout transient
        foreach ($timeout_transients as $timeout_transient) {
            // Get the transient name without the timeout prefix
            $transient_name = str_replace('_transient_timeout_', '', $timeout_transient);
            
            // Get the expiration time
            $expiration = get_option($timeout_transient);
            
            // If the transient has expired, delete it
            if ($expiration < time()) {
                delete_transient(str_replace($this->transient_prefix, '', $transient_name));
                $deleted++;
            }
        }
        
        // Log the cleanup
        if ($deleted > 0 && function_exists('mqsa_logger')) {
            mqsa_logger()->info('Cleaned up ' . $deleted . ' expired transients');
        }
    }
}

/**
 * Main instance of cache.
 *
 * @since    1.0.0
 * @return   MQSA_Cache    The main instance.
 */
function mqsa_cache() {
    return MQSA_Cache::instance();
}
