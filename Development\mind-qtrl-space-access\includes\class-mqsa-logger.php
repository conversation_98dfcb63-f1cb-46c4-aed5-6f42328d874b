<?php
/**
 * Logger class for Mind Qtrl Space Access
 *
 * This class handles logging for the plugin, providing methods for logging
 * errors, warnings, info, and debug messages.
 *
 * @since      1.0.0
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/includes
 */

class MQSA_Logger {
    /**
     * Log levels
     */
    const ERROR = 'error';
    const WARNING = 'warning';
    const INFO = 'info';
    const DEBUG = 'debug';

    /**
     * The single instance of the class.
     *
     * @since    1.0.0
     * @access   private
     * @var      MQSA_Logger    $instance    The single instance of the class.
     */
    private static $instance = null;

    /**
     * Whether debug mode is enabled.
     *
     * @since    1.0.0
     * @access   private
     * @var      bool    $debug_mode    Whether debug mode is enabled.
     */
    private $debug_mode = false;

    /**
     * The log retention period in days.
     *
     * @since    1.0.0
     * @access   private
     * @var      int    $log_retention    The log retention period in days.
     */
    private $log_retention = 7;

    /**
     * Main Logger Instance.
     *
     * Ensures only one instance of the logger is loaded or can be loaded.
     *
     * @since    1.0.0
     * @return   MQSA_Logger    Main instance.
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor.
     *
     * @since    1.0.0
     */
    private function __construct() {
        $settings = get_option('mqsa_settings', []);
        $this->debug_mode = isset($settings['debug_mode']) && $settings['debug_mode'] === 'yes';
        $this->log_retention = isset($settings['log_retention']) ? intval($settings['log_retention']) : 7;
        
        // Apply filter for log retention
        $this->log_retention = apply_filters('mqsa_debug_log_retention', $this->log_retention);
        
        // Schedule cleanup
        if (!wp_next_scheduled('mqsa_cleanup_logs')) {
            wp_schedule_event(time(), 'daily', 'mqsa_cleanup_logs');
        }
        
        // Add cleanup action
        add_action('mqsa_cleanup_logs', [$this, 'cleanup_logs']);
    }

    /**
     * Log an error message.
     *
     * @since    1.0.0
     * @param    string    $message    The message to log.
     * @param    array     $context    Additional context data.
     */
    public function error($message, $context = []) {
        $this->log(self::ERROR, $message, $context);
    }

    /**
     * Log a warning message.
     *
     * @since    1.0.0
     * @param    string    $message    The message to log.
     * @param    array     $context    Additional context data.
     */
    public function warning($message, $context = []) {
        $this->log(self::WARNING, $message, $context);
    }

    /**
     * Log an info message.
     *
     * @since    1.0.0
     * @param    string    $message    The message to log.
     * @param    array     $context    Additional context data.
     */
    public function info($message, $context = []) {
        $this->log(self::INFO, $message, $context);
    }

    /**
     * Log a debug message.
     *
     * @since    1.0.0
     * @param    string    $message    The message to log.
     * @param    array     $context    Additional context data.
     */
    public function debug($message, $context = []) {
        if ($this->debug_mode) {
            $this->log(self::DEBUG, $message, $context);
        }
    }

    /**
     * Log a message.
     *
     * @since    1.0.0
     * @param    string    $level      The log level.
     * @param    string    $message    The message to log.
     * @param    array     $context    Additional context data.
     */
    private function log($level, $message, $context = []) {
        // Always log errors, but only log other levels if debug mode is enabled
        if ($level !== self::ERROR && !$this->debug_mode) {
            return;
        }
        
        $log_entry = [
            'timestamp' => current_time('mysql'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'user_id' => get_current_user_id(),
            'ip' => $this->get_client_ip(),
            'request_uri' => isset($_SERVER['REQUEST_URI']) ? sanitize_text_field($_SERVER['REQUEST_URI']) : '',
            'request_method' => isset($_SERVER['REQUEST_METHOD']) ? sanitize_text_field($_SERVER['REQUEST_METHOD']) : '',
        ];
        
        // Get existing log
        $log = get_option('mqsa_debug_log', []);
        
        // Add new entry
        $log[] = $log_entry;
        
        // Limit log size to 1000 entries
        if (count($log) > 1000) {
            $log = array_slice($log, -1000);
        }
        
        // Save log
        update_option('mqsa_debug_log', $log);
        
        // Fire action
        do_action('mqsa_log_event', $level, $message, $context);
        
        // Log to error log if it's an error
        if ($level === self::ERROR) {
            error_log('MQSA Error: ' . $message . ' | Context: ' . json_encode($context));
        }
    }

    /**
     * Get the client IP address.
     *
     * @since    1.0.0
     * @return   string    The client IP address.
     */
    private function get_client_ip() {
        $ip = '';
        
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = sanitize_text_field($_SERVER['HTTP_CLIENT_IP']);
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = sanitize_text_field($_SERVER['HTTP_X_FORWARDED_FOR']);
        } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
            $ip = sanitize_text_field($_SERVER['REMOTE_ADDR']);
        }
        
        return $ip;
    }

    /**
     * Get the debug log.
     *
     * @since    1.0.0
     * @param    int       $limit     The maximum number of entries to return.
     * @param    string    $level     The log level to filter by.
     * @return   array     The debug log.
     */
    public function get_log($limit = 100, $level = null) {
        $log = get_option('mqsa_debug_log', []);
        
        // Filter by level if specified
        if ($level) {
            $log = array_filter($log, function($entry) use ($level) {
                return $entry['level'] === $level;
            });
        }
        
        // Sort by timestamp (newest first)
        usort($log, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        // Limit the number of entries
        if ($limit > 0) {
            $log = array_slice($log, 0, $limit);
        }
        
        return $log;
    }

    /**
     * Clear the debug log.
     *
     * @since    1.0.0
     */
    public function clear_log() {
        update_option('mqsa_debug_log', []);
        $this->info('Debug log cleared');
    }

    /**
     * Clean up old log entries.
     *
     * @since    1.0.0
     */
    public function cleanup_logs() {
        $log = get_option('mqsa_debug_log', []);
        $cutoff_time = strtotime('-' . $this->log_retention . ' days');
        
        $filtered_log = array_filter($log, function($entry) use ($cutoff_time) {
            return strtotime($entry['timestamp']) >= $cutoff_time;
        });
        
        if (count($filtered_log) !== count($log)) {
            update_option('mqsa_debug_log', $filtered_log);
            $this->info('Cleaned up ' . (count($log) - count($filtered_log)) . ' old log entries');
        }
    }

    /**
     * Check if debug mode is enabled.
     *
     * @since    1.0.0
     * @return   bool    Whether debug mode is enabled.
     */
    public function is_debug_mode() {
        return $this->debug_mode;
    }

    /**
     * Set debug mode.
     *
     * @since    1.0.0
     * @param    bool    $debug_mode    Whether to enable debug mode.
     */
    public function set_debug_mode($debug_mode) {
        $this->debug_mode = $debug_mode;
        
        $settings = get_option('mqsa_settings', []);
        $settings['debug_mode'] = $debug_mode ? 'yes' : 'no';
        update_option('mqsa_settings', $settings);
    }
}

/**
 * Main instance of logger.
 *
 * @since    1.0.0
 * @return   MQSA_Logger    The main instance.
 */
function mqsa_logger() {
    return MQSA_Logger::instance();
}
