<?php
/**
 * Mind Qtrl Menu
 *
 * This file adds the Mind Qtrl menu to the WordPress admin dashboard.
 *
 * @since      1.0.0
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/includes
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Enqueue admin styles
add_action('admin_enqueue_scripts', 'mind_qtrl_enqueue_admin_styles');

// Add body class
add_filter('admin_body_class', 'mind_qtrl_add_admin_body_class');

/**
 * Add admin body class.
 */
function mind_qtrl_add_admin_body_class($classes) {
    $screen = get_current_screen();

    // Add mqco-admin-page class to Mind Qtrl pages
    if ($screen && (strpos($screen->id, 'mind-qtrl') !== false || strpos($screen->id, 'mqco') !== false)) {
        $classes .= ' mqco-admin-page';
    }

    return $classes;
}

/**
 * Enqueue admin styles.
 */
function mind_qtrl_enqueue_admin_styles($hook) {
    // Only load on our pages
    if (strpos($hook, 'mind-qtrl') === false) {
        return;
    }

    // Enqueue main admin styles
    wp_enqueue_style(
        'mind-qtrl-admin',
        plugin_dir_url(dirname(__FILE__)) . 'admin/css/mind-qtrl-admin.css',
        [],
        '1.0.0'
    );

    // Enqueue dark mode styles
    wp_enqueue_style(
        'mqco-dark-mode',
        plugin_dir_url(dirname(__FILE__)) . 'admin/css/mqco-dark-mode.css',
        [],
        '1.0.0'
    );

    // Enqueue dark mode script
    wp_enqueue_script(
        'mqco-dark-mode',
        plugin_dir_url(dirname(__FILE__)) . 'admin/js/mqco-dark-mode.js',
        ['jquery'],
        '1.0.0',
        true
    );

    // Enqueue space settings script
    wp_enqueue_script(
        'mqsa-space-settings',
        plugin_dir_url(dirname(__FILE__)) . 'admin/js/space-settings.js',
        ['jquery'],
        time(), // Use current timestamp to force cache refresh
        true
    );

    // Localize script
    wp_localize_script('mqsa-space-settings', 'mqsaAdmin', [
        'nonce' => wp_create_nonce('mqsa-admin'),
        'ajaxurl' => admin_url('admin-ajax.php'),
        'strings' => [
            'saveSuccess' => __('Settings saved successfully.', 'mind-qtrl-space-access'),
            'saveError' => __('An error occurred while saving settings.', 'mind-qtrl-space-access')
        ]
    ]);
}

/**
 * Add Mind Qtrl menu to the WordPress admin dashboard.
 */
function mind_qtrl_add_admin_menu() {
    // Add top-level Mind Qtrl menu
    add_menu_page(
        'Mind Qtrl',                 // Page title
        'Mind Qtrl',                 // Menu title
        'manage_options',            // Capability
        'mind-qtrl-admin',           // Menu slug
        'mind_qtrl_render_admin_page', // Callback function
        plugin_dir_url(dirname(__FILE__)) . 'admin/images/mind-qtrl-icon.png', // Icon URL
        30                           // Position
    );

    // Add Space Access as submenu
    add_submenu_page(
        'mind-qtrl-admin',           // Parent slug
        'Space Access',              // Page title
        'Space Access',              // Menu title
        'manage_options',            // Capability
        'mind-qtrl-admin',           // Menu slug (same as parent to make it the default page)
        'mind_qtrl_render_admin_page' // Callback function
    );

    // Add Settings as submenu
    add_submenu_page(
        'mind-qtrl-admin',           // Parent slug
        'Settings',                  // Page title
        'Settings',                  // Menu title
        'manage_options',            // Capability
        'mind-qtrl-settings',        // Menu slug
        'mind_qtrl_render_settings_page' // Callback function
    );
}
add_action('admin_menu', 'mind_qtrl_add_admin_menu', 9); // Priority 9 to ensure it runs before other menu items

/**
 * Render the admin page.
 */
function mind_qtrl_render_admin_page() {
    // Get current tab
    $tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'rules';

    // Add admin header
    echo '<div class="wrap mqsa-admin-wrap">';
    echo '<div class="mqsa-header">';
    echo '<img src="' . plugin_dir_url(dirname(__FILE__)) . 'admin/images/mind-qtrl-icon.png' . '" alt="Mind Qtrl Logo" class="mqsa-logo">';
    echo '<h1>Mind Qtrl | Space Access Control</h1>';
    echo '</div>';

    // Add tabs navigation
    echo '<div class="mqsa-nav-tabs">';

    // Determine the base URL for tabs
    $base_url = admin_url('admin.php?page=mind-qtrl-admin');

    // Settings tab
    echo '<a href="' . esc_url(add_query_arg('tab', 'settings', $base_url)) . '" class="mqsa-nav-tab ' . ($tab === 'settings' ? 'active' : '') . '">';
    echo 'Settings';
    echo '</a>';

    // Space Rules tab
    echo '<a href="' . esc_url(add_query_arg('tab', 'rules', $base_url)) . '" class="mqsa-nav-tab ' . ($tab === 'rules' ? 'active' : '') . '">';
    echo 'Space Rules';
    echo '</a>';

    // Debug Log tab
    echo '<a href="' . esc_url(add_query_arg('tab', 'debug', $base_url)) . '" class="mqsa-nav-tab ' . ($tab === 'debug' ? 'active' : '') . '">';
    echo 'Debug Log';
    echo '</a>';

    echo '</div>';

    // Add content container
    echo '<div class="mqsa-content">';

    // Include the appropriate tab content
    switch ($tab) {
        case 'settings':
            mind_qtrl_render_settings_tab();
            break;

        case 'rules':
            mind_qtrl_render_rules_tab();
            break;

        case 'debug':
            mind_qtrl_render_debug_tab();
            break;

        default:
            mind_qtrl_render_settings_tab();
            break;
    }

    echo '</div>'; // Close mqsa-content

    // Add footer
    echo '<div class="mqsa-admin-footer">';
    echo 'Mind Qtrl | Space Access Control v' . (defined('MQSA_VERSION') ? MQSA_VERSION : '1.0.0');
    echo ' | <a href="https://mindqtrl.com" target="_blank">mindqtrl.com</a>';
    echo '</div>';

    echo '</div>'; // Close mqsa-admin-wrap
}

/**
 * Render the settings page.
 */
function mind_qtrl_render_settings_page() {
    // Add admin header
    echo '<div class="wrap mqsa-admin-wrap">';
    echo '<div class="mqsa-header">';
    echo '<img src="' . plugin_dir_url(dirname(__FILE__)) . 'admin/images/mind-qtrl-icon.png' . '" alt="Mind Qtrl Logo" class="mqsa-logo">';
    echo '<h1>Mind Qtrl | Settings</h1>';
    echo '</div>';

    // Add content container
    echo '<div class="mqsa-content">';

    // Settings form
    echo '<form method="post" action="options.php" class="mqsa-form">';
    settings_fields('mqsa_settings');
    do_settings_sections('mind-qtrl-space-access');
    submit_button('Save Settings', 'primary mqsa-button');
    echo '</form>';

    echo '</div>'; // Close mqsa-content

    // Add footer
    echo '<div class="mqsa-admin-footer">';
    echo 'Mind Qtrl | Space Access Control v' . (defined('MQSA_VERSION') ? MQSA_VERSION : '1.0.0');
    echo ' | <a href="https://mindqtrl.com" target="_blank">mindqtrl.com</a>';
    echo '</div>';

    echo '</div>'; // Close mqsa-admin-wrap
}

/**
 * Render the settings tab.
 */
function mind_qtrl_render_settings_tab() {
    echo '<h2>General Settings</h2>';
    echo '<p>Configure general settings for the Space Access plugin.</p>';

    // Settings form
    echo '<form method="post" action="options.php" class="mqsa-form">';
    settings_fields('mqsa_settings');
    do_settings_sections('mind-qtrl-space-access');
    submit_button('Save Settings', 'primary mqsa-button');
    echo '</form>';

    echo '<div class="mqsa-section" style="margin-top: 30px; padding: 20px; background: #f9f9f9; border-radius: 4px;">';
    echo '<h3>Plugin Information</h3>';
    echo '<p>Mind Qtrl Space Access Control provides advanced membership access control for Fluent Community spaces.</p>';

    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th scope="row">Version</th>';
    echo '<td>' . (defined('MQSA_VERSION') ? MQSA_VERSION : '1.0.0') . '</td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th scope="row">Support</th>';
    echo '<td><a href="https://mindqtrl.com/support" target="_blank">Get Support</a></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th scope="row">Documentation</th>';
    echo '<td><a href="https://mindqtrl.com/docs/space-access" target="_blank">View Documentation</a></td>';
    echo '</tr>';
    echo '</table>';
    echo '</div>';
}

/**
 * Render the rules tab.
 */
function mind_qtrl_render_rules_tab() {
    echo '<h2>Space Rules</h2>';
    echo '<p>Configure access rules for your Fluent Community spaces.</p>';

    // Check if Fluent Community is active
    if (!function_exists('FluentCommunity') && !class_exists('\\FluentCommunity\\App\\App')) {
        echo '<div class="notice notice-warning" style="padding: 10px 15px; background-color: #fff8e5; border-left: 4px solid #ffb900; margin: 5px 0 15px;">';
        echo '<p><strong>Fluent Community is not active.</strong> Space rules require Fluent Community to be installed and activated.</p>';
        echo '</div>';
        return;
    }

    // Spaces list
    echo '<div class="mqsa-spaces-wrapper">';

    // Spaces sidebar
    echo '<div class="mqsa-spaces-sidebar">';
    echo '<h3>Spaces</h3>';
    echo '<p>Select a space to configure access rules.</p>';

    // Get spaces
    $spaces = [];
    if (class_exists('\\FluentCommunity\\App\\Models\\Space')) {
        $spaces = \FluentCommunity\App\Models\Space::all();
    }

    if (empty($spaces)) {
        echo '<p>No spaces found.</p>';
    } else {
        echo '<ul style="margin-top: 15px;">';
        foreach ($spaces as $space) {
            echo '<li style="margin-bottom: 10px;"><a href="#" class="mqsa-space-item" data-space-id="' . esc_attr($space->id) . '">' . esc_html($space->title) . '</a></li>';
        }
        echo '</ul>';
    }

    echo '</div>'; // Close mqsa-spaces-sidebar

    // Space settings
    echo '<div class="mqsa-space-settings">';
    echo '<div class="mqsa-space-settings-placeholder">';
    echo '<h3>Select a Space</h3>';
    echo '<p>Please select a space from the list to configure access rules.</p>';
    echo '</div>';
    echo '</div>'; // Close mqsa-space-settings

    echo '</div>'; // Close mqsa-spaces-wrapper
}

/**
 * Render the debug tab.
 */
function mind_qtrl_render_debug_tab() {
    echo '<h2>Debug Log</h2>';
    echo '<p>View and manage debug logs for the Space Access plugin.</p>';

    // Debug settings
    echo '<div class="mqsa-debug-settings" style="margin-bottom: 30px;">';
    echo '<h3>Debug Settings</h3>';

    // Settings form
    echo '<form method="post" action="options.php" class="mqsa-form">';
    settings_fields('mqsa_debug_settings');

    // Get settings
    $settings = get_option('mqsa_settings', []);
    $debug_mode = isset($settings['debug_mode']) ? $settings['debug_mode'] : 'no';
    $log_retention = isset($settings['log_retention']) ? intval($settings['log_retention']) : 7;

    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th scope="row">Enable Debug Mode</th>';
    echo '<td><label><input type="checkbox" name="mqsa_settings[debug_mode]" value="1" ' . checked('yes', $debug_mode, false) . ' /> Enable detailed logging</label></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th scope="row">Log Retention (days)</th>';
    echo '<td><input type="number" name="mqsa_settings[log_retention]" value="' . esc_attr($log_retention) . '" min="1" max="30" /></td>';
    echo '</tr>';
    echo '</table>';

    submit_button('Save Debug Settings', 'primary mqsa-button');
    echo '</form>';

    echo '</div>'; // Close mqsa-debug-settings

    // Debug log
    echo '<div class="mqsa-debug-log">';
    echo '<h3>Debug Log</h3>';

    // Clear log button
    echo '<form method="post" action="" class="mqsa-clear-log-form" style="margin-bottom: 15px;">';
    echo '<input type="hidden" name="mqsa_action" value="clear_log" />';
    echo '<input type="hidden" name="mqsa_nonce" value="' . wp_create_nonce('mqsa_clear_log') . '" />';
    echo '<button type="submit" class="button">Clear Log</button>';
    echo '</form>';

    // Log entries
    echo '<div class="mqsa-log-entries">';

    // Get log entries
    $log_entries = get_option('mqsa_debug_log', []);

    if (empty($log_entries)) {
        echo '<p>No log entries found.</p>';
    } else {
        echo '<table class="widefat">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Time</th>';
        echo '<th>Level</th>';
        echo '<th>Message</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        // Sort log entries by timestamp (newest first)
        usort($log_entries, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });

        // Limit to 100 entries
        $log_entries = array_slice($log_entries, 0, 100);

        foreach ($log_entries as $entry) {
            $level_class = '';
            switch ($entry['level']) {
                case 'error':
                    $level_class = 'color: #dc3232;';
                    break;
                case 'warning':
                    $level_class = 'color: #ffb900;';
                    break;
                case 'info':
                    $level_class = 'color: #00a0d2;';
                    break;
                case 'debug':
                    $level_class = 'color: #999;';
                    break;
            }

            echo '<tr>';
            echo '<td>' . esc_html($entry['timestamp']) . '</td>';
            echo '<td style="' . $level_class . '">' . esc_html(ucfirst($entry['level'])) . '</td>';
            echo '<td>' . esc_html($entry['message']) . '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }

    echo '</div>'; // Close mqsa-log-entries

    echo '</div>'; // Close mqsa-debug-log
}

// Register settings
add_action('admin_init', 'mind_qtrl_register_settings');

/**
 * Register settings.
 */
function mind_qtrl_register_settings() {
    // Register settings
    register_setting('mqsa_settings', 'mqsa_settings', [
        'sanitize_callback' => 'mind_qtrl_sanitize_settings'
    ]);

    // Add settings section
    add_settings_section(
        'mqsa_general_section',
        'General Settings',
        'mind_qtrl_render_general_section',
        'mind-qtrl-space-access'
    );

    // Add settings fields
    add_settings_field(
        'mqsa_enable_plugin',
        'Enable Plugin',
        'mind_qtrl_render_enable_plugin_field',
        'mind-qtrl-space-access',
        'mqsa_general_section'
    );

    add_settings_field(
        'mqsa_debug_mode',
        'Debug Mode',
        'mind_qtrl_render_debug_mode_field',
        'mind-qtrl-space-access',
        'mqsa_general_section'
    );
}

/**
 * Render general section.
 */
function mind_qtrl_render_general_section() {
    echo '<p>Configure general settings for the Space Access plugin.</p>';
}

/**
 * Render enable plugin field.
 */
function mind_qtrl_render_enable_plugin_field() {
    $settings = get_option('mqsa_settings', []);
    $enabled = isset($settings['enable_plugin']) ? $settings['enable_plugin'] : 'yes';

    echo '<label><input type="checkbox" name="mqsa_settings[enable_plugin]" value="1" ' . checked('yes', $enabled, false) . ' /> Enable space access control</label>';
}

/**
 * Render debug mode field.
 */
function mind_qtrl_render_debug_mode_field() {
    $settings = get_option('mqsa_settings', []);
    $debug_mode = isset($settings['debug_mode']) ? $settings['debug_mode'] : 'no';

    echo '<label><input type="checkbox" name="mqsa_settings[debug_mode]" value="1" ' . checked('yes', $debug_mode, false) . ' /> Enable debug logging</label>';
}

/**
 * Sanitize settings.
 */
function mind_qtrl_sanitize_settings($input) {
    $sanitized = [];

    // Sanitize enable plugin
    $sanitized['enable_plugin'] = isset($input['enable_plugin']) ? 'yes' : 'no';

    // Sanitize debug mode
    $sanitized['debug_mode'] = isset($input['debug_mode']) ? 'yes' : 'no';

    // Sanitize log retention
    $sanitized['log_retention'] = isset($input['log_retention']) ? intval($input['log_retention']) : 7;

    return $sanitized;
}

// Handle clear log action
add_action('admin_init', 'mind_qtrl_handle_clear_log');

// Add AJAX handlers
add_action('wp_ajax_mqsa_get_space_settings', 'mind_qtrl_get_space_settings');
add_action('wp_ajax_mqsa_get_all_space_settings', 'mind_qtrl_get_all_space_settings');
add_action('wp_ajax_mqsa_save_space_settings', 'mind_qtrl_save_space_settings');

/**
 * Handle clear log action.
 */
function mind_qtrl_handle_clear_log() {
    if (isset($_POST['mqsa_action']) && $_POST['mqsa_action'] === 'clear_log' && isset($_POST['mqsa_nonce'])) {
        if (wp_verify_nonce($_POST['mqsa_nonce'], 'mqsa_clear_log')) {
            update_option('mqsa_debug_log', []);

            // Add admin notice
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>Debug log cleared successfully.</p></div>';
            });
        }
    }
}

/**
 * Get all space settings via AJAX.
 */
function mind_qtrl_get_all_space_settings() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqsa-admin')) {
        wp_send_json_error([
            'message' => __('Security check failed.', 'mind-qtrl-space-access')
        ]);
    }

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error([
            'message' => __('You do not have permission to perform this action.', 'mind-qtrl-space-access')
        ]);
    }

    // Get space IDs
    $space_ids = isset($_POST['space_ids']) ? (array) $_POST['space_ids'] : [];
    if (empty($space_ids)) {
        wp_send_json_error([
            'message' => __('No space IDs provided.', 'mind-qtrl-space-access')
        ]);
    }

    // Get all space settings
    $all_settings = get_option('mqsa_space_settings', []);

    // Filter settings to only include requested space IDs
    $filtered_settings = [];
    foreach ($space_ids as $space_id) {
        $space_id = intval($space_id);
        $filtered_settings[$space_id] = isset($all_settings[$space_id]) ? $all_settings[$space_id] : [];

        // Try to get settings from Fluent Community's storage if available
        if (class_exists('\\FluentCommunity\\App\\Models\\Space')) {
            try {
                $space = \FluentCommunity\App\Models\Space::find($space_id);
                if ($space) {
                    $fc_settings = $space->getCustomMeta('mqsa_settings', []);
                    if (!empty($fc_settings)) {
                        $filtered_settings[$space_id] = $fc_settings;
                    }
                }
            } catch (\Exception $e) {
                // Silently fail and use WordPress options
            }
        }
    }

    // Send response
    wp_send_json_success($filtered_settings);
}

/**
 * Get space settings via AJAX.
 */
function mind_qtrl_get_space_settings() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqsa-admin')) {
        wp_send_json_error([
            'message' => __('Security check failed.', 'mind-qtrl-space-access')
        ]);
    }

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error([
            'message' => __('You do not have permission to perform this action.', 'mind-qtrl-space-access')
        ]);
    }

    // Get space ID
    $space_id = isset($_POST['space_id']) ? intval($_POST['space_id']) : 0;
    if (!$space_id) {
        wp_send_json_error([
            'message' => __('Invalid space ID.', 'mind-qtrl-space-access')
        ]);
    }

    // Get space title
    $space_title = '';
    if (class_exists('\\FluentCommunity\\App\\Models\\Space')) {
        $space = \FluentCommunity\App\Models\Space::find($space_id);
        if ($space) {
            $space_title = $space->title;
        }
    }

    // Get space settings
    $all_settings = get_option('mqsa_space_settings', []);
    $settings = isset($all_settings[$space_id]) ? $all_settings[$space_id] : [];

    // Set default values
    $settings = wp_parse_args($settings, [
        'enable_restrictions' => 'no',
        'access_requirements' => 'none',
        'crm_tags' => [],
        'badges' => [],
        'leaderboard_levels' => [],
        'restrict_view' => 'no',
        'restrict_join' => 'no',
        'restrict_post' => 'no',
        'restrict_comment' => 'no',
        'restrict_like' => 'no',
        'hide_like_buttons' => 'yes', // Default to hiding buttons
        // Default values for unjoined member options
        'restrict_view_unjoined' => 'no',
        'restrict_join_unjoined' => 'no',
        'restrict_post_unjoined' => 'no',
        'restrict_comment_unjoined' => 'no',
        'restrict_like_unjoined' => 'no',
        'view_message' => __('You do not have access to view this space.', 'mind-qtrl-space-access'),
        'join_message' => __('You do not have permission to join this space.', 'mind-qtrl-space-access'),
        'post_message' => __('You do not have permission to post in this space.', 'mind-qtrl-space-access'),
        'comment_message' => __('You do not have permission to comment in this space.', 'mind-qtrl-space-access'),
        'like_message' => __('You do not have permission to like activities in this space.', 'mind-qtrl-space-access')
    ]);

    // Convert string values to arrays
    if (!empty($settings['crm_tags']) && is_string($settings['crm_tags'])) {
        $settings['crm_tags'] = explode(',', $settings['crm_tags']);
    }

    if (!empty($settings['badges']) && is_string($settings['badges'])) {
        $settings['badges'] = explode(',', $settings['badges']);
    }

    if (!empty($settings['leaderboard_levels']) && is_string($settings['leaderboard_levels'])) {
        $settings['leaderboard_levels'] = explode(',', $settings['leaderboard_levels']);
    }

    // Add space title
    $settings['space_title'] = $space_title;

    // Get CRM tags
    $settings['crm_tags_options'] = [];
    if (defined('FLUENTCRM')) {
        $tags = \FluentCrm\App\Models\Tag::all();
        $settings['crm_tags_options'] = $tags->map(function($tag) {
            return [
                'id' => $tag->id,
                'title' => $tag->title
            ];
        })->toArray();
    }

    // Get badges from Fluent Community
    $settings['badges_options'] = [];

    // First try to use the Fluent Community API if available
    if (function_exists('FluentCommunity')) {
        try {
            // Get badges using Fluent Community's API
            $api = FluentCommunity('api');
            if ($api && method_exists($api, 'getBadges')) {
                $badges = $api->getBadges(['status' => 1]);

                if ($badges && !empty($badges)) {
                    $settings['badges_options'] = array_map(function($badge) {
                        // Handle different versions of the API that might return different structures
                        $id = isset($badge['id']) ? $badge['id'] : (isset($badge->id) ? $badge->id : 0);
                        $name = isset($badge['name']) ? $badge['name'] : (isset($badge->name) ? $badge->name :
                               (isset($badge['title']) ? $badge['title'] : (isset($badge->title) ? $badge->title : '')));

                        return [
                            'id' => $id,
                            'title' => $name
                        ];
                    }, $badges);
                }
            }
        } catch (\Exception $e) {
            // Log error if available
            if (function_exists('mqsa_logger')) {
                mqsa_logger()->error('Error fetching badges from Fluent Community API: ' . $e->getMessage());
            }
        }
    }

    // Try alternative API methods if the primary one failed
    if (empty($settings['badges_options']) && function_exists('FluentCommunity')) {
        try {
            $api = FluentCommunity('api');
            // Try alternative method names that might exist in different versions
            $methodsToTry = ['getAllBadges', 'listBadges', 'fetchBadges'];

            foreach ($methodsToTry as $method) {
                if (method_exists($api, $method)) {
                    $badges = $api->$method(['status' => 1]);

                    if ($badges && !empty($badges)) {
                        $settings['badges_options'] = array_map(function($badge) {
                            $id = isset($badge['id']) ? $badge['id'] : (isset($badge->id) ? $badge->id : 0);
                            $name = isset($badge['name']) ? $badge['name'] : (isset($badge->name) ? $badge->name :
                                   (isset($badge['title']) ? $badge['title'] : (isset($badge->title) ? $badge->title : '')));

                            return [
                                'id' => $id,
                                'title' => $name
                            ];
                        }, $badges);
                        break;
                    }
                }
            }
        } catch (\Exception $e) {
            // Log error if available
            if (function_exists('mqsa_logger')) {
                mqsa_logger()->error('Error fetching badges from Fluent Community alternative API methods: ' . $e->getMessage());
            }
        }
    }

    // If no badges were fetched using the API, try using the model directly
    if (empty($settings['badges_options'])) {
        // Try different model namespaces that might exist in different versions
        $modelClasses = [
            '\\FluentCommunity\\App\\Models\\Badge',
            '\\FluentCommunityPro\\App\\Models\\Badge',
            '\\FluentCommunity\\App\\Modules\\UserBadge\\Models\\Badge'
        ];

        foreach ($modelClasses as $modelClass) {
            if (class_exists($modelClass)) {
                try {
                    // Get badges using Fluent Community's model
                    $badges = $modelClass::where('status', 1)->get();

                    if ($badges && !empty($badges)) {
                        $settings['badges_options'] = $badges->map(function($badge) {
                            return [
                                'id' => $badge->id,
                                'title' => isset($badge->name) ? $badge->name : $badge->title
                            ];
                        })->toArray();
                        break;
                    }
                } catch (\Exception $e) {
                    // Log error if available
                    if (function_exists('mqsa_logger')) {
                        mqsa_logger()->error('Error fetching badges from Fluent Community model ' . $modelClass . ': ' . $e->getMessage());
                    }
                }
            }
        }
    }

    // If still no badges, fallback to direct database query
    if (empty($settings['badges_options'])) {
        global $wpdb;

        // Try different possible table names based on different Fluent Community versions
        $possible_tables = [
            $wpdb->prefix . 'fc_badges',
            $wpdb->prefix . 'fcom_badges',
            $wpdb->prefix . 'fluent_community_badges',
            $wpdb->prefix . 'fcom_user_badges' // Some versions might store badges here
        ];

        foreach ($possible_tables as $table_name) {
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if ($table_exists) {
                // Check table structure to determine correct column names
                $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
                $column_names = array_map(function($col) { return $col->Field; }, $columns);

                $id_column = in_array('id', $column_names) ? 'id' : 'badge_id';
                $name_column = in_array('name', $column_names) ? 'name' :
                              (in_array('title', $column_names) ? 'title' : 'badge_name');
                $status_column = in_array('status', $column_names) ? 'status' : 'is_active';

                // Direct query to get all badges
                $query = "SELECT $id_column as id, $name_column as name FROM $table_name WHERE $status_column = 1 ORDER BY $name_column ASC";
                $badges = $wpdb->get_results($query);

                if ($badges) {
                    $settings['badges_options'] = array_map(function($badge) {
                        return [
                            'id' => $badge->id,
                            'title' => $badge->name
                        ];
                    }, $badges);
                    break; // Exit the loop if we found badges
                }
            }
        }
    }

    // Log the number of badges found
    if (function_exists('mqsa_logger')) {
        mqsa_logger()->info('Fetched ' . count($settings['badges_options']) . ' badges from Fluent Community');
    }

    // Get leaderboard levels from Fluent Community
    $settings['leaderboard_options'] = [];

    // First try to use the Fluent Community API if available
    if (function_exists('FluentCommunity')) {
        try {
            // Get leader badges using Fluent Community's API
            $api = FluentCommunity('api');
            if ($api && method_exists($api, 'getLeaderBadges')) {
                $leader_badges = $api->getLeaderBadges(['status' => 1]);

                if ($leader_badges && !empty($leader_badges)) {
                    $settings['leaderboard_options'] = array_map(function($badge) {
                        // Handle different versions of the API that might return different structures
                        $id = isset($badge['id']) ? $badge['id'] : (isset($badge->id) ? $badge->id : 0);
                        $name = isset($badge['name']) ? $badge['name'] : (isset($badge->name) ? $badge->name :
                               (isset($badge['title']) ? $badge['title'] : (isset($badge->title) ? $badge->title : '')));

                        return [
                            'id' => $id,
                            'title' => $name
                        ];
                    }, $leader_badges);
                }
            }
        } catch (\Exception $e) {
            // Log error if available
            if (function_exists('mqsa_logger')) {
                mqsa_logger()->error('Error fetching leader badges from Fluent Community API: ' . $e->getMessage());
            }
        }
    }

    // Try alternative API methods if the primary one failed
    if (empty($settings['leaderboard_options']) && function_exists('FluentCommunity')) {
        try {
            $api = FluentCommunity('api');
            // Try alternative method names that might exist in different versions
            $methodsToTry = ['getAllLeaderBadges', 'listLeaderBadges', 'fetchLeaderBadges', 'getLeaderboardLevels'];

            foreach ($methodsToTry as $method) {
                if (method_exists($api, $method)) {
                    $leader_badges = $api->$method(['status' => 1]);

                    if ($leader_badges && !empty($leader_badges)) {
                        $settings['leaderboard_options'] = array_map(function($badge) {
                            $id = isset($badge['id']) ? $badge['id'] : (isset($badge->id) ? $badge->id :
                                 (isset($badge['slug']) ? $badge['slug'] : (isset($badge->slug) ? $badge->slug : 0)));
                            $name = isset($badge['name']) ? $badge['name'] : (isset($badge->name) ? $badge->name :
                                   (isset($badge['title']) ? $badge['title'] : (isset($badge->title) ? $badge->title : '')));

                            return [
                                'id' => $id,
                                'title' => $name
                            ];
                        }, $leader_badges);
                        break;
                    }
                }
            }
        } catch (\Exception $e) {
            // Log error if available
            if (function_exists('mqsa_logger')) {
                mqsa_logger()->error('Error fetching leader badges from Fluent Community alternative API methods: ' . $e->getMessage());
            }
        }
    }

    // If no leader badges were fetched using the API, try using the model directly
    if (empty($settings['leaderboard_options'])) {
        // Try different model namespaces that might exist in different versions
        $modelClasses = [
            '\\FluentCommunity\\App\\Models\\LeaderBadge',
            '\\FluentCommunityPro\\App\\Models\\LeaderBadge',
            '\\FluentCommunity\\App\\Modules\\LeaderBoard\\Models\\LeaderBadge',
            '\\FluentCommunityPro\\App\\Modules\\LeaderBoard\\Models\\LeaderBadge'
        ];

        foreach ($modelClasses as $modelClass) {
            if (class_exists($modelClass)) {
                try {
                    // Get leader badges using Fluent Community's model
                    $leader_badges = $modelClass::where('status', 1)->get();

                    if ($leader_badges && !empty($leader_badges)) {
                        $settings['leaderboard_options'] = $leader_badges->map(function($badge) {
                            return [
                                'id' => $badge->id,
                                'title' => isset($badge->name) ? $badge->name : $badge->title
                            ];
                        })->toArray();
                        break;
                    }
                } catch (\Exception $e) {
                    // Log error if available
                    if (function_exists('mqsa_logger')) {
                        mqsa_logger()->error('Error fetching leader badges from Fluent Community model ' . $modelClass . ': ' . $e->getMessage());
                    }
                }
            }
        }
    }

    // If still no leader badges, fallback to direct database query
    if (empty($settings['leaderboard_options'])) {
        global $wpdb;

        // Try different possible table names based on different Fluent Community versions
        $possible_tables = [
            $wpdb->prefix . 'fc_leader_badges',
            $wpdb->prefix . 'fcom_leader_badges',
            $wpdb->prefix . 'fluent_community_leader_badges',
            $wpdb->prefix . 'fcom_leaderboard_levels'
        ];

        foreach ($possible_tables as $table_name) {
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if ($table_exists) {
                // Check table structure to determine correct column names
                $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
                $column_names = array_map(function($col) { return $col->Field; }, $columns);

                $id_column = in_array('id', $column_names) ? 'id' :
                            (in_array('badge_id', $column_names) ? 'badge_id' :
                            (in_array('level_id', $column_names) ? 'level_id' : 'slug'));

                $name_column = in_array('name', $column_names) ? 'name' :
                              (in_array('title', $column_names) ? 'title' :
                              (in_array('badge_name', $column_names) ? 'badge_name' : 'level_name'));

                $status_column = in_array('status', $column_names) ? 'status' : 'is_active';

                // Check if status column exists, if not, assume all are active
                $where_clause = in_array($status_column, $column_names) ? "WHERE $status_column = 1" : "";

                // Direct query to get all leader badges
                $query = "SELECT $id_column as id, $name_column as name FROM $table_name $where_clause ORDER BY $name_column ASC";
                $leader_badges = $wpdb->get_results($query);

                if ($leader_badges) {
                    $settings['leaderboard_options'] = array_map(function($badge) {
                        return [
                            'id' => $badge->id,
                            'title' => $badge->name
                        ];
                    }, $leader_badges);
                    break; // Exit the loop if we found leader badges
                }
            }
        }

        // If still no leader badges, try to get them from the leaderboard levels in options table
        if (empty($settings['leaderboard_options'])) {
            $leaderboard_levels = get_option('fcom_leaderboard_levels', []);

            if (!empty($leaderboard_levels)) {
                $settings['leaderboard_options'] = array_map(function($level) {
                    return [
                        'id' => $level['slug'],
                        'title' => $level['title']
                    ];
                }, $leaderboard_levels);
            }
        }
    }

    // Log the number of leader badges found
    if (function_exists('mqsa_logger')) {
        mqsa_logger()->info('Fetched ' . count($settings['leaderboard_options']) . ' leader badges from Fluent Community');
    }

    // Send response
    wp_send_json_success($settings);
}

/**
 * Save space settings via AJAX.
 */
function mind_qtrl_save_space_settings() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mqsa-admin')) {
        wp_send_json_error([
            'message' => __('Security check failed.', 'mind-qtrl-space-access')
        ]);
    }

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error([
            'message' => __('You do not have permission to perform this action.', 'mind-qtrl-space-access')
        ]);
    }

    // Parse form data
    $form_data = [];
    parse_str($_POST['form_data'], $form_data);

    // Get space ID
    $space_id = isset($form_data['space_id']) ? intval($form_data['space_id']) : 0;
    if (!$space_id) {
        wp_send_json_error([
            'message' => __('Invalid space ID.', 'mind-qtrl-space-access')
        ]);
    }

    // Get all space settings
    $all_settings = get_option('mqsa_space_settings', []);

    // Prepare settings
    $settings = [
        'enable_restrictions' => isset($form_data['enable_restrictions']) ? 'yes' : 'no',
        'access_requirements' => isset($form_data['access_requirements']) ? sanitize_text_field($form_data['access_requirements']) : 'none',
        'restrict_view' => isset($form_data['restrict_view']) ? 'yes' : 'no',
        'restrict_join' => isset($form_data['restrict_join']) ? 'yes' : 'no',
        'restrict_post' => isset($form_data['restrict_post']) ? 'yes' : 'no',
        'restrict_comment' => isset($form_data['restrict_comment']) ? 'yes' : 'no',
        // Add unjoined member options
        'restrict_view_unjoined' => isset($form_data['restrict_view_unjoined']) ? 'yes' : 'no',
        'restrict_join_unjoined' => isset($form_data['restrict_join_unjoined']) ? 'yes' : 'no',
        'restrict_post_unjoined' => isset($form_data['restrict_post_unjoined']) ? 'yes' : 'no',
        'restrict_comment_unjoined' => isset($form_data['restrict_comment_unjoined']) ? 'yes' : 'no',
        'view_message' => isset($form_data['view_message']) ? wp_kses_post($form_data['view_message']) : '',
        'join_message' => isset($form_data['join_message']) ? wp_kses_post($form_data['join_message']) : '',
        'post_message' => isset($form_data['post_message']) ? wp_kses_post($form_data['post_message']) : '',
        'comment_message' => isset($form_data['comment_message']) ? wp_kses_post($form_data['comment_message']) : ''
    ];

    // Handle CRM tags
    if ($settings['access_requirements'] === 'crm_tags' && isset($form_data['crm_tags']) && is_array($form_data['crm_tags'])) {
        $settings['crm_tags'] = implode(',', array_map('intval', $form_data['crm_tags']));
    } else {
        $settings['crm_tags'] = '';
    }

    // Handle badges
    if ($settings['access_requirements'] === 'badges' && isset($form_data['badges']) && is_array($form_data['badges'])) {
        // Check if we're using custom badges table or GamiPress
        global $wpdb;
        $table_name = $wpdb->prefix . 'badges';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

        if ($table_exists) {
            // Using custom badges table - IDs are already integers
            $settings['badges'] = implode(',', array_map('intval', $form_data['badges']));
        } else {
            // Using GamiPress - IDs are post IDs
            $settings['badges'] = implode(',', array_map('intval', $form_data['badges']));
        }
    } else {
        $settings['badges'] = '';
    }

    // Handle leaderboard levels
    if ($settings['access_requirements'] === 'leaderboard' && isset($form_data['leaderboard_levels']) && is_array($form_data['leaderboard_levels'])) {
        // Check if we're using custom leader_badges table or myCRED
        global $wpdb;
        $table_name = $wpdb->prefix . 'leader_badges';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

        if ($table_exists) {
            // Using custom leader_badges table - IDs are already integers
            $settings['leaderboard_levels'] = implode(',', array_map('intval', $form_data['leaderboard_levels']));
        } else {
            // Using myCRED - IDs are post IDs
            $settings['leaderboard_levels'] = implode(',', array_map('intval', $form_data['leaderboard_levels']));
        }
    } else {
        $settings['leaderboard_levels'] = '';
    }

    // Update settings in both WordPress options and Fluent Community's storage
    $all_settings[$space_id] = $settings;
    update_option('mqsa_space_settings', $all_settings);

    // Also save to Fluent Community's storage if available
    $saved_to_fc = false;

    // Try using Helper class first
    if (class_exists('\\FluentCommunity\\App\\Services\\Helper')) {
        try {
            \FluentCommunity\App\Services\Helper::updateSpaceMeta($space_id, 'mqsa_settings', $settings);
            $saved_to_fc = true;
        } catch (\Exception $e) {
            // Log error if available
            if (function_exists('mqsa_logger')) {
                mqsa_logger()->error('Error saving space settings to Fluent Community via Helper: ' . $e->getMessage());
            }
        }
    }

    // If Helper class failed, try using Space model directly
    if (!$saved_to_fc && class_exists('\\FluentCommunity\\App\\Models\\Space')) {
        try {
            $space = \FluentCommunity\App\Models\Space::find($space_id);
            if ($space) {
                $space->updateCustomMeta('mqsa_settings', $settings);
                $saved_to_fc = true;
            }
        } catch (\Exception $e) {
            // Log error if available
            if (function_exists('mqsa_logger')) {
                mqsa_logger()->error('Error saving space settings to Fluent Community via model: ' . $e->getMessage());
            }
        }
    }

    // Log settings update
    if (function_exists('mqsa_logger')) {
        $storage_info = $saved_to_fc ? ' (saved to Fluent Community storage)' : ' (saved to WordPress options only)';
        mqsa_logger()->info('Space settings updated' . $storage_info, ['space_id' => $space_id]);
    }

    // Clear cache
    if (function_exists('mqsa_cache')) {
        $cache_key = 'space_settings_' . $space_id;
        mqsa_cache()->delete($cache_key);
    }

    // Send response
    wp_send_json_success([
        'message' => __('Space settings saved successfully.', 'mind-qtrl-space-access')
    ]);
}
