<?php
/**
 * Mind Qtrl | Space Access Control
 *
 * @package     MindQtrl\SpaceAccess
 * <AUTHOR> Qtrl
 * @copyright   2024 Mind Qtrl
 * @license     GPL-2.0+
 *
 * @wordpress-plugin
 * Plugin Name: Mind Qtrl | Space Access Control
 * Plugin URI:  https://mindqtrl.com/plugins/space-access
 * Description: Advanced access control for Fluent Community spaces based on user badges, CRM tags, and leaderboard levels.
 * Version:     1.2.0
 * Author:      Mind Qtrl
 * Author URI:  https://mindqtrl.com
 * License:     GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain: mind-qtrl-space-access
 * Domain Path: /languages
 */

defined('ABSPATH') || exit;

// Define plugin constants
define('MQSA_VERSION', '1.2.0');
define('MQSA_PLUGIN_FILE', __FILE__);
define('MQSA_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MQSA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MQSA_PLUGIN_BASENAME', plugin_basename(__FILE__));

class Mind_Qtrl_Space_Access {
    private static $instance = null;
    private $loader;
    private $plugin_name;
    private $version = '1.0.1';
    private $admin;
    private $public;
    private $access_controller;

    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->plugin_name = 'mind-qtrl-space-access';
        $this->load_dependencies();
        $this->init_hooks();
    }

    private function load_dependencies() {
        // Load core files
        require_once MQSA_PLUGIN_DIR . 'includes/class-mqsa-loader.php';
        require_once MQSA_PLUGIN_DIR . 'includes/class-mqsa-logger.php';
        require_once MQSA_PLUGIN_DIR . 'includes/class-mqsa-cache.php';
        require_once MQSA_PLUGIN_DIR . 'includes/class-mqsa-access-controller.php';
        require_once MQSA_PLUGIN_DIR . 'admin/class-mqsa-admin.php';
        require_once MQSA_PLUGIN_DIR . 'public/class-mind-qtrl-space-access-public.php';

        // Load Mind Qtrl menu
        require_once MQSA_PLUGIN_DIR . 'includes/mind-qtrl-menu.php';

        // Initialize loader
        $this->loader = new MQSA_Loader();

        // Register activation and deactivation hooks
        register_activation_hook(MQSA_PLUGIN_FILE, [$this, 'activate']);
        register_deactivation_hook(MQSA_PLUGIN_FILE, [$this, 'deactivate']);

        // Load text domain
        add_action('plugins_loaded', [$this, 'load_plugin_textdomain']);
    }

    private function init_hooks() {
        add_action('admin_init', [$this, 'check_requirements']);

        // Always initialize the plugin for admin menu
        $this->loader->add_action('init', $this, 'init_plugin');

        // Only add Fluent Community specific hooks if requirements are met
        if ($this->check_requirements(false)) {
            // Initialize access controller early for filter registration
            $access_controller = MQSA_Access_Controller::instance();
            $this->loader->add_filter('fluent_community/space_access', $access_controller, 'check_space_access', 10, 3);
            $this->loader->add_action('wp_enqueue_scripts', $this, 'enqueue_frontend_assets');
        }
    }

    /**
     * Initialize the plugin.
     *
     * @since    1.0.0
     */
    public function init_plugin() {
        try {
            // Always initialize admin for the menu
            if (is_admin()) {
                $this->admin = new MQSA_Admin($this->plugin_name, $this->version);

                // Add admin notice about the menu location
                add_action('admin_notices', function() {
                    // Only show on plugins page
                    $screen = get_current_screen();
                    if ($screen && $screen->id === 'plugins') {
                        echo '<div class="notice notice-info is-dismissible"><p>' .
                             sprintf(
                                 __('Mind Qtrl Space Access Control menu can be found under <a href="%s">Mind QTRL</a> in the main admin menu.', 'mind-qtrl-space-access'),
                                 admin_url('admin.php?page=mind-qtrl-admin')
                             ) .
                             '</p></div>';
                    }
                });
            }

            // Only initialize Fluent Community integration if requirements are met
            if ($this->check_requirements(false)) {
                // Initialize access controller
                $this->access_controller = MQSA_Access_Controller::instance();

                // Initialize public class
                $this->public = new Mind_Qtrl_Space_Access_Public($this->plugin_name, $this->version);

                // Register public hooks
                add_action('wp_enqueue_scripts', [$this->public, 'enqueue_styles']);
                add_action('wp_enqueue_scripts', [$this->public, 'enqueue_scripts']);
                add_action('wp_head', [$this->public, 'inject_frontend_scripts']);

                // Add AJAX actions
                add_action('wp_ajax_mqsa_check_membership', [$this->public, 'ajax_check_membership']);
                add_action('wp_ajax_nopriv_mqsa_check_membership', [$this->public, 'ajax_check_membership']);
            }

            // Log initialization
            if (class_exists('MQSA_Logger')) {
                mqsa_logger()->debug('Plugin initialized');
            }
        } catch (Exception $e) {
            // Log error
            if (class_exists('MQSA_Logger')) {
                mqsa_logger()->error('Error initializing plugin: ' . $e->getMessage(), [
                    'exception' => $e,
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Show admin notice
            if (is_admin()) {
                add_action('admin_notices', function() use ($e) {
                    echo '<div class="error"><p>' .
                         sprintf(__('Mind Qtrl Space Access error: %s', 'mind-qtrl-space-access'), esc_html($e->getMessage())) .
                         '</p></div>';
                });
            }
        }
    }

    /**
     * Check if all requirements are met.
     *
     * @since    1.0.0
     * @param    bool    $show_notice    Whether to show admin notices.
     * @return   bool    Whether all requirements are met.
     */
    public function check_requirements($show_notice = true) {
        $requirements_met = true;
        $missing_plugins = [];

        // Check for Fluent Community
        if (!function_exists('FluentCommunity') && !class_exists('\\FluentCommunity\\App\\App')) {
            $requirements_met = false;
            $missing_plugins[] = 'Fluent Community';
        }

        // Show notice if required
        if (!$requirements_met && $show_notice) {
            add_action('admin_notices', function() use ($missing_plugins) {
                echo '<div class="error"><p>' .
                     sprintf(
                         __('Mind Qtrl Space Access requires the following plugins to be installed and activated: %s', 'mind-qtrl-space-access'),
                         '<strong>' . implode(', ', $missing_plugins) . '</strong>'
                     ) .
                     '</p>';

                // Add helpful information about where to find the plugin settings
                echo '<p>' .
                     __('Once all requirements are met, you can access the plugin settings under:', 'mind-qtrl-space-access') .
                     '<ul style="list-style-type: disc; margin-left: 20px;">' .
                     '<li>' . sprintf(__('<a href="%s">Mind Qtrl</a> in the main admin menu', 'mind-qtrl-space-access'), admin_url('admin.php?page=mind-qtrl-admin')) . '</li>' .
                     '</ul>' .
                     '</p></div>';
            });
        }

        return $requirements_met;
    }

    public function enqueue_frontend_assets() {
        // If public class is initialized, let it handle the assets
        if (isset($this->public) && $this->public instanceof Mind_Qtrl_Space_Access_Public) {
            $this->public->enqueue_styles();
            $this->public->enqueue_scripts();
            return;
        }

        // Fallback if public class is not initialized
        wp_enqueue_style(
            $this->plugin_name,
            MQSA_PLUGIN_URL . 'public/css/frontend.css',
            [],
            $this->version
        );

        wp_enqueue_script(
            $this->plugin_name,
            MQSA_PLUGIN_URL . 'public/js/frontend.js',
            ['jquery'],
            $this->version,
            true
        );

        wp_localize_script($this->plugin_name, 'mqsaSettings', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mqsa-frontend'),
            'selectors' => [
                'join_button' => '.fcom-join-space-btn',
                'post_box' => '.create_status_holder',
                'reaction_buttons' => '.fcom_reaction',
                'comment_buttons' => '.fcom_comment_btn_wrap',
                'like_buttons' => '.fcom_reaction, .fcom_reaction_list, .fcom_reaction_btn, .fcom_like_btn, .feed_actions button[data-type="like"], .mqcr-reaction-like'
            ]
        ]);
    }

    /**
     * Plugin activation hook.
     *
     * @since    1.0.0
     */
    public function activate() {
        // Create default settings if they don't exist
        if (!get_option('mqsa_settings')) {
            update_option('mqsa_settings', [
                'debug_mode' => 'no',
                'log_retention' => 7
            ]);
        }

        // Create space settings if they don't exist
        if (!get_option('mqsa_space_settings')) {
            update_option('mqsa_space_settings', []);
        }

        // Log activation
        if (class_exists('MQSA_Logger')) {
            mqsa_logger()->info('Plugin activated', ['version' => $this->version]);
        }

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation hook.
     *
     * @since    1.0.0
     */
    public function deactivate() {
        // Log deactivation
        if (class_exists('MQSA_Logger')) {
            mqsa_logger()->info('Plugin deactivated');
        }

        // Clear scheduled events
        wp_clear_scheduled_hook('mqsa_cleanup_logs');
        wp_clear_scheduled_hook('mqsa_cleanup_cache');

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Load the plugin text domain for translation.
     *
     * @since    1.0.0
     */
    public function load_plugin_textdomain() {
        load_plugin_textdomain(
            'mind-qtrl-space-access',
            false,
            dirname(MQSA_PLUGIN_BASENAME) . '/languages/'
        );
    }
}

function MQSA() {
    return Mind_Qtrl_Space_Access::instance();
}

// Initialize the plugin
$mqsa = MQSA();

// The Mind Qtrl menu is now added via includes/mind-qtrl-menu.php

// Run the loader to register all hooks with WordPress
if (isset($mqsa->loader) && $mqsa->loader instanceof MQSA_Loader) {
    $mqsa->loader->run();
}
