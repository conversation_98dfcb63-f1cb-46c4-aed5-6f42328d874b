<?php
/**
 * The public-facing functionality of the plugin.
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.0
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/public
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the public-facing side of the site.
 *
 * @package    Mind_Qtrl_Space_Access
 * @subpackage Mind_Qtrl_Space_Access/public
 * <AUTHOR> Qtrl <<EMAIL>>
 */
class Mind_Qtrl_Space_Access_Public {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name    The name of the plugin.
     * @param    string    $version        The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Add AJAX action for checking membership
        add_action('wp_ajax_mqsa_check_membership', array($this, 'ajax_check_membership'));

        // Add filter for Fluent Community portal admin settings menu
        add_filter('fluent_community/portal_settings_menu_items', array($this, 'add_portal_settings_menu_item'));

        // Add filter for Fluent Community route components
        add_filter('fluent_community/route_components', array($this, 'register_portal_settings_component'));
    }

    /**
     * Register the stylesheets for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        // Check if the CSS file exists
        $css_file = 'css/mind-qtrl-space-access-public.css';
        $css_path = plugin_dir_path(__FILE__) . $css_file;

        if (file_exists($css_path)) {
            wp_enqueue_style(
                $this->plugin_name,
                plugin_dir_url(__FILE__) . $css_file,
                array(),
                $this->version,
                'all'
            );
        } else {
            // Fallback to frontend.css
            wp_enqueue_style(
                $this->plugin_name,
                plugin_dir_url(__FILE__) . 'css/frontend.css',
                array(),
                $this->version,
                'all'
            );
        }

        // Enqueue modern dialog styles
        wp_enqueue_style(
            $this->plugin_name . '-dialog',
            plugin_dir_url(__FILE__) . 'css/mqsa-dialog.css',
            array(),
            $this->version,
            'all'
        );
    }

    /**
     * Register the JavaScript for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        // Enqueue modern dialog script first (so it's available for other scripts)
        wp_enqueue_script(
            $this->plugin_name . '-dialog',
            plugin_dir_url(__FILE__) . 'js/mqsa-dialog.js',
            array('jquery'),
            $this->version,
            true
        );

        // Enqueue the modular JavaScript files

        // 1. Utils module (must be loaded first)
        wp_enqueue_script(
            $this->plugin_name . '-utils',
            plugin_dir_url(__FILE__) . 'js/modules/utils.js',
            array('jquery', $this->plugin_name . '-dialog'),
            $this->version,
            true
        );

        // 2. API module
        wp_enqueue_script(
            $this->plugin_name . '-api',
            plugin_dir_url(__FILE__) . 'js/modules/api.js',
            array('jquery', $this->plugin_name . '-utils'),
            $this->version,
            true
        );

        // 3. Restrictions module
        wp_enqueue_script(
            $this->plugin_name . '-restrictions',
            plugin_dir_url(__FILE__) . 'js/modules/restrictions.js',
            array('jquery', $this->plugin_name . '-utils', $this->plugin_name . '-api'),
            $this->version,
            true
        );

        // 4. Vue Integration module
        wp_enqueue_script(
            $this->plugin_name . '-vue',
            plugin_dir_url(__FILE__) . 'js/modules/vue-integration.js',
            array('jquery', $this->plugin_name . '-utils', $this->plugin_name . '-api', $this->plugin_name . '-restrictions'),
            $this->version,
            true
        );

        // 5. Main frontend module
        wp_enqueue_script(
            $this->plugin_name,
            plugin_dir_url(__FILE__) . 'js/frontend-modular.js',
            array('jquery', $this->plugin_name . '-utils', $this->plugin_name . '-api', $this->plugin_name . '-restrictions', $this->plugin_name . '-vue'),
            $this->version,
            true
        );

        // Pass space settings to JavaScript
        $space_settings = $this->get_space_settings();

        wp_localize_script($this->plugin_name, 'mqsaSpaceSettings', $space_settings);
        wp_localize_script($this->plugin_name, 'mqsaPublic', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mqsa-frontend'),
            'default_messages' => array(
                'access' => __('You do not have access to view this space.', 'mind-qtrl-space-access'),
                'join' => __('You do not have permission to join this space.', 'mind-qtrl-space-access'),
                'post' => __('You do not have permission to post in this space.', 'mind-qtrl-space-access'),
                'comment' => __('You do not have permission to comment in this space.', 'mind-qtrl-space-access'),
                'like' => __('You do not have permission to like activities in this space.', 'mind-qtrl-space-access')
            )
        ));
    }

    /**
     * Inject frontend scripts in the Fluent Community portal head.
     *
     * @since    1.0.0
     */
    public function inject_frontend_scripts() {
        // Add our dialog CSS to the portal head
        echo '<link rel="stylesheet" href="' . plugin_dir_url(__FILE__) . 'css/mqsa-dialog.css' . '" type="text/css" media="all" />';

        // Add our dialog JS to the portal head
        echo '<script src="' . plugin_dir_url(__FILE__) . 'js/mqsa-dialog.js' . '"></script>';

        // Add modular JavaScript files
        echo '<script src="' . plugin_dir_url(__FILE__) . 'js/modules/utils.js' . '"></script>';
        echo '<script src="' . plugin_dir_url(__FILE__) . 'js/modules/api.js' . '"></script>';
        echo '<script src="' . plugin_dir_url(__FILE__) . 'js/modules/restrictions.js' . '"></script>';
        echo '<script src="' . plugin_dir_url(__FILE__) . 'js/modules/vue-integration.js' . '"></script>';
        echo '<script src="' . plugin_dir_url(__FILE__) . 'js/frontend-modular.js' . '"></script>';

        // Add integration with Fluent Community frontend portal admin settings
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Check if we're on the admin settings page
                if (window.location.href.includes('/admin/settings')) {
                    // Check if fluentComAdmin is defined
                    if (typeof window.fluentComAdmin === 'undefined' || !window.fluentComAdmin.portalSettingsMenus) {
                        console.error('MQSA: fluentComAdmin.portalSettingsMenus is not defined');
                        return;
                    }

                    // Add our menu item to the portalSettingsMenus object
                    window.fluentComAdmin.portalSettingsMenus.mqsa_settings = {
                        label: 'Space Access',
                        route: 'mqsa-settings',
                        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>'
                    };

                    // Register route handler
                    if (window.FluentCommunityUtil && window.FluentCommunityUtil.hooks) {
                        window.FluentCommunityUtil.hooks.addFilter(
                            'fluent_community/route_components',
                            'mqsa_settings_component',
                            function(components) {
                                // Add our component for the route
                                components['mqsa-settings'] = renderSettingsPanel;
                                return components;
                            }
                        );
                    }
                }
            });

            // Function to render the settings panel
            function renderSettingsPanel() {
                return {
                    template: `
                        <div class="fcom_settings_page">
                            <div class="fcom_settings_header">
                                <h2>Space Access Settings</h2>
                                <p>Configure access control settings for your Fluent Community spaces.</p>
                            </div>
                            <div class="fcom_settings_body">
                                <div class="fcom_settings_card">
                                    <div class="fcom_settings_card_header">
                                        <h3>Space Access Control</h3>
                                    </div>
                                    <div class="fcom_settings_card_body">
                                        <p>To configure space access control settings, please visit the WordPress admin dashboard:</p>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=mind-qtrl-space-access')); ?>" class="el-button el-button--primary" target="_blank">
                                            Open Space Access Settings
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `,
                    setup() {
                        return {}
                    }
                }
            }
        </script>
        <?php
    }

    /**
     * Filter space join status for private spaces.
     *
     * @since    1.0.0
     * @param    string    $status     The current join status.
     * @param    object    $space      The space object.
     * @param    int       $user_id    The user ID.
     * @return   string    The filtered join status.
     */
    public function filter_space_join_status($status, $space, $user_id) {
        // Get space settings
        $space_settings = $this->get_space_settings($space->id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Check if join restriction is enabled
            if (isset($space_settings['restrict_join']) && $space_settings['restrict_join'] === 'yes') {
                // Check if user meets the requirements
                if (!$this->user_meets_requirements($user_id, $space_settings)) {
                    return 'restricted';
                }
            }
        }

        return $status;
    }

    /**
     * Filter space access.
     *
     * @since    1.0.0
     * @param    object    $space    The space object.
     * @return   object    The filtered space object.
     */
    public function filter_space_access($space) {
        // Get space settings
        $space_settings = $this->get_space_settings($space->id);

        // Check if restrictions are enabled for this space
        if (!empty($space_settings) && isset($space_settings['enable_restrictions']) && $space_settings['enable_restrictions'] === 'yes') {
            // Add space settings to space object for frontend use
            $space->mqsa_settings = $space_settings;
        }

        return $space;
    }

    /**
     * AJAX handler for checking membership.
     *
     * @since    1.0.0
     */
    public function ajax_check_membership() {
        check_ajax_referer('mqsa-frontend', 'nonce');

        $space_id = isset($_GET['space_id']) ? intval($_GET['space_id']) : 0;
        if (!$space_id) {
            wp_send_json_error();
        }

        $user_id = get_current_user_id();
        $is_member = false;

        // Check membership using Fluent Community's methods
        if (class_exists('\FluentCommunity\App\Models\SpaceUserPivot')) {
            $membership = \FluentCommunity\App\Models\SpaceUserPivot::where('space_id', $space_id)
                ->where('user_id', $user_id)
                ->first();

            $is_member = !empty($membership);
        }

        wp_send_json_success([
            'is_member' => $is_member
        ]);
    }

    /**
     * Add menu item to Fluent Community portal admin settings.
     *
     * @since    1.0.0
     * @param    array    $menu_items    The current menu items.
     * @return   array    The filtered menu items.
     */
    public function add_portal_settings_menu_item($menu_items) {
        $menu_items['mqsa_settings'] = [
            'label' => __('Space Access', 'mind-qtrl-space-access'),
            'route' => 'mqsa-settings',
            'icon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>'
        ];

        return $menu_items;
    }

    /**
     * Register portal settings component.
     *
     * @since    1.0.0
     * @param    array    $components    The current components.
     * @return   array    The filtered components.
     */
    public function register_portal_settings_component($components) {
        // This is handled via JavaScript in inject_frontend_scripts
        return $components;
    }

    /**
     * Get space settings.
     *
     * @since    1.0.0
     * @param    int       $space_id    Optional. The space ID.
     * @return   array     The space settings.
     */
    private function get_space_settings($space_id = null) {
        // Use cache if available
        if (function_exists('mqsa_cache')) {
            $cache_key = 'space_settings_' . ($space_id ? $space_id : 'all');
            $cached_settings = mqsa_cache()->get($cache_key);

            if ($cached_settings !== null) {
                return $cached_settings;
            }
        }

        $all_settings = get_option('mqsa_space_settings', []);

        if ($space_id) {
            $settings = isset($all_settings[$space_id]) ? $all_settings[$space_id] : [];
        } else {
            $settings = $all_settings;
        }

        // Cache the settings
        if (function_exists('mqsa_cache')) {
            $cache_key = 'space_settings_' . ($space_id ? $space_id : 'all');
            mqsa_cache()->set($cache_key, $settings);
        }

        return $settings;
    }

    /**
     * Check if user meets the requirements.
     *
     * @since    1.0.0
     * @param    int       $user_id          The user ID.
     * @param    array     $space_settings   The space settings.
     * @return   bool      Whether the user meets the requirements.
     */
    private function user_meets_requirements($user_id, $space_settings) {
        // If no requirements are set, user meets requirements
        if (empty($space_settings['access_requirements']) || $space_settings['access_requirements'] === 'none') {
            return true;
        }

        // Check based on requirement type
        switch ($space_settings['access_requirements']) {
            case 'crm_tags':
                return $this->check_crm_tags($user_id, $space_settings);

            case 'badges':
                return $this->check_badges($user_id, $space_settings);

            case 'leaderboard':
                return $this->check_leaderboard($user_id, $space_settings);

            default:
                return true;
        }
    }

    /**
     * Check if user has required CRM tags.
     *
     * @since    1.0.0
     * @param    int       $user_id          The user ID.
     * @param    array     $space_settings   The space settings.
     * @return   bool      Whether the user has the required tags.
     */
    private function check_crm_tags($user_id, $space_settings) {
        // Check if FluentCRM is active
        if (!defined('FLUENTCRM')) {
            return false;
        }

        // Get required tags
        $required_tags = !empty($space_settings['crm_tags']) ? explode(',', $space_settings['crm_tags']) : [];
        if (empty($required_tags)) {
            return true;
        }

        // Get user's email
        $user = get_userdata($user_id);
        if (!$user) {
            return false;
        }

        // Get contact
        $contact = FluentCrmApi('contacts')->getContact($user->user_email);
        if (!$contact) {
            return false;
        }

        // Get contact's tags
        $contact_tags = $contact->tags;
        if (empty($contact_tags)) {
            return false;
        }

        // Extract tag IDs
        $contact_tag_ids = array_map(function($tag) {
            return $tag->id;
        }, $contact_tags);

        // Check if user has any of the required tags
        foreach ($required_tags as $tag_id) {
            if (in_array($tag_id, $contact_tag_ids)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has required badges.
     *
     * @since    1.0.0
     * @param    int       $user_id          The user ID.
     * @param    array     $space_settings   The space settings.
     * @return   bool      Whether the user has the required badges.
     */
    private function check_badges($user_id, $space_settings) {
        // Get required badges
        $required_badges = !empty($space_settings['badges']) ? explode(',', $space_settings['badges']) : [];
        if (empty($required_badges)) {
            return true;
        }

        // Check if GamiPress is active
        if (!function_exists('gamipress_get_user_achievements')) {
            return false;
        }

        // Get user's badges
        $user_badges = gamipress_get_user_achievements([
            'user_id' => $user_id,
            'achievement_type' => 'badge'
        ]);

        if (empty($user_badges)) {
            return false;
        }

        // Extract badge IDs
        $user_badge_ids = array_map(function($badge) {
            return $badge->ID;
        }, $user_badges);

        // Check if user has any of the required badges
        foreach ($required_badges as $badge_id) {
            if (in_array($badge_id, $user_badge_ids)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user meets leaderboard level requirements.
     *
     * @since    1.0.0
     * @param    int       $user_id          The user ID.
     * @param    array     $space_settings   The space settings.
     * @return   bool      Whether the user meets the leaderboard requirements.
     */
    private function check_leaderboard($user_id, $space_settings) {
        // Get required leaderboard levels
        $required_levels = !empty($space_settings['leaderboard_levels']) ? explode(',', $space_settings['leaderboard_levels']) : [];
        if (empty($required_levels)) {
            return true;
        }

        // Check if myCRED is active
        if (!function_exists('mycred_get_users_rank')) {
            return false;
        }

        // Get user's rank
        $user_rank = mycred_get_users_rank($user_id);
        if (!$user_rank) {
            return false;
        }

        // Check if user's rank is in the required levels
        return in_array($user_rank->post_id, $required_levels);
    }
}
