/**
 * Public CSS for Mind Qtrl | Space Access Control
 *
 * @link       https://mindqtrl.com/
 * @since      0.0.2
 */

/* Access Denied Message */
.mqsa-access-denied {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    padding: 40px 20px;
    margin: 20px auto;
    max-width: 600px;
    text-align: center;
    color: #fff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(4px);
}

.dark .mqsa-access-denied {
    background-color: rgba(30, 30, 30, 0.8);
}

.mqsa-access-denied-icon {
    margin: 0 auto 20px;
    color: #f05252;
}

.mqsa-access-denied-icon svg {
    width: 64px;
    height: 64px;
    stroke-width: 1.5;
}

.mqsa-access-denied-message {
    font-size: 18px;
    line-height: 1.5;
    margin-bottom: 20px;
}

/* Disabled elements styling */
.mqsa-disabled {
    opacity: 0.5;
    pointer-events: none;
    position: relative;
}

.mqsa-disabled::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: inherit;
}

/* Tooltip styles */
[data-mqsa-disabled] {
    position: relative;
}

[data-mqsa-disabled]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: nowrap;
    font-size: 12px;
    z-index: 1000;
}

[data-mqsa-disabled]:hover::after {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1000;
} 