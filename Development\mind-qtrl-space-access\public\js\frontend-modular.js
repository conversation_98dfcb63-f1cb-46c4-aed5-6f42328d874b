/**
 * Main JavaScript for Mind Qtrl | Space Access Control
 *
 * This file initializes the plugin and coordinates the modules.
 *
 * @link       https://mindqtrl.com/
 * @since      1.2.0
 */

(function() {
    'use strict';

    // Store Vue app reference
    let vueApp = null;

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSpaceAccessControl);
    } else {
        initSpaceAccessControl();
    }

    /**
     * Initialize space access control with timeout prevention
     */
    function initSpaceAccessControl() {
        console.log('MQSA: Initializing space access control');

        // Add timeout prevention to avoid infinite waiting
        let attempts = 0;
        const maxAttempts = 50; // 5 seconds max wait

        const appInitInterval = setInterval(() => {
            attempts++;

            if (window.fluentFrameworkApp) {
                vueApp = window.fluentFrameworkApp;
                clearInterval(appInitInterval);

                try {
                    // Set up Vue router watcher
                    MQSAVueIntegration.setupVueRouterWatcher(vueApp);

                    // Apply controls for current route
                    MQSAVueIntegration.applySpaceAccessControl(vueApp);

                    // Set up cleanup on page unload
                    setupCleanup();
                } catch (error) {
                    console.error('MQSA: Error during initialization:', error);
                }
            }

            // Clear interval after max attempts to prevent infinite waiting
            if (attempts >= maxAttempts) {
                console.warn('MQSA: Timed out waiting for Fluent Community Vue app');
                clearInterval(appInitInterval);
            }
        }, 100);
    }

    /**
     * Set up cleanup functions for when the user navigates away
     */
    function setupCleanup() {
        // Clean up when navigating away from the page
        window.addEventListener('beforeunload', () => {
            MQSAAPI.clearActiveRequests();
            MQSARestrictions.cleanupStyles();
        });

        // Clean up when the Vue app is destroyed
        if (vueApp && vueApp.$once) {
            vueApp.$once('hook:beforeDestroy', () => {
                MQSAAPI.clearActiveRequests();
                MQSARestrictions.cleanupStyles();
            });
        }
    }
})();
