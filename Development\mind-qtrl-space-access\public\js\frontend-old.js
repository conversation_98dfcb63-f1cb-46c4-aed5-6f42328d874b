/**
 * Optimized JavaScript for Mind Qtrl | Space Access Control
 *
 * This file handles the client-side functionality for controlling access to Fluent Community spaces
 * with improved performance and error handling.
 *
 * Improvements:
 * - Debouncing for expensive functions
 * - Proper error handling
 * - Event delegation
 * - Optimized DOM operations
 * - Timeout prevention for Vue app initialization
 * - Improved fetch interception
 *
 * @link       https://mindqtrl.com/
 * @since      1.1.0
 */

(function() {
    'use strict';

    // Store Vue app reference
    let vueApp = null;

    // Debounce function to limit how often a function can be called
    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSpaceAccessControl);
    } else {
        initSpaceAccessControl();
    }

    /**
     * Initialize space access control with timeout prevention
     */
    function initSpaceAccessControl() {
        console.log('MQSA: Initializing space access control');
        
        // Add timeout prevention to avoid infinite waiting
        let attempts = 0;
        const maxAttempts = 50; // 5 seconds max wait
        
        const appInitInterval = setInterval(() => {
            attempts++;
            
            if (window.fluentFrameworkApp) {
                vueApp = window.fluentFrameworkApp;
                clearInterval(appInitInterval);
                
                try {
                    // Set up Vue router watcher
                    setupVueRouterWatcher();
                    
                    // Apply controls for current route
                    applySpaceAccessControl();
                } catch (error) {
                    console.error('MQSA: Error during initialization:', error);
                }
            }
            
            // Clear interval after max attempts to prevent infinite waiting
            if (attempts >= maxAttempts) {
                console.warn('MQSA: Timed out waiting for Fluent Community Vue app');
                clearInterval(appInitInterval);
            }
        }, 100);
    }

    /**
     * Set up Vue router watcher with debouncing
     */
    function setupVueRouterWatcher() {
        if (!vueApp || !vueApp.$router) {
            console.warn('MQSA: Vue router not available');
            return;
        }
        
        // Use debouncing to prevent multiple rapid calls
        const debouncedApplyControl = debounce((to) => {
            try {
                // Check if route is space related
                if (isSpaceRoute(to)) {
                    applySpaceAccessControl();
                }
            } catch (error) {
                console.error('MQSA: Error in router watcher:', error);
            }
        }, 300);
        
        vueApp.$router.afterEach(debouncedApplyControl);
    }

    /**
     * Check if the current route is a Fluent Community space route
     * Optimized with more efficient checks
     */
    function isSpaceRoute(route) {
        try {
            // Check route name and path (most efficient)
            if (route && (
                (route.name && (route.name.startsWith('space_') || route.name.includes('space'))) ||
                (route.path && (route.path.includes('/space/') || route.path.includes('/spaces/')))
            )) {
                return true;
            }
            
            // Check URL pattern as fallback (less expensive than DOM queries)
            const url = window.location.href;
            if (url.includes('/space/') || url.includes('/spaces/') || url.includes('/community/space/')) {
                return true;
            }
            
            // Last resort: check for space-related data attributes in DOM
            // Combined selectors for fewer DOM operations
            return !!document.querySelector('[data-route="space"], [data-space-id], .fcom_space_header');
        } catch (error) {
            console.error('MQSA: Error checking space route:', error);
            return false;
        }
    }

    /**
     * Apply space access control with error handling
     */
    async function applySpaceAccessControl() {
        try {
            const spaceId = getSpaceIdFromRoute();
            if (!spaceId) return;

            // Get space data from Vuex store
            const space = vueApp.$store.state.spaces.currentSpace;
            if (!space || !space.mqsa_settings) return;

            // Check if user is member
            const isMember = await checkSpaceMembership(spaceId);

            // Update Vuex store permissions based on restrictions
            updateVuePermissions(space, isMember);

            // Apply client-side restrictions for non-members
            if (!isMember) {
                applySpaceRestrictions(spaceId, space);
            }
        } catch (error) {
            console.error('MQSA: Error applying space access control:', error);
        }
    }

    /**
     * Get space ID from current route with error handling
     */
    function getSpaceIdFromRoute() {
        try {
            // Check if we're on a space route
            const route = vueApp.$route;
            if (!isSpaceRoute(route)) return null;

            // Try to get space ID from route params
            if (route.params) {
                if (route.params.space_id) return parseInt(route.params.space_id);
                if (route.params.id) return parseInt(route.params.id);
                
                // Some routes use slug instead of ID
                if (route.params.space) {
                    const space = vueApp.$store.state.spaces.currentSpace;
                    if (space && space.id) return parseInt(space.id);
                }
            }

            // Fallback: try to get space ID from Vuex store
            const space = vueApp.$store.state.spaces.currentSpace;
            if (space && space.id) return parseInt(space.id);

            return null;
        } catch (error) {
            console.error('MQSA: Error getting space ID:', error);
            return null;
        }
    }

    /**
     * Check if user is a member of the space with improved error handling
     */
    async function checkSpaceMembership(spaceId) {
        try {
            const response = await fetch(`${mqsaSettings.ajaxurl}?action=mqsa_check_membership&space_id=${spaceId}&nonce=${mqsaSettings.nonce}`);
            const data = await response.json();

            if (data.success && data.data) {
                return data.data.is_member;
            }
        } catch (error) {
            console.error('MQSA: Error checking membership:', error);
        }

        return false;
    }

    /**
     * Update Vue permissions in the Vuex store with error handling
     */
    function updateVuePermissions(space, isMember) {
        try {
            const settings = space.mqsa_settings;
            if (!settings || settings.enable_restrictions !== 'yes') return;

            // Get current permissions from store
            const currentPermissions = vueApp.$store.state.spaces.currentSpacePermissions || {};

            // Create updated permissions object
            const updatedPermissions = { ...currentPermissions };

            // Apply view restrictions
            if (settings.restrict_view === 'yes' && !space.mqsa_user_meets_requirements) {
                updatedPermissions.can_view_posts = false;
                updatedPermissions.can_view_info = false;
            }

            // Apply unjoined member view restrictions
            if (settings.restrict_view_unjoined === 'yes' && !isMember) {
                updatedPermissions.can_view_posts = false;
                updatedPermissions.can_view_info = false;
            }

            // Apply post restrictions
            if ((settings.restrict_post === 'yes' && !space.mqsa_user_meets_requirements) || 
                (settings.restrict_post_unjoined === 'yes' && !isMember)) {
                updatedPermissions.can_create_post = false;
            }

            // Apply comment restrictions
            if ((settings.restrict_comment === 'yes' && !space.mqsa_user_meets_requirements) || 
                (settings.restrict_comment_unjoined === 'yes' && !isMember)) {
                updatedPermissions.can_comment = false;
                updatedPermissions.can_react = false;
            }

            // Apply like restrictions
            if ((settings.restrict_like === 'yes' && !space.mqsa_user_meets_requirements) || 
                (settings.restrict_like_unjoined === 'yes' && !isMember)) {
                updatedPermissions.can_like = false;
                updatedPermissions.can_react = false;
            }

            // Update the Vuex store
            vueApp.$store.commit('spaces/setCurrentSpacePermissions', updatedPermissions);
        } catch (error) {
            console.error('MQSA: Error updating Vue permissions:', error);
        }
    }

    /**
     * Apply space restrictions with error handling
     */
    function applySpaceRestrictions(spaceId, space) {
        try {
            const settings = space.mqsa_settings;
            if (!settings || settings.enable_restrictions !== 'yes') return;

            // 1. Restrict viewing space if enabled
            if ((settings.restrict_view === 'yes' && !space.mqsa_user_meets_requirements) || 
                settings.restrict_view_unjoined === 'yes') {
                restrictSpaceView(settings, space);
                restrictSpaceJoin(settings);
            }

            // 2. Restrict joining space if enabled
            if ((settings.restrict_join === 'yes' && !space.mqsa_user_meets_requirements) || 
                settings.restrict_join_unjoined === 'yes') {
                restrictSpaceJoin(settings);
            }

            // 3. Restrict posting in space if enabled
            if ((settings.restrict_post === 'yes' && !space.mqsa_user_meets_requirements) || 
                settings.restrict_post_unjoined === 'yes') {
                restrictSpacePosting(settings);
            }

            // 4. Restrict commenting if enabled
            if ((settings.restrict_comment === 'yes' && !space.mqsa_user_meets_requirements) || 
                settings.restrict_comment_unjoined === 'yes') {
                restrictSpaceComments(settings);
            }

            // 5. Restrict liking if enabled
            if ((settings.restrict_like === 'yes' && !space.mqsa_user_meets_requirements) || 
                settings.restrict_like_unjoined === 'yes') {
                restrictSpaceLikes(settings);
            }

            // Set up unified API interception for all restrictions
            setupApiInterception(settings);
        } catch (error) {
            console.error('MQSA: Error applying space restrictions:', error);
        }
    }

    /**
     * Restrict space view with error handling
     */
    function restrictSpaceView(settings, space) {
        try {
            // Find the content container
            const contentContainer = document.querySelector('.fcom_space_content_wrap');
            if (!contentContainer) return;

            // Get the message
            const message = settings.view_message || mqsaPublic.default_messages.access;

            // Check if MQSADialog is available
            if (window.MQSADialog) {
                // Show modern dialog
                MQSADialog.show({
                    title: 'Access Restricted',
                    message: message,
                    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>',
                    buttons: [
                        {
                            text: 'OK',
                            type: 'primary',
                            onClick: function() {
                                // Navigate back if possible
                                if (vueApp && vueApp.$router) {
                                    vueApp.$router.push({ name: 'spaces' });
                                }
                            }
                        }
                    ],
                    onClose: function() {
                        // Navigate back if possible
                        if (vueApp && vueApp.$router) {
                            vueApp.$router.push({ name: 'spaces' });
                        }
                    }
                });
            } else {
                // Fallback to simple HTML message
                contentContainer.innerHTML = `
                    <div class="mqsa-access-denied">
                        <div class="mqsa-access-denied-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24">
                                <path fill="none" stroke="currentColor" stroke-width="2" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                            </svg>
                        </div>
                        <div class="mqsa-access-denied-message">${message}</div>
                    </div>
                `;
            }

            // Also update the Vuex store to reflect the access restriction
            vueApp.$store.commit('spaces/setCurrentSpacePermissions', {
                can_view_posts: false,
                can_view_info: false
            });
        } catch (error) {
            console.error('MQSA: Error restricting space view:', error);
        }
    }

    /**
     * Restrict joining space with optimized event delegation
     */
    function restrictSpaceJoin(settings) {
        try {
            // Check if we should hide join buttons
            const hideJoinButtons = settings.hide_join_buttons === 'yes';
            
            // Only hide join buttons if the setting is enabled
            if (hideJoinButtons) {
                // Hide join button with CSS - using the recommended selectors from fcom_mlive_integration.md
                const style = document.createElement('style');
                style.textContent = `
                    /* Hide all join buttons */
                    .fcom_space_join_btn,
                    .btn_join_space,
                    .fcom_space_request_btn,
                    .menu_actions .fcom_primary_button,
                    .menu_actions .el-button:first-child,
                    .fcom_space_header_actions button,
                    .fcom_space_header_actions .el-button,
                    .fcom_space_actions button,
                    .fcom_space_actions .el-button,
                    [data-action="join"],
                    button[title*="Join"],
                    button[title*="join"],
                    .el-button[title*="Join"],
                    .el-button[title*="join"] {
                        display: none !important;
                    }
                `;
                document.head.appendChild(style);
            }

            // Update the Vue store permissions to prevent joining
            if (vueApp && vueApp.$store) {
                try {
                    // Get current permissions
                    const currentPermissions = vueApp.$store.state.spaces.currentSpacePermissions || {};

                    // Update permissions to prevent joining
                    vueApp.$store.commit('spaces/setCurrentSpacePermissions', {
                        ...currentPermissions,
                        can_join: false,
                        can_request: false
                    });
                } catch (e) {
                    console.error('MQSA: Error updating Vue store permissions', e);
                }
            }
        } catch (error) {
            console.error('MQSA: Error restricting space join:', error);
        }
    }

    /**
     * Restrict posting in space with optimized DOM operations
     */
    function restrictSpacePosting(settings) {
        try {
            // Hide post creation elements with CSS
            const style = document.createElement('style');
            style.textContent = `
                .create_status_holder,
                .fcom_feedform_wrap,
                .fcom_create_feed_btn,
                .fcom_feed_form,
                .fcom_status_form,
                .fcom_new_post_btn,
                .fcom_post_composer,
                .fcom_feed_composer,
                .el-button--primary[data-action="create-post"],
                .fcom_create_post_btn,
                .fcom_new_activity_btn {
                    display: none !important;
                }
            `;
            document.head.appendChild(style);
        } catch (error) {
            console.error('MQSA: Error restricting space posting:', error);
        }
    }

    /**
     * Restrict commenting in space with optimized DOM operations
     */
    function restrictSpaceComments(settings) {
        try {
            // Hide comment elements with CSS
            const style = document.createElement('style');
            style.textContent = `
                /* Hide all comment elements (text replies, not emoji reactions) */
                .fcom_comment_btn_wrap,
                .fcom_comment_form,
                .fcom_poll_voting,
                .fcom_comment_button,
                .fcom_comment_btn,
                .fcom_comment_action,
                .fcom_comment_icon,
                .fcom_comment_count,
                .fcom_comment_area,
                .fcom_comment_list,
                .fcom_comment_input,
                .fcom_comment_submit,
                .el-button[data-action="comment"],
                button[data-action="comment"],
                .fcom_feed_item_footer .fcom_comment_btn_wrap {
                    display: none !important;
                }
            `;
            document.head.appendChild(style);
        } catch (error) {
            console.error('MQSA: Error restricting space comments:', error);
        }
    }

    /**
     * Restrict liking/reacting to activities with optimized DOM operations
     */
    function restrictSpaceLikes(settings) {
        try {
            // Check if we should hide like buttons
            const hideLikeButtons = settings.hide_like_buttons === 'yes';
            
            if (hideLikeButtons) {
                // Hide like/reaction elements with CSS
                const style = document.createElement('style');
                style.textContent = `
                    /* Hide all like/reaction elements (emoji reactions, not text comments) */
                    .fcom_reaction,
                    .fcom_reaction_list,
                    .fcom_reaction_btn,
                    .fcom_reaction_icon,
                    .fcom_reaction_count,
                    .fcom_like_btn,
                    .fcom_like_icon,
                    .fcom_like_count,
                    .el-button[data-action="like"],
                    button[data-action="like"],
                    .fcom_feed_item_footer .fcom_reaction,
                    .feed_footer .fcom_reaction,
                    .feed_actions .fcom_reaction,
                    .fcom_feed_item .fcom_reaction,
                    .fcom_feed_actions .fcom_reaction,
                    .fcom_feed_item_actions .fcom_reaction,
                    .fcom_feed_item_footer .feed_actions button[data-type="like"],
                    .fcom_feed_item_footer .feed_actions .fcom_reaction_list,
                    .feed_footer .fcom_reaction_list,
                    .mqcr-reaction,
                    .mqcr-reaction-like,
                    [data-v-component="FCReactionBar"],
                    .fcom_reaction_wrapper,
                    .fcom_feed_reaction {
                        display: none !important;
                    }
                `;
                document.head.appendChild(style);
            }
        } catch (error) {
            console.error('MQSA: Error restricting space likes:', error);
        }
    }

    /**
     * Set up unified event delegation for all restriction types
     * This replaces multiple event listeners with a single one
     */
    function setupEventDelegation() {
        try {
            // Single event listener for all click events
            document.addEventListener('click', function(event) {
                // Find the clicked element
                let target = event.target;
                let maxDepth = 5; // Maximum parent levels to check
                
                // Check if we're on a space page
                if (!isSpacePage(vueApp.$route)) return;
                
                // Get space data
                const space = vueApp.$store.state.spaces.currentSpace;
                if (!space || !space.mqsa_settings) return;
                
                const settings = space.mqsa_settings;
                
                // Check up to maxDepth levels of parent elements
                for (let i = 0; i < maxDepth; i++) {
                    if (!target) break;
                    
                    // Check for join button
                    if (isJoinButton(target) && 
                        ((settings.restrict_join === 'yes' && !space.mqsa_user_meets_requirements) || 
                         settings.restrict_join_unjoined === 'yes')) {
                        event.preventDefault();
                        event.stopPropagation();
                        showMessage('Join Restriction', settings.join_message || mqsaPublic.default_messages.join);
                        return false;
                    }
                    
                    // Check for like button
                    if (isLikeButton(target) && 
                        ((settings.restrict_like === 'yes' && !space.mqsa_user_meets_requirements) || 
                         settings.restrict_like_unjoined === 'yes')) {
                        event.preventDefault();
                        event.stopPropagation();
                        showMessage('Like Restriction', settings.like_message || 'You cannot like posts in this space.');
                        return false;
                    }
                    
                    // Check for comment button
                    if (isCommentButton(target) && 
                        ((settings.restrict_comment === 'yes' && !space.mqsa_user_meets_requirements) || 
                         settings.restrict_comment_unjoined === 'yes')) {
                        event.preventDefault();
                        event.stopPropagation();
                        showMessage('Comment Restriction', settings.comment_message || 'You cannot comment in this space.');
                        return false;
                    }
                    
                    // Move up to parent
                    target = target.parentElement;
                }
            }, true); // Use capture phase to intercept events before they reach their targets
        } catch (error) {
            console.error('MQSA: Error setting up event delegation:', error);
        }
    }

    /**
     * Helper functions to identify button types
     */
    function isJoinButton(element) {
        return element.classList && (
            element.classList.contains('fcom_space_join_btn') ||
            element.classList.contains('btn_join_space') ||
            element.classList.contains('fcom_space_request_btn') ||
            (element.classList.contains('fcom_primary_button') && element.closest('.menu_actions')) ||
            (element.classList.contains('el-button') && element.closest('.menu_actions')) ||
            (element.closest('.fcom_space_header_actions') && 
             (element.tagName === 'BUTTON' || element.classList.contains('el-button'))) ||
            (element.hasAttribute('data-action') && element.getAttribute('data-action') === 'join') ||
            (element.tagName.toLowerCase() === 'button' && 
             ((element.title && (element.title.includes('Join') || element.title.includes('join'))) ||
              (element.textContent && element.textContent.toLowerCase().includes('join'))))
        );
    }

    function isLikeButton(element) {
        return element.classList && (
            element.classList.contains('fcom_reaction') ||
            element.classList.contains('fcom_reaction_btn') ||
            element.classList.contains('fcom_like_btn') ||
            element.classList.contains('mqcr-reaction') ||
            element.classList.contains('fcom_reaction_wrapper') ||
            element.closest('.fcom_reaction_wrapper') ||
            (element.hasAttribute('data-action') && element.getAttribute('data-action') === 'like') ||
            (element.hasAttribute('data-type') && element.getAttribute('data-type') === 'like')
        );
    }

    function isCommentButton(element) {
        return element.classList && (
            element.classList.contains('fcom_comment_btn') ||
            element.closest('.fcom_comment_btn_wrap') ||
            element.classList.contains('fcom_comment_action') ||
            (element.hasAttribute('data-action') && element.getAttribute('data-action') === 'comment') ||
            (element.tagName.toLowerCase() === 'button' && 
             ((element.title && (element.title.includes('Comment') || element.title.includes('comment'))) ||
              (element.textContent && element.textContent.toLowerCase().includes('comment'))))
        );
    }

    /**
     * Improved fetch interception for better performance
     */
    function setupApiInterception(settings) {
        try {
            // Store original fetch
            const originalFetch = window.fetch;
            
            // Replace fetch with our interceptor
            window.fetch = function(url, options) {
                // Only intercept string URLs and POST requests
                if (typeof url !== 'string' || !options || options.method !== 'POST') {
                    return originalFetch.apply(this, arguments);
                }
                
                // Check if this is a restricted API call
                let restrictionType = null;
                let message = null;
                
                // Join/request API calls
                if ((settings.restrict_join === 'yes' || settings.restrict_join_unjoined === 'yes') && 
                    (url.includes('/join_space') || url.includes('/request_join') || url.includes('/accept_request'))) {
                    restrictionType = 'Join';
                    message = settings.join_message || mqsaPublic.default_messages.join;
                }
                // Post API calls
                else if ((settings.restrict_post === 'yes' || settings.restrict_post_unjoined === 'yes') && 
                         (url.includes('/create_post') || url.includes('/create_status'))) {
                    restrictionType = 'Post';
                    message = settings.post_message || 'You cannot post in this space.';
                }
                // Comment API calls
                else if ((settings.restrict_comment === 'yes' || settings.restrict_comment_unjoined === 'yes') && 
                         (url.includes('/create_comment') || url.includes('/add_comment'))) {
                    restrictionType = 'Comment';
                    message = settings.comment_message || 'You cannot comment in this space.';
                }
                // Like API calls
                else if ((settings.restrict_like === 'yes' || settings.restrict_like_unjoined === 'yes') && 
                         (url.includes('/like_post') || url.includes('/like_activity') || url.includes('/react'))) {
                    restrictionType = 'Like';
                    message = settings.like_message || 'You cannot like posts in this space.';
                }
                
                // If this is a restricted call, show message and return fake response
                if (restrictionType && message) {
                    showMessage(restrictionType + ' Restriction', message);
                    
                    // Return a fake response
                    return Promise.resolve({
                        ok: false,
                        status: 403,
                        json: () => Promise.resolve({ 
                            success: false, 
                            message: message 
                        })
                    });
                }
                
                // Otherwise, proceed with the original fetch
                return originalFetch.apply(this, arguments);
            };
        } catch (error) {
            console.error('MQSA: Error setting up API interception:', error);
        }
    }

    /**
     * Unified function to show messages using the best available method
     */
    function showMessage(title, message) {
        try {
            // Try to use FluentCommunityApp.$toast first
            if (window.FluentCommunityApp && window.FluentCommunityApp.$toast) {
                window.FluentCommunityApp.$toast.warning({
                    title: title,
                    message: message,
                    duration: 5000
                });
                return;
            }
            
            // Try to use MQSADialog if available
            if (window.MQSADialog) {
                MQSADialog.show({
                    title: title,
                    message: message,
                    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>',
                    type: 'warning'
                });
                return;
            }
            
            // Try to use Element UI message box if available
            if (window.ELEMENT && window.ELEMENT.MessageBox) {
                window.ELEMENT.MessageBox.alert(message, title, {
                    confirmButtonText: 'OK',
                    type: 'warning'
                });
                return;
            }
            
            // Fallback to alert
            alert(title + ': ' + message);
        } catch (error) {
            console.error('MQSA: Error showing message:', error);
            // Ultimate fallback
            alert(title + ': ' + message);
        }
    }

    // Set up event delegation for all restriction types
    document.addEventListener('DOMContentLoaded', setupEventDelegation);
    
    // Initialize event delegation if the DOM is already ready
    if (document.readyState === 'interactive' || document.readyState === 'complete') {
        setupEventDelegation();
    }
})();
