/**
 * Public JavaScript for Mind Qtrl | Space Access Control
 *
 * This file handles the client-side functionality for controlling access to Fluent Community spaces:
 * - Restricting access to spaces based on user badges, CRM tags, and leaderboard levels
 * - Preventing users from joining spaces
 * - Preventing users from posting in spaces
 * - Preventing users from commenting and reacting in spaces
 *
 * @link       https://mindqtrl.com/
 * @since      0.0.2
 */

(function() {
    'use strict';

    // Store Vue app reference
    let vueApp = null;
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSpaceAccessControl);
    } else {
        initSpaceAccessControl();
    }

    /**
     * Initialize space access control
     */
    function initSpaceAccessControl() {
        // Wait for Fluent Community Vue app to initialize
        const appInitInterval = setInterval(() => {
            if (window.fluentFrameworkApp) {
                vueApp = window.fluentFrameworkApp;
                clearInterval(appInitInterval);
                
                // Set up Vue router watcher
                setupVueRouterWatcher();
                
                // Apply controls for current route
                applySpaceAccessControl();
            }
        }, 100);
    }

    /**
     * Set up Vue router watcher
     */
    function setupVueRouterWatcher() {
        vueApp.$router.afterEach((to) => {
            // Check if route is space related
            if (to.name && to.name.startsWith('space_')) {
                setTimeout(() => {
                    applySpaceAccessControl();
                }, 300);
            }
        });
    }

    /**
     * Apply space access control
     */
    async function applySpaceAccessControl() {
        const spaceId = getSpaceIdFromRoute();
        if (!spaceId) return;

        // Get space data from Vuex store
        const space = vueApp.$store.state.spaces.currentSpace;
        if (!space) return;

        // Check if user is member
        const isMember = await checkSpaceMembership(spaceId);
        
        if (!isMember) {
            applySpaceRestrictions(spaceId, space);
        }
    }

    /**
     * Check space membership status
     */
    async function checkSpaceMembership(spaceId) {
        try {
            const response = await fetch(`${mqsaPublic.ajax_url}?action=mqsa_check_membership&space_id=${spaceId}&_wpnonce=${mqsaPublic.nonce}`);
            const data = await response.json();
            return data.is_member;
        } catch (error) {
            console.error('Failed to check membership:', error);
            return false;
        }
    }

    /**
     * Apply space restrictions
     */
    function applySpaceRestrictions(spaceId, space) {
        const settings = mqsaSpaceSettings[spaceId];
        if (!settings) return;
        // 2. Restrict viewing space if enabled
        if (settings.restrict_view === 'yes') {
            restrictSpaceView(settings, space);
        }
        
        // 2. Restrict joining space if enabled
        if (settings.restrict_join === 'yes') {
            restrictSpaceJoin(settings);
        }
        
        // 3. Restrict posting in space if enabled
        if (settings.restrict_post === 'yes') {
            restrictSpacePosting(settings);
        }
        
        // 4. Restrict commenting and reacting if enabled
        if (settings.restrict_comment === 'yes') {
            restrictSpaceComments(settings);
        }
    }

    /**
     * Restrict space view
     */
    function restrictSpaceView(settings, space) {
        // Replace space content with access denied message
        const contentContainer = document.querySelector('.fcom_space_content_wrap');
        if (contentContainer) {
            const message = settings.view_message || mqsaPublic.default_messages.access;
            contentContainer.innerHTML = `
                <div class="mqsa-access-denied">
                    <div class="mqsa-access-denied-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24">
                            <path fill="none" stroke="currentColor" stroke-width="2" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                        </svg>
                    </div>
                    <div class="mqsa-access-denied-message">${message}</div>
                </div>
            `;
        }
    }
    
    /**
     * Restrict joining space
     * 
     * @param {Object} settings The space settings
     */
    function restrictSpaceJoin(settings) {
        logDebug('Restricting space join');
        
        // Hide join button with CSS
        const style = document.createElement('style');
        style.textContent = `
            .fcom_space_join_btn,
            .btn_join_space, 
            .fcom_space_request_btn {
                display: none !important;
            }
        `;
        document.head.appendChild(style);
        
        // Also set up observer to handle dynamically added join buttons
        const joinButtonObserver = new MutationObserver(function(mutations) {
            const joinButtons = document.querySelectorAll('.fcom_space_join_btn, .btn_join_space, .fcom_space_request_btn');
            joinButtons.forEach(function(button) {
                button.style.display = 'none';
                
                // Add tooltip with message
                const message = settings.join_message || mqsaPublic.default_messages.join;
                button.setAttribute('title', message);
                button.setAttribute('data-mqsa-disabled', 'true');
            });
        });
        
        // Observe the entire document for added buttons
        joinButtonObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * Restrict posting in space
     * 
     * @param {Object} settings The space settings
     */
    function restrictSpacePosting(settings) {
        logDebug('Restricting space posting');
        
        // Hide post creation elements with CSS
        const style = document.createElement('style');
        style.textContent = `
            .create_status_holder,
            .fcom_feedform_wrap,
            .fcom_create_feed_btn {
                display: none !important;
            }
        `;
        document.head.appendChild(style);
        
        // Also set up observer to handle dynamically added elements
        const postingElementsObserver = new MutationObserver(function(mutations) {
            const elements = document.querySelectorAll('.create_status_holder, .fcom_feedform_wrap, .fcom_create_feed_btn');
            elements.forEach(function(element) {
                element.style.display = 'none';
            });
        });
        
        // Observe the entire document for added elements
        postingElementsObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * Restrict commenting and reacting
     * 
     * @param {Object} settings The space settings
     */
    function restrictSpaceComments(settings) {
        logDebug('Restricting space comments and reactions');
        
        // Hide comment and reaction elements with CSS
        const style = document.createElement('style');
        style.textContent = `
            .fcom_comment_btn_wrap,
            .fcom_reaction,
            .fcom_comment_form,
            .fcom_poll_voting,
            .fcom_feed_actions {
                display: none !important;
            }
        `;
        document.head.appendChild(style);
        
        // Also set up observer to handle dynamically added elements
        const commentElementsObserver = new MutationObserver(function(mutations) {
            const elements = document.querySelectorAll('.fcom_comment_btn_wrap, .fcom_reaction, .fcom_comment_form, .fcom_poll_voting, .fcom_feed_actions');
            elements.forEach(function(element) {
                element.style.display = 'none';
            });
        });
        
        // Observe the entire document for added elements
        commentElementsObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
})(); 
