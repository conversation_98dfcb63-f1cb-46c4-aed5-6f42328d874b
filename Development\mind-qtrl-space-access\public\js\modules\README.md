# Mind QTRL Space Access - Modular JavaScript Structure

This directory contains the modular JavaScript files for the Mind QTRL Space Access plugin. The code has been split into logical modules to improve maintainability and performance.

## Module Structure

### 1. Utils Module (`utils.js`)
Contains utility functions used across the plugin:
- Debounce function to limit how often a function can be called
- Functions to check if a route is a space route
- Functions to get space ID from the current route
- Unified message display function

### 2. API Module (`api.js`)
Handles API requests and responses:
- Membership checking with request cancellation and caching
- API interception for restricted actions
- Functions to clear active requests and cache

### 3. Restrictions Module (`restrictions.js`)
Implements the different types of space restrictions:
- View restrictions
- Join restrictions
- Posting restrictions
- Commenting restrictions
- Like restrictions
- Event delegation for handling restricted actions
- DOM manipulation functions

### 4. Vue Integration Module (`vue-integration.js`)
Integrates with Vue.js and Vuex:
- Vue router watcher setup
- Space access control application
- Vuex store permission updates

### 5. Main Frontend Module (`frontend-modular.js`)
Initializes the plugin and coordinates the modules:
- Vue app initialization with timeout prevention
- Module coordination
- Cleanup functions

## Benefits of Modular Structure

1. **Improved Maintainability**: Each module has a single responsibility, making the code easier to understand and maintain.
2. **Better Performance**: The modular structure allows for more efficient code organization and execution.
3. **Easier Debugging**: Issues can be isolated to specific modules, making debugging easier.
4. **Code Reusability**: Modules can be reused in other parts of the plugin or in other plugins.
5. **Parallel Development**: Multiple developers can work on different modules simultaneously.

## Timeout Issue Fixes

The modular structure addresses the timeout issues that occurred when quickly switching between spaces:

1. **Request Cancellation**: The API module implements request cancellation using AbortController to cancel previous requests when a new navigation occurs.
2. **Request Caching**: A simple cache stores membership data for spaces the user has already visited, reducing redundant requests.
3. **Operation Locking**: A flag prevents multiple instances of space access control from running concurrently.
4. **Optimized DOM Operations**: DOM operations are optimized by using a single style element for each restriction type.
5. **Cleanup on Navigation**: A cleanup function cancels pending operations and removes temporary DOM elements when navigating away from a space.

## How to Use

The modular structure is automatically used if the module files are present. The plugin will fall back to the original frontend.js file if the module files are not found.
