/**
 * API Integration for Mind Qtrl | Space Access Control
 *
 * This file contains functions for making API requests and handling responses.
 *
 * @link       https://mindqtrl.com/
 * @since      1.2.0
 */

const MQSAAPI = (function() {
    'use strict';

    // Store active requests for cancellation
    const activeRequests = {};

    // Simple cache for membership data
    const membershipCache = {};

    // Cache expiration time (5 minutes)
    const CACHE_EXPIRATION = 5 * 60 * 1000;

    /**
     * Check if user is a member of the space with improved error handling
     * Includes request cancellation and caching
     *
     * @param {number} spaceId - The space ID to check
     * @return {Promise<boolean>} - Whether the user is a member
     */
    async function checkSpaceMembership(spaceId) {
        try {
            // Check cache first
            const cacheKey = `space_${spaceId}`;
            const cachedData = membershipCache[cacheKey];

            if (cachedData && (Date.now() - cachedData.timestamp < CACHE_EXPIRATION)) {
                console.log(`MQSA: Using cached membership data for space ${spaceId}`);
                return cachedData.isMember;
            }

            // Cancel any existing request for this space
            if (activeRequests[cacheKey]) {
                console.log(`MQSA: Cancelling previous membership check for space ${spaceId}`);
                activeRequests[cacheKey].abort();
            }

            // Create a new AbortController
            const controller = new AbortController();
            activeRequests[cacheKey] = controller;

            // Make the request with the signal
            const response = await fetch(
                `${mqsaSettings.ajaxurl}?action=mqsa_check_membership&space_id=${spaceId}&nonce=${mqsaSettings.nonce}`,
                { signal: controller.signal }
            );

            const data = await response.json();

            // Remove from active requests
            delete activeRequests[cacheKey];

            if (data.success && data.data) {
                // Cache the result
                membershipCache[cacheKey] = {
                    isMember: data.data.is_member,
                    timestamp: Date.now()
                };

                return data.data.is_member;
            }
        } catch (error) {
            // Don't log aborted requests as errors
            if (error.name !== 'AbortError') {
                console.error('MQSA: Error checking membership:', error);
            }
        }

        return false;
    }

    /**
     * Set up fetch interception for API requests
     *
     * @param {Object} settings - The space settings
     */
    function setupApiInterception(settings) {
        try {
            // Store original fetch
            const originalFetch = window.fetch;

            // Replace fetch with our interceptor
            window.fetch = function(url, options) {
                // Only intercept string URLs and POST requests
                if (typeof url !== 'string' || !options || options.method !== 'POST') {
                    return originalFetch.apply(this, arguments);
                }

                // Check if this is a restricted API call
                let restrictionType = null;
                let message = null;

                // Join/request API calls
                if ((settings.restrict_join === 'yes' || settings.restrict_join_unjoined === 'yes') &&
                    (url.includes('/join_space') || url.includes('/request_join') || url.includes('/accept_request'))) {
                    restrictionType = 'Join';
                    message = settings.join_message || mqsaPublic.default_messages.join;
                }
                // Post API calls
                else if ((settings.restrict_post === 'yes' || settings.restrict_post_unjoined === 'yes') &&
                         (url.includes('/create_post') || url.includes('/create_status'))) {
                    restrictionType = 'Post';
                    message = settings.post_message || 'You cannot post in this space.';
                }
                // Comment API calls
                else if ((settings.restrict_comment === 'yes' || settings.restrict_comment_unjoined === 'yes') &&
                         (url.includes('/create_comment') || url.includes('/add_comment'))) {
                    restrictionType = 'Comment';
                    message = settings.comment_message || 'You cannot comment in this space.';
                }
                // Like API calls
                else if ((settings.restrict_like === 'yes' || settings.restrict_like_unjoined === 'yes') &&
                         (url.includes('/like_post') || url.includes('/like_activity') || url.includes('/react'))) {
                    restrictionType = 'Like';
                    message = settings.like_message || 'You cannot like posts in this space.';
                }

                // If this is a restricted call, show message and return fake response
                if (restrictionType && message) {
                    MQSAUtils.showMessage(restrictionType + ' Restriction', message);

                    // Return a fake response
                    return Promise.resolve({
                        ok: false,
                        status: 403,
                        json: () => Promise.resolve({
                            success: false,
                            message: message
                        })
                    });
                }

                // Otherwise, proceed with the original fetch
                return originalFetch.apply(this, arguments);
            };
        } catch (error) {
            console.error('MQSA: Error setting up API interception:', error);
        }
    }

    /**
     * Clear all active requests
     */
    function clearActiveRequests() {
        Object.values(activeRequests).forEach(controller => {
            try {
                controller.abort();
            } catch (error) {
                console.error('MQSA: Error aborting request:', error);
            }
        });

        // Clear the active requests object
        Object.keys(activeRequests).forEach(key => {
            delete activeRequests[key];
        });
    }

    /**
     * Clear the membership cache
     *
     * @param {number} [spaceId] - Optional space ID to clear specific cache entry
     */
    function clearMembershipCache(spaceId) {
        if (spaceId) {
            const cacheKey = `space_${spaceId}`;
            delete membershipCache[cacheKey];
        } else {
            Object.keys(membershipCache).forEach(key => {
                delete membershipCache[key];
            });
        }
    }

    // Public API
    return {
        checkSpaceMembership,
        setupApiInterception,
        clearActiveRequests,
        clearMembershipCache
    };
})();

// Export the module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MQSAAPI;
}
