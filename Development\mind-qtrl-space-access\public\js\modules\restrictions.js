/**
 * Space Restrictions for Mind Qtrl | Space Access Control
 *
 * This file contains functions for applying different types of space restrictions.
 *
 * @link       https://mindqtrl.com/
 * @since      1.2.0
 */

const MQSARestrictions = (function() {
    'use strict';

    // Store style elements for cleanup
    const styleElements = {};

    /**
     * Apply space restrictions with error handling
     *
     * @param {number} spaceId - The space ID
     * @param {Object} space - The space object
     */
    function applySpaceRestrictions(spaceId, space) {
        try {
            const settings = space.mqsa_settings;
            if (!settings || settings.enable_restrictions !== 'yes') return;

            // 1. Restrict viewing space if enabled
            if ((settings.restrict_view === 'yes' && !space.mqsa_user_meets_requirements) ||
                settings.restrict_view_unjoined === 'yes') {
                restrictSpaceView(settings, space);
                restrictSpaceJoin(settings);
            }

            // 2. Restrict joining space if enabled
            if ((settings.restrict_join === 'yes' && !space.mqsa_user_meets_requirements) ||
                settings.restrict_join_unjoined === 'yes') {
                restrictSpaceJoin(settings);
            }

            // 3. Restrict posting in space if enabled
            if ((settings.restrict_post === 'yes' && !space.mqsa_user_meets_requirements) ||
                settings.restrict_post_unjoined === 'yes') {
                restrictSpacePosting(settings);
            }

            // 4. Restrict commenting if enabled
            if ((settings.restrict_comment === 'yes' && !space.mqsa_user_meets_requirements) ||
                settings.restrict_comment_unjoined === 'yes') {
                restrictSpaceComments(settings);
            }

            // 5. Restrict liking if enabled
            if ((settings.restrict_like === 'yes' && !space.mqsa_user_meets_requirements) ||
                settings.restrict_like_unjoined === 'yes') {
                restrictSpaceLikes(settings);
            }

            // Set up unified API interception for all restrictions
            MQSAAPI.setupApiInterception(settings);

            // Set up event delegation for all restrictions
            setupEventDelegation(settings, space);
        } catch (error) {
            console.error('MQSA: Error applying space restrictions:', error);
        }
    }

    /**
     * Restrict space view with error handling
     *
     * @param {Object} settings - The space settings
     * @param {Object} space - The space object
     */
    function restrictSpaceView(settings, space) {
        try {
            // Find the content container
            const contentContainer = document.querySelector('.fcom_space_content_wrap');
            if (!contentContainer) return;

            // Get the message
            const message = settings.view_message || mqsaPublic.default_messages.access;

            // Check if MQSADialog is available
            if (window.MQSADialog) {
                // Show modern dialog
                MQSADialog.show({
                    title: 'Access Restricted',
                    message: message,
                    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>',
                    buttons: [
                        {
                            text: 'OK',
                            type: 'primary',
                            onClick: function() {
                                // Navigate back if possible
                                if (window.fluentFrameworkApp && window.fluentFrameworkApp.$router) {
                                    window.fluentFrameworkApp.$router.push({ name: 'spaces' });
                                }
                            }
                        }
                    ],
                    onClose: function() {
                        // Navigate back if possible
                        if (window.fluentFrameworkApp && window.fluentFrameworkApp.$router) {
                            window.fluentFrameworkApp.$router.push({ name: 'spaces' });
                        }
                    }
                });
            } else {
                // Fallback to simple HTML message
                contentContainer.innerHTML = `
                    <div class="mqsa-access-denied">
                        <div class="mqsa-access-denied-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24">
                                <path fill="none" stroke="currentColor" stroke-width="2" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                            </svg>
                        </div>
                        <div class="mqsa-access-denied-message">${message}</div>
                    </div>
                `;
            }

            // Also update the Vuex store to reflect the access restriction
            if (window.fluentFrameworkApp && window.fluentFrameworkApp.$store) {
                window.fluentFrameworkApp.$store.commit('spaces/setCurrentSpacePermissions', {
                    can_view_posts: false,
                    can_view_info: false
                });
            }
        } catch (error) {
            console.error('MQSA: Error restricting space view:', error);
        }
    }

    /**
     * Restrict joining space with optimized DOM operations
     *
     * @param {Object} settings - The space settings
     */
    function restrictSpaceJoin(settings) {
        try {
            // Check if we should hide join buttons
            const hideJoinButtons = settings.hide_join_buttons === 'yes';

            // Only hide join buttons if the setting is enabled
            if (hideJoinButtons) {
                // Use a single style element for join button restrictions
                addOrUpdateStyle('join-restrictions', `
                    /* Hide all join buttons */
                    .fcom_space_join_btn,
                    .btn_join_space,
                    .fcom_space_request_btn,
                    .menu_actions .fcom_primary_button,
                    .menu_actions .el-button:first-child,
                    .fcom_space_header_actions button,
                    .fcom_space_header_actions .el-button,
                    .fcom_space_actions button,
                    .fcom_space_actions .el-button,
                    [data-action="join"],
                    button[title*="Join"],
                    button[title*="join"],
                    .el-button[title*="Join"],
                    .el-button[title*="join"] {
                        display: none !important;
                    }
                `);
            }

            // Update the Vue store permissions to prevent joining
            if (window.fluentFrameworkApp && window.fluentFrameworkApp.$store) {
                try {
                    // Get current permissions
                    const currentPermissions = window.fluentFrameworkApp.$store.state.spaces.currentSpacePermissions || {};

                    // Update permissions to prevent joining
                    window.fluentFrameworkApp.$store.commit('spaces/setCurrentSpacePermissions', {
                        ...currentPermissions,
                        can_join: false,
                        can_request: false
                    });
                } catch (e) {
                    console.error('MQSA: Error updating Vue store permissions', e);
                }
            }
        } catch (error) {
            console.error('MQSA: Error restricting space join:', error);
        }
    }

    /**
     * Restrict posting in space with optimized DOM operations
     *
     * @param {Object} settings - The space settings
     */
    function restrictSpacePosting(settings) {
        try {
            // Use a single style element for posting restrictions
            addOrUpdateStyle('post-restrictions', `
                .create_status_holder,
                .fcom_feedform_wrap,
                .fcom_create_feed_btn,
                .fcom_feed_form,
                .fcom_status_form,
                .fcom_new_post_btn,
                .fcom_post_composer,
                .fcom_feed_composer,
                .el-button--primary[data-action="create-post"],
                .fcom_create_post_btn,
                .fcom_new_activity_btn {
                    display: none !important;
                }
            `);
        } catch (error) {
            console.error('MQSA: Error restricting space posting:', error);
        }
    }

    /**
     * Restrict commenting in space with optimized DOM operations
     *
     * @param {Object} settings - The space settings
     */
    function restrictSpaceComments(settings) {
        try {
            // Use a single style element for comment restrictions
            addOrUpdateStyle('comment-restrictions', `
                /* Hide all comment elements (text replies, not emoji reactions) */
                .fcom_comment_btn_wrap,
                .fcom_comment_form,
                .fcom_poll_voting,
                .fcom_comment_button,
                .fcom_comment_btn,
                .fcom_comment_action,
                .fcom_comment_icon,
                .fcom_comment_count,
                .fcom_comment_area,
                .fcom_comment_list,
                .fcom_comment_input,
                .fcom_comment_submit,
                .el-button[data-action="comment"],
                button[data-action="comment"],
                .fcom_feed_item_footer .fcom_comment_btn_wrap {
                    display: none !important;
                }
            `);
        } catch (error) {
            console.error('MQSA: Error restricting space comments:', error);
        }
    }

    /**
     * Restrict liking/reacting to activities with optimized DOM operations
     *
     * @param {Object} settings - The space settings
     */
    function restrictSpaceLikes(settings) {
        try {
            // Check if we should hide like buttons
            const hideLikeButtons = settings.hide_like_buttons === 'yes';

            if (hideLikeButtons) {
                // Use a single style element for like restrictions
                addOrUpdateStyle('like-restrictions', `
                    /* Hide all like/reaction elements (emoji reactions, not text comments) */
                    .fcom_reaction,
                    .fcom_reaction_list,
                    .fcom_reaction_btn,
                    .fcom_reaction_icon,
                    .fcom_reaction_count,
                    .fcom_like_btn,
                    .fcom_like_icon,
                    .fcom_like_count,
                    .el-button[data-action="like"],
                    button[data-action="like"],
                    .fcom_feed_item_footer .fcom_reaction,
                    .feed_footer .fcom_reaction,
                    .feed_actions .fcom_reaction,
                    .fcom_feed_item .fcom_reaction,
                    .fcom_feed_actions .fcom_reaction,
                    .fcom_feed_item_actions .fcom_reaction,
                    .fcom_feed_item_footer .feed_actions button[data-type="like"],
                    .fcom_feed_item_footer .feed_actions .fcom_reaction_list,
                    .feed_footer .fcom_reaction_list,
                    .mqcr-reaction,
                    .mqcr-reaction-like,
                    [data-v-component="FCReactionBar"],
                    .fcom_reaction_wrapper,
                    .fcom_feed_reaction {
                        display: none !important;
                    }
                `);
            }
        } catch (error) {
            console.error('MQSA: Error restricting space likes:', error);
        }
    }

    /**
     * Set up unified event delegation for all restriction types
     *
     * @param {Object} settings - The space settings
     * @param {Object} space - The space object
     */
    function setupEventDelegation(settings, space) {
        try {
            // Remove any existing event listener
            document.removeEventListener('click', handleRestrictionClick);

            // Add the event listener with the current settings
            document.addEventListener('click', function(event) {
                handleRestrictionClick(event, settings, space);
            }, true);
        } catch (error) {
            console.error('MQSA: Error setting up event delegation:', error);
        }
    }

    /**
     * Handle click events for restrictions
     *
     * @param {Event} event - The click event
     * @param {Object} settings - The space settings
     * @param {Object} space - The space object
     */
    function handleRestrictionClick(event, settings, space) {
        // Find the clicked element
        let target = event.target;
        let maxDepth = 5; // Maximum parent levels to check

        // Check if we're on a space page
        if (!MQSAUtils.isSpaceRoute(window.fluentFrameworkApp.$route)) return;

        // Check up to maxDepth levels of parent elements
        for (let i = 0; i < maxDepth; i++) {
            if (!target) break;

            // Check for join button
            if (isJoinButton(target) &&
                ((settings.restrict_join === 'yes' && !space.mqsa_user_meets_requirements) ||
                 settings.restrict_join_unjoined === 'yes')) {
                event.preventDefault();
                event.stopPropagation();
                MQSAUtils.showMessage('Join Restriction', settings.join_message || mqsaPublic.default_messages.join);
                return false;
            }

            // Check for like button
            if (isLikeButton(target) &&
                ((settings.restrict_like === 'yes' && !space.mqsa_user_meets_requirements) ||
                 settings.restrict_like_unjoined === 'yes')) {
                event.preventDefault();
                event.stopPropagation();
                MQSAUtils.showMessage('Like Restriction', settings.like_message || 'You cannot like posts in this space.');
                return false;
            }

            // Check for comment button
            if (isCommentButton(target) &&
                ((settings.restrict_comment === 'yes' && !space.mqsa_user_meets_requirements) ||
                 settings.restrict_comment_unjoined === 'yes')) {
                event.preventDefault();
                event.stopPropagation();
                MQSAUtils.showMessage('Comment Restriction', settings.comment_message || 'You cannot comment in this space.');
                return false;
            }

            // Move up to parent
            target = target.parentElement;
        }
    }

    /**
     * Helper function to identify join buttons
     *
     * @param {Element} element - The element to check
     * @return {boolean} - Whether the element is a join button
     */
    function isJoinButton(element) {
        return element.classList && (
            element.classList.contains('fcom_space_join_btn') ||
            element.classList.contains('btn_join_space') ||
            element.classList.contains('fcom_space_request_btn') ||
            (element.classList.contains('fcom_primary_button') && element.closest('.menu_actions')) ||
            (element.classList.contains('el-button') && element.closest('.menu_actions')) ||
            (element.closest('.fcom_space_header_actions') &&
             (element.tagName === 'BUTTON' || element.classList.contains('el-button'))) ||
            (element.hasAttribute('data-action') && element.getAttribute('data-action') === 'join') ||
            (element.tagName.toLowerCase() === 'button' &&
             ((element.title && (element.title.includes('Join') || element.title.includes('join'))) ||
              (element.textContent && element.textContent.toLowerCase().includes('join'))))
        );
    }

    /**
     * Helper function to identify like buttons
     *
     * @param {Element} element - The element to check
     * @return {boolean} - Whether the element is a like button
     */
    function isLikeButton(element) {
        return element.classList && (
            element.classList.contains('fcom_reaction') ||
            element.classList.contains('fcom_reaction_btn') ||
            element.classList.contains('fcom_like_btn') ||
            element.classList.contains('mqcr-reaction') ||
            element.classList.contains('fcom_reaction_wrapper') ||
            element.closest('.fcom_reaction_wrapper') ||
            (element.hasAttribute('data-action') && element.getAttribute('data-action') === 'like') ||
            (element.hasAttribute('data-type') && element.getAttribute('data-type') === 'like')
        );
    }

    /**
     * Helper function to identify comment buttons
     *
     * @param {Element} element - The element to check
     * @return {boolean} - Whether the element is a comment button
     */
    function isCommentButton(element) {
        return element.classList && (
            element.classList.contains('fcom_comment_btn') ||
            element.closest('.fcom_comment_btn_wrap') ||
            element.classList.contains('fcom_comment_action') ||
            (element.hasAttribute('data-action') && element.getAttribute('data-action') === 'comment') ||
            (element.tagName.toLowerCase() === 'button' &&
             ((element.title && (element.title.includes('Comment') || element.title.includes('comment'))) ||
              (element.textContent && element.textContent.toLowerCase().includes('comment'))))
        );
    }

    /**
     * Helper function to add or update a style element
     *
     * @param {string} id - The ID for the style element
     * @param {string} css - The CSS content
     */
    function addOrUpdateStyle(id, css) {
        // Check if the style element already exists
        if (styleElements[id]) {
            // Update the existing style element
            styleElements[id].textContent = css;
        } else {
            // Create a new style element
            const style = document.createElement('style');
            style.id = 'mqsa-' + id;
            style.textContent = css;
            document.head.appendChild(style);

            // Store the style element for later cleanup
            styleElements[id] = style;
        }
    }

    /**
     * Clean up all style elements
     */
    function cleanupStyles() {
        Object.values(styleElements).forEach(style => {
            if (style && style.parentNode) {
                style.parentNode.removeChild(style);
            }
        });

        // Clear the style elements object
        Object.keys(styleElements).forEach(key => {
            delete styleElements[key];
        });
    }

    // Public API
    return {
        applySpaceRestrictions,
        restrictSpaceView,
        restrictSpaceJoin,
        restrictSpacePosting,
        restrictSpaceComments,
        restrictSpaceLikes,
        cleanupStyles
    };
})();

// Export the module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MQSARestrictions;
}
