/**
 * Utility functions for Mind Qtrl | Space Access Control
 *
 * This file contains utility functions used across the plugin.
 *
 * @link       https://mindqtrl.com/
 * @since      1.2.0
 */

const MQSAUtils = (function() {
    'use strict';

    /**
     * Debounce function to limit how often a function can be called
     *
     * @param {Function} func - The function to debounce
     * @param {number} wait - The time to wait in milliseconds
     * @return {Function} - The debounced function
     */
    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    /**
     * Check if the current route is a Fluent Community space route
     *
     * @param {Object} route - The Vue route object
     * @return {boolean} - Whether the route is a space route
     */
    function isSpaceRoute(route) {
        try {
            // Check route name and path (most efficient)
            if (route && (
                (route.name && (route.name.startsWith('space_') || route.name.includes('space'))) ||
                (route.path && (route.path.includes('/space/') || route.path.includes('/spaces/')))
            )) {
                return true;
            }

            // Check URL pattern as fallback (less expensive than DOM queries)
            const url = window.location.href;
            if (url.includes('/space/') || url.includes('/spaces/') || url.includes('/community/space/')) {
                return true;
            }

            // Last resort: check for space-related data attributes in DOM
            // Combined selectors for fewer DOM operations
            return !!document.querySelector('[data-route="space"], [data-space-id], .fcom_space_header');
        } catch (error) {
            console.error('MQSA: Error checking space route:', error);
            return false;
        }
    }

    /**
     * Get space ID from current route with error handling
     *
     * @param {Object} vueApp - The Vue app instance
     * @return {number|null} - The space ID or null if not found
     */
    function getSpaceIdFromRoute(vueApp) {
        try {
            // Check if we're on a space route
            const route = vueApp.$route;
            if (!isSpaceRoute(route)) return null;

            // Try to get space ID from route params
            if (route.params) {
                if (route.params.space_id) return parseInt(route.params.space_id);
                if (route.params.id) return parseInt(route.params.id);

                // Some routes use slug instead of ID
                if (route.params.space) {
                    const space = vueApp.$store.state.spaces.currentSpace;
                    if (space && space.id) return parseInt(space.id);
                }
            }

            // Fallback: try to get space ID from Vuex store
            const space = vueApp.$store.state.spaces.currentSpace;
            if (space && space.id) return parseInt(space.id);

            return null;
        } catch (error) {
            console.error('MQSA: Error getting space ID:', error);
            return null;
        }
    }

    /**
     * Unified function to show messages using the best available method
     *
     * @param {string} title - The message title
     * @param {string} message - The message content
     */
    function showMessage(title, message) {
        try {
            // Try to use FluentCommunityApp.$toast first
            if (window.FluentCommunityApp && window.FluentCommunityApp.$toast) {
                window.FluentCommunityApp.$toast.warning({
                    title: title,
                    message: message,
                    duration: 5000
                });
                return;
            }

            // Try to use MQSADialog if available
            if (window.MQSADialog) {
                MQSADialog.show({
                    title: title,
                    message: message,
                    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>',
                    type: 'warning'
                });
                return;
            }

            // Try to use Element UI message box if available
            if (window.ELEMENT && window.ELEMENT.MessageBox) {
                window.ELEMENT.MessageBox.alert(message, title, {
                    confirmButtonText: 'OK',
                    type: 'warning'
                });
                return;
            }

            // Fallback to alert
            alert(title + ': ' + message);
        } catch (error) {
            console.error('MQSA: Error showing message:', error);
            // Ultimate fallback
            alert(title + ': ' + message);
        }
    }

    // Public API
    return {
        debounce,
        isSpaceRoute,
        getSpaceIdFromRoute,
        showMessage
    };
})();

// Export the module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MQSAUtils;
}
