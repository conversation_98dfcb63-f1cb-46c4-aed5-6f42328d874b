/**
 * Vue Integration for Mind Qtrl | Space Access Control
 *
 * This file contains functions for integrating with Vue.js and Vuex.
 *
 * @link       https://mindqtrl.com/
 * @since      1.2.0
 */

const MQSAVueIntegration = (function() {
    'use strict';

    // Flag to track if an operation is in progress
    let operationInProgress = false;

    /**
     * Set up Vue router watcher with debouncing
     *
     * @param {Object} vueApp - The Vue app instance
     */
    function setupVueRouterWatcher(vueApp) {
        if (!vueApp || !vueApp.$router) {
            console.warn('MQSA: Vue router not available');
            return;
        }

        // Use debouncing to prevent multiple rapid calls
        const debouncedApplyControl = MQSAUtils.debounce((to) => {
            try {
                // Check if route is space related
                if (MQSAUtils.isSpaceRoute(to)) {
                    applySpaceAccessControl(vueApp);
                } else {
                    // If navigating away from a space, clean up
                    MQSAAPI.clearActiveRequests();
                }
            } catch (error) {
                console.error('MQSA: Error in router watcher:', error);
            }
        }, 300);

        vueApp.$router.afterEach(debouncedApplyControl);
    }

    /**
     * Apply space access control with error handling and operation locking
     *
     * @param {Object} vueApp - The Vue app instance
     */
    async function applySpaceAccessControl(vueApp) {
        // Skip if an operation is already in progress
        if (operationInProgress) {
            console.log('MQSA: Operation already in progress, skipping');
            return;
        }

        // Set the operation flag
        operationInProgress = true;

        try {
            const spaceId = MQSAUtils.getSpaceIdFromRoute(vueApp);
            if (!spaceId) {
                operationInProgress = false;
                return;
            }

            // Get space data from Vuex store
            const space = vueApp.$store.state.spaces.currentSpace;
            if (!space || !space.mqsa_settings) {
                operationInProgress = false;
                return;
            }

            // Check if user is member
            const isMember = await MQSAAPI.checkSpaceMembership(spaceId);

            // Update Vuex store permissions based on restrictions
            updateVuePermissions(vueApp, space, isMember);

            // Apply client-side restrictions for non-members
            if (!isMember) {
                MQSARestrictions.applySpaceRestrictions(spaceId, space);
            }
        } catch (error) {
            console.error('MQSA: Error applying space access control:', error);
        } finally {
            // Clear the operation flag
            operationInProgress = false;
        }
    }

    /**
     * Update Vue permissions in the Vuex store with error handling
     *
     * @param {Object} vueApp - The Vue app instance
     * @param {Object} space - The space object
     * @param {boolean} isMember - Whether the user is a member
     */
    function updateVuePermissions(vueApp, space, isMember) {
        try {
            const settings = space.mqsa_settings;
            if (!settings || settings.enable_restrictions !== 'yes') return;

            // Get current permissions from store
            const currentPermissions = vueApp.$store.state.spaces.currentSpacePermissions || {};

            // Create updated permissions object
            const updatedPermissions = { ...currentPermissions };

            // Apply view restrictions
            if (settings.restrict_view === 'yes' && !space.mqsa_user_meets_requirements) {
                updatedPermissions.can_view_posts = false;
                updatedPermissions.can_view_info = false;
            }

            // Apply unjoined member view restrictions
            if (settings.restrict_view_unjoined === 'yes' && !isMember) {
                updatedPermissions.can_view_posts = false;
                updatedPermissions.can_view_info = false;
            }

            // Apply post restrictions
            if ((settings.restrict_post === 'yes' && !space.mqsa_user_meets_requirements) ||
                (settings.restrict_post_unjoined === 'yes' && !isMember)) {
                updatedPermissions.can_create_post = false;
            }

            // Apply comment restrictions
            if ((settings.restrict_comment === 'yes' && !space.mqsa_user_meets_requirements) ||
                (settings.restrict_comment_unjoined === 'yes' && !isMember)) {
                updatedPermissions.can_comment = false;
                updatedPermissions.can_react = false;
            }

            // Apply like restrictions
            if ((settings.restrict_like === 'yes' && !space.mqsa_user_meets_requirements) ||
                (settings.restrict_like_unjoined === 'yes' && !isMember)) {
                updatedPermissions.can_like = false;
                updatedPermissions.can_react = false;
            }

            // Update the Vuex store
            vueApp.$store.commit('spaces/setCurrentSpacePermissions', updatedPermissions);
        } catch (error) {
            console.error('MQSA: Error updating Vue permissions:', error);
        }
    }

    // Public API
    return {
        setupVueRouterWatcher,
        applySpaceAccessControl,
        updateVuePermissions
    };
})();

// Export the module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MQSAVueIntegration;
}
