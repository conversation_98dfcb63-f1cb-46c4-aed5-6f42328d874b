/**
 * Mind Qtrl | Space Access Control - Modern Dialog Component
 *
 * @link       https://mindqtrl.com/
 * @since      1.0.1
 */

(function() {
    'use strict';

    // Dialog Manager
    window.MQSADialog = {

    };

    // Create global mqsaDialog function
    window.mqsaDialog = function(options) {
        return window.MQSADialog.show(options);
    };

    // Create global mqsaToast function
    window.mqsaToast = function(options) {
        return window.MQSADialog.toast(options);
    };

    // Assign methods to MQSADialog
    Object.assign(window.MQSADialog, {
        /**
         * Create and show a dialog
         *
         * @param {Object} options Dialog options
         * @param {string} options.title Dialog title
         * @param {string} options.message Dialog message
         * @param {string} options.icon SVG icon code (optional)
         * @param {Array} options.buttons Array of button objects (optional)
         * @param {boolean} options.closeOnBackdrop Close when clicking backdrop (default: true)
         * @param {Function} options.onClose Callback when dialog is closed (optional)
         * @returns {HTMLElement} The dialog element
         */
        show: function(options) {
            // Default options
            const defaults = {
                title: 'Access Restricted',
                message: 'You do not have permission to view this content.',
                icon: '<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>',
                buttons: [
                    {
                        text: 'OK',
                        type: 'primary',
                        onClick: function() {
                            MQSADialog.close();
                        }
                    }
                ],
                closeOnBackdrop: true,
                onClose: null
            };

            // Merge options
            const settings = Object.assign({}, defaults, options);

            // Create dialog elements
            const overlay = document.createElement('div');
            overlay.className = 'mqsa-dialog-overlay';

            const container = document.createElement('div');
            container.className = 'mqsa-dialog-container';

            // Create header
            const header = document.createElement('div');
            header.className = 'mqsa-dialog-header';

            const title = document.createElement('h3');
            title.className = 'mqsa-dialog-title';
            title.textContent = settings.title;

            const closeBtn = document.createElement('button');
            closeBtn.className = 'mqsa-dialog-close';
            closeBtn.innerHTML = '&times;';
            closeBtn.setAttribute('aria-label', 'Close');

            header.appendChild(title);
            header.appendChild(closeBtn);

            // Create body
            const body = document.createElement('div');
            body.className = 'mqsa-dialog-body';

            // Add icon if provided
            if (settings.icon) {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'mqsa-dialog-icon';
                iconDiv.innerHTML = settings.icon;
                body.appendChild(iconDiv);
            }

            // Add message
            const message = document.createElement('div');
            message.className = 'mqsa-dialog-message';
            message.innerHTML = settings.message;
            body.appendChild(message);

            // Create footer with buttons
            const footer = document.createElement('div');
            footer.className = 'mqsa-dialog-footer';

            // Add buttons
            if (settings.buttons && settings.buttons.length) {
                settings.buttons.forEach(function(btn) {
                    const button = document.createElement('button');
                    button.className = 'mqsa-dialog-button';
                    if (btn.type === 'secondary') {
                        button.classList.add('secondary');
                    }
                    button.textContent = btn.text;

                    if (typeof btn.onClick === 'function') {
                        button.addEventListener('click', btn.onClick);
                    }

                    footer.appendChild(button);
                });
            }

            // Assemble dialog
            container.appendChild(header);
            container.appendChild(body);
            container.appendChild(footer);
            overlay.appendChild(container);

            // Add to document
            document.body.appendChild(overlay);

            // Store reference to current dialog
            this.currentDialog = overlay;

            // Add event listeners
            closeBtn.addEventListener('click', function() {
                MQSADialog.close();
            });

            if (settings.closeOnBackdrop) {
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) {
                        MQSADialog.close();
                    }
                });
            }

            // Store onClose callback
            this.onCloseCallback = settings.onClose;

            // Add keyboard listener for ESC key
            document.addEventListener('keydown', this.handleEscKey);

            // Return the dialog element
            return overlay;
        },

        /**
         * Close the current dialog
         */
        close: function() {
            if (this.currentDialog) {
                // Add closing animation
                this.currentDialog.style.opacity = '0';

                // Remove after animation
                setTimeout(() => {
                    if (this.currentDialog && this.currentDialog.parentNode) {
                        this.currentDialog.parentNode.removeChild(this.currentDialog);
                    }

                    // Call onClose callback if provided
                    if (typeof this.onCloseCallback === 'function') {
                        this.onCloseCallback();
                    }

                    // Remove keyboard listener
                    document.removeEventListener('keydown', this.handleEscKey);

                    this.currentDialog = null;
                    this.onCloseCallback = null;
                }, 300);
            }
        },

        /**
         * Handle ESC key press
         *
         * @param {KeyboardEvent} e Keyboard event
         */
        handleEscKey: function(e) {
            if (e.key === 'Escape') {
                MQSADialog.close();
            }
        },

        /**
         * Current dialog reference
         */
        currentDialog: null,

        /**
         * On close callback
         */
        onCloseCallback: null,

        /**
         * Show a toast notification
         *
         * @param {Object} options Toast options
         * @param {string} options.message Toast message
         * @param {string} options.type Toast type (success, error, warning, info)
         * @param {number} options.duration Duration in milliseconds
         * @returns {HTMLElement} The toast element
         */
        toast: function(options) {
            // Default options
            const defaults = {
                message: 'Notification',
                type: 'info',
                duration: 3000
            };

            // Merge options
            const settings = Object.assign({}, defaults, options);

            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.mqsa-toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'mqsa-toast-container';
                document.body.appendChild(toastContainer);

                // Add styles for toast container
                const style = document.createElement('style');
                style.textContent = `
                    .mqsa-toast-container {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 9999;
                        display: flex;
                        flex-direction: column;
                        align-items: flex-end;
                        gap: 10px;
                    }
                    .mqsa-toast {
                        padding: 12px 20px;
                        border-radius: 4px;
                        color: white;
                        font-size: 14px;
                        font-weight: 500;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        min-width: 250px;
                        max-width: 350px;
                        opacity: 0;
                        transform: translateY(-10px);
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }
                    .mqsa-toast.show {
                        opacity: 1;
                        transform: translateY(0);
                    }
                    .mqsa-toast-success {
                        background-color: #10b981;
                    }
                    .mqsa-toast-error {
                        background-color: #ef4444;
                    }
                    .mqsa-toast-warning {
                        background-color: #f59e0b;
                    }
                    .mqsa-toast-info {
                        background-color: #3b82f6;
                    }
                    .mqsa-toast-close {
                        background: none;
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 16px;
                        margin-left: 10px;
                        opacity: 0.7;
                    }
                    .mqsa-toast-close:hover {
                        opacity: 1;
                    }
                `;
                document.head.appendChild(style);
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = `mqsa-toast mqsa-toast-${settings.type}`;
            toast.innerHTML = `
                <span>${settings.message}</span>
                <button class="mqsa-toast-close">&times;</button>
            `;

            // Add to container
            toastContainer.appendChild(toast);

            // Show toast with animation
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // Add close button event
            const closeBtn = toast.querySelector('.mqsa-toast-close');
            closeBtn.addEventListener('click', () => {
                this.removeToast(toast);
            });

            // Auto remove after duration
            setTimeout(() => {
                this.removeToast(toast);
            }, settings.duration);

            return toast;
        },

        /**
         * Remove a toast element
         *
         * @param {HTMLElement} toast The toast element to remove
         */
        removeToast: function(toast) {
            toast.classList.remove('show');

            // Remove after animation
            setTimeout(() => {
                if (toast && toast.parentNode) {
                    toast.parentNode.removeChild(toast);

                    // Remove container if empty
                    const container = document.querySelector('.mqsa-toast-container');
                    if (container && container.children.length === 0) {
                        container.parentNode.removeChild(container);
                    }
                }
            }, 300);
        }
    });
})();
