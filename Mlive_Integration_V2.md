Expansive AI Guidelines: Word<PERSON>ress Plugins with Custom Elements for Fluent Community (No Build Tools, No Progressive Enhancement)

Version: 1.1 (Updated based on new documentation)
Date: Current Date

Table of Contents:

Introduction
Purpose and Scope
Core Philosophy: Seamless Integration, No Build Tools
Target Audience
Key Assumptions & Conventions
Understanding the Fluent Community Environment (Verified & Expanded)
Architectural Synopsis
Backend: WordPress with WPFluent (MVC-like)
Frontend: Vue 3 Single Page Application (SPA)
Folder Structure Insights
Critical Extensibility Points for Plugins
PHP: WordPress Hooks & Fluent Community specific actions/filters
JavaScript: window.FluentCommunityUtil.hooks, window.FluentCommunityVars, DOM Events
Global window.Vue instance
Leveraging Fluent Community's UI/UX
Guiding Principles for Plugin Development
Custom Elements as the Frontend Cornerstone
Event-Driven Architecture
Direct Dependency on Fluent Community
Security and Data Integrity
Backend Development (WordPress & WPFluent)
Plugin Structure and Namespacing
Module-Based Integration (Recommended)
Creating a Module Structure
Registering and Initializing Your Module
Crafting REST API Endpoints
Routing: Defining namespaced routes (e.g., using WPFluent Router)
Controllers: Logic, validation, and FluentCommunity\App\Http\Controllers\Controller usage
Models: Extending FluentCommunity\App\Models\Model or using FluentCommunity\App\Models\Feed / Activity as reference
Authentication & Authorization: Utilizing Fluent Community Policies
Effective Use of Hooks and Filters
Interacting with fluent_community/portal_vars
Adding Menu Items: fluent_community/main_menu_items filter
Adding User Profile Tabs: fluent_community/user_profile_tabs and portal_vars for profile navs
Extending oEmbed Support: wp_oembed_add_provider and potentially interacting with RemoteUrlParser
Error Handling in PHP
Performance Optimization for Backend: Caching, efficient queries
Frontend Development (Custom Elements in a Vue 3 SPA - No Build Tools)
5.1. Mastering Custom Elements
Defining Custom Elements (Vanilla JavaScript, ES6+ syntax, e.g., <your-prefix-feature-element>)
Lifecycle Callbacks: connectedCallback, disconnectedCallback, attributeChangedCallback
Shadow DOM: The key to style and DOM encapsulation
Managing Attributes and Properties for Configuration
Data Fetching (e.g., activities, posts) and Rendering within Custom Elements
5.2. Seamless Integration with Fluent Community's Portal
Injecting Custom Elements:
Dynamic Placement: Using MutationObserver to find and inject elements
SPA Navigation: Re-evaluating injections on fcom:route:changed
Targeting Selectors: Identifying stable selectors (e.g., .fcom_profile_holder, feed areas).
Styling Custom Elements Consistently:
Encapsulated Styles: <style> tags within the Shadow DOM.
CSS Custom Properties & UI Mimicry: Aligning with Element Plus / Tailwind CSS look and feel.
Communication Nexus:
Internal: Simple custom event bus or standard DOM events.
External (with Fluent Community):
Listening to Fluent Community DOM Events: fcom:route:changed, fcom:feed:updated, etc.
Utilizing window.FluentCommunityUtil.hooks: For routes, simple global components, store modules.
Utilizing window.FluentCommunityAdmin (if applicable and documented for external use).
Accessing window.FluentCommunityVars: For REST nonces, API URLs, etc.
5.3. Reusing Fluent Community Libraries & Elements
window.Vue: Utilize the globally available window.Vue instance (Vue 3) provided by Fluent Community for internal Custom Element logic if needed (e.g., Composition API for complex state).
UI Consistency: Replicate UI patterns from Element Plus and Tailwind CSS rather than direct component reuse.
5.4. Error Handling and Edge Cases in Frontend
5.5. Performance Optimization for Custom Elements
Efficient DOM, debouncing/throttling, lazy loading, cleanup in disconnectedCallback.
Asset Management and Best Practices
Enqueueing Scripts and Styles (dependencies, wp_localize_script).
JavaScript Organization (IIFE).
Advanced Integration Considerations
Interacting with the Portal Lifecycle (fluent_com_portal_ready).
Security: Sanitizing, nonces, CSP.
Common Pitfalls and How to Avoid Them
Testing and Debugging
API endpoint testing (permissions, nonces).
Frontend interaction testing (success/error states).
Dynamic addition testing.
Example Snippets (Conceptual)
1. Introduction

Purpose and Scope
This document provides comprehensive guidelines for AI and human developers to create WordPress plugins that integrate deeply with the Fluent Community platform. The focus is on a WordPress backend and a JavaScript frontend built with Custom Elements, specifically without Node.js build tools (like Webpack or Vite) for the plugin's frontend assets. This approach ensures that plugins directly leverage Fluent Community's environment.

Core Philosophy: Seamless Integration, No Build Tools
Our philosophy is to empower developers to extend Fluent Community through its existing extensibility points. By using vanilla JavaScript Custom Elements and adhering to these guidelines, plugins can achieve robust UI integration and backend functionality, fitting naturally within the Fluent Community ecosystem. Backend integrity with PHP-only solutions for the plugin's server-side logic is paramount.

Target Audience
WordPress plugin developers, including AI systems, aiming to build extensions for Fluent Community. A solid understanding of WordPress, PHP, JavaScript (ES6+), Custom Elements, and the WordPress REST API is essential.

Key Assumptions & Conventions

Fluent Community is installed and active.
The plugin's functionality is intended to work exclusively within the Fluent Community portal (i.e., no progressive enhancement for standalone operation; this is a deviation from some example documents based on explicit project requirements).
Developers have access to Fluent Community's frontend DOM structure for identifying stable integration points (selectors).
The global window.FluentCommunityUtil.hooks object and window.FluentCommunityVars are available as described.
window.Vue (Vue 3) is globally available, provided by Fluent Community.
Naming Conventions (examples from):
Plugin Name for Examples: MyCustomFeature
Text Domain Convention: my-custom-feature
PHP/JS Prefix Convention: mycf_ or myCf (adapt as needed for uniqueness)

2. Understanding the Fluent Community Environment (Verified & Expanded)

Architectural Synopsis

Backend: Fluent Community operates on WordPress, significantly enhanced by its custom WPFluent MVC-like framework. This backend manages data, business logic, and serves a REST API. It heavily utilizes modules for feature organization, typically found within an app/Modules/ directory. The core application logic resides in app/ with subdirectories like Controllers, Models, Services, and Views.
Frontend: The user-facing portal is a Vue 3 Single Page Application (SPA), managing routing, state, and UI rendering. While Fluent Community itself uses Vite for its assets (indicated by app/Vite.php and assets/manifest.json), your plugin will not use a build step for its Custom Elements.
Folder Structure Insights: The Fluent Community plugin is structured with a main entry point (fluent-community.php), an app/ directory for core logic (including App.php as the main application class, Http/Controllers/ for request handlers, Models/ for database models, Services/ for business logic, and Hooks/ for WordPress action/filter handlers), assets/ for compiled frontend assets, boot/ for application bootstrap, config/ for configuration files, and Modules/ for distinct features. The vendor/ directory contains third-party dependencies, including the wpfluent/framework.
Critical Extensibility Points for Plugins

PHP (Backend):
WordPress Hooks: Standard add_action() and add_filter().
Fluent Community Hooks: Utilize specific fluent_community/* actions and filters (e.g., fluent_community/portal_loaded, fluent_community/portal_vars, fluent_community/main_menu_items, fluent_community/user_profile_tabs). These are defined in files like app/Hooks/actions.php and app/Hooks/filters.php within Fluent Community's structure.
WPFluent Router: For defining custom REST API endpoints for your plugin, typically by creating a routes file within your plugin's module.
JavaScript (Frontend - within your enqueued script):
window.FluentCommunityUtil.hooks: This object is key for interacting with the Vue SPA:
addFilter('fluent_com_portal_routes', ...): To add navigation routes.
addFilter('fluent_com_global_components', ...): For simple Vue components via string templates.
addFilter('fluent_com_store_modules', ...): To interact with Fluent Community's Vuex store.
addAction('fluent_com_portal_ready', ...): To execute code once the portal is fully initialized.
window.FluentCommunityVars: An object localized via wp_localize_script, providing REST API nonces, base URLs, user data, etc.
Fluent Community DOM Events: Listen for custom DOM events like fcom:route:changed, fcom:feed:updated, fcom:user:loaded.
Global window.Vue instance: Fluent Community is expected to make Vue 3 globally available as window.Vue. This allows your Custom Elements to leverage Vue's reactivity or Composition API for complex internal logic if desired, without needing a separate Vue import.
Leveraging Fluent Community's UI/UX

Observe Fluent Community's use of Element Plus and Tailwind CSS for design consistency.
If Fluent Community exposes CSS custom properties (variables) for theming, utilize them in your Custom Element's Shadow DOM.
Strive for visual and interactive consistency by replicating UI patterns.

3. Guiding Principles for Plugin Development

Custom Elements as the Frontend Cornerstone
Vanilla JavaScript Custom Elements (e.g., <mycf-feature-element>) with Shadow DOM will be the primary method for building your plugin's UI, ensuring encapsulation and reusability without a build step.

Event-Driven Architecture
Use standard DOM events, custom events from your Custom Elements, and listen to Fluent Community DOM events for communication. A simple plugin-specific event bus can coordinate complex interactions between your Custom Elements.

Direct Dependency on Fluent Community
Your plugin's frontend features assume Fluent Community's environment (FluentCommunityVars, window.Vue, DOM structure) is present. Functionality should be tailored for this environment.

Security and Data Integrity
Prioritize security: use nonces, sanitize inputs/outputs, validate data on both client and server, and follow WordPress security best practices. Ensure API credentials (like for custom oEmbeds) are stored securely.


4. Backend Development (WordPress & WPFluent)

Plugin Structure and Namespacing
Follow standard WordPress plugin structure. Use PHP namespaces for all classes. A suggested prefix like mycf_ (for functions/hooks) or MyCf (for classes) should be used.

Module-Based Integration (Recommended)
Structure your plugin as a Fluent Community module for cleaner integration.

Module Structure: Mirroring Fluent Community's own Modules directory, for example:
your-plugin/
├── Modules/
│   └── YourPluginFeature/
│       ├── Controllers/
│       ├── Models/      // If needed
│       ├── Services/    // If needed
│       ├── Http/routes.php
│       └── YourPluginFeatureModule.php
├── assets/
└── your-plugin-main-file.php
Registering Your Module: Use YourPluginFeatureModule.php to register routes and hooks, potentially hooking into fluent_community/portal_loaded.
Crafting REST API Endpoints

Routing: Define namespaced REST API routes (e.g., your-plugin-namespace/v1/endpoint) using the WPFluent router within your module's Http/routes.php.
Controllers: Extend \FluentCommunity\App\Http\Controllers\Controller for standardized responses (sendSuccess, sendError). Implement business logic and validation. Refer to controllers like FeedsController for patterns on fetching and filtering data.
Models: If creating custom tables, consider extending \FluentCommunity\App\Models\Model. For interacting with existing data, understand core models like \FluentCommunity\App\Models\Feed and \FluentCommunity\App\Models\Activity. Always use $wpdb->prepare() or the ORM's data sanitization methods.
Authentication & Authorization: Enforce nonces and capability checks. Leverage Fluent Community's Policy system (e.g., extending \FluentCommunity\App\Http\Policies\BasePolicy) if applicable for your module's resources.
Effective Use of Hooks and Filters

fluent_community/portal_vars: Add plugin-specific settings for frontend access.
fluent_community/main_menu_items: Add navigation items to the portal menu.
Adding User Profile Tabs: Use the fluent_community/user_profile_tabs filter to define the tab structure (slug, title, route info) and fluent_community/portal_vars to ensure it's passed to the frontend profile_navs array. Ensure unique wrapper_class for styling and correct route name configuration.
Extending oEmbed Support:
Use wp_oembed_add_provider() to register new oEmbed sources (e.g., Rumble, Facebook/Instagram videos/reels).
For deeper integration, investigate if RemoteUrlParser class or FeedsHelper::processFeedMetaData() in Fluent Community need to be aware of these or if custom handling is required via filters.
Ensure URLs are sanitized and use WordPress's built-in oEmbed functions where possible.
Error Handling in PHP
Return meaningful JSON error responses from APIs. Log errors using WordPress or Fluent Community mechanisms.

Performance Optimization for Backend
Cache API responses using WordPress Transients or Object Cache. Write optimized database queries.


5. Frontend Development (Custom Elements in a Vue 3 SPA - No Build Tools)

5.1. Mastering Custom Elements

Defining: Use ES6 class syntax for elements like <mycf-media-feed-element>. Encapsulate all JavaScript for a Custom Element within its class or an associated IIFE.
Lifecycle Callbacks: Implement connectedCallback, disconnectedCallback, and attributeChangedCallback for setup, cleanup, and reacting to attribute changes.
Shadow DOM: Always use this.attachShadow({ mode: 'open' }); for style and DOM encapsulation.
Data Fetching: Fetch data from your plugin's REST API endpoints. Refer to how Fluent Community loads activities (e.g., via FeedsController) for patterns in structuring data requests, including filters like space, user_id, topic_slug, or media_filter (e.g., 'photos', 'videos').
5.2. Seamless Integration with Fluent Community's Portal

Injecting Custom Elements:
MutationObserver: As detailed in Mlive_Hybrid_Community_integration.md and Developing_WP_Plugins_for_Mlive.md, use MutationObserver to dynamically inject your Custom Elements into designated Fluent Community DOM areas when those areas appear or change.
SPA Navigation: Listen to fcom:route:changed or similar portal events to re-evaluate and potentially re-inject or update Custom Elements as the view changes.
Styling Custom Elements Consistently:
Styles should be fully encapsulated within the Shadow DOM.
Mimic Fluent Community's look and feel (derived from Element Plus and Tailwind CSS) by crafting similar CSS or using globally available CSS Custom Properties if provided by Fluent Community.
Communication Nexus:
Internal: Use a simple custom event bus (namespaced to your plugin, e.g., window.MyCfEventBus) or standard DOM events for communication between your plugin's Custom Elements.
External:
Listen to Fluent Community DOM events (e.g., fcom:route:changed).
Use window.FluentCommunityUtil.hooks:
fluent_com_portal_routes: To add new portal pages that can host your Custom Elements (e.g., for a custom profile tab view). The component for such a route should be a simple JS object with a template string: component: { template: '<mycf-your-element></mycf-your-element>' }.
Access window.FluentCommunityVars for nonces, REST URLs, translated strings, and initial configuration.
5.3. Reusing Fluent Community Libraries & Elements

window.Vue: Leverage window.Vue (Vue 3 instance) for advanced internal state management within your Custom Elements if needed (e.g., using Vue.reactive or Vue.ref from the Composition API). This is preferable to including another copy of Vue.
JavaScript

// Inside a Custom Element method, if window.Vue is confirmed available
this.state = window.Vue.reactive({ count: 0, message: 'Hello' });
// Then use this.state.count in your rendering logic
UI Consistency: Direct reuse of Element Plus Vue components is not feasible without a build system. Replicate their appearance and behavior through custom CSS within your Shadow DOM. If Fluent Community's global CSS (e.g., from assets/app.css or theme-default.css) offers utility classes that are stable and non-conflicting, you could attempt to use them, but Shadow DOM encapsulation is the safer primary strategy.
5.4. Error Handling and Edge Cases in Frontend

Display user-friendly error messages within the Custom Element's Shadow DOM for API failures or missing data.
Handle cases where expected FluentCommunityVars or DOM injection points are not found.
Implement loading states while data is being fetched.
5.5. Performance Optimization for Custom Elements

Minimize direct DOM manipulations; update only necessary parts.
Debounce/throttle event listeners (e.g., on scroll or input).
Implement lazy loading for images or content within Custom Elements, using IntersectionObserver if applicable.
Ensure disconnectedCallback cleans up all event listeners, timers, and observers.

6. Asset Management and Best Practices

Enqueueing Scripts and Styles: Use wp_enqueue_script and wp_enqueue_style. Crucially, list fluent-community-portal (or the actual handle for Fluent Community's main portal script) as a dependency for your plugin's JavaScript files.
Use wp_localize_script to pass data such as REST API nonces (e.g., wp_create_nonce('wp_rest')), your plugin's REST API base URL (e.g., rest_url('mycf-namespace/v1/')), and any translated strings or initial configuration to your frontend JavaScript. This object will be globally accessible (e.g., YourPluginSettings.rest_url).
JavaScript Organization: Wrap all JavaScript in IIFEs to prevent global scope pollution.

7. Advanced Integration Considerations

Portal Lifecycle: Use window.FluentCommunityUtil.hooks.addAction('fluent_com_portal_ready', 'your_plugin_init', yourInitFunction); to ensure the Fluent Community portal is fully loaded before your plugin attempts complex DOM manipulations or relies on all portal features being active.
Security: Always sanitize URLs before processing for oEmbeds or other purposes. Apply appropriate content filters to any HTML rendered from external sources or user input.

8. Common Pitfalls and How to Avoid Them

Modifying Fluent Community's Vue Instance: Do not directly access or modify window.fluentFrameworkApp or its internal Vue properties (like $router, $store) unless explicitly documented by Fluent Community. Use the provided FluentCommunityUtil.hooks API.
Ignoring SPA Behavior: Ensure Custom Elements correctly re-initialize or clean up on route changes by listening to fcom:route:changed or using connectedCallback/disconnectedCallback effectively.
Not Verifying FluentCommunityVars: Always check if window.FluentCommunityVars and its expected properties exist before using them to prevent runtime errors.

9. Testing and Debugging

API Endpoints: Test all REST API endpoints for correct data responses, error handling, permission checks, and nonce validation (e.g., using tools like Postman or WP-CLI).
Frontend Interactions: Test Custom Element functionality in various portal locations, including successful operations, error states (especially 403 for CSRF/permission issues), and different user inputs.
Dynamic Injection: Verify that MutationObserver correctly injects elements and that connectedCallback / disconnectedCallback fire as expected during SPA navigation.
oEmbed Testing: If adding custom oEmbed providers, test various URL formats and ensure content displays correctly and securely. Check for CSP issues.
