# Fluent Community Custom oEmbed Support

This plugin adds oEmbed support for Rumble, Facebook videos/reels, and Instagram videos/reels in the Fluent Community portal feed.

## Features

- Adds support for embedding Rumble.com videos in Fluent Community
- Adds support for embedding Facebook videos and reels
- Adds support for embedding Instagram posts and reels
- Responsive design for all embeds
- Custom styling to match Fluent Community's design
- Support for Fluent Community's RemoteUrlParser system

## Installation

1. Upload the `fluent-community-custom-oembed.php` file to your WordPress plugins directory
2. Upload the `js/custom-embeds.js` file to your WordPress `js` directory
3. Activate the plugin through the WordPress admin interface
4. Configure your Facebook App ID and App Secret in the plugin file

## Configuration

### Facebook and Instagram Authentication

To use Facebook and Instagram embeds, you need to create a Facebook Developer App:

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app (Business type)
3. Add the "oEmbed Product" to your app
4. Copy your App ID and App Secret
5. Open the plugin file and replace `YOUR_APP_ID` and `YOUR_APP_SECRET` with your actual credentials

## Usage

Once installed and configured, users can simply paste URLs from supported providers into the Fluent Community feed composer, and they will be automatically embedded in the feed.

Supported URL formats:

- Rumble: `https://rumble.com/v4u8nl6-example.html`
- Facebook Video: `https://www.facebook.com/username/videos/123456789012345/`
- Facebook Reel: `https://www.facebook.com/reel/123456789012345/`
- Instagram Post: `https://www.instagram.com/p/abcde12345/`
- Instagram Reel: `https://www.instagram.com/reel/abcde12345/`

## How It Works

The plugin works by:

1. Registering custom oEmbed providers with WordPress
2. Enhancing oEmbed data with additional metadata needed by Fluent Community
3. Adding custom styling to ensure responsive design
4. Providing custom remote URL parsing for Fluent Community's feed system
5. Adding JavaScript to ensure proper rendering and functionality

## Integration

See the accompanying `oEmbed_Integration_Guide.md` for instructions on how to integrate this functionality directly into Fluent Community core files if preferred over using a plugin.

## Testing

See the accompanying `oEmbed_Testing.md` for test URLs and procedures to verify the implementation.

## Documentation

For detailed documentation on how oEmbed works in Fluent Community and implementation details, see the accompanying `Custom_oEmbed_Support.md` file.

## Requirements

- WordPress 5.0 or higher
- Fluent Community plugin
- For Facebook/Instagram embeds: Facebook Developer App with oEmbed product enabled

## Support

For questions or support, please contact the Fluent Community development team.
