AI Code Guideline: Developing WordPress Plugins for Fluent Community
Plugin Name Convention for Examples: MyCustomFeatureText
Domain Convention for Examples: my-custom-feature.
Prefix Convention for Examples: mycf

Core Principles & Philosophy
Backend Integrity:The WordPress plugin backend for MyCustomFeature will be exclusively PHP, adhering to WordPress development standards.Absolutely no Vue.js code, Node.js, or frontend build tools (Webpack, Vite, etc.) will be part of MyCustomFeature's backend or its direct asset generation process.Leverage WordPress hooks and, where appropriate and documented, Fluent Community's (fCom) backend framework (WPFluent) and helper functions.Frontend Hybrid Integration (Vue 3 + Custom Elements + Progressive Enhancement):MyCustomFeature's frontend user interfaces will be delivered as Custom Elements (Web Components).These Custom Elements will internally mount and manage Vue 3 instances.Vue 3 will be consumed as provided by the Fluent Community portal (expected to be globally available as window.Vue) or loaded via CDN by MyCustomFeature if necessary (though relying on fCom's instance is preferred for consistency).Progressive Enhancement is Key:Custom Elements should enhance server-rendered HTML placeholders (e.g., output by shortcodes) or existing fCom structures.Basic functionality or content should be accessible if JavaScript/Vue fails or is delayed.MutationObserver will be used to detect the presence of Custom Element tags in the DOM or relevant fCom structures, triggering the initialization of the Vue instances within them.A simple event system (custom browser events) can be used for coordination between different Custom Elements or with the broader fCom portal if direct hook integration isn't sufficient.Frontend assets (JS, CSS) for MyCustomFeature will be standard, directly usable browser assets, enqueued via WordPress.Seamless Fluent Community Integration:Utilize fCom's established frontend utilities (window.FluentCommunityUtil.hooks, window.FluentCommunityAdmin, window.FluentCommunityVars) for integrating routes, store modules, global components, and accessing shared data like nonces and REST URLs.Respect fCom's styling (Tailwind CSS, Element Plus) by either leveraging its utility classes (if not using Shadow DOM) or ensuring your component styles are well-encapsulated and prefixed.Security First:Rigorous CSRF (Cross-Site Request Forgery) protection using WordPress nonces for all state-changing operations.Correct and robust implementation of permission_callback for all custom REST API endpoints.II. Backend Development (WordPress Plugin: MyCustomFeature)Plugin Structure (Standard WordPress Layout):my-custom-feature/
├── my-custom-feature.php         # Main plugin file
├── includes/                     # PHP classes, functions
│   ├── class-mycf-module.php     # Main module registration with fCom
│   ├── class-mycf-api.php        # REST API endpoint registration & logic
│   ├── class-mycf-shortcodes.php # Shortcodes for Custom Element placeholders
│   └── class-mycf-helpers.php    # Optional helper functions
├── assets/                       # CSS, JS (for Custom Elements and Vue components)
│   ├── css/
│   │   └── mycf-styles.css
│   ├── js/
│   │   ├── mycf-main.js          # Main JS for CE definitions, Vue instances, MutationObserver
│   │   └── components/           # Directory for Vue component definitions (as JS objects)
│   │       └── my-feature-vue-component.js
├── templates/                    # PHP templates for shortcodes or server-rendered parts
│   └── my-feature-placeholder.php
├── languages/                    # .pot file and translation files
└── readme.txt
Main Plugin File (my-custom-feature.php):Standard plugin headers, constants for path/URL/version.Check for Fluent Community's existence.Initialize the main module class (MyCF_Module).<?php
/**
 * Plugin Name:       My Custom Feature for Fluent Community
 * Plugin URI:        https://example.com/my-custom-feature
 * Description:       Enhances Fluent Community with a custom feature using Vue 3 and Custom Elements.
 * Version:           1.0.0
 * Author:            Your Name
 * Author URI:        https://example.com
 * License:           GPLv2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       my-custom-feature
 * Domain Path:       /languages
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

define( 'MYCF_PLUGIN_FILE', __FILE__ );
define( 'MYCF_PLUGIN_PATH', plugin_dir_path( MYCF_PLUGIN_FILE ) );
define( 'MYCF_PLUGIN_URL', plugin_dir_url( MYCF_PLUGIN_FILE ) );
define( 'MYCF_VERSION', '1.0.0' );
define( 'MYCF_TEXT_DOMAIN', 'my-custom-feature' );

// Ensure Fluent Community is active before proceeding
function mycf_can_load_plugin() {
    if ( ! class_exists( '\FluentCommunity\App\App' ) ) {
        add_action( 'admin_notices', function() {
            echo '<div class="notice notice-error"><p>';
            esc_html_e( 'My Custom Feature requires Fluent Community to be installed and active.', MYCF_TEXT_DOMAIN );
            echo '</p></div>';
        });
        return false;
    }
    return true;
}

if ( mycf_can_load_plugin() ) {
    require_once MYCF_PLUGIN_PATH . 'includes/class-mycf-module.php';

    // Initialize the module
    // The hook 'fluent_community/loaded' or 'plugins_loaded' can be used.
    // 'plugins_loaded' with a higher priority ensures fCom is likely loaded.
    add_action( 'plugins_loaded', ['MyCF_Module', 'instance'], 20 );
}
Module Registration with Fluent Community (includes/class-mycf-module.php):Singleton pattern for the main module class.Hooks into fCom (fluent_community/portal_loaded, fluent_community/main_menu_items, fluent_community/portal_vars).Enqueues assets with wp_localize_script for nonces and other data.<?php
class MyCF_Module {
    private static $_instance = null;

    public static function instance() {
        if ( is_null( self::$_instance ) ) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    private function __construct() {
        // Load translations
        add_action( 'init', [ $this, 'load_textdomain' ] );

        // Hook into Fluent Community when its portal is loaded or general WordPress hooks
        add_action( 'fluent_community/portal_loaded', [ $this, 'integrate_with_fcom_portal' ] );
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_portal_assets' ] );

        // Register REST API endpoints
        require_once MYCF_PLUGIN_PATH . 'includes/class-mycf-api.php';
        new MyCF_API(); // Constructor should hook into 'rest_api_init'

        // Register Shortcodes for Custom Elements
        require_once MYCF_PLUGIN_PATH . 'includes/class-mycf-shortcodes.php';
        new MyCF_Shortcodes();
    }

    public function load_textdomain() {
        load_plugin_textdomain( MYCF_TEXT_DOMAIN, false, dirname( plugin_basename( MYCF_PLUGIN_FILE ) ) . '/languages/' );
    }

    public function integrate_with_fcom_portal( $fcom_app_instance ) {
        // Add variables to fCom's portal_vars (available as window.FluentCommunityVars on frontend)
        add_filter( 'fluent_community/portal_vars', function ( $vars ) {
            $vars['features']['my_custom_feature_active'] = true;
            $vars['my_custom_feature_config'] = [
                'api_namespace' => 'mycf/v1', // Used by JS to construct API URLs
                // Other specific settings your feature might need globally in the portal
            ];
            return $vars;
        } );

        // Add a menu item to Fluent Community's portal main menu
        add_filter( 'fluent_community/main_menu_items', function ( $items ) {
            $items['my_custom_feature_nav'] = [ // Unique key
                'title'    => __( 'My Feature', MYCF_TEXT_DOMAIN ),
                'route'    => [ 'name' => 'my_custom_feature_portal_route' ], // JS route name
                'icon'     => '<svg viewBox="0 0 24 24" fill="currentColor" style="width:20px; height:20px;"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"></path></svg>', // Example SVG
                'position' => 35,
                'permission' => 'manage_options' // Optional: WordPress capability check for menu visibility
            ];
            return $items;
        });
    }

    public function enqueue_portal_assets() {
        // Determine if we are in the fCom portal or a page where our CE might be used.
        $load_assets = false;
        if ( function_exists('\FluentCommunity\App\Services\Helper') && \FluentCommunity\App\Services\Helper::isPortal() ) {
            $load_assets = true;
        }
        // Add additional checks if your shortcode can be on any page, e.g., by checking post content for the shortcode.
        // For this example, we assume assets are primarily for the portal or pages where fCom's Vue is active.

        if ( ! $load_assets && ! has_shortcode( get_the_content(), 'my_custom_feature_display' ) ) {
             // Only load assets if in portal or if shortcode is present on the current page.
             // This check for has_shortcode is basic; more robust checks might be needed for block editor, etc.
             // return;
        }


        wp_enqueue_style(
            'mycf-styles',
            MYCF_PLUGIN_URL . 'assets/css/mycf-styles.css',
            ['fluent-community-portal'], // Depend on fCom's main CSS if styles need to align or override
            MYCF_VERSION
        );

        // Main script for Custom Elements and Vue instances
        wp_enqueue_script(
            'mycf-main-script',
            MYCF_PLUGIN_URL . 'assets/js/mycf-main.js',
            ['fluent-community-portal-app'], // Crucial: Depend on fCom's main app script that provides Vue
            MYCF_VERSION,
            true // Load in footer
        );

        // Localize data: nonces, URLs, translated strings for your JS
        $localized_data = [
            'rest_url_base' => rest_url( 'mycf/v1/' ), // Your plugin's REST API base
            'nonce'         => wp_create_nonce( 'wp_rest' ), // Standard WordPress REST API nonce
            'fcom_nonce'    => (class_exists('\FluentCommunity\App\App') && is_callable(['\FluentCommunity\App\App', 'get_plugin_nonce']))
                                ? \FluentCommunity\App\App::get_plugin_nonce()
                                : wp_create_nonce(\FLUENT_COMMUNITY_PLUGIN_SLUG), // Fallback, fCom specific
            'text'          => [ // Example translated strings
                'loading' => __( 'Loading My Feature...', MYCF_TEXT_DOMAIN ),
                'error'   => __( 'An error occurred.', MYCF_TEXT_DOMAIN ),
            ],
            // Add any other dynamic data your frontend needs
        ];
        wp_localize_script( 'mycf-main-script', 'MyCustomFeatureVars', $localized_data );
    }
}
REST API Endpoints (includes/class-mycf-api.php):permission_callback is critical for security. It determines who can access the endpoint.It must return true or a WP_Error object.Use current_user_can('capability_name') for capability checks.For public data (rarely for state-changing actions), __return_true can be used, but be cautious.For user-specific data, check get_current_user_id().Nonce Verification:The WordPress REST API automatically verifies the wp_rest nonce (sent as X-WP-Nonce header) for authenticated requests if the permission_callback passes for state-changing methods (POST, PUT, PATCH, DELETE). You typically don't need to call wp_verify_nonce() explicitly in the main callback for these.If permission_callback returns true for non-authenticated users (e.g., a public POST endpoint, which is highly discouraged for state changes without other strong authentication), you would need to manually check a nonce in the main callback.If you are using fCom's specific nonce (fluentCommunityAdmin.nonce or the one from App::get_plugin_nonce()) for custom AJAX handlers (not standard WP REST API), you'd verify that specific nonce action.<?php
class MyCF_API {
    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'register_routes' ] );
    }

    public function register_routes() {
        $namespace = 'mycf/v1'; // Your plugin's namespace

        // Example: Get some data
        register_rest_route( $namespace, '/my-data/(?P<id>\d+)', [
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => [ $this, 'get_my_data_item' ],
            'permission_callback' => [ $this, 'can_user_read_data' ],
            'args'                => [
                'id' => [
                    'validate_callback' => function( $param, $request, $key ) {
                        return is_numeric( $param );
                    }
                ],
            ],
        ] );

        // Example: Update some data (state-changing)
        register_rest_route( $namespace, '/update-my-data/(?P<id>\d+)', [
            'methods'             => WP_REST_Server::EDITABLE, // Handles POST, PUT, PATCH
            'callback'            => [ $this, 'update_my_data_item' ],
            'permission_callback' => [ $this, 'can_user_update_data' ], // CRITICAL
            'args'                => [
                'id' => [ /* ... */ ],
                'title' => [
                    'required'          => true,
                    'type'              => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                    'validate_callback' => function( $param ) {
                        return ! empty( trim( $param ) );
                    }
                ],
                // other args
            ],
        ] );
    }

    /**
     * Permission callback for reading data.
     *
     * @param WP_REST_Request $request Full data about the request.
     * @return true|WP_Error True if the request has read access, WP_Error object otherwise.
     */
    public function can_user_read_data( WP_REST_Request $request ) {
        // Example: Allow any logged-in user to read.
        // For more specific checks, use current_user_can('your_custom_capability')
        // or check against a specific role or user ID if relevant to the data.
        if ( ! is_user_logged_in() ) {
            return new WP_Error(
                'rest_forbidden_context',
                __( 'Sorry, you are not allowed to access this data.', MYCF_TEXT_DOMAIN ),
                [ 'status' => rest_authorization_required_code() ] // Typically 401 or 403
            );
        }
        // If data is user-specific, you might check if current user matches requested data owner.
        // $item_owner_id = get_post_field( 'post_author', $request['id'] );
        // if ( get_current_user_id() != $item_owner_id && !current_user_can('edit_others_posts') ) {
        //    return new WP_Error( 'rest_forbidden', __( 'Cannot view others data.', MYCF_TEXT_DOMAIN ), [ 'status' => 403 ] );
        // }
        return true; // Or return current_user_can('read_mycf_data');
    }

    /**
     * Permission callback for updating data.
     * This is where CSRF nonce checks are implicitly handled by WP REST API for authenticated users.
     *
     * @param WP_REST_Request $request Full data about the request.
     * @return true|WP_Error True if the request has update access, WP_Error object otherwise.
     */
    public function can_user_update_data( WP_REST_Request $request ) {
        // Example: Only allow users with 'edit_posts' capability to update.
        // ALWAYS use specific capabilities for destructive/mutating actions.
        if ( ! current_user_can( 'edit_posts' ) ) { // Replace with your specific capability
            return new WP_Error(
                'rest_forbidden',
                __( 'Sorry, you are not allowed to perform this action.', MYCF_TEXT_DOMAIN ),
                [ 'status' => is_user_logged_in() ? 403 : 401 ]
            );
        }
        // If the nonce sent by the client (X-WP-Nonce header with 'wp_rest' nonce) is invalid,
        // WordPress REST API will reject the request with a 403 'rest_cookie_invalid_nonce'
        // BEFORE this callback is even fully processed, IF the user is authenticated via cookie.
        // So, your primary job here is to check the user's *capabilities* for the action.
        return true; // Or return current_user_can('edit_mycf_data_item', $request['id']);
    }

    public function get_my_data_item( WP_REST_Request $request ) {
        $id = (int) $request['id'];
        // ... fetch data by $id ...
        $data = get_post_meta( $id, '_mycf_data_key', true );
        if ( empty( $data ) ) {
            return new WP_Error( 'rest_not_found', __( 'Data item not found.', MYCF_TEXT_DOMAIN ), [ 'status' => 404 ] );
        }
        return new WP_REST_Response( $data, 200 );
    }

    public function update_my_data_item( WP_REST_Request $request ) {
        $id = (int) $request['id'];
        $params = $request->get_params();
        $title = $params['title']; // Already sanitized by 'args' definition

        // ... update your data for $id using $title ...
        // Example: update_post_meta( $id, '_mycf_data_key', [ 'title' => $title ] );

        return new WP_REST_Response( [ 'message' => __( 'Data updated successfully.', MYCF_TEXT_DOMAIN ), 'id' => $id ], 200 );
    }
}
Shortcodes for Custom Element Placeholders (includes/class-mycf-shortcodes.php):Outputs the Custom Element tag, possibly with data attributes for initial state.Provides a basic non-JavaScript fallback if appropriate.<?php
class MyCF_Shortcodes {
    public function __construct() {
        add_shortcode( 'my_custom_feature_display', [ $this, 'render_feature_display_shortcode' ] );
    }

    public function render_feature_display_shortcode( $atts, $content = null ) {
        $atts = shortcode_atts( [
            'element_id'      => 'mycf-instance-' . wp_rand(1000, 9999), // Unique ID for the element
            'initial_message' => __( 'Loading feature...', MYCF_TEXT_DOMAIN ),
            // Add other attributes your Vue component might need
        ], $atts, 'my_custom_feature_display' );

        // Prepare data attributes to pass to the custom element / Vue component
        $data_attrs_str = ' data-initial-message="' . esc_attr( $atts['initial_message'] ) . '"';
        // Add more data attributes as needed

        // Output the custom element tag.
        // The content inside the tag can serve as a fallback if JS/Vue fails.
        $output = '<my-feature-interactive-element id="' . esc_attr( $atts['element_id'] ) . '"' . $data_attrs_str . '>';
        $output .= '<div class="mycf-placeholder">'; // Placeholder for no-JS or pre-Vue rendering
        $output .= '<h4>' . esc_html( $atts['initial_message'] ) . '</h4>';
        if ($content) {
            $output .= '<p>' . esc_html($content) . '</p>'; // Basic content if provided
        }
        $output .= '</div>';
        $output .= '</my-feature-interactive-element>';

        return $output;
    }
}
III. Frontend Development (Hybrid Vue 3 JS with Custom Elements & Progressive Enhancement)Main JavaScript File (assets/js/mycf-main.js):Defines Custom Elements.Uses MutationObserver to detect when these elements are added to the DOM.Mounts Vue 3 instances to the Custom Elements.Handles AJAX with correct nonces.Integrates with fCom's portal using FluentCommunityUtil.hooks.// assets/js/mycf-main.js
(function(Vue, fComUtils, myCfVars) {
    'use strict';

    if (!Vue || !Vue.createApp) {
        console.error('MyCustomFeature: Vue 3 is not available. Feature cannot load.');
        return;
    }

    const { ref, onMounted, onUnmounted, computed, watch, defineAsyncComponent } = Vue;

    // --- Centralized AJAX Helper with Nonce ---
    async function myCfFetchApi(endpoint, options = {}) {
        const baseUrl = myCfVars.rest_url_base; // From wp_localize_script
        const fullUrl = baseUrl.endsWith('/') ? baseUrl + endpoint.replace(/^\//, '') : `${baseUrl}/${endpoint.replace(/^\//, '')}`;

        const headers = {
            'Content-Type': 'application/json',
            'X-WP-Nonce': myCfVars.nonce, // Nonce from wp_localize_script
            ...(options.headers || {})
        };

        try {
            const response = await fetch(fullUrl, { ...options, headers });
            const responseData = await response.json();

            if (!response.ok) {
                console.error(`MyCustomFeature API Error (${response.status}):`, responseData);
                let userMessage = myCfVars.text.error || 'An error occurred.';
                if (response.status === 403 && (responseData.code === 'rest_cookie_invalid_nonce' || responseData.code === 'rest_forbidden')) {
                    userMessage = 'Security check failed. Please refresh the page and try again.';
                    // Optionally, dispatch a global event for fCom to handle (e.g., show a notification)
                    document.dispatchEvent(new CustomEvent('mycf-security-error', { detail: { message: userMessage } }));
                } else if (responseData.message) {
                    userMessage = responseData.message;
                }
                throw new Error(userMessage);
            }
            return responseData;
        } catch (error) {
            console.error('MyCustomFeature Fetch Exception:', error);
            throw error; // Re-throw to be caught by the caller
        }
    }

    // --- Vue Component Definition (can be in a separate file if structure grows) ---
    // This is the component that will be mounted inside the Custom Element.
    const MyFeatureVueComponentDefinition = {
        props: {
            initialMessage: String,
            elementId: String,
            // other props passed from Custom Element attributes
        },
        setup(props) {
            const message = ref(props.initialMessage || (myCfVars.text.loading || 'Loading...'));
            const serverData = ref(null);
            const isLoading = ref(false);

            const fetchData = async () => {
                isLoading.value = true;
                try {
                    // Example: fetch data for a specific item if an ID is passed via props
                    // const dataId = props.someIdFromAttribute;
                    // serverData.value = await myCfFetchApi(`my-data/${dataId}`, { method: 'GET' });
                    serverData.value = await myCfFetchApi('some-default-data-endpoint', { method: 'GET' }); // Placeholder
                    message.value = serverData.value.greeting || "Data loaded!";
                } catch (error) {
                    message.value = error.message;
                } finally {
                    isLoading.value = false;
                }
            };

            const updateDataOnServer = async (newData) => {
                isLoading.value = true;
                try {
                    const response = await myCfFetchApi('update-my-data/1', { // example ID
                        method: 'POST', // Or PUT
                        body: JSON.stringify({ title: newData })
                    });
                    message.value = response.message || 'Data updated!';
                    // Optionally re-fetch or update local state based on response
                } catch (error) {
                    message.value = error.message;
                } finally {
                    isLoading.value = false;
                }
            };

            onMounted(() => {
                console.log(`MyFeatureVueComponent for #${props.elementId} mounted with message: ${props.initialMessage}`);
                fetchData(); // Fetch initial data
            });

            return { // Expose to template
                message,
                isLoading,
                serverData,
                updateDataOnServer,
                // Access fCom store if needed (example)
                fcomUser: computed(() => fComUtils?.store?.state?.auth?.user?.display_name || 'Guest')
            };
        },
        template: `
            <div class="mycf-vue-component">
                <p>Hello, {{ fcomUser }}!</p>
                <div v-if="isLoading" class="mycf-loading">{{ myCfVars.text.loading }}</div>
                <div v-else>
                    <p class="mycf-message">{{ message }}</p>
                    <div v-if="serverData" class="mycf-server-data">
                        <pre>{{ serverData }}</pre>
                    </div>
                    <button @click="updateDataOnServer('New data from Vue @ ' + new Date().toLocaleTimeString())" class="mycf-button">Update Server Data</button>
                </div>
                </div>
        `
    };

    // --- Custom Element Definition ---
    class MyFeatureInteractiveElement extends HTMLElement {
        constructor() {
            super();
            this.vueApp = null;
            this._isConnected = false;
            // console.log('MyFeatureInteractiveElement constructor');
        }

        static get observedAttributes() {
            return ['data-initial-message']; // Observe attributes that might change
        }

        connectedCallback() {
            this._isConnected = true;
            // console.log(`MyFeatureInteractiveElement #${this.id} connected.`);
            // Progressive enhancement: Vue app is mounted here.
            // The initial content (placeholder) is provided by the server via shortcode.
            this.mountVueApp();
        }

        disconnectedCallback() {
            this._isConnected = false;
            if (this.vueApp && this.vueApp.unmount) {
                this.vueApp.unmount();
                this.vueApp = null;
            }
            // console.log(`MyFeatureInteractiveElement #${this.id} disconnected.`);
        }

        attributeChangedCallback(name, oldValue, newValue) {
            // console.log(`Attribute ${name} changed from ${oldValue} to ${newValue} on #${this.id}`);
            if (this.vueApp && this._isConnected) {
                // If Vue app is mounted, you might want to react to attribute changes
                // This requires the Vue component to be designed to accept these props
                // and the Vue instance to be updated. Simpler is often to re-mount or
                // have internal Vue reactivity handle it via props.
                if (name === 'data-initial-message' && this.vueApp._instance?.props) {
                     // This direct manipulation is not ideal; better to pass as prop if re-rendering
                     // this.vueApp._instance.props.initialMessage = newValue;
                     // For simplicity, often a full re-mount or internal Vue reactivity is preferred
                     // Or, the Vue component itself watches its props.
                }
            }
        }

        mountVueApp() {
            if (this.vueApp) { // Already mounted
                return;
            }

            // Clear placeholder content if any, or Vue can mount over it.
            // const placeholder = this.querySelector('.mycf-placeholder');
            // if (placeholder) placeholder.remove();

            const propsData = {};
            for (const attr of this.attributes) {
                if (attr.name.startsWith('data-')) {
                    const propName = attr.name.substring(5).replace(/-([a-z])/g, g => g[1].toUpperCase());
                    propsData[propName] = attr.value;
                }
            }
            propsData.elementId = this.id;


            this.vueApp = Vue.createApp(MyFeatureVueComponentDefinition, propsData);

            // Integrate with fCom's Vuex store and ElementPlus if available and needed
            if (fComUtils && fComUtils.store && this.vueApp.use) {
                // this.vueApp.use(fComUtils.store); // Usually fCom's root app does this.
                                                  // Components can access via `this.$store` or `useStore()`
                                                  // if fCom's Vue instance is the parent.
                                                  // If this CE is truly isolated, it won't share the same store instance
                                                  // unless explicitly passed or re-instantiated.
                                                  // For now, assume fCom's store is globally accessible if needed.
            }
            if (window.ElementPlus && this.vueApp.component) { // Check if ElementPlus components are globally registered
                // Often, fCom's root Vue app registers ElementPlus components globally.
                // If so, they can be used directly in your Vue component templates.
                // Example: this.vueApp.component('ElButton', window.ElementPlus.ElButton);
            }

            this.vueApp.mount(this); // Mount Vue app directly onto the custom element
        }
    }
    customElements.define('my-feature-interactive-element', MyFeatureInteractiveElement);


    // --- MutationObserver to Initialize Custom Elements Added Dynamically ---
    // This ensures that if fCom or other JS adds your custom element tag to the DOM later,
    // its Vue instance will still be mounted.
    const observerCallback = (mutationsList, observer) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.matches && node.matches('my-feature-interactive-element') && !node.vueApp) {
                            // console.log('MutationObserver: Detected new my-feature-interactive-element', node);
                            // The connectedCallback will handle mounting.
                            // If connectedCallback isn't firing reliably for dynamically added elements (it should),
                            // you could explicitly call node.mountVueApp() here, but it's usually not needed.
                        }
                        // Also observe child elements if needed
                        const descendants = node.querySelectorAll('my-feature-interactive-element');
                        descendants.forEach(el => {
                            if (!el.vueApp && el.mountVueApp) { // Check if it's our CE and not yet initialized
                                // el.mountVueApp(); // Again, connectedCallback should handle this.
                            }
                        });
                    }
                });
            }
        }
    };

    // Start observing the document body for additions of your custom element
    // This is crucial for progressive enhancement when content is loaded AJAX-ly by fCom or other plugins.
    const observer = new MutationObserver(observerCallback);
    observer.observe(document.documentElement, { childList: true, subtree: true });


    // --- Integration with Fluent Community's Vue Router & Store (using fComUtils.hooks) ---
    if (fComUtils && fComUtils.hooks) {
        // Register a new route in Fluent Community's Vue Router
        fComUtils.hooks.addFilter('fluent_com_portal_routes', 'my_custom_feature', (routes) => {
            routes.push({
                path: '/my-custom-feature-page',
                name: 'my_custom_feature_portal_route', // Used in PHP for menu item
                component: { // Inline Vue component for the route view
                    template: `
                        <div class="fcom-page mycf-portal-routed-page">
                            <h1 class="fcom-page-title">${myCfVars.text.myFeaturePageTitle || 'My Custom Feature Portal Page'}</h1>
                            <p>This page is integrated into Fluent Community's portal.</p>
                            <my-feature-interactive-element id="routed-instance" data-initial-message="Loaded via fCom Router!"></my-feature-interactive-element>
                        </div>
                    `,
                    setup() {
                        // Access fCom store example
                        const fcomStore = fComUtils.store;
                        const currentUser = computed(() => fcomStore?.state?.auth?.user?.display_name || 'Guest');
                        onMounted(() => {
                            console.log('MyCustomFeature portal route component mounted.');
                            // Dispatch action to your custom store module if needed
                            // fcomStore?.dispatch('myCustomFeatureStore/loadFeatureData');
                        });
                        return { currentUser };
                    }
                }
            });
            return routes;
        });

        // Register a Vuex store module within Fluent Community's store
        fComUtils.hooks.addFilter('fluent_com_store_modules', 'my_custom_feature', (modules) => {
            modules.myCustomFeatureStore = { // Store module name
                namespaced: true,
                state: () => ({
                    someData: null,
                    isLoading: false,
                }),
                mutations: {
                    SET_SOME_DATA(state, data) { state.someData = data; },
                    SET_LOADING(state, loading) { state.isLoading = loading; }
                },
                actions: {
                    async fetchPluginData({ commit }) {
                        commit('SET_LOADING', true);
                        try {
                            const data = await myCfFetchApi('plugin-specific-data', { method: 'GET' });
                            commit('SET_SOME_DATA', data);
                        } catch (error) {
                            console.error('Error fetching data for myCustomFeatureStore:', error);
                        } finally {
                            commit('SET_LOADING', false);
                        }
                    }
                },
                getters: {
                    getSomeData: state => state.someData,
                    isPluginDataLoading: state => state.isLoading
                }
            };
            return modules;
        });

        // React to fCom portal being ready (optional, for post-initialization tasks)
        fComUtils.hooks.addAction('fluent_com_portal_ready', 'my_custom_feature', (fComVueAppInstance) => {
            console.log('MyCustomFeature: Fluent Community Portal is ready. Main Vue App Instance:', fComVueAppInstance);
            // Example: dispatch an action from your newly registered store module
            // fComVueAppInstance.config.globalProperties.$store?.dispatch('myCustomFeatureStore/fetchPluginData');
        });

    } else {
        console.warn('MyCustomFeature: FluentCommunityUtil.hooks not found. Deeper portal integration (routes, store) may be limited.');
    }

    // --- Simple Event System Example (if needed for inter-component communication without Vuex/props) ---
    // Custom Elements can dispatch and listen to standard browser CustomEvents.
    // document.addEventListener('mycf-custom-event', (event) => {
    //     console.log('Received mycf-custom-event:', event.detail);
    // });
    // Inside a Custom Element or its Vue component:
    // const myEvent = new CustomEvent('mycf-custom-event', { detail: { some: 'data' } });
    // this.dispatchEvent(myEvent); // If from Custom Element
    // Or document.dispatchEvent(myEvent); // For global events


    console.log('MyCustomFeature frontend initialized.');

})(window.Vue, window.FluentCommunityUtil, window.MyCustomFeatureVars);
Styling (assets/css/mycf-styles.css):Style your Custom Elements and the Vue components within.If not using Shadow DOM (recommended for easier style integration with fCom):Prefix all your CSS classes (e.g., .mycf-container, .mycf-button) to avoid conflicts.You can leverage fCom's Tailwind utility classes directly in your Vue component templates if they are globally available.If using Shadow DOM:Styles must be explicitly included or linked within each Shadow DOM. This makes using fCom's global Tailwind/ElementPlus styles more complex. You might need to bundle a subset of necessary styles or use CSS parts/theme variables if fCom supports them.IV. Robust Security: CSRF (Nonces) and Permissions Deep Divepermission_callback in register_rest_route (PHP Backend):Purpose: This callback is executed before your main endpoint callback. It determines if the current user has permission to access/perform the action of the endpoint. It's your primary gatekeeper.Return Values:return true;: User has permission. WordPress REST API proceeds. If it's a state-changing request (POST, PUT, DELETE, PATCH) from an authenticated (cookie) user, WordPress will then automatically check the X-WP-Nonce header against the wp_rest nonce.return new WP_Error( 'code', 'message', [ 'status' => 401_or_403 ] );: User does not have permission. The request is denied with the specified error and status.Implementation Best Practices:Always use current_user_can('your_specific_capability') for capability-based checks. Define custom capabilities for your plugin if needed.For data specific to a user, check get_current_user_id().For publicly accessible GET requests (rare for sensitive data), you might use __return_true, but ensure no sensitive data is exposed.Never put nonce verification logic directly inside permission_callback. Its job is to check user authorization/capabilities, not CSRF tokens. WordPress handles the nonce for authenticated REST API requests after permission_callback (if it returns true) and before your main callback for state-changing methods.CSRF Nonce Verification (PHP Backend):For WP REST API Endpoints:When a user is logged in (cookie-based authentication), and your permission_callback returns true, the WordPress REST API automatically handles wp_rest nonce verification for state-changing methods (POST, PUT, DELETE, PATCH). The client must send the nonce generated by wp_create_nonce('wp_rest') in the X-WP-Nonce header.If the nonce is missing or invalid, the REST API will return a 403 error with a code like rest_cookie_invalid_nonce or rest_forbidden. Your main endpoint callback will not even be reached.For Custom AJAX Handlers (not using register_rest_route):If you were using older admin-ajax.php style handlers (discouraged for new features if REST API is an option), you would manually verify the nonce:// In your admin-ajax.php action handler
// check_ajax_referer('your_nonce_action_name', 'security_param_name');
Fluent Community Specific Nonce (fluentCommunityAdmin.nonce or App::get_plugin_nonce()):If fCom's internal API calls (which you might be extending or mimicking) use a plugin-specific nonce (e.g., created with wp_create_nonce(FLUENT_COMMUNITY_PLUGIN_SLUG)), and you are building endpoints that are part of that specific system rather than general WP REST API endpoints, you would verify that specific nonce action. However, for new, standalone REST endpoints for your plugin, sticking to wp_rest nonce is standard.Frontend Nonce Handling (JavaScript):Retrieval: Get the wp_rest nonce from MyCustomFeatureVars.nonce (localized by your PHP wp_localize_script).Sending: For every state-changing AJAX request (POST, PUT, DELETE, PATCH) to your plugin's REST API endpoints, include the nonce in the X-WP-Nonce HTTP header. The myCfFetchApi helper function in the JS example demonstrates this.V. Hybrid Approach: MutationObserver, Events, and Progressive EnhancementProgressive Enhancement Core:Your shortcode ([my_custom_feature_display]) should output basic, meaningful HTML. This could be a simple "Loading..." message, or even a non-interactive version of the content if feasible.This HTML will include your custom element tag (e.g., <my-feature-interactive-element>).MutationObserver for Dynamic Initialization:In mycf-main.js, a MutationObserver watches the document.documentElement (or a more specific container if known) for additions to the DOM (childList: true, subtree: true).When a new node is added, the observer checks if it's your custom element tag (e.g., node.matches('my-feature-interactive-element')).The connectedCallback of the Custom Element is the standard place to initialize its Vue app. The MutationObserver acts as a robust way to ensure this happens even if elements are added by fCom's Vue app or other dynamic means after the initial page load. You generally don't need to manually call mountVueApp() from the observer, as connectedCallback should fire. The observer is more of a safety net and a way to log or react to these additions if needed for other coordination.Custom Element Lifecycle (connectedCallback, disconnectedCallback):connectedCallback(): This is where you initialize and mount the Vue 3 app to the custom element. It reads data attributes passed from the server-rendered HTML (via shortcode) to provide initial props to the Vue component.disconnectedCallback(): When the custom element is removed from the DOM, unmount the Vue app to prevent memory leaks.Event System for Coordination (Optional but useful):If different instances of your custom elements, or your custom elements and fCom's components, need to communicate without direct parent-child props or a shared Vuex state (especially if your CEs are truly isolated), use standard browser CustomEvents.One component dispatches an event:// Inside a Vue component method within a Custom Element
const event = new CustomEvent('mycf-action-completed', {
    bubbles: true, // Optional: if it needs to bubble up the DOM
    composed: true, // Optional: if it needs to cross Shadow DOM boundaries
    detail: { itemId: 123, status: 'success' }
});
this.$el.dispatchEvent(event); // Dispatch from the root of the Vue component (which is the CE)
// Or, for a truly global event: document.dispatchEvent(event);
Other components (or fCom's main JS if it's listening) can listen:document.addEventListener('mycf-action-completed', (e) => {
    console.log('MyCustomFeature action completed:', e.detail);
});
This allows for decoupled communication, fitting the hybrid model.Rendering Regardless of Surrounding DOM:Custom Elements, by their nature (especially with Shadow DOM, though not strictly required for this point), aim for encapsulation. Your Vue app inside the CE controls its own template and logic.Styling is the main concern: without Shadow DOM, ensure your CSS is prefixed. With Shadow DOM, ensure necessary styles are included.VI. Workflow and Best Practices SummaryDevelop Backend First: Define your data structures, REST API endpoints (with solid permission_callback and understanding of WP REST API nonce handling), and shortcodes.Prepare Frontend Data: Use wp_localize_script to pass all necessary data (nonces, REST URLs, translated strings, initial config) to your frontend JS.Build Custom Elements: Define your CE tags. Inside their connectedCallback, mount your Vue 3 components.Write Vue Components: Create Vue 3 components (as JS objects, using Composition or Options API) that will render the UI within your Custom Elements.AJAX with Nonces: All state-changing client-server communication must use your REST API and include the X-WP-Nonce header.Styling: Choose Shadow DOM (more encapsulation, harder styling integration) or global prefixed CSS (easier fCom style integration, needs discipline).Test Thoroughly:Test all API endpoints directly (e.g., with Postman or WP_Test_REST_TestCase) for permission and nonce behavior.Test frontend interactions, including successful operations and error states (especially 403 for CSRF/permission issues).Test progressive enhancement: what happens if JS is off or fails?Test dynamic addition of your custom elements to ensure the MutationObserver and connectedCallback work as expected.Follow fCom's Patterns: Refer to the "Mlive Community Architecture" document for hook names and frontend utility usage. Avoid direct manipulation of fCom's global objects if hooks are provided.This comprehensive guideline should provide a solid foundation for developing robust, secure, and well-integrated WordPress plugins for the Fluent Community platform using the specified hybrid Vue 3 and Custom Elements approach.