# How Activities/Posts/Status Updates are Loaded & Filtered in Fluent Community Portal Feed

## Database Structure

### Feed Model (`fcom_posts` table)
- Main model for all posts/status updates
- Defined in `\FluentCommunity\App\Models\Feed`
- Key fields:
  - `id`, `user_id`, `title`, `slug`, `message`, `message_rendered`, `type`, `content_type`
  - `space_id`, `privacy`, `status`, `priority`, `featured_image`, `is_sticky`
  - `expired_at`, `scheduled_at`, `comments_count`, `reactions_count`, `meta`

### Activity Model (`fcom_user_activities` table)
- Tracks user activities
- Defined in `\FluentCommunity\App\Models\Activity`
- Key fields:
  - `user_id`, `feed_id`, `space_id`, `related_id`, `message`, `is_public`, `action_name`
- Activities can be filtered by action names like "feed_published", "comment_added"

## Loading Process

### FeedsController
The `FeedsController` class is responsible for fetching and filtering feeds:

```php
public function get(Request $request)
{
    // Parameters for filtering
    $bySpace = $request->get('space');  // Space slug
    $userId = $request->getSafe('user_id', 'intval', '');  // User ID
    $selectedTopic = $request->getSafe('topic_slug', 'sanitize_text_field', '');  // Topic
    $search = $request->getSafe('search', 'sanitize_text_field', '');  // Search term

    // Query construction with multiple filters
    $feedsQuery = Feed::byContentModerationAccessStatus($currentUserModel, $space)
        ->select(Feed::$publicColumns)
        ->with([
            'xprofile',
            'comments',
            'space',
            'reactions',
            'terms'
        ])
        ->searchBy($search, (array)$request->get('search_in', ['post_content']))
        ->byTopicSlug($selectedTopic)
        ->customOrderBy($request->get('order_by_type', ''));
        
    // Filter by space if specified
    if ($bySpace) {
        $feedsQuery = $feedsQuery->filterBySpaceSlug($bySpace);
    }

    // Add user access control
    $feedsQuery->byUserAccess($currentUserId);
    
    // Allow plugins to modify the query
    do_action_ref_array('fluent_community/feeds_query', [&$feedsQuery, $request->all(), $queryArgs]);
    
    // Paginate results
    $feeds = $feedsQuery->paginate();
    
    // Transform feed data
    $feeds->getCollection()->each(function ($feed) {
        FeedsHelper::transformFeed($feed);
    });
}
```

## Transformation Process

The `FeedsHelper::transformFeed()` method prepares feed items for display:

```php
public static function transformFeed(Feed $feed)
{
    $userId = get_current_user_id();

    // Add user reaction status
    if ($userId) {
        $feed->has_user_react = $feed->hasUserReact($userId, 'like');
        $feed->bookmarked = $feed->hasUserReact($userId, 'bookmark');
        
        // Add liked status to comments
        $likedIds = self::getLikedIdsByUserFeedId($feed->id, get_current_user_id());
        $feed->comments->each(function ($comment) use ($likedIds) {
            if ($likedIds && in_array($comment->id, $likedIds)) {
                $comment->liked = 1;
            }
        });
    }

    // Handle special content types
    if ($feed->content_type == 'document') {
        // Process document URLs
    }

    return $feed;
}
```

## Filtering Hooks

The main extensibility point for feed loading is the `fluent_community/feeds_query` action hook, which allows plugins to modify the query:

```php
do_action_ref_array('fluent_community/feeds_query', [&$feedsQuery, $request->all(), $queryArgs]);
```

## Feed Query Modifiers

The Feed model provides several methods for filtering posts:

- `byContentModerationAccessStatus()` - Handles content moderation filters
- `searchBy()` - Filters by search terms
- `byTopicSlug()` - Filters by topic
- `filterBySpaceSlug()` - Filters by space
- `byUserAccess()` - Handles permission filters
- `customOrderBy()` - Handles custom sorting

## Media Handling

For feeds containing media:
- Media items are stored in a separate table and linked to feeds
- The `transformFeed()` method handles processing URLs and permissions for media items
- Special handling exists for document types and surveys

### Media Structure in Posts

Posts with media content use specific data structures in their metadata:

1. **Image/Photo Content:**
   - Images are stored in either `media_images` array or in the `meta.media_items` array for multiple images
   - Single images are stored as a `meta.media_preview` object with type 'image' or 'meta_data'
   - A post with images will have one of these structures:
     - `meta.media_items` array (multiple images)
     - `meta.media_preview.type = 'image'` (single image)
     - `meta.media_preview.type = 'meta_data'` with `meta.media_preview.is_uploaded = true` (uploaded image)

2. **Video Embeds (YouTube/Vimeo):**
   - Video embeds are stored in the `media_preview` object with type 'oembed' or 'iframe_html'
   - A post with video will have `meta.media_preview.type = 'oembed'` or `meta.media_preview.type = 'iframe_html'`
   - Provider information is stored in `meta.media_preview.provider` (e.g., 'youtube', 'vimeo')
   - HTML embed code is stored in `meta.media_preview.html`

3. **Media Storage Process:**
   - Images uploaded through the composer are processed by `FeedsHelper::handleFeedData()`
   - External media (YouTube/Vimeo) is processed using `RemoteUrlParser::parse()`
   - For uploaded images, metadata is stored in the Media model and linked to the feed
   - External media previews are stored directly in feed metadata

### Filtering Posts by Media Type

To filter posts containing specific types of media, you can:

1. **Filter Posts with Images:**
   ```php
   // When adding to an existing FeedsController query
   $feedsQuery = $feedsQuery->where(function ($query) {
       $query->where(function ($q) {
           // Single uploaded image
           $q->whereJsonContains('meta->media_preview->type', 'image')
             ->orWhereJsonContains('meta->media_preview->type', 'meta_data')
             ->whereJsonContains('meta->media_preview->is_uploaded', true);
       })->orWhereJsonLength('meta->media_items', '>', 0); // Multiple images
   });
   ```

2. **Filter Posts with Video Embeds (YouTube/Vimeo):**
   ```php
   // Add this to your existing query
   $feedsQuery = $feedsQuery->where(function ($query) {
       $query->whereJsonContains('meta->media_preview->type', 'oembed')
             ->orWhereJsonContains('meta->media_preview->type', 'iframe_html')
             ->where(function ($q) {
                 $q->whereJsonContains('meta->media_preview->provider', 'youtube')
                   ->orWhereJsonContains('meta->media_preview->provider', 'vimeo');
             });
   });

   // Alternatively, add this filter through the feeds hook
   add_action('fluent_community/feeds_query', function(&$feedsQuery, $requestData) {
       if (!empty($requestData['filter_videos'])) {
           $feedsQuery->whereJsonContains('meta->media_preview->type', 'oembed');
       }
   }, 10, 2);
   ```

3. **Check for Media in PHP Code:**
   ```php
   // Check if a feed has images
   function hasImagesInFeed($feed) {
       // Check for multiple images
       if (!empty($feed->meta['media_items'])) {
           return true;
       }
       
       // Check for single image
       if (isset($feed->meta['media_preview'])) {
           $preview = $feed->meta['media_preview'];
           if ($preview['type'] === 'image' || 
               ($preview['type'] === 'meta_data' && !empty($preview['is_uploaded']))) {
               return true;
           }
       }
       
       return false;
   }
                 
   // Check if a feed has video embeds
   function hasVideosInFeed($feed) {
       if (!isset($feed->meta['media_preview'])) {
           return false;
       }
       
       $preview = $feed->meta['media_preview'];
       return ($preview['type'] === 'oembed' || $preview['type'] === 'iframe_html') && 
              (isset($preview['provider']) && 
               in_array($preview['provider'], ['youtube', 'vimeo']));
   }
   ```

## Integration Points for Custom Plugins

To extend or modify feed behavior, plugins can:

1. Hook into `fluent_community/feeds_query` to modify feed queries
2. Use the Fluent Community models (Feed, Activity) to query custom data
3. Transform feed data in custom ways before display
4. Add new feed content types through the Feed model

## Custom Media Filtering Plugin Example

Here's a practical example of implementing a plugin that adds media type filtering capabilities to the feed:

```php
<?php
/**
 * Plugin Name: Fluent Community Media Filters
 * Description: Add filter options for feeds containing images or videos
 * Version: 1.0.0
 */

defined('ABSPATH') or die;

class FluentCommunityMediaFilters {
    
    public function __construct() {
        // Add filter to modify feed queries
        add_action('fluent_community/feeds_query', array($this, 'modify_feeds_query'), 10, 2);
        
        // Add filter parameter to REST API
        add_filter('fluent_community/api/feeds_query_args', array($this, 'add_media_filter_params'));
    }
    
    /**
     * Modify the feed query to filter by media type
     */
    public function modify_feeds_query(&$feedsQuery, $requestData) {
        // Filter by photos/images
        if (!empty($requestData['media_filter']) && $requestData['media_filter'] === 'photos') {
            $feedsQuery->where(function ($query) {
                $query->where(function ($q) {
                    $q->whereJsonContains('meta->media_preview->type', 'image')
                      ->orWhereJsonContains('meta->media_preview->type', 'meta_data')
                      ->whereJsonContains('meta->media_preview->is_uploaded', true);
                })->orWhereJsonLength('meta->media_items', '>', 0);
            });
        }
        
        // Filter by videos (YouTube/Vimeo)
        if (!empty($requestData['media_filter']) && $requestData['media_filter'] === 'videos') {
            $feedsQuery->where(function ($query) {
                $query->whereJsonContains('meta->media_preview->type', 'oembed')
                      ->orWhereJsonContains('meta->media_preview->type', 'iframe_html');
            });
        }
    }
    
    /**
     * Add media filter parameters to API query args
     */
    public function add_media_filter_params($args) {
        $args['media_filter'] = array(
            'description' => 'Filter posts by media type',
            'type' => 'string',
            'enum' => array('photos', 'videos', 'all'),
            'default' => 'all',
        );
        
        return $args;
    }
}

// Initialize the plugin
new FluentCommunityMediaFilters();
