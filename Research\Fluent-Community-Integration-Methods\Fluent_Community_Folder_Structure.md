Full Fluent Community folder structure:

fluent-community/fluent-community/
├── fluent-community.php          # Main plugin entry point
├── index.php                     # Security file
├── readme.txt                    # Plugin documentation
├── composer.json                 # Dependency management
├── app/                          # Contains the core plugin functionality following an MVC-like pattern
│   ├── App.php                 # Main application class
│   ├── Vite.php                # Manages Vite frontend assets
│   ├── Functions/              # Helper functions
│   │   └── Utility.php         # General utility functions
│   ├── Hooks/                  # WordPress action/filter handlers
│   │   ├── actions.php         # Action registrations
│   │   └── Handlers/           # Handler classes for hooks
│   │       ├── ActivationHandler.php
│   │       ├── ActivityMonitorHandler.php
│   │       ├── CleanupHandler.php
│   │       ├── CustomizerHander.php
│   │       ├── EmailNotificationHandler.php
│   │       ├── NotificationEventHandler.php
│   │       ├── PortalHandler.php
│   │       ├── PortalSettingsHandler.php
│   │       ├── RateLimitHandler.php
│   │       └── Scheduler.php
│   ├── Http/                   # Request handling
│   │   ├── Controllers/        # Controller classes
│   │   │   ├── ActivityController.php
│   │   │   ├── AdminController.php
│   │   │   ├── CommentsController.php
│   │   │   ├── Controller.php  # Base controller
│   │   │   ├── FeedsController.php
│   │   │   ├── MembersController.php
│   │   │   ├── NotificationsController.php
│   │   │   ├── OptionController.php
│   │   │   ├── ProfileController.php
│   │   │   ├── ReactionController.php
│   │   │   ├── ReportsController.php
│   │   │   ├── SettingController.php
│   │   │   └── SpaceController.php
│   │   ├── Policies/           # Authorization policies
│   │   └── Requests/           # Form request validation
│   ├── Models/                 # Database models
│   │   ├── Activity.php
│   │   ├── BaseSpace.php
│   │   ├── Comment.php
│   │   ├── Feed.php
│   │   ├── Media.php
│   │   ├── Meta.php
│   │   ├── Model.php           # Base model class
│   │   ├── Notification.php
│   │   ├── NotificationSubscriber.php
│   │   ├── NotificationSubscription.php
│   │   ├── Reaction.php
│   │   ├── Space.php
│   │   ├── SpaceGroup.php
│   │   ├── SpaceUserPivot.php
│   │   ├── Term.php
│   │   ├── User.php
│   │   ├── UserMeta.php
│   │   └── XProfile.php
│   ├── Services/               # Business logic services
│   │   ├── AdminTransStrings.php # Admin translations
│   │   ├── AuthenticationService.php
│   │   ├── CustomSanitizer.php
│   │   ├── EmailDomBuilder.php
│   │   ├── FeedsHelper.php
│   │   ├── FormBuilder.php
│   │   ├── Helper.php
│   │   ├── OnboardingService.php
│   │   ├── Parsedown.php
│   │   ├── ProfileHelper.php
│   │   ├── RemoteUrlParser.php
│   │   └── TransStrings.php    # Frontend translations
│   └── Views/                  # Template files
├── assets/                       # Vue 3 JS Vite Compiled frontend assets
│   ├── admin_app.css           # Admin UI styles
│   ├── admin_app.js            # Admin UI JavaScript
│   ├── admin_app.rtl.css       # RTL styles for admin
│   ├── app.css                 # Main application styles
│   ├── app.js                  # Main application JavaScript
│   ├── app.rtl.css             # RTL styles for main app
│   ├── customizer_app.css      # Customizer UI styles
│   ├── customizer_app.js       # Customizer UI JavaScript
│   ├── customizer_app.rtl.css  # RTL styles for customizer
│   ├── global.css              # Global styles
│   ├── global.rtl.css          # RTL global styles
│   ├── manifest.json           # Asset manifest for versioning
│   ├── onboarding.css          # Onboarding wizard styles
│   ├── onboarding.js           # Onboarding wizard JavaScript
│   ├── onboarding.rtl.css      # RTL styles for onboarding
│   ├── portal_general.js       # Portal-wide JavaScript
│   └── start.js                # Initial JavaScript
├── boot/                         # Application initialization
├── config/                       # Main configuration files
├── database/                     # Database migrations
│   ├── DBMigrator.php          # Database migration manager
│   ├── DBSeeder.php            # Seeding functionality
│   ├── Migrations/             # Database schema migrations
│   │   ├── FeedCommentsMigrator.php
│   │   ├── FeedMigrator.php
│   │   ├── FeedReactionsMigrator.php
│   │   ├── FeedSpaceMigrator.php
│   │   ├── FeedSpaceUserMigrator.php
│   │   └── UserActivitiesMigrator.php
│   └── Dev/Seeds/              # Development data seeders
│       ├── CommentTableSeeder.php
│       ├── FeedTableSeeder.php
│       ├── PostReactionsTableSeeder.php
│       ├── SpaceTableSeeder.php
│       ├── SpaceUserTableSeeder.php
│       ├── TermFeedTableSeeder.php
│       ├── TermsTableSeeder.php
│       ├── UserActivityTableSeeder.php
│       ├── UserTableSeeder.php
│       └── XProfileTableSeeder.php
├── language/                     # Translation files
├── Modules/                      # Feature modules
│   ├── Auth/                   # Authentication features
│   │   ├── AuthHelper.php
│   │   ├── AuthModdule.php
│   │   ├── Classes/
│   │   │   ├── Invitation.php
│   │   │   ├── InvitationController.php
│   │   │   ├── InvitationHandler.php
│   │   │   └── InvitationPolicy.php
│   │   └── InvitationModule.php
│   ├── Course/                 # Learning management features
│   │   ├── CourseModule.php
│   │   ├── Http/
│   │   │   ├── Controllers/
│   │   │   │   ├── CourseAdminController.php
│   │   │   │   └── CourseController.php
│   │   │   └── Policies/
│   │   │       └── CourseAdminPolicy.php
│   │   ├── Model/
│   │   │   ├── Course.php
│   │   │   ├── CourseLesson.php
│   │   │   └── CourseTopic.php
│   │   └── Services/
│   │       └── CourseHelper.php
│   ├── FeaturesHandler.php     # Feature management
│   ├── Gutenberg/              # WordPress block editor integration
│   │   └── EditorBlock.php
│   ├── Integrations/           # Third-party integrations
│   │   ├── FluentCRM/          # CRM integration
│   │   │   ├── CourseEnrollmentTrigger.php
│   │   │   ├── ProfileSection.php
│   │   │   └── SpaceJoinTrigger.php
│   │   ├── FluentForms/        # Forms integration
│   │   │   └── Bootstrap.php
│   │   └── Integrations.php
│   └── modules_init.php        # Module initialization
├── vendor/                       # Third-party dependencies
│   ├── wpfluent/framework/       # WPFluent framework
│   │   ├── Database/             # Database abstraction
│   │   │   ├── ConnectionInterface.php
│   │   │   ├── ConnectionResolver.php
│   │   │   ├── Orm/              # Object-Relational Mapping
│   │   │   │   ├── Builder.php
│   │   │   │   ├── Collection.php
│   │   │   │   ├── Concerns/HasRelationships.php
│   │   │   │   └── Model.php
│   │   │   └── Query/            # Query building
│   │   │       ├── Builder.php
│   │   │       ├── Grammars/Grammar.php
│   │   │       └── WPDBConnection.php
│   │   ├── Foundation/           # Application foundation
│   │   │   ├── App.php
│   │   │   ├── Application.php
│   │   │   └── ComponentBinder.php
│   │   ├── Http/                 # HTTP handling
│   │   │   ├── Request/Request.php
│   │   │   └── Response/Response.php
│   │   ├── Support/              # Helper classes
│   │   │   ├── Arr.php
│   │   │   ├── Collection.php
│   │   │   └── Helper.php
│   │   └── View/                 # Templating system
│   │       └── View.php