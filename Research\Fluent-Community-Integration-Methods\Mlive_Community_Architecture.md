# Mlive Community Architecture

This document provides a comprehensive overview of the Fluent Community architecture, its components, libraries, and how to build new applications within the existing framework. It also details the limitations of extending functionality without build tools or Vue components.

Fluent Community = Mlive = fCom = Mindstate.live

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Core Components & Libraries](#core-components--libraries)
3. [Building New Apps in Fluent Community](#building-new-apps-in-fluent-community)
4. [Integration Methods](#integration-methods)
5. [Limitations of No-Build-Tools Approach](#limitations-of-no-build-tools-approach)
6. [Best Practices](#best-practices)
7. [Common Pitfalls](#common-pitfalls)

## Architecture Overview

Fluent Community is built on a modern architecture that combines WordPress backend with a Vue 3 frontend portal. The architecture follows a modular approach with clear separation of concerns.

### Backend Architecture

The backend is built on WordPress using a custom MVC-like framework called WPFluent. The core structure includes:

```
fluent-community/
├── app/                 # Core application logic
│   ├── Controllers/     # Request handlers
│   ├── Models/          # Database models
│   ├── Services/        # Business logic
│   └── Views/           # Template files
├── assets/              # Compiled frontend assets
├── boot/                # Application bootstrap
├── config/              # Configuration files
└── Modules/             # Feature modules
```

Key architectural components:

1. **Application Bootstrap**: The application is bootstrapped in `boot/app.php`, which initializes the core application and loads modules.

2. **Module System**: Features are organized into modules, each with its own controllers, models, and services.

3. **REST API**: The backend exposes a REST API for the frontend to consume, with endpoints defined in `app/Http/Routes/`.

4. **Hooks System**: The application uses WordPress hooks extensively for extensibility.

### Frontend Architecture

The frontend is a Vue 3 single-page application (SPA) with the following architecture:

1. **Core Libraries**:
   - Vue 3 (Composition API)
   - Vue Router 4
   - Vuex 4 (State Management)
   - Element Plus UI Components
   - Tailwind CSS

2. **Portal Structure**:
   - Main layout components (header, sidebar, content, footer)
   - Route-based views
   - Reusable components
   - Global state management

3. **Asset Management**:
   - Vite for development and production builds
   - CSS modules and Tailwind for styling

## Core Components & Libraries

### Backend Components

1. **Application Class**: The main application class (`FluentCommunity\App\App`) that bootstraps the application.

2. **Router**: Handles routing for the REST API (`FluentCommunity\Framework\Http\Router`).

3. **Controllers**: Handle incoming requests and return responses.

4. **Models**: Represent database tables and provide an ORM-like interface.

5. **Services**: Contain business logic and are used by controllers.

6. **Hooks Handlers**: Handle WordPress actions and filters.

### Frontend Libraries

1. **Vue 3**: The progressive JavaScript framework used for building the user interface.

2. **Vue Router 4**: Handles client-side routing in the SPA.

3. **Vuex 4**: Manages application state.

4. **Element Plus**: UI component library providing ready-to-use components.

5. **Tailwind CSS**: Utility-first CSS framework for styling.

6. **VueUse**: Collection of Vue Composition API utilities.

### Key Frontend Components

```javascript
// Portal Layout Components
- PortalLayout.vue          // Main portal wrapper
- PortalHeader.vue          // Portal header with navigation
- PortalSidebar.vue         // Sidebar with navigation links
- PortalContent.vue         // Main content area
- PortalFooter.vue          // Footer component

// Space Components
- SpaceList.vue            // List of community spaces
- SpaceDetail.vue          // Single space view
- SpaceMembers.vue         // Space members management
- SpaceSettings.vue        // Space configuration

// Feed Components
- FeedList.vue             // Activity feed list
- FeedItem.vue             // Single feed item
- FeedComments.vue         // Comments section
- FeedReactions.vue        // Reactions interface

// User Components
- UserProfile.vue          // User profile page
- UserSettings.vue         // User settings interface
- UserNotifications.vue    // Notifications center
```

## Building New Apps in Fluent Community

Building new applications within the Fluent Community framework involves several key steps and approaches.

### Module-Based Approach

The recommended approach is to create a new module that integrates with the existing framework:

1. **Create a Module Structure**:

```
your-module/
├── Controllers/       # API controllers
├── Models/            # Database models
├── Services/          # Business logic
├── Http/              # Routes and middleware
│   └── routes.php     # API routes
└── YourModule.php     # Module registration
```

2. **Register Your Module**:

```php
<?php

namespace YourNamespace\Modules\YourModule;

use FluentCommunity\Framework\Foundation\Application;

class YourModule
{
    public function register(Application $app)
    {
        // Register routes
        $app->router->group(function ($router) {
            require_once __DIR__ . '/Http/routes.php';
        });

        // Add to portal variables
        add_filter('fluent_community/portal_vars', function ($vars) {
            $vars['features']['your_feature'] = true;
            return $vars;
        });
    }
}
```

3. **Initialize Your Module**:

```php
add_action('fluent_community/portal_loaded', function ($app) {
    (new \YourNamespace\Modules\YourModule\YourModule())->register($app);
});
```

### Frontend Integration

To integrate with the frontend portal, you need to:

1. **Register Routes**:

```javascript
// Add custom routes
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_portal_routes',
    'your_plugin',
    function(routes) {
        routes.push({
            path: '/your-feature',
            name: 'your_feature',
            component: {
                template: `
                    <div class="your-feature">
                        <h1>{{ title }}</h1>
                        <p>{{ description }}</p>
                    </div>
                `,
                data() {
                    return {
                        title: 'Your Feature',
                        description: 'This is your custom feature'
                    }
                }
            }
        });
        return routes;
    }
);
```

2. **Register Components**:

```javascript
// Register global components
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_global_components',
    'your_plugin',
    function(components) {
        components.YourComponent = {
            template: `
                <div class="your-component">
                    <h2>{{ title }}</h2>
                    <slot></slot>
                </div>
            `,
            props: {
                title: {
                    type: String,
                    default: 'Default Title'
                }
            }
        };
        return components;
    }
);
```

3. **Add Menu Items**:

```php
// Add a menu item to the main menu
add_filter('fluent_community/main_menu_items', function($items) {
    $items['your_feature'] = [
        'title' => __('Your Feature', 'your-plugin'),
        'route' => [
            'name' => 'your_feature'
        ],
        'icon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>',
        'position' => 25 // Controls menu order (lower numbers appear first)
    ];
    
    return $items;
});
```

4. **Register Store Modules**:

```javascript
// Add custom store module
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_store_modules',
    'your_plugin',
    function(modules) {
        modules.yourModule = {
            namespaced: true,
            state: {
                data: null
            },
            mutations: {
                SET_DATA(state, data) {
                    state.data = data;
                }
            },
            actions: {
                fetchData({ commit }) {
                    // Fetch data from API
                    fetch('/wp-json/your-plugin/v1/data')
                        .then(response => response.json())
                        .then(data => {
                            commit('SET_DATA', data);
                        });
                }
            }
        };
        return modules;
    }
);
```

## Integration Methods

### Backend Integration

1. **REST API Endpoints**:

```php
// In your routes.php file
$router->prefix('your-feature')->namespace('YourNamespace\Modules\YourModule\Controllers')->withPolicy('YourPolicy')->group(function ($router) {
    $router->get('/', 'YourController@index');
    $router->post('/', 'YourController@store');
    $router->get('/{id}', 'YourController@show')->int('id');
    $router->put('/{id}', 'YourController@update')->int('id');
    $router->delete('/{id}', 'YourController@destroy')->int('id');
});
```

2. **Models**:

```php
<?php

namespace YourNamespace\Modules\YourModule\Models;

use FluentCommunity\App\Models\Model;

class YourModel extends Model
{
    protected $table = 'your_table';

    protected $fillable = [
        'title',
        'description',
        'user_id'
    ];

    public function user()
    {
        return $this->belongsTo('FluentCommunity\App\Models\User');
    }
}
```

3. **Controllers**:

```php
<?php

namespace YourNamespace\Modules\YourModule\Controllers;

use FluentCommunity\App\Http\Controllers\Controller;
use FluentCommunity\Framework\Request\Request;
use YourNamespace\Modules\YourModule\Models\YourModel;

class YourController extends Controller
{
    public function index(Request $request)
    {
        $items = YourModel::paginate();
        return $this->sendSuccess($items);
    }

    public function store(Request $request)
    {
        $this->validate($request->all(), [
            'title' => 'required',
            'description' => 'required'
        ]);

        $item = YourModel::create([
            'title' => $request->get('title'),
            'description' => $request->get('description'),
            'user_id' => get_current_user_id()
        ]);

        return $this->sendSuccess($item);
    }
}
```

### Frontend Integration

1. **Component Registration**:

```javascript
// Define your component
const YourComponent = {
    template: `
        <div class="your-component">
            <h2>{{ title }}</h2>
            <div class="content">
                <slot></slot>
            </div>
        </div>
    `,
    props: {
        title: {
            type: String,
            required: true
        }
    }
};

// Register the component
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_global_components',
    'your_plugin',
    function(components) {
        components.YourComponent = YourComponent;
        return components;
    }
);
```

2. **Route Registration**:

```javascript
// Register a new route
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_portal_routes',
    'your_plugin',
    function(routes) {
        routes.push({
            path: '/your-feature',
            name: 'your_feature',
            component: {
                template: `
                    <div class="your-feature-page">
                        <h1>Your Feature</h1>
                        <your-component title="Custom Component">
                            This is a custom component in a custom route.
                        </your-component>
                    </div>
                `
            }
        });
        return routes;
    }
);
```

3. **Store Integration**:

```javascript
// Register a store module
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_store_modules',
    'your_plugin',
    function(modules) {
        modules.yourFeature = {
            namespaced: true,
            state: {
                items: []
            },
            mutations: {
                SET_ITEMS(state, items) {
                    state.items = items;
                },
                ADD_ITEM(state, item) {
                    state.items.push(item);
                }
            },
            actions: {
                fetchItems({ commit }) {
                    fetch('/wp-json/your-plugin/v1/items')
                        .then(response => response.json())
                        .then(data => {
                            commit('SET_ITEMS', data);
                        });
                },
                addItem({ commit }, item) {
                    fetch('/wp-json/your-plugin/v1/items', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': window.FluentCommunityVars.rest.nonce
                        },
                        body: JSON.stringify(item)
                    })
                        .then(response => response.json())
                        .then(data => {
                            commit('ADD_ITEM', data);
                        });
                }
            }
        };
        return modules;
    }
);
```

## Limitations of No-Build-Tools Approach

When extending Fluent Community without build tools or Vue components, several limitations and challenges arise:

### 1. Limited Module Organization

Without build tools like Webpack or Vite:

- **No ES Modules**: Cannot use `import`/`export` syntax for modular code organization
- **No Code Splitting**: All code must be loaded at once, increasing initial load time
- **No Tree Shaking**: Unused code cannot be automatically removed

### 2. Development Experience Limitations

- **No Hot Module Replacement**: Changes require manual page refresh
- **No TypeScript Support**: Cannot use TypeScript for type checking
- **Limited Debugging**: No source maps for easier debugging
- **No Linting Integration**: Cannot integrate ESLint or other code quality tools

### 3. Component Architecture Constraints

Without Vue Single-File Components (SFCs):

- **Template/Logic Separation**: Cannot use the clean separation of concerns in SFCs
- **No Scoped CSS**: Cannot use scoped styles for components
- **Limited Component Reuse**: More difficult to create reusable components
- **String Templates**: Must use string templates which are harder to maintain
- **No Pre-processors**: Cannot use SCSS, Less, or other CSS pre-processors

### 4. Performance Implications

- **No Code Optimization**: No minification or optimization of JavaScript and CSS
- **No Asset Optimization**: No automatic image optimization
- **Larger Bundle Sizes**: All code is loaded at once, potentially slowing initial load
- **No Code Splitting**: Cannot split code into smaller chunks for better loading

### 5. Maintenance Challenges

- **Global Namespace Pollution**: Risk of conflicts with other plugins
- **Harder Refactoring**: More difficult to refactor code without build tools
- **Version Management**: More challenging to manage dependencies
- **Testing Limitations**: More difficult to set up automated testing

## Best Practices

Despite these limitations, you can still effectively extend Fluent Community by following these best practices:

### 1. Code Organization

```javascript
// Use IIFE pattern for scoping
(function() {
    'use strict';
    
    // Your plugin's code here
    const YourPlugin = {
        init: function() {
            // Initialization code
        },
        
        setupComponents: function() {
            // Register components
        },
        
        setupRoutes: function() {
            // Register routes
        }
    };
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        YourPlugin.init();
    });
})();
```

### 2. Component Registration

```javascript
// Define components using object literals
const YourComponent = {
    template: `
        <div class="your-component">
            <h2>{{ title }}</h2>
            <div class="content">
                <slot></slot>
            </div>
        </div>
    `,
    props: {
        title: {
            type: String,
            required: true
        }
    },
    setup() {
        // Use Vue 3 Composition API
        const { ref, onMounted } = Vue;
        
        const count = ref(0);
        
        onMounted(() => {
            console.log('Component mounted');
        });
        
        return {
            count
        };
    }
};

// Register the component
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_global_components',
    'your_plugin',
    function(components) {
        components.YourComponent = YourComponent;
        return components;
    }
);
```

### 3. State Management

```javascript
// Register a store module
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_store_modules',
    'your_plugin',
    function(modules) {
        modules.yourFeature = {
            namespaced: true,
            state: {
                items: []
            },
            mutations: {
                SET_ITEMS(state, items) {
                    state.items = items;
                }
            },
            actions: {
                fetchItems({ commit }) {
                    // Use fetch API for data retrieval
                    fetch('/wp-json/your-plugin/v1/items')
                        .then(function(response) {
                            return response.json();
                        })
                        .then(function(data) {
                            commit('SET_ITEMS', data);
                        })
                        .catch(function(error) {
                            console.error('Error fetching items:', error);
                        });
                }
            }
        };
        return modules;
    }
);
```

### 4. Asset Management

```php
/**
 * Enqueue scripts and styles
 */
function your_plugin_enqueue_assets() {
    // Only load in Fluent Community portal
    if (!\FluentCommunity\App\Services\Helper::isPortal()) {
        return;
    }
    
    wp_enqueue_style(
        'your-plugin-styles',
        plugin_dir_url(__FILE__) . 'assets/css/your-plugin.css',
        ['fluent-community-portal'],
        '1.0.0'
    );
    
    wp_enqueue_script(
        'your-plugin-scripts',
        plugin_dir_url(__FILE__) . 'assets/js/your-plugin.js',
        ['fluent-community-portal'],
        '1.0.0',
        true
    );
    
    // Pass data to JavaScript
    wp_localize_script('your-plugin-scripts', 'YourPluginVars', [
        'restBase' => rest_url('your-plugin/v1'),
        'nonce' => wp_create_nonce('wp_rest')
    ]);
}
add_action('wp_enqueue_scripts', 'your_plugin_enqueue_assets');
```

## Common Pitfalls

### 1. Direct Template Modifications

❌ **Don't use**:
```php
add_filter('template_include', function($template) {
    if (is_fluent_community()) {
        return your_template_path();
    }
    return $template;
});
```

✅ **Do this instead**:
```php
add_action('fluent_community/portal_content', function($content) {
    return modify_content($content);
});
```

### 2. Global JavaScript Modifications

❌ **Don't use**:
```javascript
// Directly modifying global objects
window.fluentFrameworkApp.$router.addRoute({
    path: '/your-route',
    component: YourComponent
});
```

✅ **Do this instead**:
```javascript
// Use the hooks system
window.FluentCommunityUtil.hooks.addFilter(
    'fluent_com_portal_routes',
    'your_plugin',
    function(routes) {
        routes.push({
            path: '/your-route',
            component: YourComponent
        });
        return routes;
    }
);
```

### 3. Direct Database Queries

❌ **Don't use**:
```php
global $wpdb;
$results = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}fcom_feeds");
```

✅ **Do this instead**:
```php
use FluentCommunity\App\Models\Feed;
$feeds = Feed::all();
```

### 4. Ignoring Portal Lifecycle

❌ **Don't use**:
```javascript
// Executing code immediately
const element = document.querySelector('.fcom_feed_item');
if (element) {
    element.addEventListener('click', handleClick);
}
```

✅ **Do this instead**:
```javascript
// Wait for portal to be ready
window.FluentCommunityUtil.hooks.addAction(
    'fluent_com_portal_ready',
    'your_plugin',
    function() {
        const element = document.querySelector('.fcom_feed_item');
        if (element) {
            element.addEventListener('click', handleClick);
        }
    }
);
```

By understanding these limitations and following the best practices, you can effectively extend Fluent Community without build tools while maintaining code quality and performance.
