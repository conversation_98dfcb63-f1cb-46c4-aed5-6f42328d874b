# User Profile Tabs in Fluent Community

## How Profile Tabs Work

Fluent Community generates user profile tabs within its portal interface. Profile tabs appear in the navigation menu of a user profile page (e.g., `https://mindstate.live/feed/u/username/`).

Profile Tab Implementation in Fluent Community Plugin
Backend Architecture
ProfileController.php:

Defines the default profile tabs in the getProfile method using an array structure called profile_navs
Each tab is defined with properties like: slug, title, url, wrapper_class, and route information
The tabs are conditionally added based on permissions (e.g., the Spaces tab is only added if canViewUserSpaces is true)
The tabs are passed to the frontend through the API response
Tab Extension Mechanism:

Developers can add custom tabs using WordPress filters
Two primary methods are available:
fluent_community/user_profile_tabs filter (adds tabs to the user profile)
fluent_community/portal_vars filter (modifies portal variables, including profile tabs)
Frontend Architecture
Vue.js Components:

The main profile component (tZe) renders a container with the profile header and tab navigation
The profile navigation uses the profile_navs array from the API response
The tabs are rendered as either Vue Router links or regular <a> elements depending on whether they have route information
Vue Router Integration:

The app uses Vue Router for client-side routing
Profile tabs are set up as nested routes under the main profile path (/u/:username/)
Each tab has a corresponding Vue component that renders when the tab is activated
The router-view component in the profile page displays the active tab content
Tab Content Rendering:

Each tab's content is lazy-loaded when the tab is clicked
The component matching the route is displayed in the router-view area
Default tabs include: About, Posts, Spaces, and Comments
Custom Tab Implementation
When adding a custom tab, developers need to:

Register the tab in the backend using one of the WordPress filters
Create a Vue component for the tab's content
Register the component and a corresponding route in the Vue application
This architecture allows for a modular approach to extending the profile interface with new tabs while maintaining a consistent user experience.

Data Flow
User navigates to a profile page
ProfileController returns profile data with the profile_navs array
Vue.js renders the profile header and tab navigation
When a tab is clicked, Vue Router updates the URL and loads the corresponding component
The component may fetch additional data specific to that tab

## Default Profile Tabs

The default profile tabs are defined in `ProfileController.php`:

```php
$profile['profile_navs'] = [
    [
        'slug'          => 'user_profile',
        'title'         => __('About', 'fluent-community'),
        'url'           => $profileBaseUrl,
        'wrapper_class' => 'fcom_profile_about',
        'route'         => [
            'name' => 'user_profile'
        ]
    ],
    [
        'slug'          => 'user_profile_feeds',
        'title'         => __('Posts', 'fluent-community'),
        'wrapper_class' => 'fcom_profile_posts',
        'url'           => $profileBaseUrl . 'posts',
        'route'         => [
            'name' => 'user_profile_feeds'
        ]
    ]
];

// Add Spaces tab conditionally
if ($profile['canViewUserSpaces']) {
    $profile['profile_navs'][] = [
        'slug'          => 'user_spaces',
        'wrapper_class' => 'fcom_profile_spaces',
        'title'         => __('Spaces', 'fluent-community'),
        'url'           => $profileBaseUrl . 'spaces',
        'route'         => [
            'name' => 'user_spaces'
        ]
    ];
}

// Add Comments tab
$profile['profile_navs'][] = [
    'slug'          => 'user_comments',
    'wrapper_class' => 'fcom_profile_comments',
    'title'         => __('Comments', 'fluent-community'),
    'url'           => $profileBaseUrl . 'comments',
    'route'         => [
        'name' => 'user_comments'
    ]
];
```

## Adding Custom Profile Tabs

There are two primary methods to add custom profile tabs:

### Method 1: Using the `fluent_community/user_profile_tabs` Filter

This filter allows you to add new tabs to the user profile navigation:

```php
add_filter('fluent_community/user_profile_tabs', function($tabs, $vars) {
    // Add a new tab
    $tabs['your_tab_key'] = [
        'name' => __('Your Tab Name', 'your-text-domain'),
        'icon' => 'el-icon-your-icon',  // Element UI icon class
        'route' => 'your_route'  // Route identifier
    ];
    
    return $tabs;
}, 10, 2);
```

### Method 2: Using the `fluent_community/portal_vars` Filter

This filter allows you to modify the portal variables including profile tabs:

```php
add_filter('fluent_community/portal_vars', function($vars) {
    // Check if profile_navs exists
    if (!isset($vars['profile_navs']) || !is_array($vars['profile_navs'])) {
        return $vars;
    }
    
    // Check if we have profile user info
    if (!isset($vars['profile_user']) || !isset($vars['profile_user']['username'])) {
        return $vars;
    }
    
    $username = $vars['profile_user']['username'];
    
    // Construct profile base URL
    $profileBaseUrl = '';
    if (function_exists('\\FluentCommunity\\App\\Services\\Helper::baseUrl')) {
        $profileBaseUrl = \FluentCommunity\App\Services\Helper::baseUrl('u/' . $username . '/');
    } else {
        $profileBaseUrl = '/feed/u/' . $username . '/';
    }
    
    // Add new tab
    $vars['profile_navs'][] = [
        'title' => __('Your Tab Name', 'your-text-domain'),
        'icon' => 'el-icon-your-icon',
        'url' => $profileBaseUrl . 'your-tab-slug',
        'route' => [
            'name' => 'your_route_name'
        ],
        'wrapper_class' => 'your_custom_class',
        'position' => 30  // Control tab order
    ];
    
    return $vars;
}, 20);  // Higher priority to ensure it runs after Fluent Community's own filters
```

## Tab Structure

Each tab is defined with these properties:

| Property | Description |
|----------|-------------|
| `title` | The display name of the tab |
| `icon` | Element UI icon class (optional) |
| `url` | URL for the tab |
| `route` | Vue router route information |
| `slug` or `wrapper_class` | CSS class for the tab wrapper |
| `position` | Optional numeric position for ordering tabs |

## Real-World Example: Mind Qtrl Community Image Feed CE

The Mind Qtrl plugin adds a "Media" tab using both methods for compatibility:

```php
// Method 1: Using fluent_community/user_profile_tabs filter
add_filter('fluent_community/user_profile_tabs', function($tabs, $vars) {
    // Add the Media tab
    $tabs['media'] = [
        'name' => __('Media', 'mqcif-ce'),
        'icon' => 'el-icon-picture',
        'route' => 'media'
    ];
    
    return $tabs;
}, 10, 2);

// Method 2: Using fluent_community/portal_vars filter
add_filter('fluent_community/portal_vars', function($vars) {
    // Add Media tab to profile_navs
    $vars['profile_navs'][] = [
        'title' => __('Media', 'mqcif-ce'),
        'icon'  => 'el-icon-picture',
        'url'   => $profileBaseUrl . 'media',
        'route' => [
            'name' => 'user_profile_media'
        ],
        'wrapper_class' => 'fcom_profile_media',
        'position' => 30 // After posts, before spaces
    ];
    
    return $vars;
}, 20);
```

## Frontend Rendering

Profile tabs are rendered in Vue.js using the profile_navs array from portal_vars:

```javascript
v("div",$Ge,[($=(w=o.profile)==null?void 0:w.profile_navs)!=null&&$.length?(_(),C("ul",EGe,[(_(!0),C(te,null,Me(o.profile.profile_navs,M=>(_(),C("li",{class:F(M.wrapper_class??"")},[M.route?(_(),V(p,{key:0,class:F(M.css_class??""),to:M.route},{default:S(()=>[M.svg_icon?(_(),V(u,{key:0,innerHTML:M.svg_icon},null,8,["innerHTML"])):P("",!0),v("span",null,A(M.title),1)]),_:2},1032,["class","to"])):(_(),C("a",{key:1,href:M.url,class:F(M.css_class??""),target:M.is_external?"_blank":"_self"},[M.svg_icon?(_(),V(u,{key:0,innerHTML:M.svg_icon},null,8,["innerHTML"])):P("",!0),v("span",null,A(M.title),1)],10,TGe))],2))),256))])):P("",!0)
```

## Frontend Rendering

Profile tabs are rendered in Vue.js using the profile_navs array from portal_vars:

```javascript
v("div",$Ge,[($=(w=o.profile)==null?void 0:w.profile_navs)!=null&&$.length?(_(),C("ul",EGe,[(_(!0),C(te,null,Me(o.profile.profile_navs,M=>(_(),C("li",{class:F(M.wrapper_class??"")},[M.route?(_(),V(p,{key:0,class:F(M.css_class??""),to:M.route},{default:S(()=>[M.svg_icon?(_(),V(u,{key:0,innerHTML:M.svg_icon},null,8,["innerHTML"])):P("",!0),v("span",null,A(M.title),1)]),_:2},1032,["class","to"])):(_(),C("a",{key:1,href:M.url,class:F(M.css_class??""),target:M.is_external?"_blank":"_self"},[M.svg_icon?(_(),V(u,{key:0,innerHTML:M.svg_icon},null,8,["innerHTML"])):P("",!0),v("span",null,A(M.title),1)],10,TGe))],2))),256))])):P("",!0)
```

## How Tab Content Changes

When a user clicks on a profile tab, several key mechanisms work together to change the page content:

### 1. Vue Router Configuration

Fluent Community uses Vue Router to manage profile tab navigation with nested routes defined as follows:

```javascript
{
  path: "/u/:username/",
  component: tZe, // User profile component
  props: true,
  meta: {active_menu: "all_members"},
  children: [
    {
      name: "user_profile",
      path: "",
      component: xJe, // About tab component
      meta: {active_menu: "all_members"}
    },
    {
      name: "user_profile_feeds",
      path: "posts",
      component: lZe, // Posts tab component
      meta: {active_menu: "all_members"}
    },
    {
      name: "user_spaces",
      path: "spaces",
      component: XZe,
      meta: {active_menu: "all_members", disable_sidebar: true}
    },
    {
      name: "user_comments",
      path: "comments",
      component: DJe,
      meta: {active_menu: "all_members"}
    },
    // Additional routes for other tabs
  ]
}
```

### 2. Router View Component

The parent profile component (`tZe`) includes a `router-view` component that serves as a placeholder for the child route components:

```javascript
// Inside the profile component template
y(h, {}) // router-view component
```

When a tab is clicked, the Vue Router changes the current route, and the corresponding component is rendered in the router-view without reloading the entire page.

### 3. Adding Custom Tab Content

When adding a custom tab, you need to:

1. Register the tab in the `profile_navs` array (as shown in previous sections)
2. Define a Vue component to render the tab content
3. Register a route for your tab in the Vue router

For example, to add a Media tab that displays images:

```javascript
// In your plugin's JavaScript file
const MediaTab = {
  name: 'UserProfileMedia',
  props: ['username', 'profile'],
  data() {
    return {
      images: [],
      loading: false
    }
  },
  created() {
    this.fetchImages();
  },
  methods: {
    fetchImages() {
      this.loading = true;
      this.$get('media/user/' + this.username)
        .then(response => {
          this.images = response.data;
        })
        .finally(() => {
          this.loading = false;
        });
    }
  },
  template: `
    <div class="user-media-tab">
      <h3>User Media</h3>
      <div v-if="loading">Loading...</div>
      <div v-else class="media-grid">
        <div v-for="image in images" :key="image.id" class="media-item">
          <img :src="image.url" :alt="image.title" />
        </div>
      </div>
    </div>
  `
};

// Register the component and route
window.FluentCommunityApp.component('user-profile-media', MediaTab);
window.FluentCommunityApp.addRoute({
  name: 'user_profile_media',
  path: '/u/:username/media',
  component: MediaTab,
  props: true
});
```

### 4. Dynamic Route Handling

The JavaScript router is configured to handle dynamic routes for custom tabs. When a user clicks on a tab, the following happens:

1. The `router-link` component captures the click event
2. Vue Router updates the URL without a full page reload
3. The matching route component is rendered in the `router-view`
4. The component's lifecycle hooks trigger data fetching for the new tab

## Best Practices

1. **Use Both Filter Methods**: For maximum compatibility, implement both `fluent_community/user_profile_tabs` and `fluent_community/portal_vars` filters.

2. **Check for Existing Data**: Always verify that the expected arrays exist before modifying them.

3. **Add Unique Classes**: Include a unique wrapper_class to style your tab properly.

4. **Handle Route Configuration**: Ensure your route name matches what your Vue component expects.

5. **Set Appropriate Priority**: Use a higher priority (like 20) for the filter to ensure it runs after Fluent Community's internal filters.

6. **Register Vue Components**: Make sure to register any custom Vue components needed for your tab content.

7. **Consider Tab Order**: Use the 'position' property to control where your tab appears in the navigation.

8. **Lazy Load Data**: For performance, lazy load tab content data when the tab is activated rather than loading all tab data on initial page load.
