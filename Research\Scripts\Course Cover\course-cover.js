
<script type="text/javascript">
(function() {
  console.log("🔍 FC header unified injector v2");

  // 1) slug → root-relative image paths
  const courseImages = {
    "build-community": "/wp-content/uploads/2025/05/5-1.jpg",
    "gathering-info":  "/wp-content/uploads/2025/05/6.jpg",
    "monthly-muse":    "/wp-content/uploads/2025/05/4.jpg",
    "testing-this":    "/wp-content/uploads/2025/05/test-this.jpg"
  };

  // 2) extract the slug
  function getCourseSlug() {
    const m = location.pathname.match(/\/course\/([^\/]+)(?:\/|$)/);
    return m ? m[1] : null;
  }

  // 3) try injecting when the real intro appears
  function tryInject() {
    const slug = getCourseSlug();
    const url  = slug && courseImages[slug];
    if (!url) return;

    // a) find the body wrapper
    const body = document.querySelector('.fhr_content_layout_body');
    if (!body) return;

    // b) wait for the real course intro (not skeletons)
    const intro = body.querySelector('.fcom_course_sections .fcom_course_intro');
    if (!intro) return;

    // c) guard against double-inject
    if (body.querySelector(':scope > img.fc-course-header-img')) return;

    // d) build & prepend your <img>
    const img = document.createElement('img');
    img.src       = url;
    img.alt       = "";
    img.className = "fc-course-header-img";
    body.prepend(img);

    console.log("✅ Header injected for", slug);
  }

  // 4) poll every 300ms
  setInterval(tryInject, 300);
})();
</script>