<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 3 Music Player Custom Element</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://unpkg.com/vue@3.4.21/dist/vue.global.js"></script>
    <style>
        /* Basic page styling */
        body {
            font-family: 'Inter', sans-serif; /* Tailwind's default sans-serif stack */
            background-color: #333; /* Dark background for the page to see the player better */
            color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end; /* Place player at the bottom */
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        /* Custom element will have its own encapsulated styles */
        music-player-footer {
            width: 100%;
            max-width: 1200px; /* Max width for the player */
            box-shadow: 0 -2px 10px rgba(0,0,0,0.5);
        }

        /* Style for the component itself (will be injected by Vue into Shadow DOM) */
        /* We define it here so it's clear, Vue will handle encapsulation */
        .music-player-styles {
            /* Player container */
            /* background-color: #181818; /* Dark background for the player */
            /* color: #b3b3b3; /* Light grey text */
            /* padding: 12px 16px; */
            /* display: flex; */
            /* align-items: center; */
            /* justify-content: space-between; */
            /* font-size: 14px; */
            /* border-top: 1px solid #282828; */
        }
    </style>
</head>
<body>

    <p class="mb-4 text-center">This is a demo of the Vue 3 Custom Music Player Element.</p>
    <p class="mb-8 text-center text-sm text-gray-400">The player should appear below.</p>

    <music-player-footer
        initial-song-title="What Them A Go Do"
        initial-artist-name="Gentleman"
        initial-album-art-url="https://placehold.co/64x64/333/FFF?text=Art"
        initial-audio-src="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3"
        initial-duration="170" initial-current-time="51" ></music-player-footer>

    <script type="module">
        const { defineCustomElement, ref, computed, onMounted, watch } = Vue;

        const MusicPlayer = {
            // Props definition for the custom element
            // Custom elements receive props as kebab-case attributes
            props: {
                initialSongTitle: { type: String, default: 'Song Title' },
                initialArtistName: { type: String, default: 'Artist Name' },
                initialAlbumArtUrl: { type: String, default: 'https://placehold.co/64x64/555/eee?text=Art' },
                initialAudioSrc: { type: String, default: '' },
                initialDuration: { type: Number, default: 0 }, // in seconds
                initialCurrentTime: { type: Number, default: 0 } // in seconds
            },
            // Use setup function for Composition API
            setup(props) {
                // Reactive state
                const song = ref({
                    title: props.initialSongTitle,
                    artist: props.initialArtistName,
                    albumArt: props.initialAlbumArtUrl,
                    audioSrc: props.initialAudioSrc,
                    duration: Number(props.initialDuration) || 250, // Default duration if 0
                });

                const isPlaying = ref(false);
                const currentTime = ref(Number(props.initialCurrentTime) || 0);
                const volume = ref(0.75); // 0 to 1
                const isMuted = ref(false);
                const isShuffle = ref(false);
                const repeatMode = ref('none'); // 'none', 'one', 'all'
                const showVolumeSlider = ref(false); // To toggle volume slider visibility

                const audioElement = ref(null); // Reference to the <audio> element

                // Computed properties
                const progressPercent = computed(() => {
                    return song.value.duration ? (currentTime.value / song.value.duration) * 100 : 0;
                });

                const formatTime = (secs) => {
                    if (isNaN(secs) || secs === Infinity) {
                        return '0:00';
                    }
                    const minutes = Math.floor(secs / 60);
                    const seconds = Math.floor(secs % 60).toString().padStart(2, '0');
                    return `${minutes}:${seconds}`;
                };

                const formattedCurrentTime = computed(() => formatTime(currentTime.value));
                const formattedDuration = computed(() => formatTime(song.value.duration));

                // Methods
                const togglePlayPause = () => {
                    if (!audioElement.value) return;
                    if (isPlaying.value) {
                        audioElement.value.pause();
                    } else {
                        // Attempt to play. Modern browsers might block autoplay without user interaction.
                        audioElement.value.play().catch(error => console.warn("Playback prevented:", error));
                    }
                    isPlaying.value = !isPlaying.value;
                };

                const playNext = () => console.log('Next song'); // Placeholder
                const playPrevious = () => console.log('Previous song'); // Placeholder

                const toggleShuffle = () => isShuffle.value = !isShuffle.value;

                const cycleRepeatMode = () => {
                    if (repeatMode.value === 'none') repeatMode.value = 'all';
                    else if (repeatMode.value === 'all') repeatMode.value = 'one';
                    else repeatMode.value = 'none';
                };

                const seek = (event) => {
                    if (!audioElement.value || !song.value.duration) return;
                    const progressBar = event.currentTarget;
                    const clickPosition = event.offsetX / progressBar.offsetWidth;
                    const newTime = clickPosition * song.value.duration;
                    audioElement.value.currentTime = newTime;
                    currentTime.value = newTime;
                };

                const updateProgress = () => {
                    if (!audioElement.value) return;
                    currentTime.value = audioElement.value.currentTime;
                };

                const handleAudioEnded = () => {
                    isPlaying.value = false;
                    if (repeatMode.value === 'one') {
                        audioElement.value.currentTime = 0;
                        audioElement.value.play();
                        isPlaying.value = true;
                    } else if (repeatMode.value === 'all') {
                        // In a real app, you'd load the next song or restart the playlist
                        audioElement.value.currentTime = 0;
                        audioElement.value.play();
                        isPlaying.value = true;
                    } else {
                         currentTime.value = 0; // Reset to beginning
                    }
                };
                
                const handleLoadedMetadata = () => {
                    if (audioElement.value && audioElement.value.duration !== Infinity) {
                         song.value.duration = audioElement.value.duration;
                    }
                     // Set initial time if provided after metadata loaded
                    if (props.initialCurrentTime > 0 && audioElement.value) {
                         audioElement.value.currentTime = Number(props.initialCurrentTime);
                         currentTime.value = Number(props.initialCurrentTime);
                    }
                };


                const toggleMute = () => {
                    if (!audioElement.value) return;
                    isMuted.value = !isMuted.value;
                    audioElement.value.muted = isMuted.value;
                };
                
                const setVolume = (event) => {
                    if (!audioElement.value) return;
                    const newVolume = parseFloat(event.target.value);
                    volume.value = newVolume;
                    audioElement.value.volume = newVolume;
                    if (newVolume > 0 && isMuted.value) {
                        isMuted.value = false;
                        audioElement.value.muted = false;
                    } else if (newVolume === 0 && !isMuted.value) {
                         isMuted.value = true;
                         audioElement.value.muted = true;
                    }
                };

                // Lifecycle hooks
                onMounted(() => {
                    if (audioElement.value) {
                        audioElement.value.volume = volume.value;
                        audioElement.value.muted = isMuted.value;
                        // If an initial audio source is provided, load it.
                        if (song.value.audioSrc) {
                            audioElement.value.src = song.value.audioSrc;
                            // Wait for metadata to load to get duration
                            audioElement.value.onloadedmetadata = handleLoadedMetadata;
                        } else {
                            // Use initial duration if no audio src
                             song.value.duration = Number(props.initialDuration) || 250;
                        }
                         // Set initial time if audio already loaded (e.g. from cache)
                        if (audioElement.value.readyState >= 1 && props.initialCurrentTime > 0) { // HAVE_METADATA
                            audioElement.value.currentTime = Number(props.initialCurrentTime);
                            currentTime.value = Number(props.initialCurrentTime);
                        }
                    }
                });
                
                // Watch for changes in initialAudioSrc prop to update the player
                watch(() => props.initialAudioSrc, (newSrc) => {
                    song.value.audioSrc = newSrc;
                    if (audioElement.value) {
                        audioElement.value.src = newSrc;
                        audioElement.value.load(); // Important to load the new source
                        // Reset states for new song
                        isPlaying.value = false;
                        currentTime.value = 0;
                        // Play can be attempted here, or wait for user interaction
                        // audioElement.value.play().catch(e => console.warn("Autoplay prevented for new song", e));
                    }
                });

                watch(() => props.initialSongTitle, (newTitle) => song.value.title = newTitle);
                watch(() => props.initialArtistName, (newArtist) => song.value.artist = newArtist);
                watch(() => props.initialAlbumArtUrl, (newArt) => song.value.albumArt = newArt);
                watch(() => props.initialDuration, (newDuration) => {
                    if (audioElement.value && !audioElement.value.src) { // Only update if no audio src is playing
                        song.value.duration = Number(newDuration);
                    }
                });
                 watch(() => props.initialCurrentTime, (newTime, oldTime) => {
                    // Only update if it's a meaningful change and different from current audio time
                    if (audioElement.value && Math.abs(newTime - audioElement.value.currentTime) > 1) {
                         audioElement.value.currentTime = Number(newTime);
                         currentTime.value = Number(newTime);
                    } else if (!audioElement.value) {
                        currentTime.value = Number(newTime);
                    }
                });


                // Template for the component
                // Using a string template here. For .vue files, this would be in <template>
                // Important: Custom elements are often rendered into a Shadow DOM.
                // Styles here are encapsulated.
                const template = `
                    <div class="music-player-styles bg-[#181818] text-[#b3b3b3] p-3 sm:p-4 flex items-center justify-between text-xs sm:text-sm border-t border-[#282828] gap-2 sm:gap-4">
                        <div class="flex items-center gap-2 sm:gap-3 min-w-[150px] sm:min-w-[200px] flex-1">
                            <img :src="song.albumArt" alt="Album Art" class="w-10 h-10 sm:w-14 sm:h-14 rounded">
                            <div class="flex flex-col justify-center">
                                <span class="text-white font-medium text-sm sm:text-base truncate" :title="song.title">{{ song.title }}</span>
                                <span class="text-xs sm:text-sm truncate" :title="song.artist">{{ song.artist }}</span>
                            </div>
                            <i class="fa-solid fa-heart text-gray-500 hover:text-green-500 cursor-pointer ml-2 text-sm sm:text-base"></i>
                            </div>

                        <div class="flex flex-col items-center gap-1 sm:gap-2 flex-grow-[2] max-w-xl">
                            <div class="flex items-center gap-2 sm:gap-4 text-gray-400">
                                <button @click="toggleShuffle" :class="{'text-green-500': isShuffle, 'hover:text-white': !isShuffle}" title="Shuffle">
                                    <i class="fa-solid fa-shuffle text-base sm:text-lg"></i>
                                </button>
                                <button @click="playPrevious" class="hover:text-white" title="Previous">
                                    <i class="fa-solid fa-backward-step text-base sm:text-lg"></i>
                                </button>
                                <button @click="togglePlayPause" class="bg-white text-black rounded-full w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center hover:scale-105" title="Play/Pause">
                                    <i :class="isPlaying ? 'fa-solid fa-pause' : 'fa-solid fa-play'" class="text-sm sm:text-base ml-[1px]"></i>
                                </button>
                                <button @click="playNext" class="hover:text-white" title="Next">
                                    <i class="fa-solid fa-forward-step text-base sm:text-lg"></i>
                                </button>
                                <button @click="cycleRepeatMode" :class="{'text-green-500': repeatMode !== 'none', 'hover:text-white': repeatMode === 'none'}" title="Repeat">
                                    <i v-if="repeatMode === 'one'" class="fa-solid fa-repeat-1 text-base sm:text-lg">1</i>
                                    <i v-else class="fa-solid fa-repeat text-base sm:text-lg"></i>
                                </button>
                            </div>
                            <div class="flex items-center w-full gap-2 text-xs">
                                <span class="w-8 sm:w-10 text-right">{{ formattedCurrentTime }}</span>
                                <div @click="seek" class="w-full h-1 bg-gray-600 rounded-full cursor-pointer group">
                                    <div class="h-full bg-green-500 rounded-full group-hover:bg-green-400" :style="{ width: progressPercent + '%' }"></div>
                                </div>
                                <span class="w-8 sm:w-10 text-left">{{ formattedDuration }}</span>
                            </div>
                        </div>

                        <div class="flex items-center gap-2 sm:gap-3 text-gray-400 min-w-[120px] sm:min-w-[180px] justify-end flex-1">
                             <button class="hover:text-white" title="Lyrics">
                                <i class="fa-solid fa-microphone-lines text-sm sm:text-base"></i>
                            </button>
                             <button class="hover:text-white" title="Queue">
                                <i class="fa-solid fa-list-ol text-sm sm:text-base"></i>
                            </button>
                             <button class="hover:text-white" title="Connect to a device">
                                <i class="fa-solid fa-computer-speaker sm:fa-solid fa-desktop text-sm sm:text-base"></i> </button>
                            <div class="flex items-center gap-1 sm:gap-2 relative">
                                <button @click="toggleMute" class="hover:text-white" title="Mute/Unmute">
                                    <i v-if="isMuted || volume === 0" class="fa-solid fa-volume-xmark text-sm sm:text-base"></i>
                                    <i v-else-if="volume < 0.5" class="fa-solid fa-volume-low text-sm sm:text-base"></i>
                                    <i v-else class="fa-solid fa-volume-high text-sm sm:text-base"></i>
                                </button>
                                <input 
                                    type="range" 
                                    min="0" 
                                    max="1" 
                                    step="0.01" 
                                    :value="isMuted ? 0 : volume"
                                    @input="setVolume"
                                    class="w-12 sm:w-20 h-1 bg-gray-600 rounded-full appearance-none cursor-pointer accent-green-500 hover:accent-green-400"
                                    title="Volume"
                                >
                            </div>
                            <button class="hover:text-white" title="Fullscreen">
                                <i class="fa-solid fa-expand text-sm sm:text-base"></i>
                            </button>
                        </div>
                        
                        <audio ref="audioElement" 
                               @timeupdate="updateProgress" 
                               @ended="handleAudioEnded"
                               @loadedmetadata="handleLoadedMetadata"
                               class="hidden">
                        </audio>
                    </div>
                `;

                return {
                    song,
                    isPlaying,
                    currentTime,
                    volume,
                    isMuted,
                    isShuffle,
                    repeatMode,
                    progressPercent,
                    formattedCurrentTime,
                    formattedDuration,
                    audioElement,
                    togglePlayPause,
                    playNext,
                    playPrevious,
                    toggleShuffle,
                    cycleRepeatMode,
                    seek,
                    toggleMute,
                    setVolume,
                    showVolumeSlider,
                    // Return the template string
                    template
                };
            },
            // Styles for the custom element (will be injected into Shadow DOM)
            // Note: For complex styles, .ce.vue files are better. Here, we keep it simple.
            // Tailwind classes in the template will work if Tailwind is global,
            // but for true encapsulation, define styles here or ensure they are self-contained.
            // The template string above uses Tailwind classes, assuming Tailwind is loaded on the page.
            // For fully encapsulated styles without relying on global Tailwind, define them here.
            // For this example, we'll rely on the global Tailwind and Font Awesome.
            styles: [`
                /* Encapsulated styles for the music player. */
                /* These styles will be inside the Shadow DOM. */
                /* Basic reset/normalize can be useful if not relying on external CSS. */
                
                /* Example of an encapsulated style: */
                /* .text-white { color: #fff; } */
                /* .bg-green-500 { background-color: #22c55e; } */
                /* ... and so on for all Tailwind classes used if you don't want to depend on global Tailwind. */
                /* For simplicity, this example assumes global Tailwind/FontAwesome are available to the shadow DOM */
                /* or that the browser/Vue handles this sufficiently for common cases. */
                /* Actual behavior can vary with shadow DOM mode (open/closed) and browser. */

                /* Custom styling for the range input to match the theme */
                input[type="range"] {
                    -webkit-appearance: none; /* Override default Safar/Chrome styles */
                    appearance: none;
                    background: transparent; /* Remove default background */
                    cursor: pointer;
                }

                /* Track styling */
                input[type="range"]::-webkit-slider-runnable-track {
                    background: #4b5563; /* bg-gray-600 */
                    height: 0.25rem; /* h-1 */
                    border-radius: 9999px; /* rounded-full */
                }
                input[type="range"]::-moz-range-track {
                    background: #4b5563;
                    height: 0.25rem;
                    border-radius: 9999px;
                    border: none; 
                }

                /* Thumb styling */
                input[type="range"]::-webkit-slider-thumb {
                    -webkit-appearance: none; /* Override default look */
                    appearance: none;
                    margin-top: -4px; /* Position thumb centrally on track */
                    background-color: #10b981; /* accent-green-500 (approx) */
                    height: 0.75rem; /* w-3 h-3 approx */
                    width: 0.75rem;
                    border-radius: 9999px; /* rounded-full */
                }
                input[type="range"]:hover::-webkit-slider-thumb {
                    background-color: #34d399; /* accent-green-400 (approx) */
                }
                input[type="range"]::-moz-range-thumb {
                    border: none; /* Remove border in Firefox */
                    background-color: #10b981;
                    height: 0.75rem;
                    width: 0.75rem;
                    border-radius: 9999px;
                }
                 input[type="range"]:hover::-moz-range-thumb {
                    background-color: #34d399;
                }

                /* Ensure icons in buttons are centered if they have specific widths */
                button i.fa-play { margin-left: 2px; } /* Slight adjustment for visual centering */

                /* Ensure the repeat 1 icon is styled correctly */
                button i.fa-repeat-1 { font-style: normal; font-weight: bold; }

                /* Truncate helper (if not using Tailwind's truncate effectively in shadow DOM) */
                .truncate {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            `]
        };

        // Define the custom element
        // Vue's defineCustomElement wraps the component and handles props, events, slots.
        const MusicPlayerElement = defineCustomElement(MusicPlayer);

        // Register the custom element with the browser.
        // The tag name must contain a hyphen.
        customElements.define('music-player-footer', MusicPlayerElement);
    </script>

</body>
</html>
