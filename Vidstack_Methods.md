### Vidstack JS CDN documentation integration method 1:

Import Styles
Section titled 6. Import Styles
js
Copy
<link rel="stylesheet" href="https://cdn.vidstack.io/player/theme.css" />
<link rel="stylesheet" href="https://cdn.vidstack.io/player/video.css" />
7. Create Player
Section titled 7. Create Player
Copy
<script type="module">
  import { VidstackPlayer, VidstackPlayerLayout } from 'https://cdn.vidstack.io/player';

  const player = await VidstackPlayer.create({
    target: '#target',
    title: 'Sprite Fight',
    src: 'youtube/_cMxraX_5RE',
    poster: 'https://files.vidstack.io/sprite-fight/poster.webp',
    layout: new VidstackPlayerLayout({
      thumbnails: 'https://files.vidstack.io/sprite-fight/thumbnails.vtt',
    }),
  });
</script>
The target config option is a target element which can be a CSS string selector or a HTMLElement. The target can be any element but if it’s a <audio>, <video>, or <iframe> element it will be replaced and enhanced (i.e., progressive enhancement).

<!-- The player will be appended to this element. -->
<div id="target"></div>
<!-- This element will be replaced and enhanced. -->
<iframe id="target" src="..." allowfullscreen></iframe>
Props can also be set on the target element using data attributes like so:

<div id="target" data-poster="..." data-autoplay ...></div>

'''

### The Vidstack JSDelivr CDN documentation integration method 2:
 Import Styles
Section titled 5. Import Styles
html
Copy
<link rel="stylesheet" href="https://cdn.vidstack.io/player/theme.css" />
<link rel="stylesheet" href="https://cdn.vidstack.io/player/video.css" />
6. Import Elements
Section titled 6. Import Elements
The following import will register all custom media elements (e.g., <media-player>) and import global TypeScript types:

html
Copy
<script src="https://cdn.vidstack.io/player" type="module"></script>
You can append .dev to any CDN URL to load the development version:

html
Copy
<script src="https://cdn.vidstack.io/player.dev" type="module"></script>
You can also pin the CDN package version by appending @x.x.x like so:

html
Copy
<!-- Remember to pin the styles as well! -->
<script src="https://cdn.vidstack.io/player@1.11.21" type="module"></script>
7. Player Markup
Section titled 7. Player Markup
html
Copy
<media-player title="Sprite Fight" src="youtube/_cMxraX_5RE">
  <media-provider></media-provider>
  <media-video-layout thumbnails="https://files.vidstack.io/sprite-fight/thumbnails.vtt"></media-video-layout>
</media-player>
You can also construct the player using JavaScript. The target argument can be a query selector or HTMLElement. If the target is a <audio>, <video>, or <iframe> element it will be progressively enhanced. See the JavaScript installation page for more information.

Copy
<script type="module">
  import { VidstackPlayer, VidstackPlayerLayout } from 'https://cdn.vidstack.io/player';

  const player = await VidstackPlayer.create({
    target: '#target',
    title: 'Sprite Fight',
    src: 'youtube/_cMxraX_5RE',
    poster: 'https://files.vidstack.io/sprite-fight/poster.webp',
    layout: new VidstackPlayerLayout({
      thumbnails: 'https://files.vidstack.io/sprite-fight/thumbnails.vtt',
    }),
  });
</script>
8. Poster (Optional)
Section titled 8. Poster (Optional)
See the Poster component for how to display an image while video content is loading, or until the user hits play. If this is not included, the first frame of the video will be used instead.

9. Playsinline (Optional)
Section titled 9. Playsinline (Optional)
The playsinline property will indicate that video content should be played inline (on mobile only), rather than launching the video in fullscreen mode on play. In addition, setting this property will also ensure custom player UI is displayed on iPhone when playing inline (hidden in fullscreen mode as native controls are forcefully displayed by the browser).

Important to note, we normalize the playing inline behaviour across all mobile browsers. If you do not set playsinline, the video will launch in fullscreen on play in all mobile browsers, not just iOS Safari where the attribute is accepted.

<media-player playsinline>
  <!-- ... -->
</media-player>
10. Keep Alive (Optional)
Section titled 10. Keep Alive (Optional)
By default, the player and all components will destroy themselves if they’re removed from the DOM and not reattached after an animation frame tick (i.e., requestAnimationFrame). If you or your router are moving player components around the DOM for unknown amounts of time, consider keeping the player and all children alive, and manually destroying all component instances.

<script>
  const player = document.querySelector('media-player');

  function onDestroy() {
    // This call will destroy the player and all child instances.
    player.destroy();
  }
</script>

<!-- `keep-alive` is forwarded to all child components. -->
<media-player keep-alive>
  <!-- ... -->
</media-player>
INFO
The keep-alive attribute can be set on any media element, not just the player. Important to note that it’s forwarded to all children. Any root component with keep-alive must be destroyed by calling the destroy() method on the element instance as shown above.
'''


###  Architecture
An overview of the player architecture to understand how media playback and updates are handled.

Overview
Section titled Overview
Vidstack Player is designed around a request and response model, similar to the client-server model or HTTP. State is stored as signals and pushed down via context to consumers such as UI components who subscribe to updates via effects, DOM events are dispatched up to the player for requesting state changes, and the current media provider will asynchronously respond to the request by satisfying or rejecting it.

Anything we “ask” the player (more precisely the current provider) to do is simply a request as we can’t guarantee success or failure, at least from the requestors perspective. Requests are dispatched from child player components via DOM events to the MediaRequestManager which performs the appropriate actions on the current media provider. The MediaProviderAdapter ensures we can speak a common language across various providers as the API might differ. After the MediaRequestManager performs it’s tasks, a request is now pending and put into queue. The media provider will asynchronously notify the MediaStateManager that the request was either satisfied or rejected. Finally, the state manager will satisfy the request by attaching it as a trigger to the media event (e.g., media-play-request will be attached to the media play or play-fail event), release it from the queue, and update the media store. This completes the request/response lifecycle. In the rest of this guide, we’ll briefly unpack each of these core architectural components and processes.

Source Selection
Section titled Source Selection
See source code.

The source selection process determines which provider loader is active and consequently which provider to load and render. The selection process is as follows:

Detect src attr or prop change.
Normalize src into an array of Src objects ({src, type}). If a source object is provided (e.g., MediaStream or MediaSource), the type will default to video/object, otherwise unknown.
The sources-change event is fired.
Walk through each source at a time in given order and attempt to find a provider who can play it. The canPlay method on each provider loader will check if the media extension or type can be played. The first loader to return true will be promoted to active.
The source-change event is fired. The current source will be null if no provider was matched.
Start the provider loading process.
Media Provider Loader
Section titled Media Provider Loader
See interface and reference.

Media provider loaders are responsible for determining whether the underlying provider can play a given source, dynamically loading and initializing the provider, and rendering content inside the media provider component. Rendered output includes <audio>, <video>, and <iframe> elements.

When a loader becomes active via the source selection process, it will go through the following setup process:

Destroy the old provider if no longer active and fire the provider-change event with detail set to null.
The loader will attempt to preconnect any URLs for the current provider or source.
The provider-loader-change event is fired.
Wait for the new media provider loader to render so the underlying element (e.g., <video>) is ready.
The loader will dynamically import and initialize the provider instance.
The provider-change event is fired. This is the best time to configure the provider before it runs through setup.
Once the specified player loading strategy has resolved, the provider setup method is called. This step generally involves loading required libraries and attaching event listeners.
The provider-setup event is fired.
Finally, the loadSource method is called on the provider with the selected source.
If the provider has not changed during a source change, then the setup process will be skipped and only the new source will be loaded (step 9).

Media Provider
Section titled Media Provider
See interface and reference.

In general, media provider’s are responsible for rendering the underlying media element or iframe, determining the media and view types, loading sources, managing tracks, setting properties, performing media requests, attaching event listeners, and notifying the MediaStateManager of state changes. In addition, each provider will implement the MediaProviderAdapter interface to ensure they have a consistent API.

Media Context
Section titled Media Context
See interface.

The media context is a singleton object passed down from each player instance to all consumers. It contains important objects such as the player itself, remote control for dispatching requests, player delegate for notifying the state manager of updates, media store for UI to subscribe to state changes, and the current media provider.

Media Store
Section titled Media Store
See source code.

The media store is a collection of signals which store and track individual pieces of state. The MediaStateManger is responsible for updating the store when the media provider notifies it of any changes. Player components will subscribe to media state via effects to handle rendering attributes, managing the DOM, and performing operations.

Signals are way for us to create reactive observables to store state, create computed properties, and subscribe to updates as its value changes via effects. We created our own signals library called Maverick Signals which handles the scoping and reactivity complexity. See the link for more information, and you can also read about the evolution of signals by Ryan Carniato if you’d like to dive deeper.

UI Components
Section titled UI Components
See example: Base Component, Custom Element, and React Component.

UI components are abstracted to avoid rewriting complex logic across Custom Elements and React. They’re built on top of our component library called Maverick. Thanks to Signals being our reactivie primitive of choice, we can adapt reactivity to work with any framework easily. The component lifecycle has been simplified down to onSetup (initial setup), onAttach (attached to a DOM or server element), onConnect (connected to the DOM), and onDestroy (end of life). These lifecycle hooks are pure as they can run more than once in React, and they can be individually disposed of.

The base component defines the general contract for the component across props, state, and events. They have no rendered UI out of the box and are responsible for: accessibility, setting data attributes and CSS variables for styling purposes, managing props and internal state, subscribing to media state via the media context, attaching DOM event listeners, dispatching DOM events, and exposing methods.

The mixin Host(Component, HTMLElement) is used to create a Custom Element and attach the base component to it, and createReactComponent(Component) is used to create a client/server React Component and attach to it.

Media Remote Control
Section titled Media Remote Control
See source code.

The MediaRemoteControl is a simple facade for dispatching media request events to the nearest player component in the DOM. It helps consumers avoid creating and dispatching an event such as el.dispatchEvent(new DOMEvent('media-play-request')), and instead just call remote.play().

Media Request Manager
Section titled Media Request Manager
See source code.

The MediaRequestManager routes media request events to the current media provider by calling the appropriate actions on it. In addition, it queues the request event so the MediaStateManager can satisfy it by attaching it to the correct media event. Important to note, the manager can speak with any provider because of the MediaProviderAdapter interface. The interface ensures each provider has the same API for performing operations such as play, pause, seek, etc.

Media State Manager
Section titled Media State Manager
See source code.

The MediaStateManager is responsible for handling media state changes as they’re delegated from the media provider to it, satisfying media request events by attaching them as triggers on the respective success/failure media event and releasing them from the queue, dispatching media events, and updating the media store to ensure it’s in-sync with the currently playing media and provider.

Default Layout
A guide on how to setup the default audio/video layout and available customization options.

Installation
Section titled Installation
See the installation guide to setup the Default Layout.

Demo
Section titled Demo
You can try out the Default Layout on our player demo page.

Usage
Section titled Usage
The Default Layout ships with support for audio, video, and live streams. You can include both layouts at the same time, only one will be matched depending on the view type.

import { MediaPlayer, MediaProvider } from '@vidstack/react';
import {
  DefaultAudioLayout,
  defaultLayoutIcons,
  DefaultVideoLayout,
} from '@vidstack/react/player/layouts/default';

<MediaPlayer title="..." src="...">
  <MediaProvider />
  <DefaultAudioLayout icons={defaultLayoutIcons} />
  <DefaultVideoLayout icons={defaultLayoutIcons} />
</MediaPlayer>
By default, the view type is inferred from the provider and media type. You can specify the desired type like so:

// Force view type to be audio.
<MediaPlayer src="video.mp4" viewType="audio">
The same is true for the stream type:

// Force stream type to be live.
<MediaPlayer streamType="live">
Color Scheme
Section titled Color Scheme
Both the audio and video layout accept color scheme to be either light or dark themed. By default it will use the user’s preferred color scheme. You can set a specific theme by setting the colorScheme prop on the layout like so:

// System (default)
<DefaultAudioLayout colorScheme="system" />
<DefaultVideoLayout colorScheme="system" />
// Light
<DefaultAudioLayout colorScheme="light" />
<DefaultVideoLayout colorScheme="light" />
// Dark
<DefaultAudioLayout colorScheme="dark" />
<DefaultVideoLayout colorScheme="dark" />
You can also set the color scheme by setting a light or dark class on a parent element:

// Set `light` or `dark` on a parent element.
<html className="light">
  {/* ... */}
  <MediaPlayer>
    {/* ... */}
    <DefaultAudioLayout colorScheme="default" />
    <DefaultVideoLayout colorScheme="default" />
  </MediaPlayer>
</html>
Size
Section titled Size
Both the audio and video layout will adapt to small containers to ensure an optimal user experience. You configure when the small layout is displayed like so:

import { type MediaPlayerQuery } from '@vidstack/react';
import { useCallback } from 'react';

const smallAudioLayoutQuery = useCallback<MediaPlayerQuery>(({ width }) => {
  return width < 576;
}, []);

const smallVideoLayoutQuery = useCallback<MediaPlayerQuery>(({ width, height }) => {
  return width < 576 || height < 380;
}, []);

<DefaultAudioLayout smallLayoutWhen={smallAudioLayoutQuery} />
<DefaultVideoLayout smallLayoutWhen={smallVideoLayoutQuery} />
If you’d like to disable small layouts, set the query to false or 'never':

<DefaultAudioLayout smallLayoutWhen={false} />
<DefaultVideoLayout smallLayoutWhen={false} />
Icons
Section titled Icons
You can easily replace the icons used in the layouts to match the style of your application like so:

tsx
Copy
import type { DefaultLayoutIcons } from '@vidstack/react/player/layouts/default';

// Icon should be: `() => ReactNode`
const None = () => null;

// All icons are optional, replace only what you want.
const customIcons: Partial<DefaultLayoutIcons> = {
  AirPlayButton: {
    Default: None,
    Connecting: None,
    Connected: None,
  },
  GoogleCastButton: {
    Default: None,
    Connecting: None,
    Connected: None,
  },
  PlayButton: {
    Play: None,
    Pause: None,
    Replay: None,
  },
  MuteButton: {
    Mute: None,
    VolumeLow: None,
    VolumeHigh: None,
  },
  CaptionButton: {
    On: None,
    Off: None,
  },
  PIPButton: {
    Enter: None,
    Exit: None,
  },
  FullscreenButton: {
    Enter: None,
    Exit: None,
  },
  SeekButton: {
    Backward: None,
    Forward: None,
  },
  DownloadButton: {
    Default: None,
  },
  Menu: {
    Accessibility: None,
    ArrowLeft: None,
    ArrowRight: None,
    Audio: None,
    AudioBoostUp: None,
    AudioBoostDown: None,
    Chapters: None,
    Captions: None,
    Playback: None,
    Settings: None,
    SpeedUp: None,
    SpeedDown: None,
    QualityUp: None,
    QualityDown: None,
    FontSizeUp: None,
    FontSizeDown: None,
    OpacityUp: None,
    OpacityDown: None,
    RadioCheck: None,
  },
  KeyboardDisplay: {
    Play: None,
    Pause: None,
    Mute: None,
    VolumeUp: None,
    VolumeDown: None,
    EnterFullscreen: None,
    ExitFullscreen: None,
    EnterPiP: None,
    ExitPiP: None,
    CaptionsOn: None,
    CaptionsOff: None,
    SeekForward: None,
    SeekBackward: None,
  },
};
<DefaultAudioLayout icons={customIcons} />;
<DefaultVideoLayout icons={customIcons} />
Thumbnails
Section titled Thumbnails
You can provide thumbnails which will be used to display preview images when interacting with the time slider and in the chapters menu. See the loading thumbnails guide for more information.

<DefaultVideoLayout thumbnails="https://files.vidstack.io/sprite-fight/thumbnails.vtt" />
Language
Section titled Language
Both the audio and video layout support internationalization (i18n) by accepting custom language translations. You can dynamically set the translations property to update the language like so:

ts
Copy
import { type DefaultLayoutTranslations } from '@vidstack/react';

const SPANISH: DefaultLayoutTranslations = {
  'Caption Styles': '',
  'Captions look like this': '',
  'Closed-Captions Off': '',
  'Closed-Captions On': '',
  'Display Background': '',
  'Enter Fullscreen': '',
  'Enter PiP': '',
  'Exit Fullscreen': '',
  'Exit PiP': '',
  'Google Cast': '',
  'Keyboard Animations': '',
  'Seek Backward': '',
  'Seek Forward': '',
  'Skip To Live': '',
  'Text Background': '',
  Accessibility: '',
  AirPlay: '',
  Announcements: '',
  Audio: '',
  Auto: '',
  Boost: '',
  Captions: '',
  Chapters: '',
  Color: '',
  Connected: '',
  Connecting: '',
  Continue: '',
  Default: '',
  Disabled: '',
  Disconnected: '',
  Download: '',
  Family: '',
  Font: '',
  Fullscreen: '',
  LIVE: '',
  Loop: '',
  Mute: '',
  Normal: '',
  Off: '',
  Opacity: '',
  Pause: '',
  PiP: '',
  Play: '',
  Playback: '',
  Quality: '',
  Replay: '',
  Reset: '',
  Seek: '',
  Settings: '',
  Shadow: '',
  Size: '',
  Speed: '',
  Text: '',
  Track: '',
  Unmute: '',
  Volume: '',
};
CSS Variables
Section titled CSS Variables
The following snippets contain a complete list of CSS variables and their respective default values. They can all be adjusted by you to customize the audio/video layout and child components as desired.

Audio Layout
Section titled Audio Layout
The following variables can be used to specifically customize the audio layout. See the Components section for more.

css
Copy
.vds-audio-layout {
  --audio-brand: #f5f5f5;
  --audio-controls-color: #f5f5f5;
  --audio-focus-ring-color: rgb(78 156 246);
  --audio-focus-ring: 0 0 0 3px var(--media-focus-ring-color);
  --audio-font-family: sans-serif;

  --audio-bg: black;
  --audio-border-radius: 6px;
  --audio-border: 1px solid rgb(255 255 255 / 0.1);

  /* Buttons. */
  --audio-button-size: 40px;

  --audio-play-button-size: 43px;
  --audio-play-button-color: rgb(0 0 0 / 0.8);
  --audio-play-button-bg: var(--media-brand);
  --audio-play-button-border-radius: 100%;
  --audio-caption-button-off-opacity: 0.64;

  /* Sliders. */
  --audio-slider-chapter-title-color: black;
  --audio-slider-value-border: 1px solid rgb(255 255 255 / 0.1);

  --audio-volume-height: 96px;
  --audio-volume-bg: var(--media-menu-bg, rgb(10 10 10));
  --audio-volume-border-radius: 8px;

  /* Menus. */
  --audio-menu-max-height: 320px;

  /* Buffering. */
  --audio-buffering-stripe-color: rgb(0 0 0 / 0.25);
  --audio-buffering-stripe-size: 30px;
  --audio-buffering-stripe-speed: 2s;

  /* Captions. */
  --audio-captions-offset: 4px;
  --audio-captions-bg: transparent;
  --audio-cue-font-size: 14px;
  --audio-cue-color: white;
  --audio-cue-border: 1px solid rgb(255 255 255 / 0.1);
  --audio-cue-bg: black;

  /* Time */
  --audio-time-font-size: 15px;
}
Video Layout
Section titled Video Layout
The following variables can be used to specifically customize the video layout. See the Components section for more.

css
Copy
.vds-video-layout {
  --video-brand: #f5f5f5;
  --video-controls-color: #f5f5f5;
  --video-focus-ring-color: rgb(78 156 246);
  --video-focus-ring: 0 0 0 3px var(--media-focus-ring-color);
  --video-font-family: sans-serif;

  --video-bg: black;
  --video-border-radius: 6px;
  --video-border: 1px solid rgb(255 255 255 / 0.1);

  --video-fullscreen-chapter-title-font-size: 16px;
  --video-gesture-seek-width: 20%;

  /* Load. */
  --video-load-button-size: 56px;
  --video-load-button-border: var(--color);
  --video-load-button-bg: var(--media-brand);
  --video-load-button-border-radius: 100%;

  --video-sm-load-button-size: 48px;

  /* Buttons. */
  --video-sm-button-size: 32px;
  --video-sm-play-button-size: 40px;
  --video-sm-play-button-transform: translateY(25%);
  --video-sm-play-button-bg: rgba(0 0 0 / 0.6);

  /* Sliders. */
  --video-slider-thumbnail-border: 1px solid #f5f5f5;
  --video-slider-thumbnail-border-radius: 2px;
  --video-volume-slider-max-width: 72px;

  --video-sm-slider-focus-track-height: 12px;

  /* Time. */
  --video-time-bg: unset;
  --video-fullscreen-time-font-size: 16px;

  --video-sm-time-font-size: 14px;
  --video-sm-start-duration-bg: rgba(0 0 0 / 0.64);
  --video-sm-start-duration-padding: 3px 6px;
  --video-sm-start-duration-color: var(--video: controls-color);

  /* Captions. */
  --video-captions-offset: 78px;
  --video-captions-transition: bottom 0.15s linear;

  --video-sm-captions-offset: 48px;
  --video-lg-fullscreen-captions-offset: 54px;

  --video-sm-captions-offset: 48px;
}
Components
Section titled Components
css
Copy
.vds-audio-layout,
.vds-video-layout {
  /* Shared. */
  --media-brand: #f5f5f5;
  --media-controls-color: #f5f5f5;
  --media-font-family: sans-serif;

  /* Buffering. */
  --media-buffering-animation: vds-buffering-spin 1s linear infinite;
  --media-buffering-size: 96px;
  --media-buffering-track-color: #f5f5f5;
  --media-buffering-track-fill-color: var(--media-brand);
  --media-buffering-track-fill-offset: 50;
  --media-buffering-track-fill-opacity: 0.75;
  --media-buffering-track-fill-width: 9;
  --media-buffering-track-opacity: 0.25;
  --media-buffering-track-width: 8;
  --media-buffering-transition: opacity 200ms ease;

  /* Buttons. */
  --media-button-border-radius: 8px;
  --media-button-color: var(--media-controls-color, #f5f5f5);
  --media-button-hover-bg: rgb(255 255 255 / 0.2);
  --media-button-hover-transform: scale(1);
  --media-button-hover-transition: transform 0.2s ease-in;
  --media-button-icon-size: 80%;
  --media-button-padding: 0px;
  --media-button-size: 40px;
  --media-button-touch-hover-bg: rgb(255 255 255 / 0.2);
  --media-button-touch-hover-border-radius: 100%;
  --media-sm-fullscreen-button-size: 42px;
  --media-fullscreen-button-size: 42px;

  /* Tooltips. */
  --media-tooltip-bg-color: black;
  --media-tooltip-border-radius: 4px;
  --media-tooltip-border: 1px solid rgb(255 255 255 / 0.1);
  --media-tooltip-color: hsl(0, 0%, 80%);
  --media-tooltip-font-size: 13px;
  --media-tooltip-font-weight: 500;
  --media-tooltip-padding: 2px 8px;
  --media-tooltip-enter-animation: vds-tooltip-enter 0.2s ease-in;
  --media-tooltip-exit-animation: vds-tooltip-exit 0.2s ease-out;

  /* Live Indicator. */
  --media-live-button-bg: #8a8a8a;
  --media-live-button-border-radius: 2px;
  --media-live-button-color: #161616;
  --media-live-button-edge-bg: #dc2626;
  --media-live-button-edge-color: #f5f5f5;
  --media-live-button-font-size: 12px;
  --media-live-button-font-weight: 600;
  --media-live-button-height: 40px;
  --media-live-button-letter-spacing: 1.5px;
  --media-live-button-padding: 1px 4px;
  --media-live-button-width: 40px;

  /* Captions. */
  --media-captions-padding: 1%;
  --media-cue-backdrop: blur(8px);
  --media-cue-bg: rgba(0, 0, 0, 0.7);
  --media-cue-border-radius: 2px;
  --media-cue-border: unset;
  --media-cue-box-shadow: var(--cue-box-shadow);
  --media-cue-color: white;
  --media-cue-display-bg: unset;
  --media-cue-display-border-radius: unset;
  --media-cue-display-padding: unset;
  --media-cue-font-size: calc(var(--overlay-height) / 100 * 4.5);
  --media-cue-line-height: calc(var(--cue-font-size) * 1.2);
  --media-cue-padding-x: calc(var(--cue-font-size) * 0.4);
  --media-cue-padding-x: calc(var(--cue-font-size) * 0.6);

  /* Chapter Title. */
  --media-chapter-title-color: rgba(255 255 255 / 0.64);
  --media-chapter-title-font-size: 14px;
  --media-chapter-title-font-weight: 500;
  --media-chapter-title-separator-color: var(--color);
  --media-chapter-title-separator-gap: 6px;
  --media-chapter-title-separator: '\2022';

  /* Controls. */
  --media-controls-padding: 0px;
  --media-controls-in-transition: opacity 0.2s ease-in;
  --media-controls-out-transition: opacity 0.2s ease-out;

  /* Thumbnails. */
  --media-thumbnail-bg: black;
  --media-thumbnail-border: 1px solid white;
  --media-thumbnail-aspect-ratio: 16 / 9;
  --media-thumbnail-min-width: 120px;
  --media-thumbnail-min-height: calc(var(--media-thumbnail-min-width) / var(--aspect-ratio));
  --media-thumbnail-max-width: 180px;
  --media-thumbnail-max-height: calc(var(--media-thumbnail-max-width) / var(--aspect-ratio));

  /* Time. */
  --media-time-bg: unset;
  --media-time-border-radius: unset;
  --media-time-border: unset;
  --media-time-color: #f5f5f5;
  --media-time-divider-color: #e0e0e0;
  --media-time-divider-gap: 2.5px;
  --media-time-font-size: 14px;
  --media-time-font-weight: 400;
  --media-time-letter-spacing: 0.025em;

  /* Sliders. */
  --media-slider-width: 100%;
  --media-slider-height: 48px;

  /* Slider Thumb. */
  --media-slider-thumb-bg: #fff;
  --media-slider-thumb-border-radius: 9999px;
  --media-slider-thumb-border: 1px solid #cacaca;
  --media-slider-thumb-size: 15px;
  --media-slider-thumb-transition: opacity 0.2s ease-in, box-shadow 0.2s ease;

  /* Slider Tracks. */
  --media-slider-track-width: 100%;
  --media-slider-track-bg: rgb(255 255 255 / 0.3);
  --media-slider-track-border-radius: 1px;
  --media-slider-track-fill-bg: var(--media-brand);
  --media-slider-track-fill-live-bg: #dc2626;
  --media-slider-track-height: 5px;
  --media-slider-track-progress-bg: rgb(255 255 255 / 0.5);
  --media-slider-focused-thumb-shadow: 0 0 0 4px hsla(0, 0%, 100%, 0.4);
  --media-slider-focused-thumb-size: calc(var(--thumb-size) * 1.1);
  --media-slider-focused-track-height: calc(var(--track-height) * 1.25);
  --media-slider-focused-track-height: var(--track-height);
  --media-slider-focused-track-width: calc(var(--track-width) * 1.25);
  --media-slider-focused-track-width: var(--track-width);

  /* Slider Steps. */
  --media-slider-step-width: 2.5px;
  --media-slider-step-color: rgb(124, 124, 124);

  /* Slider Chapter. */
  --media-slider-chapter-hover-transform: scaleY(2);
  --media-slider-chapter-hover-transition: transform 0.1s cubic-bezier(0.4, 0, 1, 1);

  /* Slider Preview. */
  --media-slider-preview-bg: unset;
  --media-slider-preview-border-radius: 2px;

  /* Slider Chapter Title. */
  --media-slider-chapter-title-bg: unset;
  --media-slider-chapter-title-color: #f5f5f5;
  --media-slider-chapter-title-font-size: 14px;
  --media-slider-chapter-title-gap: 6px;

  /* Slider Value. */
  --media-slider-value-bg: black;
  --media-slider-value-border-radius: 2px;
  --media-slider-value-border: unset;
  --media-slider-value-color: white;
  --media-slider-value-gap: 0px;
  --media-slider-value-padding: 1px 10px;

  /* Menu Theme. */
  --media-menu-color-gray-50: rgb(245 245 245 / 0.1);
  --media-menu-color-gray-100: rgb(245 245 245 / 0.45);
  --media-menu-color-gray-200: rgb(10 10 10 / 0.6);
  --media-menu-color-gray-300: rgb(27 27 27);

  /* Menu Text. */
  --media-menu-text-color: #f5f5f5;
  --media-menu-text-secondary-color: #6b6b6b;

  /* Menu. */
  --media-menu-bg: var(--media-menu-bg, var(--color-gray-400));
  --media-menu-border-radius: 4px;
  --media-menu-border: 1px solid rgb(255 255 255 / 0.1);
  --media-menu-box-shadow: 1px 1px 1px rgb(10 10 10 / 0.5);
  --media-menu-divider: 1px solid var(--color-gray-50);
  --media-menu-font-size: 14px;
  --media-menu-font-weight: 500;
  --media-menu-max-height: 250px;
  --media-menu-min-width: 220px;
  --media-menu-padding: 12px;
  --media-menu-top-bar-bg: rgb(10 10 10 / 0.6);
  --media-menu-arrow-icon-size: 18px;
  --media-menu-icon-rotate-deg: 90deg;

  --media-menu-enter-animation: vds-menu-enter 0.3s ease-out;
  --media-menu-exit-animation: vds-menu-exit 0.2s ease-out;

  --media-menu-scrollbar-track-bg: transparent;
  --media-menu-scrollbar-thumb-bg: var(--color-gray-50);

  --media-sm-menu-landscape-max-height: min(70vh, 400px);
  --media-sm-menu-portrait-max-height: 40vh;

  /* Menu Section. */
  --media-menu-section-bg: var(--color-gray-300);
  --media-menu-section-border: unset;
  --media-menu-section-divider: var(--divider);
  --media-menu-section-header-font-size: 12px;
  --media-menu-section-header-font-weight: 500;
  --media-menu-section-gap: 8px;
  --media-menu-section-border-radius: 2px;

  /* Menu Item. */
  --media-menu-item-bg: transparent;
  --media-menu-item-border-radius: 2px;
  --media-menu-item-border: 0;
  --media-menu-item-height: 40px;
  --media-menu-item-hover-bg: var(--color-gray-50);
  --media-menu-item-icon-size: 18px;
  --media-menu-item-icon-spacing: 6px;
  --media-menu-item-padding: 10px;

  /* Menu Radio. */
  --media-menu-radio-icon-color: var(--text-color);

  /* Menu Checkbox. */
  --media-menu-checkbox-width: 40px;
  --media-menu-checkbox-height: 18px;
  --media-menu-checkbox-bg-active: #1ba13f;
  --media-menu-checkbox-bg: var(--color-gray-100);
  --media-menu-checkbox-handle-bg: #f5f5f5;
  --media-menu-checkbox-handle-border: unset;
  --media-menu-checkbox-handle-diameter: calc(var(--checkbox-height) - 2px);

  /* Menu Slider. */
  --media-menu-slider-height: 32px;
  --media-menu-slider-track-bg: var(--color-gray-50);
  --media-menu-slider-track-fill-bg: var(--color-inverse);

  /* Menu Hint. */
  --media-menu-hint-color: var(--text-secondary-color);
  --media-menu-hint-font-size: 13px;
  --media-menu-hint-font-weight: 400;

  /* Chapters Menu. */
  --media-chapters-divider: var(--divider);
  --media-chapters-duration-bg: unset;
  --media-chapters-duration-border-radius: 2px;
  --media-chapters-focus-padding: 4px;
  --media-chapters-item-active-bg: var(--color-gray-50);
  --media-chapters-item-active-border-left: unset;
  --media-chapters-min-width: var(--media-menu-min-width, 220px);
  --media-chapters-padding: 0;
  --media-chapters-progress-bg: var(--color-inverse);
  --media-chapters-progress-border-radius: 0;
  --media-chapters-progress-height: 4px;
  --media-chapters-start-time-border-radius: 2px;
  --media-chapters-start-time-letter-spacing: 0.4px;
  --media-chapters-start-time-padding: 1px 4px;
  --media-chapters-thumbnail-border: 0;
  --media-chapters-thumbnail-gap: 12px;
  --media-chapters-thumbnail-max-height: 68px;
  --media-chapters-thumbnail-max-width: 120px;
  --media-chapters-thumbnail-min-height: 56px;
  --media-chapters-thumbnail-min-width: 100px;
  --media-chapters-time-font-size: 12px;
  --media-chapters-time-font-weight: 500;
  --media-chapters-time-gap: 6px;
  --media-chapters-with-thumbnails-min-width: 300px;
}
Slots
Section titled Slots
Audio Layout
Section titled Audio Layout
The slots prop can be used to insert or replace content inside the DefaultAudioLayout. You can find all available slot positions below.

tsx
Copy
<DefaultAudioLayout
  slots={{
    beforePlayButton: null,
    // Accepts a `ReactNode`, setting the slot to `null` will remove it.
    playButton: CustomPlayButton,
    afterPlayButton: null,
    // 72 other slots positions...
  }}
/>
Video Layout
Section titled Video Layout
The slots prop can be used to insert or replace content inside the DefaultVideoLayout. You can find all available slot positions below.

tsx
Copy
<DefaultVideoLayout
  slots={{
    beforePlayButton: null,
    // Accepts a `ReactNode`, setting the slot to `null` will remove it.
    playButton: CustomPlayButton,
    afterPlayButton: null,
    // 72 other slots positions...
  }}
/>
Content can be slotted inside specific video layout sizes like so:

tsx
Copy
<DefaultVideoLayout
  slots={{
    smallLayout: {
      playButton: CustomPlayButton,
      // ...
    },
    largeLayout: {
      // ...
    },
  }}
/>
Positions
Section titled Positions
The following slot positions are available for inserting or replacing content:

bufferingIndicator
captionButton
captions
title
chapterTitle
currentTime
endTime
fullscreenButton
liveButton
livePlayButton
muteButton
pipButton
airPlayButton
googleCastButton
playButton
loadButton
seekBackwardButton
seekForwardButton
startDuration
timeSlider
volumeSlider
chaptersMenu
settingsMenu
settingsMenuItemsStart
settingsMenuItemsEnd
playbackMenuItemsStart
playbackMenuItemsEnd
playbackMenuLoop
accessibilityMenuItemsStart
accessibilityMenuItemsEnd
audioMenuItemsStart
audioMenuItemsEnd
captionsMenuItemsStart
captionsMenuItemsEnd
INFO
Any slot position can be prefixed with either before or after to insert content before or after that position. For example, afterCaptionButton will insert content after the caption button.

API Reference Section titled API Reference
DefaultAudioLayout Section titled DefaultAudioLayout
RefAttributes
The audio layout is our production-ready UI that's displayed when the media view type is set to 'audio'. It includes support for audio tracks, slider chapters, captions, live streams and more out of the box.

import { DefaultAudioLayout } from "@vidstack/react/player/layouts/plyr";
<MediaPlayer src="audio.mp3">
  <MediaProvider />
  <DefaultAudioLayout icons={defaultLayoutIcons} />
</MediaPlayer>
Props Section titled Props
Prop	Type	Default
children
Show children description
ReactNode
Show more info
null
icons
Show icons description
DefaultLayoutIcons
undefined
colorScheme
Show colorScheme description
string
Show more info
undefined
download
Show download description
FileDownloadInfo
Show more info
undefined
showTooltipDelay
Show showTooltipDelay description
number
700
showMenuDelay
Show showMenuDelay description
number
0
hideQualityBitrate
Show hideQualityBitrate description
boolean
false
smallLayoutWhen
Show smallLayoutWhen description
boolean
Show more info
`({ width, height }) => width < 576 || height < 380`
thumbnails
Show thumbnails description
ThumbnailSrc
Show more info
undefined
translations
Show translations description
Partial<DefaultLayoutTranslations>
Show more info
undefined
menuContainer
Show menuContainer description
string
Show more info
`document.body`
menuGroup
Show menuGroup description
string
Show more info
undefined
noAudioGain
Show noAudioGain description
boolean
undefined
audioGains
Show audioGains description
object
Show more info
undefined
noModal
Show noModal description
boolean
undefined
noScrubGesture
Show noScrubGesture description
boolean
undefined
sliderChaptersMinWidth
Show sliderChaptersMinWidth description
number
undefined
disableTimeSlider
Show disableTimeSlider description
boolean
undefined
noGestures
Show noGestures description
boolean
undefined
noKeyboardAnimations
Show noKeyboardAnimations description
boolean
undefined
playbackRates
Show playbackRates description
object
Show more info
undefined
seekStep
Show seekStep description
number
undefined
slots
Show slots description
DefaultAudioLayoutSlots
undefined
asChild
Show asChild description
boolean
false
Data Attributes Section titled Data Attributes
/* Example. */
.component[data-foo] {}
Name	Description
data-match
Whether this layout is being used.
data-sm
The small layout is active
data-lg
The large layout is active.
data-size
The active layout size (sm or lg).
DefaultVideoLayout Section titled DefaultVideoLayout
RefAttributes
The video layout is our production-ready UI that's displayed when the media view type is set to 'video'. It includes support for picture-in-picture, fullscreen, slider chapters, slider previews, captions, audio/quality settings, live streams, and more out of the box.

import { DefaultVideoLayout } from "@vidstack/react/player/layouts/plyr";
<MediaPlayer src="video.mp4">
  <MediaProvider />
  <DefaultVideoLayout thumbnails="/thumbnails.vtt" icons={defaultLayoutIcons} />
</MediaPlayer>
Props Section titled Props
Prop	Type	Default
children
Show children description
ReactNode
Show more info
null
icons
Show icons description
DefaultLayoutIcons
undefined
colorScheme
Show colorScheme description
string
Show more info
undefined
download
Show download description
FileDownloadInfo
Show more info
undefined
showTooltipDelay
Show showTooltipDelay description
number
700
showMenuDelay
Show showMenuDelay description
number
0
hideQualityBitrate
Show hideQualityBitrate description
boolean
false
smallLayoutWhen
Show smallLayoutWhen description
boolean
Show more info
`({ width, height }) => width < 576 || height < 380`
thumbnails
Show thumbnails description
ThumbnailSrc
Show more info
undefined
translations
Show translations description
Partial<DefaultLayoutTranslations>
Show more info
undefined
menuContainer
Show menuContainer description
string
Show more info
`document.body`
menuGroup
Show menuGroup description
string
Show more info
undefined
noAudioGain
Show noAudioGain description
boolean
undefined
audioGains
Show audioGains description
object
Show more info
undefined
noModal
Show noModal description
boolean
undefined
noScrubGesture
Show noScrubGesture description
boolean
undefined
sliderChaptersMinWidth
Show sliderChaptersMinWidth description
number
undefined
disableTimeSlider
Show disableTimeSlider description
boolean
undefined
noGestures
Show noGestures description
boolean
undefined
noKeyboardAnimations
Show noKeyboardAnimations description
boolean
undefined
playbackRates
Show playbackRates description
object
Show more info
undefined
seekStep
Show seekStep description
number
undefined
slots
Show slots description
DefaultVideoLayoutSlots
undefined
asChild
Show asChild description
boolean
false
Data Attributes Section titled Data Attributes
/* Example. */
.component[data-foo] {}
Name	Description
data-match
Whether this layout is being used.
data-sm
The small layout is active
data-lg
The large layout is active.
data-size
The active layout size (sm or lg).

### Core -> Player
Core

Player
This is the top-most component in the library used to group media elements and control the flow of media state.

See the following sections for more information:

The loading guide covers how to handle loading various media resources.
The events guide covers the basics on how to listen and track updates.
The state management guide covers how to read, update, and subscribe to state changes.
The API pages cover how to interact with various player APIs and hooks.
API Reference Section titled API Reference
HTMLAttributes
Ref<MediaPlayerInstance>
All media components exist inside the <MediaPlayer> component. This component's main responsibilities are to manage media state updates, dispatch media events, handle media requests, and expose media state through HTML attributes and CSS properties for styling purposes.

import { MediaPlayer, type MediaPlayerProps } from "@vidstack/react";
<MediaPlayer src="...">
  <MediaProvider />
</MediaPlayer>
Props Section titled Props
Prop	Type	Default
src
Show src description
PlayerSrc
Show more info
undefined
aspectRatio
string
undefined
asChild
Show asChild description
boolean
false
children
Show children description
ReactNode
Show more info
null
artist
Show artist description
string
''
artwork
Show artwork description
MediaImage[]
null
autoPlay
Show autoPlay description
boolean
false
autoplay
boolean
false
clipEndTime
Show clipEndTime description
number
0
clipStartTime
Show clipStartTime description
number
0
controls
Show controls description
boolean
false
controlsDelay
Show controlsDelay description
number
2000
crossOrigin
Show crossOrigin description
mixed
Show more info
null
crossorigin
mixed
Show more info
null
currentTime
Show currentTime description
number
0
duration
Show duration description
number
-1
fullscreenOrientation
Show fullscreenOrientation description
string
Show more info
'landscape'
googleCast
Show googleCast description
GoogleCastOptions
{}
hideControlsOnMouseLeave
Show hideControlsOnMouseLeave description
boolean
false
keyDisabled
Show keyDisabled description
boolean
'false'
keyShortcuts
Show keyShortcuts description
MediaKeyShortcuts
MEDIA_KEY_SHORTCUTS
keyTarget
Show keyTarget description
MediaKeyTarget
Show more info
`player`
liveEdgeTolerance
Show liveEdgeTolerance description
number
10
load
Show load description
MediaLoadingStrategy
Show more info
'visible'
logLevel
Show logLevel description
LogLevel
Show more info
__DEV__ ? 'warn' : 'silent'
loop
Show loop description
boolean
false
minLiveDVRWindow
Show minLiveDVRWindow description
number
60
muted
Show muted description
boolean
false
paused
Show paused description
boolean
true
playbackRate
Show playbackRate description
number
1
playsInline
Show playsInline description
boolean
false
playsinline
boolean
false
poster
Show poster description
string
''
posterLoad
Show posterLoad description
MediaPosterLoadingStrategy
Show more info
'visible'
preferNativeHLS
Show preferNativeHLS description
boolean
false
preload
Show preload description
string
Show more info
'metadata'
storage
Show storage description
string
Show more info
null
streamType
Show streamType description
MediaStreamType
Show more info
'unknown'
title
Show title description
string
''
viewType
Show viewType description
MediaViewType
Show more info
'unknown'
volume
Show volume description
number
1
State Section titled State
import { MediaPlayer, MediaPlayerInstance } from "@vidstack/react"

const ref = useRef<MediaPlayerInstance>(null),
  { /* state props */ } = useStore(MediaPlayerInstance, ref);

<MediaPlayer ref={ref}>
Prop	Type	Default
artist
Show artist description
string
''
artwork
Show artwork description
object
Show more info
null
audioGain
Show audioGain description
number
null
audioTrack
Show audioTrack description
object
Show more info
null
audioTracks
Show audioTracks description
object
Show more info
[]
autoPlay
Show autoPlay description
boolean
false
autoPlayError
Show autoPlayError description
object
Show more info
null
autoQuality
Show autoQuality description
boolean
false
buffered
Show buffered description
object
Show more info
new TimeRange()
bufferedEnd
Show bufferedEnd description
number
undefined
bufferedStart
Show bufferedStart description
number
undefined
bufferedWindow
Show bufferedWindow description
number
undefined
canAirPlay
Show canAirPlay description
boolean
false
canFullscreen
Show canFullscreen description
boolean
false
canGoogleCast
Show canGoogleCast description
boolean
false
canLoad
Show canLoad description
boolean
false
canLoadPoster
Show canLoadPoster description
boolean
false
canOrientScreen
Show canOrientScreen description
boolean
canOrientScreen()
canPictureInPicture
Show canPictureInPicture description
boolean
false
canPlay
Show canPlay description
boolean
false
canSeek
Show canSeek description
boolean
undefined
canSetAudioGain
Show canSetAudioGain description
boolean
false
canSetPlaybackRate
Show canSetPlaybackRate description
boolean
true
canSetQuality
Show canSetQuality description
boolean
true
canSetVolume
Show canSetVolume description
boolean
false
clipEndTime
Show clipEndTime description
number
0
clipStartTime
Show clipStartTime description
number
0
controls
Show controls description
boolean
false
controlsHidden
Show controlsHidden description
boolean
undefined
controlsVisible
Show controlsVisible description
boolean
false
crossOrigin
Show crossOrigin description
string
Show more info
null
currentSrc
Show currentSrc description
object
Show more info
undefined
currentTime
Show currentTime description
number
undefined
duration
Show duration description
number
undefined
ended
Show ended description
boolean
false
error
Show error description
object
Show more info
null
fullscreen
Show fullscreen description
boolean
false
hasCaptions
Show hasCaptions description
boolean
undefined
height
Show height description
number
0
iOSControls
Show iOSControls description
boolean
undefined
inferredLiveDVRWindow
number
0
isAirPlayConnected
Show isAirPlayConnected description
boolean
undefined
isGoogleCastConnected
Show isGoogleCastConnected description
boolean
undefined
isLiveDVR
Show isLiveDVR description
boolean
undefined
lastKeyboardAction
Show lastKeyboardAction description
object
Show more info
null
live
Show live description
boolean
undefined
liveDVRWindow
Show liveDVRWindow description
number
undefined
liveEdge
Show liveEdge description
boolean
undefined
liveEdgeStart
Show liveEdgeStart description
number
undefined
liveEdgeTolerance
Show liveEdgeTolerance description
number
10
liveEdgeWindow
Show liveEdgeWindow description
number
undefined
logLevel
Show logLevel description
string
Show more info
__DEV__ ? 'warn' : 'silent'
loop
Show loop description
boolean
undefined
mediaHeight
Show mediaHeight description
number
0
mediaType
Show mediaType description
string
Show more info
'unknown'
mediaWidth
Show mediaWidth description
number
0
minLiveDVRWindow
Show minLiveDVRWindow description
number
60
muted
Show muted description
boolean
false
nativeControls
Show nativeControls description
boolean
undefined
orientation
Show orientation description
string
Show more info
'landscape'
paused
Show paused description
boolean
true
pictureInPicture
Show pictureInPicture description
boolean
false
playbackRate
Show playbackRate description
number
1
played
Show played description
object
Show more info
new TimeRange()
playing
Show playing description
boolean
false
playsInline
Show playsInline description
boolean
false
pointer
Show pointer description
string
Show more info
'fine'
poster
Show poster description
string
undefined
preload
Show preload description
string
Show more info
'metadata'
qualities
Show qualities description
object
Show more info
[]
quality
Show quality description
object
Show more info
null
remotePlaybackInfo
Show remotePlaybackInfo description
object
Show more info
null
remotePlaybackLoader
Show remotePlaybackLoader description
object
Show more info
null
remotePlaybackState
Show remotePlaybackState description
string
Show more info
'disconnected'
remotePlaybackType
Show remotePlaybackType description
string
Show more info
'none'
seekable
Show seekable description
object
Show more info
new TimeRange()
seekableEnd
Show seekableEnd description
number
undefined
seekableStart
Show seekableStart description
number
undefined
seekableWindow
Show seekableWindow description
number
undefined
seeking
Show seeking description
boolean
false
source
Show source description
object
Show more info
{ src: '', type: '' }
sources
Show sources description
object
Show more info
[]
started
Show started description
boolean
false
streamType
Show streamType description
string
Show more info
undefined
textTrack
Show textTrack description
object
Show more info
null
textTracks
Show textTracks description
object
Show more info
[]
title
Show title description
string
undefined
userBehindLiveEdge
Show userBehindLiveEdge description
boolean
false
viewType
Show viewType description
string
Show more info
undefined
volume
Show volume description
number
1
waiting
Show waiting description
boolean
false
width
Show width description
number
0
Callbacks Section titled Callbacks
Callback	Type
onAbort
Show onAbort description
function
Show more info
onAudioGainChange
Show onAudioGainChange description
function
Show more info
onAudioTrackChange
Show onAudioTrackChange description
function
Show more info
onAudioTracksChange
Show onAudioTracksChange description
function
Show more info
onAutoPlay
Show onAutoPlay description
function
Show more info
onAutoPlayChange
Show onAutoPlayChange description
function
Show more info
onAutoPlayFail
Show onAutoPlayFail description
function
Show more info
onCanLoad
Show onCanLoad description
function
Show more info
onCanLoadPoster
Show onCanLoadPoster description
function
Show more info
onCanPlay
Show onCanPlay description
function
Show more info
onCanPlayThrough
Show onCanPlayThrough description
function
Show more info
onControlsChange
Show onControlsChange description
function
Show more info
onDestroy
Show onDestroy description
function
Show more info
onDurationChange
Show onDurationChange description
function
Show more info
onEmptied
Show onEmptied description
function
Show more info
onEnd
Show onEnd description
function
Show more info
onEnded
Show onEnded description
function
Show more info
onError
Show onError description
function
Show more info
onFullscreenChange
Show onFullscreenChange description
function
Show more info
onFullscreenError
Show onFullscreenError description
function
Show more info
onLiveChange
Show onLiveChange description
function
Show more info
onLiveEdgeChange
Show onLiveEdgeChange description
function
Show more info
onLoadStart
Show onLoadStart description
function
Show more info
onLoadedData
Show onLoadedData description
function
Show more info
onLoadedMetadata
Show onLoadedMetadata description
function
Show more info
onLoopChange
Show onLoopChange description
function
Show more info
onMediaAirplayRequest
Show onMediaAirplayRequest description
function
Show more info
onMediaAudioGainChangeRequest
Show onMediaAudioGainChangeRequest description
function
Show more info
onMediaAudioTrackChangeRequest
Show onMediaAudioTrackChangeRequest description
function
Show more info
onMediaClipEndChangeRequest
Show onMediaClipEndChangeRequest description
function
Show more info
onMediaClipStartChangeRequest
Show onMediaClipStartChangeRequest description
function
Show more info
onMediaDurationChangeRequest
Show onMediaDurationChangeRequest description
function
Show more info
onMediaEnterFullscreenRequest
Show onMediaEnterFullscreenRequest description
function
Show more info
onMediaEnterPipRequest
Show onMediaEnterPipRequest description
function
Show more info
onMediaExitFullscreenRequest
Show onMediaExitFullscreenRequest description
function
Show more info
onMediaExitPipRequest
Show onMediaExitPipRequest description
function
Show more info
onMediaGoogleCastRequest
Show onMediaGoogleCastRequest description
function
Show more info
onMediaLiveEdgeRequest
Show onMediaLiveEdgeRequest description
function
Show more info
onMediaMuteRequest
Show onMediaMuteRequest description
function
Show more info
onMediaOrientationLockRequest
Show onMediaOrientationLockRequest description
function
Show more info
onMediaOrientationUnlockRequest
Show onMediaOrientationUnlockRequest description
function
Show more info
onMediaPauseControlsRequest
Show onMediaPauseControlsRequest description
function
Show more info
onMediaPauseRequest
Show onMediaPauseRequest description
function
Show more info
onMediaPlayRequest
Show onMediaPlayRequest description
function
Show more info
onMediaPlayerConnect
Show onMediaPlayerConnect description
function
Show more info
onMediaPosterStartLoading
Show onMediaPosterStartLoading description
function
Show more info
onMediaQualityChangeRequest
Show onMediaQualityChangeRequest description
function
Show more info
onMediaRateChangeRequest
Show onMediaRateChangeRequest description
function
Show more info
onMediaResumeControlsRequest
Show onMediaResumeControlsRequest description
function
Show more info
onMediaSeekRequest
Show onMediaSeekRequest description
function
Show more info
onMediaSeekingRequest
Show onMediaSeekingRequest description
function
Show more info
onMediaStartLoading
Show onMediaStartLoading description
function
Show more info
onMediaTextTrackChangeRequest
Show onMediaTextTrackChangeRequest description
function
Show more info
onMediaTypeChange
Show onMediaTypeChange description
function
Show more info
onMediaUnmuteRequest
Show onMediaUnmuteRequest description
function
Show more info
onMediaUserLoopChangeRequest
Show onMediaUserLoopChangeRequest description
function
Show more info
onMediaVolumeChangeRequest
Show onMediaVolumeChangeRequest description
function
Show more info
onOrientationChange
Show onOrientationChange description
function
Show more info
onPause
Show onPause description
function
Show more info
onPictureInPictureChange
Show onPictureInPictureChange description
function
Show more info
onPictureInPictureError
Show onPictureInPictureError description
function
Show more info
onPlay
Show onPlay description
function
Show more info
onPlayFail
Show onPlayFail description
function
Show more info
onPlaying
Show onPlaying description
function
Show more info
onPlaysInlineChange
Show onPlaysInlineChange description
function
Show more info
onPosterChange
Show onPosterChange description
function
Show more info
onProgress
Show onProgress description
function
Show more info
onProviderChange
Show onProviderChange description
function
Show more info
onProviderLoaderChange
Show onProviderLoaderChange description
function
Show more info
onProviderSetup
Show onProviderSetup description
function
Show more info
onQualitiesChange
Show onQualitiesChange description
function
Show more info
onQualityChange
Show onQualityChange description
function
Show more info
onRateChange
Show onRateChange description
function
Show more info
onRemotePlaybackChange
Show onRemotePlaybackChange description
function
Show more info
onReplay
Show onReplay description
function
Show more info
onSeeked
Show onSeeked description
function
Show more info
onSeeking
Show onSeeking description
function
Show more info
onSourceChange
Show onSourceChange description
function
Show more info
onSourcesChange
Show onSourcesChange description
function
Show more info
onStalled
Show onStalled description
function
Show more info
onStarted
Show onStarted description
function
Show more info
onStreamTypeChange
Show onStreamTypeChange description
function
Show more info
onSuspend
Show onSuspend description
function
Show more info
onTextTrackChange
Show onTextTrackChange description
function
Show more info
onTextTracksChange
Show onTextTracksChange description
function
Show more info
onTimeChange
Show onTimeChange description
function
Show more info
onTimeUpdate
Show onTimeUpdate description
function
Show more info
onTitleChange
Show onTitleChange description
function
Show more info
onVdsLog
function
Show more info
onVideoPresentationChange
Show onVideoPresentationChange description
function
Show more info
onViewTypeChange
Show onViewTypeChange description
function
Show more info
onVolumeChange
Show onVolumeChange description
function
Show more info
onWaiting
Show onWaiting description
function
Show more info
Instance Section titled Instance
import { MediaPlayer, type MediaPlayerInstance } from "@vidstack/react"

const ref = useRef<MediaPlayerInstance>(null);

useEffect(() => { /* Use props/methods here. */ }, [])

<MediaPlayer ref={ref}>
Prop	Type
audioTracks
Show audioTracks description
AudioTrackList
canPlayQueue
RequestQueue
orientation
Show orientation description
ScreenOrientationController
provider
Show provider description
AnyMediaProvider
Show more info
qualities
Show qualities description
VideoQualityList
remoteControl
MediaRemoteControl
state
Show state description
MediaPlayerState
textRenderers
Show textRenderers description
TextRenderers
textTracks
Show textTracks description
TextTrackList
enterFullscreen
Show enterFullscreen description
method
Show more info
enterPictureInPicture
Show enterPictureInPicture description
method
Show more info
exitFullscreen
Show exitFullscreen description
method
Show more info
exitPictureInPicture
Show exitPictureInPicture description
method
Show more info
pause
Show pause description
method
Show more info
play
Show play description
method
Show more info
requestAirPlay
Show requestAirPlay description
method
Show more info
requestGoogleCast
Show requestGoogleCast description
method
Show more info
seekToLiveEdge
Show seekToLiveEdge description
method
Show more info
setAudioGain
Show setAudioGain description
method
Show more info
startLoading
Show startLoading description
method
Show more info
startLoadingPoster
Show startLoadingPoster description
method
Show more info
subscribe
Show subscribe description
method
Show more info
Data Attributes Section titled Data Attributes
/* Example. */
.component[data-foo] {}
Name	Description
data-airplay
Whether AirPlay is connected.
data-autoplay
Autoplay has successfully started.
data-autoplay-error
Autoplay has failed to start.
data-buffering
Media is not ready for playback or waiting for more data.
data-can-airplay
Whether AirPlay is available.
data-can-fullscreen
Fullscreen mode is available.
data-can-google-cast
Whether Google Cast is available.
data-can-load
Media can now begin loading.
data-can-pip
Picture-in-Picture mode is available.
data-can-play
Media is ready for playback.
data-can-seek
Seeking operations are permitted.
data-captions
Captions are available and visible.
data-controls
Controls are visible.
data-ended
Playback has ended.
data-error
Issue with media loading/playback.
data-fullscreen
Fullscreen mode is active.
data-google-cast
Whether Google Cast is connected.
data-ios-controls
iOS controls are visible.
data-load
Specified load strategy.
data-live
Media is live stream.
data-live-edge
Playback is at the live edge.
data-loop
Media is set to replay on end.
data-media-type
Current media type (audio/video).
data-muted
Whether volume is muted (0).
data-orientation
Current screen orientation (landscape/portrait).
data-paused
Whether playback is paused.
data-pip
Picture-in-picture mode is active.
data-playing
Playback is active.
data-playsinline
Media should play inline by default (iOS).
data-pointer
The user's pointer device type (coarse/fine).
data-preview
The user is interacting with the time slider.
data-remote-type
The remote playback type (airplay/google-cast).
data-remote-state
The remote playback state (connecting/connected/disconnected).
data-seeking
User is seeking to a new playback position.
data-started
Media playback has started.
data-stream-type
Current stream type.
data-view-type
Current view type (audio/video).
data-waiting
Media is waiting for more data to resume playback.
data-focus
Whether player is being keyboard focused.
data-hocus
Whether player is being keyboard focused or hovered over.

### Core -> Provider
This component is used as a render target for the current provider.

The media provider component gives you complete control of where the provider will be rendered in the DOM. For example, the VideoProvider or HLSProvider will render the HTML <video> element inside of it, and the AudioProvider will render the <audio> element.

In a typical video player setup, you’ll place the provider directly in the player and overlay some UI (e.g., controls) over it like so:

<MediaPlayer>
  {/* Can detach from DOM and move around to create floating, popup, and mini players. */}
  <MediaProvider>
    {/* Content here that should be rendered inside outlet (e.g., poster). */}
  </MediaProvider>

  {/* Controls and other media UI can be outside and placed on top. */}
</MediaPlayer>
API Reference Section titled API Reference
HTMLAttributes
Ref<MediaProviderInstance>
Renders the current provider at this component location.

import { MediaProvider, type MediaProviderProps } from "@vidstack/react";
<MediaPlayer src="...">
  <MediaProvider />
</MediaPlayer>
Props Section titled Props
Prop	Type	Default
loaders
object
Show more info
undefined
iframeProps
IframeHTMLAttributes<HTMLIFrameElement>
undefined
mediaProps
HTMLAttributes<HTMLMediaElement>
undefined
children
Show children description
ReactNode
Show more info
null
State Section titled State
import { MediaProvider, MediaProviderInstance } from "@vidstack/react"

const ref = useRef<MediaProviderInstance>(null),
  { /* state props */ } = useStore(MediaProviderInstance, ref);

<MediaProvider ref={ref}>
Prop	Type	Default
loader
object
Show more info
null
Instance Section titled Instance
import { MediaProvider, type MediaProviderInstance } from "@vidstack/react"

const ref = useRef<MediaProviderInstance>(null);

useEffect(() => { /* Use props/methods here. */ }, [])

<MediaProvider ref={ref}>
Prop	Type
state
Show state description
MediaProviderState
load
method
Show more info
subscribe
Show subscribe description
method

### Core Concepts

Loading
A guide on how to handle loading various media resources.

Sizing
Section titled Sizing
By default, the browser will use the intrinsic size of the loaded media to set the dimensions of the provider. As media loads over the network, the element will jump from the default size to the intrinsic media size, triggering a layout shift which is a poor user experience indicator for both your users and search engines (i.e., Google).

Aspect Ratio

To avoid a layout shift, we recommend setting the aspect ratio like so:

tsx
<MediaPlayer aspectRatio="16/9">
Ideally the ratio set should match the ratio of the media content itself (i.e., intrinsic aspect ratio) otherwise you’ll end up with a letterbox template (empty black bars on the left/right of the media).

Specify Dimensions

If you’d like to be more specific for any reason, you can specify the width and height of the player simply using CSS like so:

.player {
  width: 600px;
  height: 338px;
  aspect-ratio: unset;
}
Load Strategies
Section titled Load Strategies
A loading strategy specifies when media or the poster image should begin loading. Loading media too early can effectively slow down your entire application, so choose wisely.

The following media loading strategies are available:

eager: Load media immediately - use when media needs to be interactive as soon as possible.
idle: Load media once the page has loaded and the requestIdleCallback is fired - use when media is lower priority and doesn’t need to be interactive immediately.
visible: Load media once it has entered the visual viewport - use when media is below the fold and you prefer delaying loading until it’s required.
play: Load the provider and media on play - use when you want to delay loading until interaction.
custom: Load media when the startLoading()/startLoadingPoster() method is called or the media-start-loading/media-start-loading-poster event is dispatched - use when you need fine control of when media should begin loading.
<MediaPlayer load="visible" posterLoad="visible">
INFO
The poster load strategy specifies when the poster should begin loading. Poster loading is separate from media loading so you can display an image before media is ready for playback. This generally works well in combination with load="play" to create thumbnails.

Custom Strategy
Section titled Custom Strategy
A custom load strategy lets you control when media or the poster image should begin loading:

import { useEffect, useRef } from 'react';

import { MediaPlayer, type MediaPlayerInstance } from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  useEffect(() => {
    // Call whenever you like - also available on `useMediaRemote`.
    player.current!.startLoading();

    // Call when poster should start loading.
    player.current!.startLoadingPoster();
  }, []);

  return (
    <MediaPlayer load="custom" posterLoad="custom" ref={player}>
      {/* ... */}
    </MediaPlayer>
  );
}
View Type
Section titled View Type
The view type suggests what type of media layout will be displayed. It can be either audio or video. This is mostly to inform layouts, whether your own or the defaults, how to appropriately display the controls and general UI. By default, the view type is inferred from the provider and media type. You can specify the desired type like so:

<MediaPlayer viewType="audio">
Stream Type
Section titled Stream Type
The stream type refers to the mode in which content is delivered through the video player. The player will use the type to determine how to manage state/internals such as duration updates, seeking, and how to appropriately present UI components and layouts. The stream type can be one of the following values:

on-demand: Video on Demand (VOD) content is pre-recorded and can be accessed and played at any time. VOD streams allow viewers to control playback, pause, rewind, and fast forward.
live: Live streaming delivers real-time content as it happens. Viewers join the stream and watch the content as it’s being broadcast, with limited control over playback.
live:dvr: Live DVR (Live Digital Video Recording) combines the features of both live and VOD. Viewers can join a live stream and simultaneously pause, rewind, and fast forward, offering more flexibility in watching live events.
ll-live: A live streaming mode optimized for reduced latency, providing a near-real-time viewing experience with minimal delay between the live event and the viewer.
ll-live:dvr: Similar to low-latency live, this mode enables viewers to experience live content with minimal delay while enjoying the benefits of DVR features (same as live:dvr).
If the value is not set, it will be inferred by the player which can be less accurate (e.g., at identifying DVR support). When possible, prefer specifying it like so:

<MediaPlayer streamType="live">
Duration
Section titled Duration
By default, the duration is inferred from the provider and media. It’s always best to provide the duration when known to avoid any inaccuracies such as rounding errors, and to ensure UI is set to the correct state without waiting on metadata to load. You can specify the exact duration like so:

// 5 minutes.
<MediaPlayer duration={300}>
Clipping
Section titled Clipping
Clipping allows shortening the media by specifying the time at which playback should start and end.

<MediaPlayer clipStartTime={10} clipEndTime={30}>
You can set a clip start time or just an end time, both are not required.
The media duration and chapter durations will be updated to match the clipped length.
Any media resources such as text tracks and thumbnails should use the full duration.
Seeking to a new time is based on the clipped duration. For example, if a 1 minute video is clipped to 30 seconds, seeking to 30s will be the end of the video.
Media URI Fragments are set internally to efficiently load audio and video files between the clipped start and end times (e.g., /video.mp4#t=30,60).
Media Session
Section titled Media Session
The Media Session API is automatically set using the provided title, artist, and artwork (poster is used as fallback) player properties.

Storage
Section titled Storage
Storage enables saving player and media settings so that the user can resume where they left off. This includes saving and initializing on load settings such as language, volume, muted, captions visibility, and playback time.

Local Storage
Section titled Local Storage
Local Storage enables saving data locally on the user’s browser. This is a simple and fast option for remembering player settings, but it won’t persist across domains, devices, or browsers.

Provide a storage key prefix for turning local storage on like so:

<MediaPlayer storage="storage-key">
Extending Local Storage

Optionally, you can extend and customize local storage behaviour like so:

import { LocalMediaStorage } from '@vidstack/react';

class CustomLocalMediaStorage extends LocalMediaStorage {
  // override methods here...
}

// Provide storage to player.
<MediaPlayer storage={CustomLocalMediaStorage}>
Remote Storage
Section titled Remote Storage
Remote Storage enables asynchronously saving and loading data from anywhere. This is great as settings willl persist across user sessions even if the domain, device, or browser changes. Generally, you will save player/media settings to a remote database based on the currently authenticated user.

Implement the MediaStorage interface and provide it to the player like so:

ts
Copy
import { type MediaStorage } from '@vidstack/react';

class MediaDatabaseStorage implements MediaStorage {
  async getVolume() {}
  async setVolume(volume: number) {}

  async getMuted() {}
  async setMuted(isMuted: boolean) {}

  async getTime() {}
  async setTime(time: number) {}

  async getLang() {}
  async setLang(lang: string | null) {}

  async getCaptions() {}
  async setCaptions(isOn: boolean) {}

  async onLoad() {}

  onChange(src, mediaId, playerId) {}

  onDestroy() {}
}
const storage = useMemo(() => new MediaDatabaseStorage(), []);

<MediaPlayer storage={storage}>
Sources
Section titled Sources
The player can accept one or more media sources which can be a string URL of the media resource to load, or any of the following objects: MediaStream, MediaSource, Blob, or File.

Single Source

<MediaPlayer src="https://files.vidstack.io/sprite-fight/720p.mp4" />
Multiple Source Types

The list of supported media formats varies from one browser to the other. You should either provide your source in a single format that all relevant browsers support, or provide multiple sources in enough different formats that all the browsers you need to support are covered.

<MediaPlayer
  src={[
    // Audio
    { src: 'https://files.vidstack.io/agent-327/audio.mp3', type: 'audio/mpeg' },
    { src: 'https://files.vidstack.io/agent-327/audio.ogg', type: 'audio/ogg' },
    // Video
    { src: 'https://files.vidstack.io/agent-327/720p.ogv', type: 'video/ogg' },
    { src: 'https://files.vidstack.io/agent-327/720p.avi', type: 'video/avi' },
    { src: 'https://files.vidstack.io/agent-327/720p.mp4', type: 'video/mp4' },
  ]}
/>
Source Objects
Section titled Source Objects
The player accepts both audio and video source objects. This includes MediaStream, MediaSource, Blob, and File.

import { useEffect, useState } from 'react';

import { MediaPlayer, MediaProvider, type MediaSrc } from '@vidstack/react';

function Player() {
  const [src, setSrc] = useState<MediaSrc>();

  useEffect(() => {
    async function getMediaStream() {
      // Example 1: Audio
      const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setSrc({ src: audioStream, type: 'audio/object' });

      // Example 2: Video
      const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
      setSrc({ src: videoStream, type: 'video/object' });
    }

    getMediaStream();
  }, []);

  return (
    <MediaPlayer src={src}>
      <MediaProvider />
    </MediaPlayer>
  );
}
Changing Source
Section titled Changing Source
The player supports changing the source dynamically. Simply update the src property when you want to load new media. You can also set it to an empty string "" to unload media.

import { useState } from 'react';

import { MediaPlayer, type MediaSrc } from '@vidstack/react';

const sources = ['/video-a.mp4', '/video-b.mp4', './video-c.mp4'];

function Player() {
  const [src, setSrc] = useState(0);

  function prevVideo() {
    setSrc((n) => Math.max(0, n - 1));
  }

  function nextVideo() {
    setSrc((n) => Math.min(sources.length - 1, n + 1));
  }

  return (
    <MediaPlayer src={sources[src]}>
      {/* ... */}
      {/* Playlist controls */}
      <button onClick={prevVideo}>Previous Video</button>
      <button onClick={nextVideo}>Next Video</button>
    </MediaPlayer>
  );
}
Source Types
Section titled Source Types
The player source selection process relies on file extensions, object types, and type hints to determine which provider to load and how to play a given source. The following is a table of supported media file extensions and types for each provider:

Media	Extensions	Types
Audio	m4a, m4b, mp4a, mpga, mp2, mp2a, mp3, m2a, m3a, wav, weba, aac, oga, spx	audio/mpeg, audio/ogg, audio/3gp, audio/mp4, audio/webm, audio/flac, audio/object
Video	mp4, ogg, ogv, webm, mov, m4v	video/mp4, video/webm, video/3gp, video/ogg, video/avi, video/mpeg
HLS	m3u8	application/vnd.apple.mpegurl, audio/mpegurl, audio/x-mpegurl, application/x-mpegurl, video/x-mpegurl, video/mpegurl, application/mpegurl
DASH	mpd	application/dash+xml
The following are valid as they have a file extension (e.g, video.mp4) or type hint (e.g., video/mp4):

src="https://example.com/video.mp4"
src="https://example.com/hls.m3u8"
src="https://example.com/dash.mpd"
src = { src: "https://example.com/video", type: "video/mp4" }
src = { src: "https://example.com/hls", type: "application/x-mpegurl" }
src = { src: "https://example.com/dash", type: "application/dash+xml" }
The following are invalid as they are missing a file extension and type hint:

src="https://example.com/video"
src="https://example.com/hls"
src="https://example.com/dash"
Source Sizes
Section titled Source Sizes
You can provide video qualities/resolutions using multiple video files with different sizes (e.g, 1080p, 720p, 480p) like so:

<MediaPlayer
  src={[
    {
      src: 'https://files.vidstack.io/sprite-fight/1080p.mp4',
      type: 'video/mp4',
      width: 1920,
      height: 1080,
    },
    {
      src: 'https://files.vidstack.io/sprite-fight/720p.mp4',
      type: 'video/mp4',
      width: 1280,
      height: 720,
    },
    {
      src: 'https://files.vidstack.io/sprite-fight/480p.mp4',
      type: 'video/mp4',
      width: 853,
      height: 480,
    },
  ]}
/>
NOTE
We strongly recommend using adaptive streaming protocols such as HLS over providing multiple static media files, see the Video Qualities section for more information.

Supported Codecs
Section titled Supported Codecs
Vidstack Player relies on the native browser runtime to handle media playback, hence it’s important you review what containers and codecs are supported by them. This also applies to libraries like hls.js and dash.js which we use for HLS/DASH playback in browsers that don’t support it natively.

While there are a vast number of media container formats, the ones listed below are the ones you are most likely to encounter. Some support only audio while others support both audio and video. The most commonly used containers for media on the web are probably MPEG-4 (MP4), Web Media File (WEBM), and MPEG Audio Layer III (MP3).

It’s important that both the media container and codecs are supported by the native runtime. Please review the following links for what’s supported and where:

Media Containers
Audio Codecs
Video Codecs
Providers
Section titled Providers
Providers are auto-selected during the source selection process and dynamically loaded via a provider loader (e.g., VideoProviderLoader). The following providers are supported at this time:

Audio
Video
HLS
DASH
YouTube
Vimeo
Remotion
Google Cast
INFO
See source types for how to ensure the correct media provider is loaded.

Provider Events
Section titled Provider Events
The following events will fire as providers change or setup:

import {
  isHLSProvider,
  MediaPlayer,
  MediaProvider,
  type MediaProviderAdapter,
} from '@vidstack/react';

function Player() {
  // This is where you should configure providers.
  function onProviderChange(provider: MediaProviderAdapter | null) {
    if (isHLSProvider(provider)) {
      provider.config = {};
      provider.onInstance((hls) => {
        // ...
      });
    }
  }

  // Provider is rendered, attached event listeners, and ready to load source.
  function onProviderSetup(provider: MediaProviderAdapter) {
    if (isHLSProvider(provider)) {
      // ...
    }
  }

  return (
    <MediaPlayer onProviderChange={onProviderChange} onProviderSetup={onProviderSetup}>
      <MediaProvider />
    </MediaPlayer>
  );
}
Provider Types
Section titled Provider Types
The following utilities can be useful for narrowing the type of a media provider:

import {
  isAudioProvider,
  isDASHProvider,
  isGoogleCastProvider,
  isHLSProvider,
  isVideoProvider,
  isVimeoProvider,
  isYouTubeProvider,
  MediaPlayer,
  MediaProvider,
  type AudioProvider,
  type DASHProvider,
  type GoogleCastProvider,
  type HLSProvider,
  type MediaProviderAdapter,
  type VideoProvider,
  type VimeoProvider,
  type YouTubeProvider,
} from '@vidstack/react';
import { isRemotionProvider, type RemotionProvider } from '@vidstack/react/player/remotion';

function Player() {
  function onProviderChange(provider: MediaProviderAdapter | null) {
    if (isAudioProvider(provider)) {
      const audioElement = provider.audio;
    }

    if (isVideoProvider(provider)) {
      const videoElement = provider.video;
    }

    if (isHLSProvider(provider)) {
      provider.config = { lowLatencyMode: true };
      provider.onInstance((hls) => {
        // ...
      });
    }

    if (isDASHProvider(provider)) {
      provider.config = {};
      provider.onInstance((dash) => {
        // ...
      });
    }

    if (isYouTubeProvider(provider)) {
      provider.cookies = true;
      // ...
    }

    if (isVimeoProvider(provider)) {
      provider.cookies = true;
      // ...
    }

    if (isRemotionProvider(provider)) {
      // ...
    }

    if (isGoogleCastProvider(provider)) {
      // ...
    }
  }

  return (
    <MediaPlayer onProviderChange={onProviderChange}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
Audio Tracks
Section titled Audio Tracks
Audio tracks are loaded from your HLS playlist. You can not manually add audio tracks to the player at this time. See the Audio Tracks API guide for how to interact with audio track programmatically.

Text Tracks
Section titled Text Tracks
Text tracks allow you to provide text-based information or content associated with video or audio. These text tracks can be used to enhance the accessibility and user experience of media content in various ways. You can provide multiple text tracks dynamically like so:

import { MediaPlayer, MediaProvider, Track } from '@vidstack/react';

<MediaPlayer>
  <MediaProvider>
    {/* Dynamically add/remove tracks as needed. */}
    <Track src="/subs/english.vtt" kind="subtitles" label="English" lang="en-US" default />
    <Track src="/subs/spanish.vtt" kind="subtitles" label="Spanish" lang="es-ES" />
  </MediaProvider>
</MediaPlayer>
INFO
See the Text Tracks API guide for how to interact with text tracks programmatically.

Text Track Default
Section titled Text Track Default
When default is set on a text track it will set the mode of that track to showing immediately. In other words, this track is immediately active. Only one default is allowed per track kind.

// One default per kind is allowed.
<Track ... kind="captions" default />
<Track ... kind="chapters" default />
<Track ... kind="descriptions" default />
Text Track Formats
Section titled Text Track Formats
The vidstack/media-captions library handles loading, parsing, and rendering captions inside of the player. The following caption formats are supported:

VTT
SRT
SSA/ASS
JSON
See the links provided for more information and any limitations. Do note, all caption formats are mapped to VTT which is extended to support custom styles. In addition, browsers or providers may also support loading additional text tracks. For example, Safari and the HLS provider will load captions embedded in HLS playlists.

You can specify the desired text track format like so:

<Track ... type="srt" />
Text Track Kinds
Section titled Text Track Kinds
The following text track kinds are supported:

subtitles: Provides a written version of the audio for non-native speakers.
captions: Includes dialog and descriptions of important audio elements, like music or sound effects.
chapters: Contains information (e.g, title and start times) about the different chapters or sections of the media file.
descriptions: Provides information about the visual content to assist individuals who are blind or visually impaired.
metadata: Additional information or descriptive data within a media file. This metadata can be used for various purposes, like providing descriptions, comments, or annotations related to the media content. It is not displayed as subtitles or captions but serves as background information that can be used for various purposes, including search engine optimization, accessibility enhancements, or supplementary details for the audience.
<Track ... kind="subtitles" />
JSON Tracks
Section titled JSON Tracks
JSON content can be provided directly to text tracks or loaded from a remote location like so:

import { type VTTContent } from '@vidstack/react';

const content: VTTContent = {
  cues: [
    { startTime: 0, endTime: 5, text: '...' },
    { startTime: 5, endTime: 10, text: '...' },
  ],
};

// Option 1. Provide JSON directly.
<Track content={content} label="English" kind="captions" lang="en-US" type="json" />;

// Option 2. Load from a remote location.
<Track src="/subs/english.json" ...  type="json" />
Example JSON text tracks:

[
  {
    "label": "English",
    "kind": "captions",
    "lang": "en-US",
    "type": "json",
    "content": { "regions": [], "cues": [] },
    "default": true
  },
  ...
]
<MediaPlayer>
  <MediaProvider>
    {tracks.map((track) => (
      <Track {...track} key={track.content} />
    ))}
  </MediaProvider>
</MediaPlayer>
Example JSON cues:

cues.json
[
  { "startTime": 0, "endTime": 5, "text": "Cue One!" },
  { "startTime": 5, "endTime": 10, "text": "Cue Two!" }
]
Example JSON regions and cues:

regions.json
{
  "regions": [{ "id": "0", "lines": 3, "scroll": "up" }],
  "cues": [{ "region": { "id": "0" }, "startTime": 0, "endTime": 5, "text": "Hello!" }]
}
LibASS
Section titled LibASS
We provide a direct integration for a WASM port of libass if you’d like to use advanced ASS features that are not supported.

npm i jassub

Copy the node_modules/jassub/dist directory to your public directory (e.g, public/jassub)

Add the LibASSTextRenderer to the player like so:

tsx
Copy
import { useEffect, useRef } from 'react';

import {
  LibASSTextRenderer,
  MediaPlayer,
  MediaProvider,
  Track,
  type MediaPlayerInstance,
} from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  useEffect(() => {
    const renderer = new LibASSTextRenderer(() => import('jassub'), {
      workerUrl: '/jassub/jassub-worker.js',
      legacyWorkerUrl: '/jassub/jassub-worker-legacy.js',
    });

    player.current!.textRenderers.add(renderer);
  }, []);

  return (
    <MediaPlayer ref={player}>
      <MediaProvider>
        <Track
          src="/english.ass"
          kind="subtitles"
          type="ass"
          label="English"
          lang="en-US"
          default
        />
      </MediaProvider>
      {/* ... */}
    </MediaPlayer>
  );
}
INFO
See the JASSUB options for how to further configure the LibASS renderer.

Thumbnails
Section titled Thumbnails
Thumbnails are small, static images or frames extracted from the video or audio content. These images serve as a visual preview or representation of the media content, allowing users to quickly identify and navigate to specific points within the video or audio. Thumbnails are often displayed in the time slider or chapters menu; enabling users to visually browse and select the part of the content they want to play.

Usage
Section titled Usage
Thumbnails can be loaded using the Thumbnail component or useThumbnails hook. They’re also supported out the box by the Default Layout and Plyr Layout.

// 1. Layouts.
<PlyrLayout thumbnails="/thumbnails.vtt" />
<DefaultVideoLayout thumbnails="/thumbnails.vtt" />

// 2. Thumbnail component.
<Thumbnail.Root src="/thumbnails.vtt">
  <Thumbnail.Img />
</Thumbnail.Root>

// 3. Hook.
const thumbnails = useThumbnails('/thumbnails.vtt');
VTT
Section titled VTT
Thumbnails are generally provided in the Web Video Text Tracks (WebVTT) format. The WebVTT file specifies the time ranges of when to display images, with the respective image URL and coordinates (only required if using a sprite). You can refer to our thumbnails example to get a better idea of how this file looks.

Sprite

Sprites are large storyboard images that contain multiple small tiled thumbnails. They’re preferred over loading multiple images because:

Sprites reduce total file size due to compression.
Avoid loading delays for each thumbnail.
Reduce the number of server requests.
The WebVTT file must append the coordinates of each thumbnail like so:

WEBVTT

00:00:00.000 --> 00:00:04.629
storyboard.jpg#xywh=0,0,284,160

00:00:04.629 --> 00:00:09.258
storyboard.jpg#xywh=284,0,284,160

...
Multiple Images

Sprites should generally be preferred but in the case you only have multiple individual thumbnail images, they can be specified like so:

WEBVTT

00:00:00.000 --> 00:00:04.629
/media/thumbnail-1.jpg

00:00:04.629 --> 00:00:09.258
/media/thumbnail-2.jpg

...
JSON
Section titled JSON
Thumbnails can be loaded as a JSON file. Ensure the Content-Type header is set to application/json on the response. The returned JSON can be VTT cues, an array of images, or a storyboard.

import { Thumbnail } from '@vidstack/react';

<Thumbnail.Root src="/thumbnails.json">
  <Thumbnail.Img />
</Thumbnail.Root>
Mux storyboards are supported out of the box:

<DefaultVideoLayout src="https://image.mux.com/{PLAYBACK_ID}/storyboard.json">
Example JSON VTT:

vtt.json
[
  { "startTime": 0, "endTime": 5, "text": "/media/thumbnail-1.jpg" },
  { "startTime": 5, "endTime": 10, "text": "/media/thumbnail-2.jpg" }
]
Example JSON images:

images.json
[
  { "startTime": 0, "endTime": 5, "url": "/media/thumbnail-1.jpg" },
  { "startTime": 5, "endTime": 10, "url": "/media/thumbnail-2.jpg" }
]
Example JSON storyboard:

storyboard.json
{
  "url": "https://example.com/storyboard.jpg",
  "tileWidth": 256,
  "tileHeight": 160,
  "tiles": [
    { "startTime": 0, "x": 0, "y": 0 },
    { "startTime": 50, "x": 256, "y": 0 }
  ]
}
Object
Section titled Object
Example object with multiple images:

import { type ThumbnailImageInit } from '@vidstack/react';

const thumbnails: ThumbnailImageInit[] = [
  { startTime: 0, url: '/media/thumbnail-1.jpg' },
  { startTime: 5, url: '/media/thumbnail-2.jpg' },
  // ...
];
Example storyboard object:

import { type ThumbnailStoryboard } from '@vidstack/react';

const storyboard: ThumbnailStoryboard = {
  url: 'https://example.com/storyboard.jpg',
  tileWidth: 256,
  tileHeight: 160,
  tiles: [
    { startTime: 0, x: 0, y: 0 },
    { startTime: 50, x: 256, y: 0 },
  ],
};
Provide objects directly like so:

// 1. Layouts.
<PlyrLayout thumbnails={storyboard} />
<DefaultVideoLayout thumbnails={storyboard} />

// 2. Thumbnail component.
<Thumbnail.Root src={storyboard}>...</Thumbnail.Root>

// 3. Hook
const thumbnails = useThumbnails(storyboard);
Video Qualities
Section titled Video Qualities
Adaptive streaming protocols like HLS and DASH not only enable streaming media in chunks, but also have the ability to adapt playback quality based on the device size, network conditions, and other information. Adaptive qualities is important for speeding up initial delivery and to avoid loading excessive amounts of data which cause painful buffering delays.

Video streaming platforms such as Cloudflare Stream and Mux will take an input video file (e.g., awesome-video.mp4) and create multiple renditions out of the box for you, with multiple resolutions (width/height) and bit rates:

HLS manifest with multiple child resolution manifests.
By default, the best quality is automatically selected by the streaming engine such as hls.js or dash.js. You’ll usually see this as an “Auto” option in the player quality menu. It can also be manually set if the engine is not making optimal decisions, as they’re generally more conservative to avoid excessive bandwidth usage.

Once you have your HLS or DASH playlist by either creating it yourself using FFMPEG or using a streaming provider, you can pass it to the player like so:

{/* Example with Cloudflare Stream. */}
<MediaPlayer src="https://customer-<CODE>.cloudflarestream.com/<UID>/manifest/video.m3u8">

{/* Example with Mux. */}
<MediaPlayer src="https://stream.mux.com/<PLAYBACK_ID>.m3u8">
INFO
See the Video Qualities API guide for how to interact with renditions programmatically.

Events
A guide on how events and tracking works inside the library.

Media Events
Section titled Media Events
You can find a complete list of media events fired in the Player API Reference. The player smoothes out any unexpected behavior across browsers, attaches additional metadata to the event detail, and rich information such as the request event that triggered it or the origin event that kicked it off.

import { MediaPlayer, MediaProvider, type MediaLoadedMetadataEvent } from '@vidstack/react';

function Player() {
  function onLoadedMetadata(nativeEvent: MediaLoadedMetadataEvent) {
    // original media event (`loadedmetadata`) is still available.
    const originalMediaEvent = nativeEvent.trigger;
  }

  return (
    <MediaPlayer onLoadedMetadata={onLoadedMetadata}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
Media Request Events
Section titled Media Request Events
Vidstack Player is built upon a request and response model for updating media state. Requests are dispatched as events to the player component. The player attempts to satisfy requests by performing operations on the provider based on the given request, and then attaching it to the corresponding media event.

For example, the media-play-request event is a request to begin/resume playback, and as a consequence it’ll trigger a play() call on the provider. The provider will respond with a play or play-fail event to confirm the request was satisfied. You can find a complete list of media request events fired in the Player API Reference.

import {
  MediaPlayer,
  MediaProvider,
  type MediaPlayEvent,
  type MediaPlayFailEvent,
  type MediaPlayRequestEvent,
} from '@vidstack/react';

function Player() {
  // 1. request was made
  function onPlayRequest(nativeEvent: MediaPlayRequestEvent) {
    // ...
  }

  // 2. request succeeded
  function onPlay(nativeEvent: MediaPlayEvent) {
    // request events are attached to media events
    const playRequestEvent = nativeEvent.request; // MediaPlayRequestEvent
  }

  // 2. request failed
  function onPlayFail(error: Error, nativeEvent: MediaPlayFailEvent) {
    // ...
  }

  return (
    <MediaPlayer onPlay={onPlay} onPlayFail={onPlayFail} onMediaPlayRequest={onPlayRequest}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
When are request events fired?

Media request events are fired by Vidstack components generally in response to user actions. Most actions are a direct consequence to UI events such as pressing a button or dragging a slider. However, some actions may be indirect such as scrolling the player out of view, switching browser tabs, or the device going to sleep.

How are request events fired?

Request events are standard DOM events which can be dispatched like any other, however, they’re generally dispatched by using the MediaRemoteControl as it’s simpler. A good practice is to always attach event triggers to ensure requests can be traced back to their origin. This is the same way all Vidstack components dispatch requests internally.

Cancelling Requests
Section titled Cancelling Requests
Meida request events can be cancelled by listening for them on the player or the component dispatching it and preventing the default behavior:

import { type MediaSeekRequestEvent } from '@vidstack/react';

function onSeekRequest(time: number, nativeEvent: MediaSeekRequestEvent) {
  nativeEvent.preventDefault();
}

// Option 1. Cancel requests on the player.
<MediaPlayer onMediaSeekRequest={onSeekRequest} />;

// Option 2. Cancel requests on the component dispatching it.
<TimeSlider.Root onMediaSeekRequest={onSeekRequest} />
Event Triggers
Section titled Event Triggers
All events in the library keep a history of trigger events which are stored as a chain. Each event points to the event that came before it all the way up to the origin event. The following is an example of a chain that is created when the play button is clicked and media begins playing:

Media playing event chain diagram
import { MediaPlayer, MediaProvider, type MediaPlayingEvent } from '@vidstack/react';

function Player() {
  function onPlaying(nativeEvent: MediaPlayingEvent) {
    // the event that triggered the media play request
    const origin = nativeEvent.originEvent; // e.g., PointerEvent

    // was this triggered by an actual person?
    const userPlayed = nativeEvent.isOriginTrusted;

    // equivalent to above
    const isTrusted = nativeEvent.originEvent?.isTrusted;
  }

  return (
    <MediaPlayer onPlaying={onPlaying}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
INFO
See event trigger helpers for how you can inspect and walk event trigger chains.

Event Types
Section titled Event Types
All event types are named using PascalCase and suffixed with the word Event (e.g., SliderDragStartEvent). Furthermore, media events are all prefixed with the word Media as seen in the examples below. Refer to each component’s docs page to see what events are fired.

import {
  type MediaCanPlayEvent,
  type MediaPlayEvent,
  type MediaPlayRequestEvent,
  type MediaStartedEvent,
  type MediaTimeUpdateEvent,
} from '@vidstack/react';

State Management
A guide on how to read, update, and subscribe to media state.

Reading
Section titled Reading
The useMediaState and useMediaStore hooks enable you to subscribe directly to specific media state changes, rather than listening to potentially multiple DOM events and binding it yourself.

Tracking media state via events is error prone and tedious:

import { useState } from 'react';

import { MediaPlayer } from '@vidstack/react';

function Player() {
  const [paused, setPaused] = useState(true);

  return (
    <MediaPlayer onPlay={() => setPaused(false)} onPause={() => setPaused(true)}>
      {/* ... */}
    </MediaPlayer>
  );
}
Tracking media state via hooks:

import { useRef } from 'react';

import {
  MediaPlayer,
  useMediaState,
  useMediaStore,
  type MediaPlayerInstance,
} from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  // ~~ Option 1
  // - This hook is simpler when accessing a single piece of state.
  // - This hook is much cheaper/faster than `useMediaStore`.
  const paused = useMediaState('paused', player);

  // ~~ Option 2
  // - This hook creates a live subscription to the media paused state.
  // - All state subscriptions are lazily created on prop access.
  // - This hook makes it easy to access all media state.
  const { paused } = useMediaStore(player);

  return <MediaPlayer ref={player}>{/* ... */}</MediaPlayer>;
}
You can omit the ref if you’re calling the hooks inside a player child component as the media context is available:

import { useMediaStore } from '@vidstack/react';

// This component is a child of `<MediaPlayer>`
function PlayerChildComponent() {
  // No ref required.
  const { paused } = useMediaStore();
}
INFO
You can find a complete list of all media states available in the Player State Reference.

Avoiding Renders
Section titled Avoiding Renders
The useMediaState and useMediaStore hook will trigger re-renders. For some media state this may be too expensive or unnecessary. You can subscribe to state updates directly on the player instance to avoid triggering renders:

import { useEffect, useRef } from 'react';

import { MediaPlayer, MediaProvider, type MediaPlayerInstance } from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  useEffect(() => {
    // Access snapshot of player state.
    const { paused } = player.current!.state;

    // Subscribe for updates without triggering renders.
    return player.current!.subscribe(({ currentTime }) => {
      // ...
    });
  }, []);

  return (
    <MediaPlayer ref={player}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
When inside a player child component you can get a player instance reference with useMediaPlayer:

import { useEffect } from 'react';

import { useMediaPlayer } from '@vidstack/react';

// This component is a child of `<MediaPlayer>`
function PlayerChildComponent() {
  const player = useMediaPlayer();

  useEffect(() => {
    if (!player) return;
    // Same as example above here.
  }, [player]);
}
Updating
Section titled Updating
The useMediaRemote hook creates and returns a MediaRemoteControl object. The returned class provides a simple facade for dispatching media request events. This can be used to request media playback to play/pause, change the current volume level, seek to a different time position, and other actions that change media state.

import { type PointerEvent } from 'react';

import { useMediaRemote } from '@vidstack/react';

function PlayerChildComponent() {
  const remote = useMediaRemote();

  function onClick({ nativeEvent }: PointerEvent) {
    // Attaching trigger here to trace this play call back to this event.
    remote.play(nativeEvent);
  }

  return <button onPointerUp={onClick}>{/* ... */}</button>;
}
The example above shows that event triggers can be provided to all methods on the MediaRemoteControl class. Trigger events enable connecting media events back to their origin event. This can be useful when trying to understand how a media event was triggered, or when analyzing data such as the time difference between the request and when it was performed.

INFO
See the MediaRemoteControl API docs for all available methods.

### Styling

Introduction
An introduction to styling media elements and components using CSS.

Styling Elements
Section titled Styling Elements
Vidstack Player enables styling any child element of the player with CSS based on the current media state. This is made possible by exposing media state as data attributes on the player DOM element. You can use the presence and/or absence of these media data attributes to style children of the player element with CSS. Here’s a few simple examples:

examples.css
/* Apply styles to `foo` class when media is paused. */
.player[data-paused] .foo {
}

/* Apply styles to `bar` class when media is _not_ paused. */
.player:not([data-paused]) .bar {
}
INFO
See the Media Player Data Attributes reference table.

The [attr] selector will select elements based on the presence or value of an attribute, and the :not() pseudo-class represents elements that do not match a list of selectors. You can combine attributes and selectors to express newer and more powerful conditional selectors like so:

examples.css
/* AND conditional style. */
/* Apply styles when media playback has ended and controls are hidden. */
.player[data-ended]:not([data-controls]) {
}

/* OR conditional style. */
/* Apply styles if media is not ready for playback or it's playing. */
.player:not([data-can-play]),
.player[data-playing] {
}
Styling Components
Section titled Styling Components
Similarly to styling player elements, components also expose data attributes and CSS variables for styling with CSS:

// Apply styles via classes.

<Poster className="poster" />

<PlayButton className="play-button">{/* ... */}</PlayButton>

<Slider.Root className="slider">
  <Slider.TrackFill className="slider-track-fill" />
  {/* ... */}
</Slider.Root>

<Menu.Root className="menu">
  {/* ... */}
</Menu.Root>
/* Examples of component data attrs and CSS vars. */

.poster[data-visible] {
  /* ... */
}

.play-button[data-paused] {
  /* ... */
}

.slider[data-dragging] {
  /* ... */
}

.slider-track-fill {
  width: var(--slider-fill, 0%);
}

.menu[data-open] {
  /* ... */
}
INFO
Refer to the Data Attributes and CSS Variables section on each component’s page.

Default Theme
Section titled Default Theme
To speed up development, we provide default styles out of the box for all components. The installation guide can get you set up. You’re free to override CSS properties and use CSS variables to further customize components as desired. The default theme is applied via classes like so:

examples.tsx
<Poster className="vds-poster" />

<PlayButton className="vds-button">{/* ... */}</PlayButton>

<Slider.Root className="vds-slider">
  {/* ... */}
</Slider.Root>

<Menu.Root className="vds-menu">
  {/* ... */}
</Menu.Root>
You can see our player examples to see how to apply the default theme to a variety of components. You can also find code examples and CSS variables on each component page.

Animations
Section titled Animations
Some components such as Tooltips and Menus are hidden (i.e., display: none) when they’re inactive to prevent ARIA and focus issues, and to not appear on initial mount. You can add animations to the mentioned components using CSS. The library will wait for the animation to end before performing any operations such as focusing the first interactive element, or forcefully hiding the component.

/* Simple fade in/out animation example. */

/* Enter animation. */
.menu[data-open] {
  animation: menu-fade-in 300ms ease-out;
}

/* Exit animation. */
.menu:not([data-open]) {
  animation: menu-fade-out 300ms ease-in;
}

@keyframes menu-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes menu-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
Where to start?
Section titled Where to start?
Now that you’ve familiarized yourself with how styling elements and components works, you can start building! The best place is to start with styling any of the following:

The player is responsive by default but you might want to set a specific width or aspect ratio to prevent layout shifts.
The provider can generally be left as-is, but it’s important you understand what role it plays in your design.
The controls is a great place to start as most media components are generally grouped and shown/hidden together.
From there you’re free to start exploring all of our components by scrolling down through the components sidebar. They’re organized into categories by their role (e.g., display, buttons, sliders, etc.) and each contains docs on everything you need for using and customizing them. Enjoy!

Core Concepts

Loading
A guide on how to handle loading various media resources.

Sizing
Section titled Sizing
By default, the browser will use the intrinsic size of the loaded media to set the dimensions of the provider. As media loads over the network, the element will jump from the default size to the intrinsic media size, triggering a layout shift which is a poor user experience indicator for both your users and search engines (i.e., Google).

Aspect Ratio

To avoid a layout shift, we recommend setting the aspect ratio like so:

tsx
<MediaPlayer aspectRatio="16/9">
Ideally the ratio set should match the ratio of the media content itself (i.e., intrinsic aspect ratio) otherwise you’ll end up with a letterbox template (empty black bars on the left/right of the media).

Specify Dimensions

If you’d like to be more specific for any reason, you can specify the width and height of the player simply using CSS like so:

.player {
  width: 600px;
  height: 338px;
  aspect-ratio: unset;
}
Load Strategies
Section titled Load Strategies
A loading strategy specifies when media or the poster image should begin loading. Loading media too early can effectively slow down your entire application, so choose wisely.

The following media loading strategies are available:

eager: Load media immediately - use when media needs to be interactive as soon as possible.
idle: Load media once the page has loaded and the requestIdleCallback is fired - use when media is lower priority and doesn’t need to be interactive immediately.
visible: Load media once it has entered the visual viewport - use when media is below the fold and you prefer delaying loading until it’s required.
play: Load the provider and media on play - use when you want to delay loading until interaction.
custom: Load media when the startLoading()/startLoadingPoster() method is called or the media-start-loading/media-start-loading-poster event is dispatched - use when you need fine control of when media should begin loading.
<MediaPlayer load="visible" posterLoad="visible">
INFO
The poster load strategy specifies when the poster should begin loading. Poster loading is separate from media loading so you can display an image before media is ready for playback. This generally works well in combination with load="play" to create thumbnails.

Custom Strategy
Section titled Custom Strategy
A custom load strategy lets you control when media or the poster image should begin loading:

import { useEffect, useRef } from 'react';

import { MediaPlayer, type MediaPlayerInstance } from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  useEffect(() => {
    // Call whenever you like - also available on `useMediaRemote`.
    player.current!.startLoading();

    // Call when poster should start loading.
    player.current!.startLoadingPoster();
  }, []);

  return (
    <MediaPlayer load="custom" posterLoad="custom" ref={player}>
      {/* ... */}
    </MediaPlayer>
  );
}
View Type
Section titled View Type
The view type suggests what type of media layout will be displayed. It can be either audio or video. This is mostly to inform layouts, whether your own or the defaults, how to appropriately display the controls and general UI. By default, the view type is inferred from the provider and media type. You can specify the desired type like so:

<MediaPlayer viewType="audio">
Stream Type
Section titled Stream Type
The stream type refers to the mode in which content is delivered through the video player. The player will use the type to determine how to manage state/internals such as duration updates, seeking, and how to appropriately present UI components and layouts. The stream type can be one of the following values:

on-demand: Video on Demand (VOD) content is pre-recorded and can be accessed and played at any time. VOD streams allow viewers to control playback, pause, rewind, and fast forward.
live: Live streaming delivers real-time content as it happens. Viewers join the stream and watch the content as it’s being broadcast, with limited control over playback.
live:dvr: Live DVR (Live Digital Video Recording) combines the features of both live and VOD. Viewers can join a live stream and simultaneously pause, rewind, and fast forward, offering more flexibility in watching live events.
ll-live: A live streaming mode optimized for reduced latency, providing a near-real-time viewing experience with minimal delay between the live event and the viewer.
ll-live:dvr: Similar to low-latency live, this mode enables viewers to experience live content with minimal delay while enjoying the benefits of DVR features (same as live:dvr).
If the value is not set, it will be inferred by the player which can be less accurate (e.g., at identifying DVR support). When possible, prefer specifying it like so:

<MediaPlayer streamType="live">
Duration
Section titled Duration
By default, the duration is inferred from the provider and media. It’s always best to provide the duration when known to avoid any inaccuracies such as rounding errors, and to ensure UI is set to the correct state without waiting on metadata to load. You can specify the exact duration like so:

// 5 minutes.
<MediaPlayer duration={300}>
Clipping
Section titled Clipping
Clipping allows shortening the media by specifying the time at which playback should start and end.

<MediaPlayer clipStartTime={10} clipEndTime={30}>
You can set a clip start time or just an end time, both are not required.
The media duration and chapter durations will be updated to match the clipped length.
Any media resources such as text tracks and thumbnails should use the full duration.
Seeking to a new time is based on the clipped duration. For example, if a 1 minute video is clipped to 30 seconds, seeking to 30s will be the end of the video.
Media URI Fragments are set internally to efficiently load audio and video files between the clipped start and end times (e.g., /video.mp4#t=30,60).
Media Session
Section titled Media Session
The Media Session API is automatically set using the provided title, artist, and artwork (poster is used as fallback) player properties.

Storage
Section titled Storage
Storage enables saving player and media settings so that the user can resume where they left off. This includes saving and initializing on load settings such as language, volume, muted, captions visibility, and playback time.

Local Storage
Section titled Local Storage
Local Storage enables saving data locally on the user’s browser. This is a simple and fast option for remembering player settings, but it won’t persist across domains, devices, or browsers.

Provide a storage key prefix for turning local storage on like so:

<MediaPlayer storage="storage-key">
Extending Local Storage

Optionally, you can extend and customize local storage behaviour like so:

import { LocalMediaStorage } from '@vidstack/react';

class CustomLocalMediaStorage extends LocalMediaStorage {
  // override methods here...
}

// Provide storage to player.
<MediaPlayer storage={CustomLocalMediaStorage}>
Remote Storage
Section titled Remote Storage
Remote Storage enables asynchronously saving and loading data from anywhere. This is great as settings willl persist across user sessions even if the domain, device, or browser changes. Generally, you will save player/media settings to a remote database based on the currently authenticated user.

Implement the MediaStorage interface and provide it to the player like so:

ts
Copy
import { type MediaStorage } from '@vidstack/react';

class MediaDatabaseStorage implements MediaStorage {
  async getVolume() {}
  async setVolume(volume: number) {}

  async getMuted() {}
  async setMuted(isMuted: boolean) {}

  async getTime() {}
  async setTime(time: number) {}

  async getLang() {}
  async setLang(lang: string | null) {}

  async getCaptions() {}
  async setCaptions(isOn: boolean) {}

  async onLoad() {}

  onChange(src, mediaId, playerId) {}

  onDestroy() {}
}
const storage = useMemo(() => new MediaDatabaseStorage(), []);

<MediaPlayer storage={storage}>
Sources
Section titled Sources
The player can accept one or more media sources which can be a string URL of the media resource to load, or any of the following objects: MediaStream, MediaSource, Blob, or File.

Single Source

<MediaPlayer src="https://files.vidstack.io/sprite-fight/720p.mp4" />
Multiple Source Types

The list of supported media formats varies from one browser to the other. You should either provide your source in a single format that all relevant browsers support, or provide multiple sources in enough different formats that all the browsers you need to support are covered.

<MediaPlayer
  src={[
    // Audio
    { src: 'https://files.vidstack.io/agent-327/audio.mp3', type: 'audio/mpeg' },
    { src: 'https://files.vidstack.io/agent-327/audio.ogg', type: 'audio/ogg' },
    // Video
    { src: 'https://files.vidstack.io/agent-327/720p.ogv', type: 'video/ogg' },
    { src: 'https://files.vidstack.io/agent-327/720p.avi', type: 'video/avi' },
    { src: 'https://files.vidstack.io/agent-327/720p.mp4', type: 'video/mp4' },
  ]}
/>
Source Objects
Section titled Source Objects
The player accepts both audio and video source objects. This includes MediaStream, MediaSource, Blob, and File.

import { useEffect, useState } from 'react';

import { MediaPlayer, MediaProvider, type MediaSrc } from '@vidstack/react';

function Player() {
  const [src, setSrc] = useState<MediaSrc>();

  useEffect(() => {
    async function getMediaStream() {
      // Example 1: Audio
      const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setSrc({ src: audioStream, type: 'audio/object' });

      // Example 2: Video
      const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
      setSrc({ src: videoStream, type: 'video/object' });
    }

    getMediaStream();
  }, []);

  return (
    <MediaPlayer src={src}>
      <MediaProvider />
    </MediaPlayer>
  );
}
Changing Source
Section titled Changing Source
The player supports changing the source dynamically. Simply update the src property when you want to load new media. You can also set it to an empty string "" to unload media.

import { useState } from 'react';

import { MediaPlayer, type MediaSrc } from '@vidstack/react';

const sources = ['/video-a.mp4', '/video-b.mp4', './video-c.mp4'];

function Player() {
  const [src, setSrc] = useState(0);

  function prevVideo() {
    setSrc((n) => Math.max(0, n - 1));
  }

  function nextVideo() {
    setSrc((n) => Math.min(sources.length - 1, n + 1));
  }

  return (
    <MediaPlayer src={sources[src]}>
      {/* ... */}
      {/* Playlist controls */}
      <button onClick={prevVideo}>Previous Video</button>
      <button onClick={nextVideo}>Next Video</button>
    </MediaPlayer>
  );
}
Source Types
Section titled Source Types
The player source selection process relies on file extensions, object types, and type hints to determine which provider to load and how to play a given source. The following is a table of supported media file extensions and types for each provider:

Media	Extensions	Types
Audio	m4a, m4b, mp4a, mpga, mp2, mp2a, mp3, m2a, m3a, wav, weba, aac, oga, spx	audio/mpeg, audio/ogg, audio/3gp, audio/mp4, audio/webm, audio/flac, audio/object
Video	mp4, ogg, ogv, webm, mov, m4v	video/mp4, video/webm, video/3gp, video/ogg, video/avi, video/mpeg
HLS	m3u8	application/vnd.apple.mpegurl, audio/mpegurl, audio/x-mpegurl, application/x-mpegurl, video/x-mpegurl, video/mpegurl, application/mpegurl
DASH	mpd	application/dash+xml
The following are valid as they have a file extension (e.g, video.mp4) or type hint (e.g., video/mp4):

src="https://example.com/video.mp4"
src="https://example.com/hls.m3u8"
src="https://example.com/dash.mpd"
src = { src: "https://example.com/video", type: "video/mp4" }
src = { src: "https://example.com/hls", type: "application/x-mpegurl" }
src = { src: "https://example.com/dash", type: "application/dash+xml" }
The following are invalid as they are missing a file extension and type hint:

src="https://example.com/video"
src="https://example.com/hls"
src="https://example.com/dash"
Source Sizes
Section titled Source Sizes
You can provide video qualities/resolutions using multiple video files with different sizes (e.g, 1080p, 720p, 480p) like so:

<MediaPlayer
  src={[
    {
      src: 'https://files.vidstack.io/sprite-fight/1080p.mp4',
      type: 'video/mp4',
      width: 1920,
      height: 1080,
    },
    {
      src: 'https://files.vidstack.io/sprite-fight/720p.mp4',
      type: 'video/mp4',
      width: 1280,
      height: 720,
    },
    {
      src: 'https://files.vidstack.io/sprite-fight/480p.mp4',
      type: 'video/mp4',
      width: 853,
      height: 480,
    },
  ]}
/>
NOTE
We strongly recommend using adaptive streaming protocols such as HLS over providing multiple static media files, see the Video Qualities section for more information.

Supported Codecs
Section titled Supported Codecs
Vidstack Player relies on the native browser runtime to handle media playback, hence it’s important you review what containers and codecs are supported by them. This also applies to libraries like hls.js and dash.js which we use for HLS/DASH playback in browsers that don’t support it natively.

While there are a vast number of media container formats, the ones listed below are the ones you are most likely to encounter. Some support only audio while others support both audio and video. The most commonly used containers for media on the web are probably MPEG-4 (MP4), Web Media File (WEBM), and MPEG Audio Layer III (MP3).

It’s important that both the media container and codecs are supported by the native runtime. Please review the following links for what’s supported and where:

Media Containers
Audio Codecs
Video Codecs
Providers
Section titled Providers
Providers are auto-selected during the source selection process and dynamically loaded via a provider loader (e.g., VideoProviderLoader). The following providers are supported at this time:

Audio
Video
HLS
DASH
YouTube
Vimeo
Remotion
Google Cast
INFO
See source types for how to ensure the correct media provider is loaded.

Provider Events
Section titled Provider Events
The following events will fire as providers change or setup:

import {
  isHLSProvider,
  MediaPlayer,
  MediaProvider,
  type MediaProviderAdapter,
} from '@vidstack/react';

function Player() {
  // This is where you should configure providers.
  function onProviderChange(provider: MediaProviderAdapter | null) {
    if (isHLSProvider(provider)) {
      provider.config = {};
      provider.onInstance((hls) => {
        // ...
      });
    }
  }

  // Provider is rendered, attached event listeners, and ready to load source.
  function onProviderSetup(provider: MediaProviderAdapter) {
    if (isHLSProvider(provider)) {
      // ...
    }
  }

  return (
    <MediaPlayer onProviderChange={onProviderChange} onProviderSetup={onProviderSetup}>
      <MediaProvider />
    </MediaPlayer>
  );
}
Provider Types
Section titled Provider Types
The following utilities can be useful for narrowing the type of a media provider:

import {
  isAudioProvider,
  isDASHProvider,
  isGoogleCastProvider,
  isHLSProvider,
  isVideoProvider,
  isVimeoProvider,
  isYouTubeProvider,
  MediaPlayer,
  MediaProvider,
  type AudioProvider,
  type DASHProvider,
  type GoogleCastProvider,
  type HLSProvider,
  type MediaProviderAdapter,
  type VideoProvider,
  type VimeoProvider,
  type YouTubeProvider,
} from '@vidstack/react';
import { isRemotionProvider, type RemotionProvider } from '@vidstack/react/player/remotion';

function Player() {
  function onProviderChange(provider: MediaProviderAdapter | null) {
    if (isAudioProvider(provider)) {
      const audioElement = provider.audio;
    }

    if (isVideoProvider(provider)) {
      const videoElement = provider.video;
    }

    if (isHLSProvider(provider)) {
      provider.config = { lowLatencyMode: true };
      provider.onInstance((hls) => {
        // ...
      });
    }

    if (isDASHProvider(provider)) {
      provider.config = {};
      provider.onInstance((dash) => {
        // ...
      });
    }

    if (isYouTubeProvider(provider)) {
      provider.cookies = true;
      // ...
    }

    if (isVimeoProvider(provider)) {
      provider.cookies = true;
      // ...
    }

    if (isRemotionProvider(provider)) {
      // ...
    }

    if (isGoogleCastProvider(provider)) {
      // ...
    }
  }

  return (
    <MediaPlayer onProviderChange={onProviderChange}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
Audio Tracks
Section titled Audio Tracks
Audio tracks are loaded from your HLS playlist. You can not manually add audio tracks to the player at this time. See the Audio Tracks API guide for how to interact with audio track programmatically.

Text Tracks
Section titled Text Tracks
Text tracks allow you to provide text-based information or content associated with video or audio. These text tracks can be used to enhance the accessibility and user experience of media content in various ways. You can provide multiple text tracks dynamically like so:

import { MediaPlayer, MediaProvider, Track } from '@vidstack/react';

<MediaPlayer>
  <MediaProvider>
    {/* Dynamically add/remove tracks as needed. */}
    <Track src="/subs/english.vtt" kind="subtitles" label="English" lang="en-US" default />
    <Track src="/subs/spanish.vtt" kind="subtitles" label="Spanish" lang="es-ES" />
  </MediaProvider>
</MediaPlayer>
INFO
See the Text Tracks API guide for how to interact with text tracks programmatically.

Text Track Default
Section titled Text Track Default
When default is set on a text track it will set the mode of that track to showing immediately. In other words, this track is immediately active. Only one default is allowed per track kind.

// One default per kind is allowed.
<Track ... kind="captions" default />
<Track ... kind="chapters" default />
<Track ... kind="descriptions" default />
Text Track Formats
Section titled Text Track Formats
The vidstack/media-captions library handles loading, parsing, and rendering captions inside of the player. The following caption formats are supported:

VTT
SRT
SSA/ASS
JSON
See the links provided for more information and any limitations. Do note, all caption formats are mapped to VTT which is extended to support custom styles. In addition, browsers or providers may also support loading additional text tracks. For example, Safari and the HLS provider will load captions embedded in HLS playlists.

You can specify the desired text track format like so:

<Track ... type="srt" />
Text Track Kinds
Section titled Text Track Kinds
The following text track kinds are supported:

subtitles: Provides a written version of the audio for non-native speakers.
captions: Includes dialog and descriptions of important audio elements, like music or sound effects.
chapters: Contains information (e.g, title and start times) about the different chapters or sections of the media file.
descriptions: Provides information about the visual content to assist individuals who are blind or visually impaired.
metadata: Additional information or descriptive data within a media file. This metadata can be used for various purposes, like providing descriptions, comments, or annotations related to the media content. It is not displayed as subtitles or captions but serves as background information that can be used for various purposes, including search engine optimization, accessibility enhancements, or supplementary details for the audience.
<Track ... kind="subtitles" />
JSON Tracks
Section titled JSON Tracks
JSON content can be provided directly to text tracks or loaded from a remote location like so:

import { type VTTContent } from '@vidstack/react';

const content: VTTContent = {
  cues: [
    { startTime: 0, endTime: 5, text: '...' },
    { startTime: 5, endTime: 10, text: '...' },
  ],
};

// Option 1. Provide JSON directly.
<Track content={content} label="English" kind="captions" lang="en-US" type="json" />;

// Option 2. Load from a remote location.
<Track src="/subs/english.json" ...  type="json" />
Example JSON text tracks:

[
  {
    "label": "English",
    "kind": "captions",
    "lang": "en-US",
    "type": "json",
    "content": { "regions": [], "cues": [] },
    "default": true
  },
  ...
]
<MediaPlayer>
  <MediaProvider>
    {tracks.map((track) => (
      <Track {...track} key={track.content} />
    ))}
  </MediaProvider>
</MediaPlayer>
Example JSON cues:

cues.json
[
  { "startTime": 0, "endTime": 5, "text": "Cue One!" },
  { "startTime": 5, "endTime": 10, "text": "Cue Two!" }
]
Example JSON regions and cues:

regions.json
{
  "regions": [{ "id": "0", "lines": 3, "scroll": "up" }],
  "cues": [{ "region": { "id": "0" }, "startTime": 0, "endTime": 5, "text": "Hello!" }]
}
LibASS
Section titled LibASS
We provide a direct integration for a WASM port of libass if you’d like to use advanced ASS features that are not supported.

npm i jassub

Copy the node_modules/jassub/dist directory to your public directory (e.g, public/jassub)

Add the LibASSTextRenderer to the player like so:

tsx
Copy
import { useEffect, useRef } from 'react';

import {
  LibASSTextRenderer,
  MediaPlayer,
  MediaProvider,
  Track,
  type MediaPlayerInstance,
} from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  useEffect(() => {
    const renderer = new LibASSTextRenderer(() => import('jassub'), {
      workerUrl: '/jassub/jassub-worker.js',
      legacyWorkerUrl: '/jassub/jassub-worker-legacy.js',
    });

    player.current!.textRenderers.add(renderer);
  }, []);

  return (
    <MediaPlayer ref={player}>
      <MediaProvider>
        <Track
          src="/english.ass"
          kind="subtitles"
          type="ass"
          label="English"
          lang="en-US"
          default
        />
      </MediaProvider>
      {/* ... */}
    </MediaPlayer>
  );
}
INFO
See the JASSUB options for how to further configure the LibASS renderer.

Thumbnails
Section titled Thumbnails
Thumbnails are small, static images or frames extracted from the video or audio content. These images serve as a visual preview or representation of the media content, allowing users to quickly identify and navigate to specific points within the video or audio. Thumbnails are often displayed in the time slider or chapters menu; enabling users to visually browse and select the part of the content they want to play.

Usage
Section titled Usage
Thumbnails can be loaded using the Thumbnail component or useThumbnails hook. They’re also supported out the box by the Default Layout and Plyr Layout.

// 1. Layouts.
<PlyrLayout thumbnails="/thumbnails.vtt" />
<DefaultVideoLayout thumbnails="/thumbnails.vtt" />

// 2. Thumbnail component.
<Thumbnail.Root src="/thumbnails.vtt">
  <Thumbnail.Img />
</Thumbnail.Root>

// 3. Hook.
const thumbnails = useThumbnails('/thumbnails.vtt');
VTT
Section titled VTT
Thumbnails are generally provided in the Web Video Text Tracks (WebVTT) format. The WebVTT file specifies the time ranges of when to display images, with the respective image URL and coordinates (only required if using a sprite). You can refer to our thumbnails example to get a better idea of how this file looks.

Sprite

Sprites are large storyboard images that contain multiple small tiled thumbnails. They’re preferred over loading multiple images because:

Sprites reduce total file size due to compression.
Avoid loading delays for each thumbnail.
Reduce the number of server requests.
The WebVTT file must append the coordinates of each thumbnail like so:

WEBVTT

00:00:00.000 --> 00:00:04.629
storyboard.jpg#xywh=0,0,284,160

00:00:04.629 --> 00:00:09.258
storyboard.jpg#xywh=284,0,284,160

...
Multiple Images

Sprites should generally be preferred but in the case you only have multiple individual thumbnail images, they can be specified like so:

WEBVTT

00:00:00.000 --> 00:00:04.629
/media/thumbnail-1.jpg

00:00:04.629 --> 00:00:09.258
/media/thumbnail-2.jpg

...
JSON
Section titled JSON
Thumbnails can be loaded as a JSON file. Ensure the Content-Type header is set to application/json on the response. The returned JSON can be VTT cues, an array of images, or a storyboard.

import { Thumbnail } from '@vidstack/react';

<Thumbnail.Root src="/thumbnails.json">
  <Thumbnail.Img />
</Thumbnail.Root>
Mux storyboards are supported out of the box:

<DefaultVideoLayout src="https://image.mux.com/{PLAYBACK_ID}/storyboard.json">
Example JSON VTT:

vtt.json
[
  { "startTime": 0, "endTime": 5, "text": "/media/thumbnail-1.jpg" },
  { "startTime": 5, "endTime": 10, "text": "/media/thumbnail-2.jpg" }
]
Example JSON images:

images.json
[
  { "startTime": 0, "endTime": 5, "url": "/media/thumbnail-1.jpg" },
  { "startTime": 5, "endTime": 10, "url": "/media/thumbnail-2.jpg" }
]
Example JSON storyboard:

storyboard.json
{
  "url": "https://example.com/storyboard.jpg",
  "tileWidth": 256,
  "tileHeight": 160,
  "tiles": [
    { "startTime": 0, "x": 0, "y": 0 },
    { "startTime": 50, "x": 256, "y": 0 }
  ]
}
Object
Section titled Object
Example object with multiple images:

import { type ThumbnailImageInit } from '@vidstack/react';

const thumbnails: ThumbnailImageInit[] = [
  { startTime: 0, url: '/media/thumbnail-1.jpg' },
  { startTime: 5, url: '/media/thumbnail-2.jpg' },
  // ...
];
Example storyboard object:

import { type ThumbnailStoryboard } from '@vidstack/react';

const storyboard: ThumbnailStoryboard = {
  url: 'https://example.com/storyboard.jpg',
  tileWidth: 256,
  tileHeight: 160,
  tiles: [
    { startTime: 0, x: 0, y: 0 },
    { startTime: 50, x: 256, y: 0 },
  ],
};
Provide objects directly like so:

// 1. Layouts.
<PlyrLayout thumbnails={storyboard} />
<DefaultVideoLayout thumbnails={storyboard} />

// 2. Thumbnail component.
<Thumbnail.Root src={storyboard}>...</Thumbnail.Root>

// 3. Hook
const thumbnails = useThumbnails(storyboard);
Video Qualities
Section titled Video Qualities
Adaptive streaming protocols like HLS and DASH not only enable streaming media in chunks, but also have the ability to adapt playback quality based on the device size, network conditions, and other information. Adaptive qualities is important for speeding up initial delivery and to avoid loading excessive amounts of data which cause painful buffering delays.

Video streaming platforms such as Cloudflare Stream and Mux will take an input video file (e.g., awesome-video.mp4) and create multiple renditions out of the box for you, with multiple resolutions (width/height) and bit rates:

HLS manifest with multiple child resolution manifests.
By default, the best quality is automatically selected by the streaming engine such as hls.js or dash.js. You’ll usually see this as an “Auto” option in the player quality menu. It can also be manually set if the engine is not making optimal decisions, as they’re generally more conservative to avoid excessive bandwidth usage.

Once you have your HLS or DASH playlist by either creating it yourself using FFMPEG or using a streaming provider, you can pass it to the player like so:

{/* Example with Cloudflare Stream. */}
<MediaPlayer src="https://customer-<CODE>.cloudflarestream.com/<UID>/manifest/video.m3u8">

{/* Example with Mux. */}
<MediaPlayer src="https://stream.mux.com/<PLAYBACK_ID>.m3u8">
INFO
See the Video Qualities API guide for how to interact with renditions programmatically.

Core Concepts

Events
A guide on how events and tracking works inside the library.

Media Events
Section titled Media Events
You can find a complete list of media events fired in the Player API Reference. The player smoothes out any unexpected behavior across browsers, attaches additional metadata to the event detail, and rich information such as the request event that triggered it or the origin event that kicked it off.

import { MediaPlayer, MediaProvider, type MediaLoadedMetadataEvent } from '@vidstack/react';

function Player() {
  function onLoadedMetadata(nativeEvent: MediaLoadedMetadataEvent) {
    // original media event (`loadedmetadata`) is still available.
    const originalMediaEvent = nativeEvent.trigger;
  }

  return (
    <MediaPlayer onLoadedMetadata={onLoadedMetadata}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
Media Request Events
Section titled Media Request Events
Vidstack Player is built upon a request and response model for updating media state. Requests are dispatched as events to the player component. The player attempts to satisfy requests by performing operations on the provider based on the given request, and then attaching it to the corresponding media event.

For example, the media-play-request event is a request to begin/resume playback, and as a consequence it’ll trigger a play() call on the provider. The provider will respond with a play or play-fail event to confirm the request was satisfied. You can find a complete list of media request events fired in the Player API Reference.

import {
  MediaPlayer,
  MediaProvider,
  type MediaPlayEvent,
  type MediaPlayFailEvent,
  type MediaPlayRequestEvent,
} from '@vidstack/react';

function Player() {
  // 1. request was made
  function onPlayRequest(nativeEvent: MediaPlayRequestEvent) {
    // ...
  }

  // 2. request succeeded
  function onPlay(nativeEvent: MediaPlayEvent) {
    // request events are attached to media events
    const playRequestEvent = nativeEvent.request; // MediaPlayRequestEvent
  }

  // 2. request failed
  function onPlayFail(error: Error, nativeEvent: MediaPlayFailEvent) {
    // ...
  }

  return (
    <MediaPlayer onPlay={onPlay} onPlayFail={onPlayFail} onMediaPlayRequest={onPlayRequest}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
When are request events fired?

Media request events are fired by Vidstack components generally in response to user actions. Most actions are a direct consequence to UI events such as pressing a button or dragging a slider. However, some actions may be indirect such as scrolling the player out of view, switching browser tabs, or the device going to sleep.

How are request events fired?

Request events are standard DOM events which can be dispatched like any other, however, they’re generally dispatched by using the MediaRemoteControl as it’s simpler. A good practice is to always attach event triggers to ensure requests can be traced back to their origin. This is the same way all Vidstack components dispatch requests internally.

Cancelling Requests
Section titled Cancelling Requests
Meida request events can be cancelled by listening for them on the player or the component dispatching it and preventing the default behavior:

import { type MediaSeekRequestEvent } from '@vidstack/react';

function onSeekRequest(time: number, nativeEvent: MediaSeekRequestEvent) {
  nativeEvent.preventDefault();
}

// Option 1. Cancel requests on the player.
<MediaPlayer onMediaSeekRequest={onSeekRequest} />;

// Option 2. Cancel requests on the component dispatching it.
<TimeSlider.Root onMediaSeekRequest={onSeekRequest} />
Event Triggers
Section titled Event Triggers
All events in the library keep a history of trigger events which are stored as a chain. Each event points to the event that came before it all the way up to the origin event. The following is an example of a chain that is created when the play button is clicked and media begins playing:

Media playing event chain diagram
import { MediaPlayer, MediaProvider, type MediaPlayingEvent } from '@vidstack/react';

function Player() {
  function onPlaying(nativeEvent: MediaPlayingEvent) {
    // the event that triggered the media play request
    const origin = nativeEvent.originEvent; // e.g., PointerEvent

    // was this triggered by an actual person?
    const userPlayed = nativeEvent.isOriginTrusted;

    // equivalent to above
    const isTrusted = nativeEvent.originEvent?.isTrusted;
  }

  return (
    <MediaPlayer onPlaying={onPlaying}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
INFO
See event trigger helpers for how you can inspect and walk event trigger chains.

Event Types
Section titled Event Types
All event types are named using PascalCase and suffixed with the word Event (e.g., SliderDragStartEvent). Furthermore, media events are all prefixed with the word Media as seen in the examples below. Refer to each component’s docs page to see what events are fired.

import {
  type MediaCanPlayEvent,
  type MediaPlayEvent,
  type MediaPlayRequestEvent,
  type MediaStartedEvent,
  type MediaTimeUpdateEvent,
} from '@vidstack/react';

Core Concepts

State Management
A guide on how to read, update, and subscribe to media state.

Reading
Section titled Reading
The useMediaState and useMediaStore hooks enable you to subscribe directly to specific media state changes, rather than listening to potentially multiple DOM events and binding it yourself.

Tracking media state via events is error prone and tedious:

import { useState } from 'react';

import { MediaPlayer } from '@vidstack/react';

function Player() {
  const [paused, setPaused] = useState(true);

  return (
    <MediaPlayer onPlay={() => setPaused(false)} onPause={() => setPaused(true)}>
      {/* ... */}
    </MediaPlayer>
  );
}
Tracking media state via hooks:

import { useRef } from 'react';

import {
  MediaPlayer,
  useMediaState,
  useMediaStore,
  type MediaPlayerInstance,
} from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  // ~~ Option 1
  // - This hook is simpler when accessing a single piece of state.
  // - This hook is much cheaper/faster than `useMediaStore`.
  const paused = useMediaState('paused', player);

  // ~~ Option 2
  // - This hook creates a live subscription to the media paused state.
  // - All state subscriptions are lazily created on prop access.
  // - This hook makes it easy to access all media state.
  const { paused } = useMediaStore(player);

  return <MediaPlayer ref={player}>{/* ... */}</MediaPlayer>;
}
You can omit the ref if you’re calling the hooks inside a player child component as the media context is available:

import { useMediaStore } from '@vidstack/react';

// This component is a child of `<MediaPlayer>`
function PlayerChildComponent() {
  // No ref required.
  const { paused } = useMediaStore();
}
INFO
You can find a complete list of all media states available in the Player State Reference.

Avoiding Renders
Section titled Avoiding Renders
The useMediaState and useMediaStore hook will trigger re-renders. For some media state this may be too expensive or unnecessary. You can subscribe to state updates directly on the player instance to avoid triggering renders:

import { useEffect, useRef } from 'react';

import { MediaPlayer, MediaProvider, type MediaPlayerInstance } from '@vidstack/react';

function Player() {
  const player = useRef<MediaPlayerInstance>(null);

  useEffect(() => {
    // Access snapshot of player state.
    const { paused } = player.current!.state;

    // Subscribe for updates without triggering renders.
    return player.current!.subscribe(({ currentTime }) => {
      // ...
    });
  }, []);

  return (
    <MediaPlayer ref={player}>
      <MediaProvider />
      {/* ... */}
    </MediaPlayer>
  );
}
When inside a player child component you can get a player instance reference with useMediaPlayer:

import { useEffect } from 'react';

import { useMediaPlayer } from '@vidstack/react';

// This component is a child of `<MediaPlayer>`
function PlayerChildComponent() {
  const player = useMediaPlayer();

  useEffect(() => {
    if (!player) return;
    // Same as example above here.
  }, [player]);
}
Updating
Section titled Updating
The useMediaRemote hook creates and returns a MediaRemoteControl object. The returned class provides a simple facade for dispatching media request events. This can be used to request media playback to play/pause, change the current volume level, seek to a different time position, and other actions that change media state.

import { type PointerEvent } from 'react';

import { useMediaRemote } from '@vidstack/react';

function PlayerChildComponent() {
  const remote = useMediaRemote();

  function onClick({ nativeEvent }: PointerEvent) {
    // Attaching trigger here to trace this play call back to this event.
    remote.play(nativeEvent);
  }

  return <button onPointerUp={onClick}>{/* ... */}</button>;
}
The example above shows that event triggers can be provided to all methods on the MediaRemoteControl class. Trigger events enable connecting media events back to their origin event. This can be useful when trying to understand how a media event was triggered, or when analyzing data such as the time difference between the request and when it was performed.

INFO
See the MediaRemoteControl API docs for all available methods.

Styling

Introduction
An introduction to styling media elements and components using CSS.

Styling Elements
Section titled Styling Elements
Vidstack Player enables styling any child element of the player with CSS based on the current media state. This is made possible by exposing media state as data attributes on the player DOM element. You can use the presence and/or absence of these media data attributes to style children of the player element with CSS. Here’s a few simple examples:

examples.css
/* Apply styles to `foo` class when media is paused. */
.player[data-paused] .foo {
}

/* Apply styles to `bar` class when media is _not_ paused. */
.player:not([data-paused]) .bar {
}
INFO
See the Media Player Data Attributes reference table.

The [attr] selector will select elements based on the presence or value of an attribute, and the :not() pseudo-class represents elements that do not match a list of selectors. You can combine attributes and selectors to express newer and more powerful conditional selectors like so:

examples.css
/* AND conditional style. */
/* Apply styles when media playback has ended and controls are hidden. */
.player[data-ended]:not([data-controls]) {
}

/* OR conditional style. */
/* Apply styles if media is not ready for playback or it's playing. */
.player:not([data-can-play]),
.player[data-playing] {
}
Styling Components
Section titled Styling Components
Similarly to styling player elements, components also expose data attributes and CSS variables for styling with CSS:

// Apply styles via classes.

<Poster className="poster" />

<PlayButton className="play-button">{/* ... */}</PlayButton>

<Slider.Root className="slider">
  <Slider.TrackFill className="slider-track-fill" />
  {/* ... */}
</Slider.Root>

<Menu.Root className="menu">
  {/* ... */}
</Menu.Root>
/* Examples of component data attrs and CSS vars. */

.poster[data-visible] {
  /* ... */
}

.play-button[data-paused] {
  /* ... */
}

.slider[data-dragging] {
  /* ... */
}

.slider-track-fill {
  width: var(--slider-fill, 0%);
}

.menu[data-open] {
  /* ... */
}
INFO
Refer to the Data Attributes and CSS Variables section on each component’s page.

Default Theme
Section titled Default Theme
To speed up development, we provide default styles out of the box for all components. The installation guide can get you set up. You’re free to override CSS properties and use CSS variables to further customize components as desired. The default theme is applied via classes like so:

examples.tsx
<Poster className="vds-poster" />

<PlayButton className="vds-button">{/* ... */}</PlayButton>

<Slider.Root className="vds-slider">
  {/* ... */}
</Slider.Root>

<Menu.Root className="vds-menu">
  {/* ... */}
</Menu.Root>
You can see our player examples to see how to apply the default theme to a variety of components. You can also find code examples and CSS variables on each component page.

Animations
Section titled Animations
Some components such as Tooltips and Menus are hidden (i.e., display: none) when they’re inactive to prevent ARIA and focus issues, and to not appear on initial mount. You can add animations to the mentioned components using CSS. The library will wait for the animation to end before performing any operations such as focusing the first interactive element, or forcefully hiding the component.

/* Simple fade in/out animation example. */

/* Enter animation. */
.menu[data-open] {
  animation: menu-fade-in 300ms ease-out;
}

/* Exit animation. */
.menu:not([data-open]) {
  animation: menu-fade-out 300ms ease-in;
}

@keyframes menu-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes menu-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
Where to start?
Section titled Where to start?
Now that you’ve familiarized yourself with how styling elements and components works, you can start building! The best place is to start with styling any of the following:

The player is responsive by default but you might want to set a specific width or aspect ratio to prevent layout shifts.
The provider can generally be left as-is, but it’s important you understand what role it plays in your design.
The controls is a great place to start as most media components are generally grouped and shown/hidden together.
From there you’re free to start exploring all of our components by scrolling down through the components sidebar. They’re organized into categories by their role (e.g., display, buttons, sliders, etc.) and each contains docs on everything you need for using and customizing them. Enjoy!

Styling

Responsive Design
A guide on the primitive building blocks available when creating responsive media designs.

Sizing
Section titled Sizing
Refer to the “avoiding layout shifts” section for how to ensure the player doesn’t jump between it’s default size and intrinsic size. This is important to avoid the page and your player layout shifting as content is loaded over the network.

Layouts
Section titled Layouts
A media player layout refers to the arrangement and presentation of various elements and controls within a media player interface. It determines how these components are organized and displayed to the user while playing audio or video content. A well-designed media player layout enhances the user experience and provides easy access to essential functionalities.

The arrangement and appearance of these elements can vary, but a well-structured media player layout ensures that users can easily control and enjoy the content, whether it’s video, audio, live streaming, or other forms of multimedia. The layout design should consider user-friendliness, accessibility, and aesthetics to create a seamless and engaging media playback experience.

Our library comes with pre-built production-ready layouts such as the Default Layout and Plyr Layout. However, if you prefer you can build your own custom layout. A custom layout is preferrable when you want more control over how the player UI is arranged and presented. You can easily build your own layout by composing and styling various player components.

tsx
Copy
import {
  MediaPlayer,
  MediaProvider,
  useMediaState,
  // ...
} from '@vidstack/react';

function MyPlayer() {
  return (
    <MediaPlayer>
      {/* This component is where the video canvas will be rendered. */}
      <MediaProvider />
      <CustomVideoLayout />
    </MediaPlayer>
  );
}

// Pick, compose, arrange, and style player components to build your layout.
function CustomVideoLayout() {
  const viewType = useMediaState('viewType'),
    streamType = useMediaState('streamType');

  // If we have multiple layouts based on view/stream type we can skip rendering.
  if (viewType !== 'video' || streamType !== 'on-demand') return null;

  return (
    <>
      <Captions className="..." />
      <Controls.Root className="...">
        <Controls.Group className="...">
          <TimeSlider.Root className="...">{/* ... */}</TimeSlider.Root>
        </Controls.Group>
        <Controls.Group className="...">
          <PlayButton className="...">{/* ... */}</PlayButton>
          <MuteButton className="...">{/* ... */}</MuteButton>
          <VolumeSlider.Root className="...">{/* ... */}</VolumeSlider.Root>
          {/* ... */}
        </Controls.Group>
      </Controls.Root>
    </>
  );
}
Queries
Section titled Queries
CSS Media Queries are a CSS feature used to apply styles based on the characteristics of the viewing device, such as screen size, orientation, or resolution. While media queries are widely used for responsive web design, they have limitations when styling a media player, particularly in scenarios where the player’s size and layout need to adapt dynamically to its container element, view type, or stream type. This is where container queries are preferable.

Container Queries
Section titled Container Queries
CSS Container Queries are a web development feature that allows styles to adapt based on the dimensions and characteristics of a specific container element, rather than the overall viewport. When designing a media player, container queries are useful for creating responsive and adaptive layouts within the player’s container. They enable precise control over the player’s appearance, ensuring it adjusts seamlessly to changes in the container’s size and context.

css
Copy
.player {
  container-name: media-player;
  container-type: inline-size;
}

/* The following are example container breakpoints. */

/* X-Small. */
@container (inline-size < 576px) {
  .controls {
  }
}

/* Small */
@container (576px <= inline-size < 768px) {
}

/* Medium */
@container (768px <= inline-size < 992px) {
}

/* Large */
@container (992px <= inline-size < 1200px) {
}

/* X-Large */
@container (min-width: 1200px) {
}
INFO
See the Container Queries support table as they’re a relatively new feature added to browsers.

Layouts

Default Layout
A guide on how to setup the default audio/video layout and available customization options.

Installation
Section titled Installation
See the installation guide to setup the Default Layout.

Demo
Section titled Demo
You can try out the Default Layout on our player demo page.

Usage
Section titled Usage
The Default Layout ships with support for audio, video, and live streams. You can include both layouts at the same time, only one will be matched depending on the view type.

import { MediaPlayer, MediaProvider } from '@vidstack/react';
import {
  DefaultAudioLayout,
  defaultLayoutIcons,
  DefaultVideoLayout,
} from '@vidstack/react/player/layouts/default';

<MediaPlayer title="..." src="...">
  <MediaProvider />
  <DefaultAudioLayout icons={defaultLayoutIcons} />
  <DefaultVideoLayout icons={defaultLayoutIcons} />
</MediaPlayer>
By default, the view type is inferred from the provider and media type. You can specify the desired type like so:

// Force view type to be audio.
<MediaPlayer src="video.mp4" viewType="audio">
The same is true for the stream type:

// Force stream type to be live.
<MediaPlayer streamType="live">
Color Scheme
Section titled Color Scheme
Both the audio and video layout accept color scheme to be either light or dark themed. By default it will use the user’s preferred color scheme. You can set a specific theme by setting the colorScheme prop on the layout like so:

// System (default)
<DefaultAudioLayout colorScheme="system" />
<DefaultVideoLayout colorScheme="system" />
// Light
<DefaultAudioLayout colorScheme="light" />
<DefaultVideoLayout colorScheme="light" />
// Dark
<DefaultAudioLayout colorScheme="dark" />
<DefaultVideoLayout colorScheme="dark" />
You can also set the color scheme by setting a light or dark class on a parent element:

// Set `light` or `dark` on a parent element.
<html className="light">
  {/* ... */}
  <MediaPlayer>
    {/* ... */}
    <DefaultAudioLayout colorScheme="default" />
    <DefaultVideoLayout colorScheme="default" />
  </MediaPlayer>
</html>
Size
Section titled Size
Both the audio and video layout will adapt to small containers to ensure an optimal user experience. You configure when the small layout is displayed like so:

import { type MediaPlayerQuery } from '@vidstack/react';
import { useCallback } from 'react';

const smallAudioLayoutQuery = useCallback<MediaPlayerQuery>(({ width }) => {
  return width < 576;
}, []);

const smallVideoLayoutQuery = useCallback<MediaPlayerQuery>(({ width, height }) => {
  return width < 576 || height < 380;
}, []);

<DefaultAudioLayout smallLayoutWhen={smallAudioLayoutQuery} />
<DefaultVideoLayout smallLayoutWhen={smallVideoLayoutQuery} />
If you’d like to disable small layouts, set the query to false or 'never':

<DefaultAudioLayout smallLayoutWhen={false} />
<DefaultVideoLayout smallLayoutWhen={false} />
Icons
Section titled Icons
You can easily replace the icons used in the layouts to match the style of your application like so:

tsx
Copy
import type { DefaultLayoutIcons } from '@vidstack/react/player/layouts/default';

// Icon should be: `() => ReactNode`
const None = () => null;

// All icons are optional, replace only what you want.
const customIcons: Partial<DefaultLayoutIcons> = {
  AirPlayButton: {
    Default: None,
    Connecting: None,
    Connected: None,
  },
  GoogleCastButton: {
    Default: None,
    Connecting: None,
    Connected: None,
  },
  PlayButton: {
    Play: None,
    Pause: None,
    Replay: None,
  },
  MuteButton: {
    Mute: None,
    VolumeLow: None,
    VolumeHigh: None,
  },
  CaptionButton: {
    On: None,
    Off: None,
  },
  PIPButton: {
    Enter: None,
    Exit: None,
  },
  FullscreenButton: {
    Enter: None,
    Exit: None,
  },
  SeekButton: {
    Backward: None,
    Forward: None,
  },
  DownloadButton: {
    Default: None,
  },
  Menu: {
    Accessibility: None,
    ArrowLeft: None,
    ArrowRight: None,
    Audio: None,
    AudioBoostUp: None,
    AudioBoostDown: None,
    Chapters: None,
    Captions: None,
    Playback: None,
    Settings: None,
    SpeedUp: None,
    SpeedDown: None,
    QualityUp: None,
    QualityDown: None,
    FontSizeUp: None,
    FontSizeDown: None,
    OpacityUp: None,
    OpacityDown: None,
    RadioCheck: None,
  },
  KeyboardDisplay: {
    Play: None,
    Pause: None,
    Mute: None,
    VolumeUp: None,
    VolumeDown: None,
    EnterFullscreen: None,
    ExitFullscreen: None,
    EnterPiP: None,
    ExitPiP: None,
    CaptionsOn: None,
    CaptionsOff: None,
    SeekForward: None,
    SeekBackward: None,
  },
};
<DefaultAudioLayout icons={customIcons} />;
<DefaultVideoLayout icons={customIcons} />
Thumbnails
Section titled Thumbnails
You can provide thumbnails which will be used to display preview images when interacting with the time slider and in the chapters menu. See the loading thumbnails guide for more information.

<DefaultVideoLayout thumbnails="https://files.vidstack.io/sprite-fight/thumbnails.vtt" />
Language
Section titled Language
Both the audio and video layout support internationalization (i18n) by accepting custom language translations. You can dynamically set the translations property to update the language like so:

ts
Copy
import { type DefaultLayoutTranslations } from '@vidstack/react';

const SPANISH: DefaultLayoutTranslations = {
  'Caption Styles': '',
  'Captions look like this': '',
  'Closed-Captions Off': '',
  'Closed-Captions On': '',
  'Display Background': '',
  'Enter Fullscreen': '',
  'Enter PiP': '',
  'Exit Fullscreen': '',
  'Exit PiP': '',
  'Google Cast': '',
  'Keyboard Animations': '',
  'Seek Backward': '',
  'Seek Forward': '',
  'Skip To Live': '',
  'Text Background': '',
  Accessibility: '',
  AirPlay: '',
  Announcements: '',
  Audio: '',
  Auto: '',
  Boost: '',
  Captions: '',
  Chapters: '',
  Color: '',
  Connected: '',
  Connecting: '',
  Continue: '',
  Default: '',
  Disabled: '',
  Disconnected: '',
  Download: '',
  Family: '',
  Font: '',
  Fullscreen: '',
  LIVE: '',
  Loop: '',
  Mute: '',
  Normal: '',
  Off: '',
  Opacity: '',
  Pause: '',
  PiP: '',
  Play: '',
  Playback: '',
  Quality: '',
  Replay: '',
  Reset: '',
  Seek: '',
  Settings: '',
  Shadow: '',
  Size: '',
  Speed: '',
  Text: '',
  Track: '',
  Unmute: '',
  Volume: '',
};
<DefaultVideoLayout translations={SPANISH} />
CSS Variables
Section titled CSS Variables
The following snippets contain a complete list of CSS variables and their respective default values. They can all be adjusted by you to customize the audio/video layout and child components as desired.

Audio Layout
Section titled Audio Layout
The following variables can be used to specifically customize the audio layout. See the Components section for more.

css
Copy
.vds-audio-layout {
  --audio-brand: #f5f5f5;
  --audio-controls-color: #f5f5f5;
  --audio-focus-ring-color: rgb(78 156 246);
  --audio-focus-ring: 0 0 0 3px var(--media-focus-ring-color);
  --audio-font-family: sans-serif;

  --audio-bg: black;
  --audio-border-radius: 6px;
  --audio-border: 1px solid rgb(255 255 255 / 0.1);

  /* Buttons. */
  --audio-button-size: 40px;

  --audio-play-button-size: 43px;
  --audio-play-button-color: rgb(0 0 0 / 0.8);
  --audio-play-button-bg: var(--media-brand);
  --audio-play-button-border-radius: 100%;
  --audio-caption-button-off-opacity: 0.64;

  /* Sliders. */
  --audio-slider-chapter-title-color: black;
  --audio-slider-value-border: 1px solid rgb(255 255 255 / 0.1);

  --audio-volume-height: 96px;
  --audio-volume-bg: var(--media-menu-bg, rgb(10 10 10));
  --audio-volume-border-radius: 8px;

  /* Menus. */
  --audio-menu-max-height: 320px;

  /* Buffering. */
  --audio-buffering-stripe-color: rgb(0 0 0 / 0.25);
  --audio-buffering-stripe-size: 30px;
  --audio-buffering-stripe-speed: 2s;

  /* Captions. */
  --audio-captions-offset: 4px;
  --audio-captions-bg: transparent;
  --audio-cue-font-size: 14px;
  --audio-cue-color: white;
  --audio-cue-border: 1px solid rgb(255 255 255 / 0.1);
  --audio-cue-bg: black;

  /* Time */
  --audio-time-font-size: 15px;
}
Video Layout
Section titled Video Layout
The following variables can be used to specifically customize the video layout. See the Components section for more.

css
Copy
.vds-video-layout {
  --video-brand: #f5f5f5;
  --video-controls-color: #f5f5f5;
  --video-focus-ring-color: rgb(78 156 246);
  --video-focus-ring: 0 0 0 3px var(--media-focus-ring-color);
  --video-font-family: sans-serif;

  --video-bg: black;
  --video-border-radius: 6px;
  --video-border: 1px solid rgb(255 255 255 / 0.1);

  --video-fullscreen-chapter-title-font-size: 16px;
  --video-gesture-seek-width: 20%;

  /* Load. */
  --video-load-button-size: 56px;
  --video-load-button-border: var(--color);
  --video-load-button-bg: var(--media-brand);
  --video-load-button-border-radius: 100%;

  --video-sm-load-button-size: 48px;

  /* Buttons. */
  --video-sm-button-size: 32px;
  --video-sm-play-button-size: 40px;
  --video-sm-play-button-transform: translateY(25%);
  --video-sm-play-button-bg: rgba(0 0 0 / 0.6);

  /* Sliders. */
  --video-slider-thumbnail-border: 1px solid #f5f5f5;
  --video-slider-thumbnail-border-radius: 2px;
  --video-volume-slider-max-width: 72px;

  --video-sm-slider-focus-track-height: 12px;

  /* Time. */
  --video-time-bg: unset;
  --video-fullscreen-time-font-size: 16px;

  --video-sm-time-font-size: 14px;
  --video-sm-start-duration-bg: rgba(0 0 0 / 0.64);
  --video-sm-start-duration-padding: 3px 6px;
  --video-sm-start-duration-color: var(--video: controls-color);

  /* Captions. */
  --video-captions-offset: 78px;
  --video-captions-transition: bottom 0.15s linear;

  --video-sm-captions-offset: 48px;
  --video-lg-fullscreen-captions-offset: 54px;

  --video-sm-captions-offset: 48px;
}
Components
Section titled Components
css
Copy
.vds-audio-layout,
.vds-video-layout {
  /* Shared. */
  --media-brand: #f5f5f5;
  --media-controls-color: #f5f5f5;
  --media-font-family: sans-serif;

  /* Buffering. */
  --media-buffering-animation: vds-buffering-spin 1s linear infinite;
  --media-buffering-size: 96px;
  --media-buffering-track-color: #f5f5f5;
  --media-buffering-track-fill-color: var(--media-brand);
  --media-buffering-track-fill-offset: 50;
  --media-buffering-track-fill-opacity: 0.75;
  --media-buffering-track-fill-width: 9;
  --media-buffering-track-opacity: 0.25;
  --media-buffering-track-width: 8;
  --media-buffering-transition: opacity 200ms ease;

  /* Buttons. */
  --media-button-border-radius: 8px;
  --media-button-color: var(--media-controls-color, #f5f5f5);
  --media-button-hover-bg: rgb(255 255 255 / 0.2);
  --media-button-hover-transform: scale(1);
  --media-button-hover-transition: transform 0.2s ease-in;
  --media-button-icon-size: 80%;
  --media-button-padding: 0px;
  --media-button-size: 40px;
  --media-button-touch-hover-bg: rgb(255 255 255 / 0.2);
  --media-button-touch-hover-border-radius: 100%;
  --media-sm-fullscreen-button-size: 42px;
  --media-fullscreen-button-size: 42px;

  /* Tooltips. */
  --media-tooltip-bg-color: black;
  --media-tooltip-border-radius: 4px;
  --media-tooltip-border: 1px solid rgb(255 255 255 / 0.1);
  --media-tooltip-color: hsl(0, 0%, 80%);
  --media-tooltip-font-size: 13px;
  --media-tooltip-font-weight: 500;
  --media-tooltip-padding: 2px 8px;
  --media-tooltip-enter-animation: vds-tooltip-enter 0.2s ease-in;
  --media-tooltip-exit-animation: vds-tooltip-exit 0.2s ease-out;

  /* Live Indicator. */
  --media-live-button-bg: #8a8a8a;
  --media-live-button-border-radius: 2px;
  --media-live-button-color: #161616;
  --media-live-button-edge-bg: #dc2626;
  --media-live-button-edge-color: #f5f5f5;
  --media-live-button-font-size: 12px;
  --media-live-button-font-weight: 600;
  --media-live-button-height: 40px;
  --media-live-button-letter-spacing: 1.5px;
  --media-live-button-padding: 1px 4px;
  --media-live-button-width: 40px;

  /* Captions. */
  --media-captions-padding: 1%;
  --media-cue-backdrop: blur(8px);
  --media-cue-bg: rgba(0, 0, 0, 0.7);
  --media-cue-border-radius: 2px;
  --media-cue-border: unset;
  --media-cue-box-shadow: var(--cue-box-shadow);
  --media-cue-color: white;
  --media-cue-display-bg: unset;
  --media-cue-display-border-radius: unset;
  --media-cue-display-padding: unset;
  --media-cue-font-size: calc(var(--overlay-height) / 100 * 4.5);
  --media-cue-line-height: calc(var(--cue-font-size) * 1.2);
  --media-cue-padding-x: calc(var(--cue-font-size) * 0.4);
  --media-cue-padding-x: calc(var(--cue-font-size) * 0.6);

  /* Chapter Title. */
  --media-chapter-title-color: rgba(255 255 255 / 0.64);
  --media-chapter-title-font-size: 14px;
  --media-chapter-title-font-weight: 500;
  --media-chapter-title-separator-color: var(--color);
  --media-chapter-title-separator-gap: 6px;
  --media-chapter-title-separator: '\2022';

  /* Controls. */
  --media-controls-padding: 0px;
  --media-controls-in-transition: opacity 0.2s ease-in;
  --media-controls-out-transition: opacity 0.2s ease-out;

  /* Thumbnails. */
  --media-thumbnail-bg: black;
  --media-thumbnail-border: 1px solid white;
  --media-thumbnail-aspect-ratio: 16 / 9;
  --media-thumbnail-min-width: 120px;
  --media-thumbnail-min-height: calc(var(--media-thumbnail-min-width) / var(--aspect-ratio));
  --media-thumbnail-max-width: 180px;
  --media-thumbnail-max-height: calc(var(--media-thumbnail-max-width) / var(--aspect-ratio));

  /* Time. */
  --media-time-bg: unset;
  --media-time-border-radius: unset;
  --media-time-border: unset;
  --media-time-color: #f5f5f5;
  --media-time-divider-color: #e0e0e0;
  --media-time-divider-gap: 2.5px;
  --media-time-font-size: 14px;
  --media-time-font-weight: 400;
  --media-time-letter-spacing: 0.025em;

  /* Sliders. */
  --media-slider-width: 100%;
  --media-slider-height: 48px;

  /* Slider Thumb. */
  --media-slider-thumb-bg: #fff;
  --media-slider-thumb-border-radius: 9999px;
  --media-slider-thumb-border: 1px solid #cacaca;
  --media-slider-thumb-size: 15px;
  --media-slider-thumb-transition: opacity 0.2s ease-in, box-shadow 0.2s ease;

  /* Slider Tracks. */
  --media-slider-track-width: 100%;
  --media-slider-track-bg: rgb(255 255 255 / 0.3);
  --media-slider-track-border-radius: 1px;
  --media-slider-track-fill-bg: var(--media-brand);
  --media-slider-track-fill-live-bg: #dc2626;
  --media-slider-track-height: 5px;
  --media-slider-track-progress-bg: rgb(255 255 255 / 0.5);
  --media-slider-focused-thumb-shadow: 0 0 0 4px hsla(0, 0%, 100%, 0.4);
  --media-slider-focused-thumb-size: calc(var(--thumb-size) * 1.1);
  --media-slider-focused-track-height: calc(var(--track-height) * 1.25);
  --media-slider-focused-track-height: var(--track-height);
  --media-slider-focused-track-width: calc(var(--track-width) * 1.25);
  --media-slider-focused-track-width: var(--track-width);

  /* Slider Steps. */
  --media-slider-step-width: 2.5px;
  --media-slider-step-color: rgb(124, 124, 124);

  /* Slider Chapter. */
  --media-slider-chapter-hover-transform: scaleY(2);
  --media-slider-chapter-hover-transition: transform 0.1s cubic-bezier(0.4, 0, 1, 1);

  /* Slider Preview. */
  --media-slider-preview-bg: unset;
  --media-slider-preview-border-radius: 2px;

  /* Slider Chapter Title. */
  --media-slider-chapter-title-bg: unset;
  --media-slider-chapter-title-color: #f5f5f5;
  --media-slider-chapter-title-font-size: 14px;
  --media-slider-chapter-title-gap: 6px;

  /* Slider Value. */
  --media-slider-value-bg: black;
  --media-slider-value-border-radius: 2px;
  --media-slider-value-border: unset;
  --media-slider-value-color: white;
  --media-slider-value-gap: 0px;
  --media-slider-value-padding: 1px 10px;

  /* Menu Theme. */
  --media-menu-color-gray-50: rgb(245 245 245 / 0.1);
  --media-menu-color-gray-100: rgb(245 245 245 / 0.45);
  --media-menu-color-gray-200: rgb(10 10 10 / 0.6);
  --media-menu-color-gray-300: rgb(27 27 27);

  /* Menu Text. */
  --media-menu-text-color: #f5f5f5;
  --media-menu-text-secondary-color: #6b6b6b;

  /* Menu. */
  --media-menu-bg: var(--media-menu-bg, var(--color-gray-400));
  --media-menu-border-radius: 4px;
  --media-menu-border: 1px solid rgb(255 255 255 / 0.1);
  --media-menu-box-shadow: 1px 1px 1px rgb(10 10 10 / 0.5);
  --media-menu-divider: 1px solid var(--color-gray-50);
  --media-menu-font-size: 14px;
  --media-menu-font-weight: 500;
  --media-menu-max-height: 250px;
  --media-menu-min-width: 220px;
  --media-menu-padding: 12px;
  --media-menu-top-bar-bg: rgb(10 10 10 / 0.6);
  --media-menu-arrow-icon-size: 18px;
  --media-menu-icon-rotate-deg: 90deg;

  --media-menu-enter-animation: vds-menu-enter 0.3s ease-out;
  --media-menu-exit-animation: vds-menu-exit 0.2s ease-out;

  --media-menu-scrollbar-track-bg: transparent;
  --media-menu-scrollbar-thumb-bg: var(--color-gray-50);

  --media-sm-menu-landscape-max-height: min(70vh, 400px);
  --media-sm-menu-portrait-max-height: 40vh;

  /* Menu Section. */
  --media-menu-section-bg: var(--color-gray-300);
  --media-menu-section-border: unset;
  --media-menu-section-divider: var(--divider);
  --media-menu-section-header-font-size: 12px;
  --media-menu-section-header-font-weight: 500;
  --media-menu-section-gap: 8px;
  --media-menu-section-border-radius: 2px;

  /* Menu Item. */
  --media-menu-item-bg: transparent;
  --media-menu-item-border-radius: 2px;
  --media-menu-item-border: 0;
  --media-menu-item-height: 40px;
  --media-menu-item-hover-bg: var(--color-gray-50);
  --media-menu-item-icon-size: 18px;
  --media-menu-item-icon-spacing: 6px;
  --media-menu-item-padding: 10px;

  /* Menu Radio. */
  --media-menu-radio-icon-color: var(--text-color);

  /* Menu Checkbox. */
  --media-menu-checkbox-width: 40px;
  --media-menu-checkbox-height: 18px;
  --media-menu-checkbox-bg-active: #1ba13f;
  --media-menu-checkbox-bg: var(--color-gray-100);
  --media-menu-checkbox-handle-bg: #f5f5f5;
  --media-menu-checkbox-handle-border: unset;
  --media-menu-checkbox-handle-diameter: calc(var(--checkbox-height) - 2px);

  /* Menu Slider. */
  --media-menu-slider-height: 32px;
  --media-menu-slider-track-bg: var(--color-gray-50);
  --media-menu-slider-track-fill-bg: var(--color-inverse);

  /* Menu Hint. */
  --media-menu-hint-color: var(--text-secondary-color);
  --media-menu-hint-font-size: 13px;
  --media-menu-hint-font-weight: 400;

  /* Chapters Menu. */
  --media-chapters-divider: var(--divider);
  --media-chapters-duration-bg: unset;
  --media-chapters-duration-border-radius: 2px;
  --media-chapters-focus-padding: 4px;
  --media-chapters-item-active-bg: var(--color-gray-50);
  --media-chapters-item-active-border-left: unset;
  --media-chapters-min-width: var(--media-menu-min-width, 220px);
  --media-chapters-padding: 0;
  --media-chapters-progress-bg: var(--color-inverse);
  --media-chapters-progress-border-radius: 0;
  --media-chapters-progress-height: 4px;
  --media-chapters-start-time-border-radius: 2px;
  --media-chapters-start-time-letter-spacing: 0.4px;
  --media-chapters-start-time-padding: 1px 4px;
  --media-chapters-thumbnail-border: 0;
  --media-chapters-thumbnail-gap: 12px;
  --media-chapters-thumbnail-max-height: 68px;
  --media-chapters-thumbnail-max-width: 120px;
  --media-chapters-thumbnail-min-height: 56px;
  --media-chapters-thumbnail-min-width: 100px;
  --media-chapters-time-font-size: 12px;
  --media-chapters-time-font-weight: 500;
  --media-chapters-time-gap: 6px;
  --media-chapters-with-thumbnails-min-width: 300px;
}
Slots
Section titled Slots
Audio Layout
Section titled Audio Layout
The slots prop can be used to insert or replace content inside the DefaultAudioLayout. You can find all available slot positions below.

tsx
Copy
<DefaultAudioLayout
  slots={{
    beforePlayButton: null,
    // Accepts a `ReactNode`, setting the slot to `null` will remove it.
    playButton: CustomPlayButton,
    afterPlayButton: null,
    // 72 other slots positions...
  }}
/>
Video Layout
Section titled Video Layout
The slots prop can be used to insert or replace content inside the DefaultVideoLayout. You can find all available slot positions below.

tsx
Copy
<DefaultVideoLayout
  slots={{
    beforePlayButton: null,
    // Accepts a `ReactNode`, setting the slot to `null` will remove it.
    playButton: CustomPlayButton,
    afterPlayButton: null,
    // 72 other slots positions...
  }}
/>
Content can be slotted inside specific video layout sizes like so:

tsx
Copy
<DefaultVideoLayout
  slots={{
    smallLayout: {
      playButton: CustomPlayButton,
      // ...
    },
    largeLayout: {
      // ...
    },
  }}
/>
Positions
Section titled Positions
The following slot positions are available for inserting or replacing content:

bufferingIndicator
captionButton
captions
title
chapterTitle
currentTime
endTime
fullscreenButton
liveButton
livePlayButton
muteButton
pipButton
airPlayButton
googleCastButton
playButton
loadButton
seekBackwardButton
seekForwardButton
startDuration
timeSlider
volumeSlider
chaptersMenu
settingsMenu
settingsMenuItemsStart
settingsMenuItemsEnd
playbackMenuItemsStart
playbackMenuItemsEnd
playbackMenuLoop
accessibilityMenuItemsStart
accessibilityMenuItemsEnd
audioMenuItemsStart
audioMenuItemsEnd
captionsMenuItemsStart
captionsMenuItemsEnd
INFO
Any slot position can be prefixed with either before or after to insert content before or after that position. For example, afterCaptionButton will insert content after the caption button.

API Reference Section titled API Reference
DefaultAudioLayout Section titled DefaultAudioLayout
RefAttributes
The audio layout is our production-ready UI that's displayed when the media view type is set to 'audio'. It includes support for audio tracks, slider chapters, captions, live streams and more out of the box.

import { DefaultAudioLayout } from "@vidstack/react/player/layouts/plyr";
<MediaPlayer src="audio.mp3">
  <MediaProvider />
  <DefaultAudioLayout icons={defaultLayoutIcons} />
</MediaPlayer>
Props Section titled Props
Prop	Type	Default
children
Show children description
ReactNode
Show more info
null
icons
Show icons description
DefaultLayoutIcons
undefined
colorScheme
Show colorScheme description
string
Show more info
undefined
download
Show download description
FileDownloadInfo
Show more info
undefined
showTooltipDelay
Show showTooltipDelay description
number
700
showMenuDelay
Show showMenuDelay description
number
0
hideQualityBitrate
Show hideQualityBitrate description
boolean
false
smallLayoutWhen
Show smallLayoutWhen description
boolean
Show more info
`({ width, height }) => width < 576 || height < 380`
thumbnails
Show thumbnails description
ThumbnailSrc
Show more info
undefined
translations
Show translations description
Partial<DefaultLayoutTranslations>
Show more info
undefined
menuContainer
Show menuContainer description
string
Show more info
`document.body`
menuGroup
Show menuGroup description
string
Show more info
undefined
noAudioGain
Show noAudioGain description
boolean
undefined
audioGains
Show audioGains description
object
Show more info
undefined
noModal
Show noModal description
boolean
undefined
noScrubGesture
Show noScrubGesture description
boolean
undefined
sliderChaptersMinWidth
Show sliderChaptersMinWidth description
number
undefined
disableTimeSlider
Show disableTimeSlider description
boolean
undefined
noGestures
Show noGestures description
boolean
undefined
noKeyboardAnimations
Show noKeyboardAnimations description
boolean
undefined
playbackRates
Show playbackRates description
object
Show more info
undefined
seekStep
Show seekStep description
number
undefined
slots
Show slots description
DefaultAudioLayoutSlots
undefined
asChild
Show asChild description
boolean
false
Data Attributes Section titled Data Attributes
/* Example. */
.component[data-foo] {}
Name	Description
data-match
Whether this layout is being used.
data-sm
The small layout is active
data-lg
The large layout is active.
data-size
The active layout size (sm or lg).
DefaultVideoLayout Section titled DefaultVideoLayout
RefAttributes
The video layout is our production-ready UI that's displayed when the media view type is set to 'video'. It includes support for picture-in-picture, fullscreen, slider chapters, slider previews, captions, audio/quality settings, live streams, and more out of the box.

import { DefaultVideoLayout } from "@vidstack/react/player/layouts/plyr";
<MediaPlayer src="video.mp4">
  <MediaProvider />
  <DefaultVideoLayout thumbnails="/thumbnails.vtt" icons={defaultLayoutIcons} />
</MediaPlayer>
Props Section titled Props
Prop	Type	Default
children
Show children description
ReactNode
Show more info
null
icons
Show icons description
Default LayoutIcons
undefined
colorScheme
Show color Scheme description
string
Show more info
undefined
download
Show download description
FileDownloadInfo
Show more info
undefined
showTooltipDelay
Show showTooltip Delay description
number
700
showMenuDelay
Show showMenuDelay description
number
0
hideQuality Bitrate
Show hideQuality Bitrate description
boolean
false
smallLayout When
Show smallLayout When description
boolean
Show more info
`({ width, height }) => width < 576 || height < 380`
thumbnails
Show thumbnails description
ThumbnailSrc
Show more info
undefined
translations
Show translations description
Partial


Show more info
undefined
menuContainer
Show menuContainer description
string
Show more info

`document.body`
menuGroup
Show menuGroup description
string
Show more info
undefined
noAudioGain
Show noAudioGain description
boolean
undefined
audioGains
Show audioGains description
object
Show more info
undefined
noModal
Show noModal description
boolean
undefined
noScrubGesture
Show noScrubGesture description
boolean
undefined
sliderChaptersMinWidth
Show sliderChaptersMinWidth description
number
undefined
disableTimeSlider
Show disableTimeSlider description
boolean
undefined
noGestures
Show noGestures description
boolean
undefined
noKeyboardAnimations
Show noKeyboardAnimations description
boolean
undefined
playbackRates
Show playbackRates description
object
Show more info
undefined
seekStep
Show seekStep description
number
undefined
slots
Show slots description
DefaultVideoLayoutSlots
undefined
asChild
Show asChild description
boolean
false
Data Attributes Section titled Data Attributes
/* Example. */
.component[data-foo] {}
Name	Description
data-match
Whether this layout is being used.
data-sm
The small layout is active
data-lg
The large layout is active.
data-size
The active layout size (sm or lg).
