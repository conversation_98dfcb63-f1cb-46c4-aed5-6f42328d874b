# Fluent Community Integration
Mindstate.live/Mlive = Fcom/Fluent Community, same thing
Also use Mlive_Hybrid_Community_integration.md
## Core Architecture
Backend: WP/PHP REST API
- No Vue in admin
- No build tools
- Native WP admin

Frontend: Vue 3 Portal
- Element Plus UI
- Portal API only
- State management

## Integration Matrix

### WP Backend ✅
API:
- REST endpoints
- Auth middleware
- API versioning
- Controllers

Data:
- Models only
- Cache layers
- No direct SQL
- Use relations

Hooks:
- Actions/filters
- Event system
- Error handling
- Priorities

Security:
- Capabilities
- Validation
- CSRF/Nonce
- Sanitize

### Portal Frontend ✅
UI:
- Element Plus
- Portal layouts
- Portal widgets
- Slots

State:
- Vuex store
- Mutations
- Modules
- Persist

Routes:
- Vue Router
- Event bus
- Guards
- Auth

## Prohibited ❌

### Never Use
Templates:
- `get_template_part`
- `locate_template`
- Theme system
- `wp_head/footer`

Data:
- Direct `$wpdb`
- `WP_Query`
- Meta functions
- Raw queries

JS:
- Global scope
- Direct DOM
- jQuery
- Build tools

Security:
- Auth bypass
- Direct DB
- Raw SQL
- Local tokens

## Required ✓

### Must Use
API:
- Portal API
- REST patterns
- Error handling
- Caching

State:
- Vuex only
- Mutations
- Async handlers
- Persistence

Auth:
- Token check
- Role verify
- Route guards
- Capabilities

Performance:
- Lazy load
- Component cache
- API cache
- State clean

## Best Practices

### Code
- Error handle
- Standards

### Security
- Validate input
- Escape output
- CSRF tokens
- Role checks

### Speed
- Cache data
- Optimize
- Lazy load
- Monitor

### Maintain
- Version
- Document
- Log errors