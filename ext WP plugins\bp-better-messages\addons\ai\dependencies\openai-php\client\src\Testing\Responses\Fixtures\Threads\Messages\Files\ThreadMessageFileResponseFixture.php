<?php
/**
 * @license MIT
 *
 * Modified by __root__ on 08-April-2024 using {@see https://github.com/BrianHenryIE/strauss}.
 */

namespace BetterMessages\OpenAI\Testing\Responses\Fixtures\Threads\Messages\Files;

final class ThreadMessageFileResponseFixture
{
    public const ATTRIBUTES = [
        'id' => 'file-DhxjnFCaSHc4ZELRGKwTMFtI',
        'object' => 'thread.message.file',
        'created_at' => 1_699_624_660,
        'message_id' => 'msg_KNsDDwE41BUAHhcPNpDkdHWZ',
    ];
}
