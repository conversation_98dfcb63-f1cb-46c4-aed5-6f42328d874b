<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace BetterMessages\Symfony\Component\HttpClient\Chunk;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
class FirstChunk extends DataChunk
{
    public function isFirst(): bool
    {
        return true;
    }
}
