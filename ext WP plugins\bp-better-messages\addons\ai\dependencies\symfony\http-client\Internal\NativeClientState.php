<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace BetterMessages\Symfony\Component\HttpClient\Internal;

/**
 * Internal representation of the native client's state.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class NativeClientState extends ClientState
{
    public int $id;
    public int $maxHostConnections = \PHP_INT_MAX;
    public int $responseCount = 0;
    /** @var string[] */
    public array $dnsCache = [];
    public bool $sleep = false;
    /** @var int[] */
    public array $hosts = [];

    public function __construct()
    {
        $this->id = random_int(\PHP_INT_MIN, \PHP_INT_MAX);
    }

    public function reset(): void
    {
        $this->responseCount = 0;
        $this->dnsCache = [];
        $this->hosts = [];
    }
}
