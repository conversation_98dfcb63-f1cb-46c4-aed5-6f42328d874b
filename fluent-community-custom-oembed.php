<?php
/**
 * Plugin Name: Fluent Community Custom oEmbed Support
 * Description: Adds oEmbed support for Rumble, Facebook videos/reels, and Instagram videos/reels in Fluent Community portal feed
 * Version: 1.0.0
 * Author: Mindstate.live
 * Text Domain: fluent-community-custom-oembed
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) exit;

class Fluent_Community_Custom_oEmbed {
    
    /**
     * Facebook/Instagram App credentials
     * Replace with your actual credentials
     */
    private $fb_app_id = 'YOUR_APP_ID';
    private $fb_app_secret = 'YOUR_APP_SECRET';
    
    /**
     * Constructor - Hook into WordPress
     */
    public function __construct() {
        // Register oEmbed providers
        add_action( 'init', array( $this, 'register_oembed_providers' ) );
        
        // Enhance oEmbed data
        add_filter( 'oembed_dataparse', array( $this, 'enhance_oembed_html' ), 10, 3 );
        add_filter( 'oembed_result', array( $this, 'filter_oembed_html' ), 10, 3 );
        
        // Add custom remote URL parser for Fluent Community
        add_filter( 'fluent_community/remote_url_parser', array( $this, 'custom_remote_url_parser' ), 10, 2 );
        
        // Add script to improve embeds in feed display
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_embed_scripts' ) );
        
        // Add support for custom embeds in RemoteUrlParser
        add_filter( 'fluent_community/pre_remote_parse_url', array( $this, 'custom_remote_parse' ), 10, 2 );
    }
    
    /**
     * Register custom oEmbed providers with WordPress
     */
    public function register_oembed_providers() {
        // Register Rumble
        wp_oembed_add_provider( 
            '#https?://(?:www\.)?rumble\.com/.*#i', 
            'https://rumble.com/api/Media/oembed.json', 
            true 
        );
        
        // Only register Facebook & Instagram if we have credentials
        if ( $this->fb_app_id && $this->fb_app_secret && $this->fb_app_id !== 'YOUR_APP_ID' ) {
            $fb_token = $this->fb_app_id . '|' . $this->fb_app_secret;
            
            // Facebook videos
            wp_oembed_add_provider( 
                '#https?://(www\.)?facebook\.com/.*/videos/.*#i', 
                'https://graph.facebook.com/v18.0/oembed_video?access_token=' . $fb_token, 
                true 
            );
            
            // Facebook posts and reels
            wp_oembed_add_provider( 
                '#https?://(www\.)?facebook\.com/.*/posts/.*#i', 
                'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $fb_token, 
                true 
            );
            
            wp_oembed_add_provider( 
                '#https?://(www\.)?facebook\.com/reel/.*#i', 
                'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $fb_token, 
                true 
            );
            
            // Instagram
            wp_oembed_add_provider( 
                '#https?://(www\.)?instagram\.com/p/.*#i', 
                'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $fb_token, 
                true 
            );
            
            wp_oembed_add_provider( 
                '#https?://(www\.)?instagram\.com/reel/.*#i', 
                'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $fb_token, 
                true 
            );
        }
    }
    
    /**
     * Enhance oEmbed HTML output with additional data
     */
    public function enhance_oembed_html( $html, $data, $url ) {
        if ( !is_object( $data ) ) {
            return $html;
        }
        
        // Check URL for provider type
        $provider_type = $this->get_provider_type($url);
        
        if ( $provider_type ) {
            // Add a wrapper div with provider information
            $html = sprintf(
                '<div class="fcom-custom-embed fcom-embed-%s">%s</div>',
                sanitize_html_class( $provider_type ),
                $html
            );
        }
        
        return $html;
    }
    
    /**
     * Filter oEmbed HTML to ensure it displays properly
     */
    public function filter_oembed_html( $html, $url, $args ) {
        // Make embeds responsive
        if ( strpos( $html, '<iframe' ) !== false ) {
            // Ensure iframe has appropriate attributes
            if ( strpos( $html, 'loading=' ) === false ) {
                $html = str_replace( '<iframe', '<iframe loading="lazy"', $html );
            }
            
            // Add title attribute if missing
            if ( strpos( $html, 'title=' ) === false ) {
                $provider_type = $this->get_provider_type( $url );
                $title = $provider_type ? ucfirst( $provider_type ) . ' embed' : 'Media embed';
                $html = str_replace( '<iframe', '<iframe title="' . esc_attr( $title ) . '"', $html );
            }
            
            // Add responsive wrapper if not already present
            if ( strpos( $html, 'wp-embed-responsive' ) === false && strpos( $html, 'fcom-embed-responsive' ) === false ) {
                $html = '<div class="fcom-embed-responsive">' . $html . '</div>';
            }
        }
        
        return $html;
    }
    
    /**
     * Parse URL to get provider type
     */
    private function get_provider_type( $url ) {
        if ( strpos( $url, 'rumble.com' ) !== false ) {
            return 'rumble';
        }
        elseif ( strpos( $url, 'facebook.com' ) !== false ) {
            if ( strpos( $url, '/videos/' ) !== false ) {
                return 'facebook-video';
            }
            elseif ( strpos( $url, '/reel/' ) !== false ) {
                return 'facebook-reel';
            }
            return 'facebook';
        }
        elseif ( strpos( $url, 'instagram.com' ) !== false ) {
            if ( strpos( $url, '/reel/' ) !== false ) {
                return 'instagram-reel';
            }
            return 'instagram';
        }
        
        return false;
    }
    
    /**
     * Add custom embedding scripts
     */
    public function enqueue_embed_scripts() {
        // Only enqueue on pages with Fluent Community
        if ( function_exists( 'is_fluent_community_page' ) && is_fluent_community_page() ) {            wp_enqueue_script(
                'fcom-custom-embeds',
                plugin_dir_url( __FILE__ ) . '../js/custom-embeds.js',
                array( 'jquery' ),
                '1.0.0',
                true
            );
            
            // Add CSS for responsive embeds
            wp_add_inline_style( 'fluent-community-main', '
                .fcom-embed-responsive {
                    position: relative;
                    padding-bottom: 56.25%; /* 16:9 aspect ratio */
                    height: 0;
                    overflow: hidden;
                    max-width: 100%;
                }
                .fcom-embed-responsive iframe,
                .fcom-embed-responsive object,
                .fcom-embed-responsive embed {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }
            ' );
        }
    }
    
    /**
     * Add custom remote URL parser for Fluent Community
     */
    public function custom_remote_url_parser( $parsed_data, $url ) {
        if ( is_wp_error( $parsed_data ) || empty( $parsed_data ) ) {
            $provider_type = $this->get_provider_type( $url );
            
            if ( $provider_type ) {
                // Try to get oEmbed data directly
                $oembed = $this->fetch_oembed_data( $url );
                
                if ( $oembed && !is_wp_error( $oembed ) ) {
                    return $this->format_oembed_data( $oembed, $url, $provider_type );
                }
            }
        }
        
        return $parsed_data;
    }
    
    /**
     * Custom implementation for RemoteUrlParser to handle our providers
     */
    public function custom_remote_parse( $pre_parse, $url ) {
        $provider_type = $this->get_provider_type( $url );
        
        if ( $provider_type ) {
            $oembed = $this->fetch_oembed_data( $url );
            
            if ( $oembed && !is_wp_error( $oembed ) ) {
                return $this->format_oembed_data( $oembed, $url, $provider_type );
            }
        }
        
        return $pre_parse;
    }
    
    /**
     * Format oEmbed data for Fluent Community
     */
    private function format_oembed_data( $oembed, $url, $provider_type ) {
        // Basic data structure for Fluent Community
        $data = array(
            'title'        => isset( $oembed->title ) ? $oembed->title : '',
            'author_name'  => isset( $oembed->author_name ) ? $oembed->author_name : '',
            'type'         => 'oembed',
            'provider'     => $this->get_provider_name( $provider_type ),
            'content_type' => $this->get_content_type( $provider_type ),
            'url'          => $url,
            'html'         => isset( $oembed->html ) ? $oembed->html : '',
            'image'        => isset( $oembed->thumbnail_url ) ? $oembed->thumbnail_url : '',
        );
        
        // Add additional data if available
        if ( isset( $oembed->width ) ) {
            $data['width'] = $oembed->width;
        }
        
        if ( isset( $oembed->height ) ) {
            $data['height'] = $oembed->height;
        }
        
        if ( isset( $oembed->description ) ) {
            $data['description'] = $oembed->description;
        } elseif ( isset( $oembed->title ) ) {
            $data['description'] = $oembed->title;
        }
        
        return array_filter( $data );
    }
    
    /**
     * Get provider display name
     */
    private function get_provider_name( $provider_type ) {
        switch ( $provider_type ) {
            case 'rumble':
                return 'Rumble';
            case 'facebook-video':
            case 'facebook-reel':
            case 'facebook':
                return 'Facebook';
            case 'instagram-reel':
            case 'instagram':
                return 'Instagram';
            default:
                return ucfirst( $provider_type );
        }
    }
    
    /**
     * Get content type based on provider
     */
    private function get_content_type( $provider_type ) {
        if ( strpos( $provider_type, '-video' ) !== false ) {
            return 'video';
        }
        elseif ( strpos( $provider_type, '-reel' ) !== false ) {
            return 'reel';
        }
        elseif ( $provider_type === 'rumble' ) {
            return 'video';
        }
        
        return 'embed';
    }
    
    /**
     * Fetch oEmbed data directly
     */
    private function fetch_oembed_data( $url ) {
        $oembed_url = false;
        
        // Determine oEmbed endpoint based on URL
        if ( strpos( $url, 'rumble.com' ) !== false ) {
            $oembed_url = add_query_arg( array(
                'url' => urlencode( $url )
            ), 'https://rumble.com/api/Media/oembed.json' );
        }
        elseif ( strpos( $url, 'facebook.com' ) !== false && $this->fb_app_id && $this->fb_app_secret ) {
            $fb_token = $this->fb_app_id . '|' . $this->fb_app_secret;
            
            if ( strpos( $url, '/videos/' ) !== false ) {
                $oembed_url = add_query_arg( array(
                    'url' => urlencode( $url ),
                    'access_token' => $fb_token
                ), 'https://graph.facebook.com/v18.0/oembed_video' );
            } else {
                $oembed_url = add_query_arg( array(
                    'url' => urlencode( $url ),
                    'access_token' => $fb_token
                ), 'https://graph.facebook.com/v18.0/oembed_post' );
            }
        }
        elseif ( strpos( $url, 'instagram.com' ) !== false && $this->fb_app_id && $this->fb_app_secret ) {
            $fb_token = $this->fb_app_id . '|' . $this->fb_app_secret;
            
            $oembed_url = add_query_arg( array(
                'url' => urlencode( $url ),
                'access_token' => $fb_token
            ), 'https://graph.facebook.com/v18.0/instagram_oembed' );
        }
        
        // If we have an oEmbed URL, fetch the data
        if ( $oembed_url ) {
            $response = wp_remote_get( $oembed_url );
            
            if ( !is_wp_error( $response ) && wp_remote_retrieve_response_code( $response ) === 200 ) {
                $body = wp_remote_retrieve_body( $response );
                return json_decode( $body );
            }
        }
        
        return false;
    }
}

// Initialize the plugin
new Fluent_Community_Custom_oEmbed();
