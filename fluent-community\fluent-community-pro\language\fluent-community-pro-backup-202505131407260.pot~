#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: FluentCommunity Pro\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-09 14:25+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.11; wp-6.6.2\n"
"X-Domain: fluent-community-pro"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:222
#, php-format
msgid " %s Integration"
msgstr ""

#. %s is the number of other people
#: app/Hooks/Handlers/ModerationHandler.php:261
#: app/Hooks/Handlers/ModerationHandler.php:331
#, php-format
msgid " and %s other people"
msgstr ""

#. %1$s is the user name, %2$s is the comment title
#: app/Hooks/Handlers/ModerationHandler.php:268
#, php-format
msgid "%1$s flagged a comment for your review: %2$s"
msgstr ""

#. %1$s is the user name, %2$s is the feed title
#: app/Hooks/Handlers/ModerationHandler.php:338
#, php-format
msgid "%1$s flagged a post %2$s for your review"
msgstr ""

#. %1$s is the user name, %2$s is the feed title  and %3$s space title
#: app/Hooks/Handlers/ModerationHandler.php:346
#, php-format
msgid "%1$s flagged a post %2$s for your review in %3$s"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:193
msgid "[FluentCommunity] Add Badge to Profile"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:168
msgid "[FluentCommunity] Add to Course"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:144
msgid "[FluentCommunity] Add to Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:201
msgid "[FluentCommunity] Remove Badge from Profile"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:153
msgid "[FluentCommunity] Remove from a Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:176
msgid "[FluentCommunity] Remove from Course"
msgstr ""

#. %1$s is the comment title
#: app/Hooks/Handlers/ModerationHandler.php:244
#, php-format
msgid "A comment has been automatically flagged for review: %1$s"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:69
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:65
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:58
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:52
msgid ""
"A new WordPress user will be created if the contact does not have a connect "
"WP User."
msgstr ""

#. %1$s is the feed title
#: app/Hooks/Handlers/ModerationHandler.php:315
#, php-format
msgid "A post has been automatically flagged for review: %1$s"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:23
msgid "Activate or Block a user from a Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:43
msgid "Active (Can access to the portal)"
msgstr ""

#: app/Services/Analytics/Members.php:29
msgid "Active Members"
msgstr ""

#: app/Services/Analytics/Spaces.php:81 app/Services/Analytics/Spaces.php:98
#: app/Services/Analytics/Members.php:46
msgid "Activity"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:26
msgid "Add Badge To User"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:27
msgid "Add Badge to user's profile"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:50
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:40
msgid "Add Badges to the user profile"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:49
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:39
msgid "Add Badges to user profile"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:26
msgid "Add or Remove Verification Sign (Blue Badge) To User"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:27
msgid "Add or Remove Verification Sign (Blue Badge) user's profile"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:195
msgid "Add Selected Badges to users"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:170
msgid "Add to selected Course"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:146
msgid "Add to selected Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:26
#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:59
#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:34
msgid "Add to Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:27
msgid "Add user to a space"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:43
msgid "Add user to the selected Courses"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:27
msgid "Add user to the selected courses"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:35
msgid "Add user to the selected Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:60
msgid "Add user to the selected spaces"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:48
msgid "Add Verification Sign (Blue Badge)"
msgstr ""

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:94
msgid "All time"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:135
msgid "Allow the user login automatically after Form submission"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:173
msgid "Allow this integration conditionally based on your submission values"
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:46
msgid "An Automated double-opt-in email will be sent for new subscribers"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:47
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:46
msgid "An Automated double-optin email will be sent for new subscribers"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:97
msgid ""
"Associate your user fields to the appropriate Paymattic fields by selecting "
"the appropriate form field from the list."
msgstr ""

#: app/Http/Controllers/ProAdminController.php:376
msgid "Auth settings have been updated successfully"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:134
msgid "Auto Login & Password Reset Email (For New Users Only)"
msgstr ""

#: app/Modules/UserBadge/Controllers/UserBadgeController.php:60
msgid "Badges saved successfully"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:51
msgid "Blocked (Can not access to the portal)"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:22
msgid "Change Space Membership Status"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:45
msgid "Check Update"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:87
msgid "Choose options"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:351
msgid "click here"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:104
msgid "Click here to activate"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:353
msgid "Click Here to purchase another license"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:370
msgid "Click Here to Renew Your License"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:355
msgid "Color configuration has been updated successfully"
msgstr ""

#. %s is the comment excerpt (max 30 chars)
#: app/Hooks/Handlers/ModerationHandler.php:416
#, php-format
msgid "Comment flagged for review: %s"
msgstr ""

#: app/Services/Analytics/Spaces.php:149 app/Services/Analytics/Spaces.php:201
#: app/Services/Analytics/Overview.php:32
#: app/Services/Analytics/Overview.php:42
#: app/Services/Analytics/AnalyticsService.php:35
msgid "Comments"
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:596
#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:25
#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:25
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:24
#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:23
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:24
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:25
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:25
#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:23
#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:21
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:25
msgid "Community"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:172
msgid "Conditional Logics"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:35
msgid "Connect Paymattic with Fluent Community"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:67
msgid "Course Enrollment"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:459
msgid "CRM Profile"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:442
#: app/Http/Controllers/ProAdminController.php:473
msgid "CRM Tagging settings have been updated successfully"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:214
msgid "Current Course Title"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:215
msgid "Current Course Title with Hyperlink"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:220
msgid "Current Space Title"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:221
msgid "Current Space Title with Hyperlink"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:43
msgid "Docs"
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:214
#: app/Modules/DocumentLibrary/DocumentModule.php:256
msgid "Document not found"
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:146
msgid "Document title is required"
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:181
msgid "Document updated successfully"
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:56
msgid "Documents"
msgstr ""

#: app/Modules/Webhooks/WebhookModule.php:47
msgid "Email is required"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:26
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:42
msgid "Enroll to Courses"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:205
msgid "Enrolled Course Names (Comma Separated)"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:206
msgid "Enrolled Course Names with links (list)"
msgstr ""

#: app/Services/PluginManager/Updater.php:342
msgid "Error"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:149
msgid ""
"Error when contacting with license server. Please check that your server "
"have curl installed"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:369
msgid "expired at"
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:143
msgid "First post requires approval"
msgstr ""

#: app/Hooks/Handlers/CoreDepenedencyHandler.php:12
#: app/Hooks/Handlers/CoreDepenedencyHandler.php:13
#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:49
msgid "FluentCommunity"
msgstr ""

#. Name of the plugin
msgid "FluentCommunity Pro"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:105
msgid "For new users"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:106
msgid "For New Users - Data mapping"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:100
msgid "Form Field"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:271
msgid "Friday"
msgstr ""

#: app/Modules/Giphy/Http/Controllers/GiphyController.php:20
msgid ""
"Giphy API key is not set or invalid. Please set a valid API key in the giphy "
"module."
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:137
msgid "Global moderation settings require review"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:44
msgid "Help & Support"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:216
msgid "HTTP Link of the current course"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:222
msgid "HTTP Link of the current Space"
msgstr ""

#. URI of the plugin
#. Author URI of the plugin
msgid "https://fluentcommunity.co"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:81
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:71
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:71
msgid "If Contact Already Exist?"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:75
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:71
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:64
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:58
msgid ""
"If you enable this, . The newly created user will get the welcome email send "
"by WordPress to with the login info & password reset link"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:61
msgid ""
"If you enable this, existing badges will be replaced from the user profile"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:98
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:88
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:88
msgid ""
"If you enable this, it will restart the automation for a contact even if "
"they are already in the automation. Otherwise, it will skip if the contact "
"already exists."
msgstr ""

#: app/Http/Controllers/ProAdminController.php:323
msgid "Invalid color schema selected"
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:266
msgid "Invalid document source"
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:91
msgid "Invalid documents found to save. Please try again"
msgstr ""

#: app/Hooks/Handlers/CoreDepenedencyHandler.php:90
msgid "Invalid plugin or file mods are disabled."
msgstr ""

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:89
msgid "Last 30 days"
msgstr ""

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:84
msgid "Last 7 days"
msgstr ""

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:186
msgid "Leaderboard configuration has been updated."
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:82
msgid "Leave blank to run for any courses"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:92
msgid "Leave blank to run for any level upgrade"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:82
msgid "Leave blank to run for any Spaces"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:25
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:34
msgid "Left from a Space"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:369
msgid "license has been"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:474
msgid "Lifetime Value"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:486
msgid "Lists"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:394
msgid "Lockscreen settings have been updated successfully."
msgstr ""

#: app/Http/Controllers/ProAdminController.php:78
msgid "Manager has been updated successfully"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:96
msgid "Map Fields"
msgstr ""

#: app/Services/Analytics/Spaces.php:153 app/Services/Analytics/Overview.php:30
#: app/Services/Analytics/Overview.php:46
#: app/Services/Analytics/AnalyticsService.php:39
msgid "Members"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:200
msgid "Membership Space Names (Comma Separated)"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:201
msgid "Membership Space Names with links (list)"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:602
msgid "Messaging settings have been updated successfully"
msgstr ""

#: app/Http/Controllers/ModerationController.php:244
msgid "Moderation config saved successfully"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:267
msgid "Monday"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:79
msgid "Name"
msgstr ""

#: app/Services/Analytics/Members.php:30
msgid "New Members"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:39
msgid "New Status"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:353
msgid ""
"No Activation Site left: You have activated all the sites that your license "
"offer. Please go to wpmanageninja.com account and review your sites. You may "
"deactivate your unused sites from wpmanageninja account or you can purchase "
"another license."
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:70
#: app/Modules/DocumentLibrary/DocumentModule.php:81
msgid "No documents found to save. Please try again"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:242
msgid "No license key available"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:169
msgid "No license key found"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:49
msgid ""
"Only if contact is already a WordPress user and is in the selected space "
"then this action will run."
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:47
msgid "Pending (Require Admin Approval)"
msgstr ""

#: app/Services/Analytics/Members.php:31
msgid "Pending Members"
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:234
msgid "Permission Denied"
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:229
msgid "Please login to view this document."
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:230
msgid "Please login."
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:82
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:72
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:72
msgid ""
"Please specify what will happen if the subscriber already exists in the "
"database"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:581
msgid "Please update Fluent Messaging plugin to latest version"
msgstr ""

#. %s is the post excerpt (max 30 chars)
#: app/Hooks/Handlers/ModerationHandler.php:493
#, php-format
msgid "Post flagged for review: %s"
msgstr ""

#: app/Services/Analytics/Spaces.php:197
msgid "Post Title"
msgstr ""

#: app/Services/Analytics/Spaces.php:145 app/Services/Analytics/Overview.php:31
#: app/Services/Analytics/Overview.php:50
#: app/Services/Analytics/AnalyticsService.php:43
msgid "Posts"
msgstr ""

#: app/Services/Analytics/Spaces.php:205
msgid "Reactions"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:24
msgid "Remove Courses"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:39
msgid "Remove from Courses"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:178
msgid "Remove from selected Course"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:155
msgid "Remove from selected Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:24
#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:39
msgid "Remove from Space"
msgstr ""

#: app/Modules/Integrations/Paymattic/RemoveFromSpaceAction.php:146
msgid ""
"Remove from space/spaces Skipped because user/space could not be found -"
"fluent-community"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:203
msgid "Remove Selected Badges from users"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:25
msgid "Remove user from the selected courses"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:40
msgid "Remove user from the selected Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:25
msgid "Remove user from the Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:40
msgid "Remove user to the selected Courses"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:52
msgid "Remove Verification Sign (Blue Badge)"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:60
msgid "Replace existing badges"
msgstr ""

#: app/Http/Controllers/ModerationController.php:225
msgid "Report deleted successfully"
msgstr ""

#: app/Http/Controllers/ModerationController.php:212
msgid "Report updated successfully"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:87
msgid ""
"Restart automation multiple times for a contact for this event. (Enable only "
"if you want to restart automation for the same contact.)"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:97
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:87
msgid ""
"Restart the automation multiple times for a contact for this event. (Only "
"enable this if you want to restart the automation for the same contact)"
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:570
msgid "Review the comment"
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:600
msgid "Review the post"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:272
msgid "Saturday"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:54
msgid "Select Badges"
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:80
msgid "Select Communities"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:44
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:47
msgid "Select Courses"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:89
msgid "Select for which levels the automation will run for"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:90
msgid "Select Level"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:64
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:80
msgid "Select Spaces"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:86
msgid "Select Spaces or Courses to Enroll"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:43
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:42
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:42
msgid "Select Status"
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:79
msgid "Select which courses this automation is for."
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:79
msgid "Select which spcaes this automation funnel is for."
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:218
msgid "Selected document has been deleted"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:397
msgid "Selected registered users has been added to the course successfully"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:249
msgid "Selected registered users has been added to the space successfully"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:425
msgid "Selected registered users has been removed from the course successfully"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:369
msgid "Selected users has been removed from the space successfully"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:141
msgid "Send default WordPress welcome email to user after registration"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:74
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:70
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:63
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:57
msgid "Send WordPress Welcome Email for new WP Users"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:204
msgid "Slug already exist. Please use a different slug."
msgstr ""

#: app/Http/Controllers/ProAdminController.php:274
msgid "Snippets settings have been saved successfully."
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:196
#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:44
msgid "Space"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:213
msgid "Space Advocate"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:205
msgid "Space Contributor"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:197
msgid "Space Enthusiast"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:237
msgid "Space Hero"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:181
msgid "Space Initiate"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:245
msgid "Space Legend"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:53
msgid "Space Membership"
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:140
msgid "Space moderation settings require review"
msgstr ""

#: app/Services/Analytics/Spaces.php:141
msgid "Space Name"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:189
msgid "Space Pathfinder"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:229
msgid "Space Sage"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:221
msgid "Space Virtuoso"
msgstr ""

#: app/Services/Analytics/Overview.php:33
#: app/Services/Analytics/Overview.php:54
#: app/Services/Analytics/AnalyticsService.php:47
msgid "Spaces"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:42
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:41
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:41
msgid "Subscription Status"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:273
msgid "Sunday"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:498
msgid "Tags"
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:78
msgid "Targeted Courses"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:88
msgid "Targeted Levels"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:78
msgid "Targeted Spaces"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:102
#, php-format
msgid "The %1$s license needs to be activated. %2$s"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:290
msgid "The Badge has been added to the selected users"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:333
msgid "The Badge has been removed from the selected users"
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:215
#: app/Modules/DocumentLibrary/DocumentModule.php:257
msgid "The document you are trying to download is not found. Please try again"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:355
msgid ""
"The given license key is not valid. Please verify that your license is "
"correct. You may login to wpmanageninja.com account and get your valid "
"license key for your purchase."
msgstr ""

#. Description of the plugin
msgid "The Pro version of FluentCommunity Plugin"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:339
msgid ""
"There was an error activating the license, please verify your license is "
"correct and try again or contact support."
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:189
#: app/Services/PluginManager/LicenseManager.php:204
msgid ""
"There was an error deactivating the license, please try again or login at "
"wpmanageninja.com to manually deactivated the license"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:106
msgid ""
"These settings will apply only if the provided email address is not a "
"registered WordPress user"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:35
msgid "This automation will be initiated when a user leaves a Space"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:26
msgid "This automation will be initiated when a user leaves a Space."
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:26
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:35
msgid "This automation will be initiated when a user unenroll from a course."
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:27
#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:36
msgid ""
"This Funnel will be initiated when a user upgraded to a level in the "
"leaderboard"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:270
msgid "Thursday"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:351
msgid "to renew your license"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:291
msgid "Topic has been deleted successfully"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:250
msgid "Topic has been saved successfully"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:309
msgid "Topics configuration has been updated successfully"
msgstr ""

#: app/Services/Analytics/Spaces.php:55
msgid "Total Comments"
msgstr ""

#: app/Services/Analytics/Spaces.php:56 app/Services/Analytics/Members.php:28
msgid "Total Members"
msgstr ""

#: app/Services/Analytics/Spaces.php:54
msgid "Total Posts"
msgstr ""

#: app/Services/Analytics/Spaces.php:53
msgid "Total Spaces"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:268
msgid "Tuesday"
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:25
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:34
msgid "Unenrolled from a course"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:114
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:105
msgid "User does not have an XProfile"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:26
#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:35
msgid "User Level (Leaderboard) Upgraded"
msgstr ""

#: app/Modules/Webhooks/WebhookModule.php:57
msgid "User not found"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:99
msgid "User Profile  Field"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:43
msgid "View FluentBooking documentation"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:509
msgid "Visible for admin only"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:44
msgid "Visit Support"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:572
msgid "Webhook has been deleted successfully"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:554
msgid "Webhook has been saved successfully"
msgstr ""

#: app/Modules/Webhooks/WebhookModule.php:103
msgid "webhook has been successfully executied"
msgstr ""

#: app/Modules/Webhooks/WebhookModule.php:37
msgid "Webhook not found"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:269
msgid "Wednesday"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:149
msgid "Will apply to the contacts who are already a registered Site User"
msgstr ""

#. Author of the plugin
msgid "WPManageNinja LLC"
msgstr ""

#: app/Http/Controllers/ModerationController.php:83
msgid "You cannot report this content posted by a moderator."
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:276
#: app/Modules/DocumentLibrary/Http/DocumentController.php:285
#: app/Modules/DocumentLibrary/Http/DocumentController.php:294
msgid "You do not have permission to edit this document"
msgstr ""

#: app/Services/PluginManager/Updater.php:342
msgid "You do not have permission to install plugin updates"
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:92
msgid "You do not have permission to upload documents to this lesson."
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:23
msgid "You do not have permission to view documents in this space."
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:222
msgid "You do not have permission to view this document"
msgstr ""

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:20
msgid "You don't have permission to view the leaderboard members"
msgstr ""

#: app/Http/Controllers/ModerationController.php:100
msgid "You have already reported this content."
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:81
msgid "Your Feed Name"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:351
msgid "Your license has been expired at "
msgstr ""

#: app/Http/Controllers/LicenseController.php:60
msgid "Your license key has been successfully deactivated"
msgstr ""

#: app/Http/Controllers/LicenseController.php:42
msgid "Your license key has been successfully updated"
msgstr ""

#: app/Http/Controllers/ModerationController.php:127
msgid ""
"Your report has been successfully submitted. A moderator will review as soon "
"as possible."
msgstr ""
