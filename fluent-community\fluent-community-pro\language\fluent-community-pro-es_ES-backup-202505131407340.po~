msgid ""
msgstr ""
"Project-Id-Version: FluentCommunity Pro\n"
"POT-Creation-Date: 2025-01-27 17:05+0100\n"
"PO-Revision-Date: 2025-04-09 14:25+0000\n"
"Last-Translator: \n"
"Language-Team: Spanish (Spain)\n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: fluent-community-pro.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"Plural-Forms: nplurals=2; plural=n != 1;"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:222
#, php-format
msgid " %s Integration"
msgstr ""

#. %s is the number of other people
#: app/Hooks/Handlers/ModerationHandler.php:261
#: app/Hooks/Handlers/ModerationHandler.php:331
#, php-format
msgid " and %s other people"
msgstr ""

#. %1$s is the user name, %2$s is the comment title
#: app/Hooks/Handlers/ModerationHandler.php:268
#, php-format
msgid "%1$s flagged a comment for your review: %2$s"
msgstr ""

#. %1$s is the user name, %2$s is the feed title
#: app/Hooks/Handlers/ModerationHandler.php:338
#, php-format
msgid "%1$s flagged a post %2$s for your review"
msgstr ""

#. %1$s is the user name, %2$s is the feed title  and %3$s space title
#: app/Hooks/Handlers/ModerationHandler.php:346
#, php-format
msgid "%1$s flagged a post %2$s for your review in %3$s"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:193
msgid "[FluentCommunity] Add Badge to Profile"
msgstr "[FluentCommunity] Añadir insignia al perfil"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:168
msgid "[FluentCommunity] Add to Course"
msgstr "[FluentCommunity] Añadir un curso"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:144
msgid "[FluentCommunity] Add to Space"
msgstr "[FluentCommunity] Añadir un espacio"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:201
msgid "[FluentCommunity] Remove Badge from Profile"
msgstr "[FluentCommunity] Quitar la insignia del perfil"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:153
msgid "[FluentCommunity] Remove from a Space"
msgstr "[FluentCommunity] Eliminar de un espacio"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:176
msgid "[FluentCommunity] Remove from Course"
msgstr "[FluentCommunity] Eliminar del curso"

#. %1$s is the comment title
#: app/Hooks/Handlers/ModerationHandler.php:244
#, php-format
msgid "A comment has been automatically flagged for review: %1$s"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:69
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:65
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:58
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:52
msgid ""
"A new WordPress user will be created if the contact does not have a connect "
"WP User."
msgstr ""
"Se creará un nuevo usuario de WordPress si el contacto no tiene un usuario "
"de WP conectado."

#. %1$s is the feed title
#: app/Hooks/Handlers/ModerationHandler.php:315
#, php-format
msgid "A post has been automatically flagged for review: %1$s"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:23
msgid "Activate or Block a user from a Space"
msgstr "Activar o Bloquear a un usuario de un Espacio"

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:43
msgid "Active (Can access to the portal)"
msgstr "Activo (Puede acceder al portal)"

#: app/Services/Analytics/Members.php:29
msgid "Active Members"
msgstr ""

#: app/Services/Analytics/Spaces.php:81 app/Services/Analytics/Spaces.php:98
#: app/Services/Analytics/Members.php:46
msgid "Activity"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:26
msgid "Add Badge To User"
msgstr "Añadir insignia al usuario"

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:27
msgid "Add Badge to user's profile"
msgstr "Añadir Insignia al perfil del usuario"

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:50
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:40
msgid "Add Badges to the user profile"
msgstr "Añadir insignias al perfil de usuario"

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:49
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:39
msgid "Add Badges to user profile"
msgstr "Añadir insignias al perfil de usuario"

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:26
msgid "Add or Remove Verification Sign (Blue Badge) To User"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:27
msgid "Add or Remove Verification Sign (Blue Badge) user's profile"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:195
msgid "Add Selected Badges to users"
msgstr "Añadir insignias seleccionadas a los usuarios"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:170
msgid "Add to selected Course"
msgstr "Añadir al curso seleccionado"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:146
msgid "Add to selected Space"
msgstr "Añadir al espacio seleccionado"

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:26
#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:59
#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:34
msgid "Add to Space"
msgstr "Añadir al espacio"

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:27
msgid "Add user to a space"
msgstr "Añadir usuario a un espacio"

#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:43
msgid "Add user to the selected Courses"
msgstr "Añadir usuario a los Cursos seleccionados"

#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:27
msgid "Add user to the selected courses"
msgstr "Añadir usuario a los cursos seleccionados"

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:35
msgid "Add user to the selected Space"
msgstr "Añadir usuario al Espacio seleccionado"

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:60
msgid "Add user to the selected spaces"
msgstr "Añadir usuario a los espacios seleccionados"

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:48
msgid "Add Verification Sign (Blue Badge)"
msgstr ""

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:94
msgid "All time"
msgstr "Todo el tiempo"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:135
msgid "Allow the user login automatically after Form submission"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:173
msgid "Allow this integration conditionally based on your submission values"
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:46
msgid "An Automated double-opt-in email will be sent for new subscribers"
msgstr ""
"Los nuevos suscriptores recibirán un correo electrónico automático de doble "
"confirmación"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:47
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:46
msgid "An Automated double-optin email will be sent for new subscribers"
msgstr ""
"Los nuevos suscriptores recibirán un correo electrónico automático de doble "
"confirmación"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:97
msgid ""
"Associate your user fields to the appropriate Paymattic fields by selecting "
"the appropriate form field from the list."
msgstr ""

#: app/Http/Controllers/ProAdminController.php:376
msgid "Auth settings have been updated successfully"
msgstr "La configuración de autenticación se ha actualizado correctamente"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:134
msgid "Auto Login & Password Reset Email (For New Users Only)"
msgstr ""

#: app/Modules/UserBadge/Controllers/UserBadgeController.php:60
msgid "Badges saved successfully"
msgstr "Las insignias se guardan correctamente"

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:51
msgid "Blocked (Can not access to the portal)"
msgstr "Bloqueado (No se puede acceder al portal)"

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:22
msgid "Change Space Membership Status"
msgstr "Cambiar el estado de afiliación al Espacio"

#: app/Services/PluginManager/LicenseManager.php:45
msgid "Check Update"
msgstr "Comprobar actualizaciones"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:87
msgid "Choose options"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:351
msgid "click here"
msgstr "clic aquí"

#: app/Services/PluginManager/LicenseManager.php:104
msgid "Click here to activate"
msgstr "Haga clic aquí para activar"

#: app/Services/PluginManager/LicenseManager.php:353
msgid "Click Here to purchase another license"
msgstr "Click aquí para comprar otra licencia"

#: app/Services/PluginManager/LicenseManager.php:370
msgid "Click Here to Renew Your License"
msgstr "Click aquí para renovar tu licencia"

#: app/Http/Controllers/ProAdminController.php:355
msgid "Color configuration has been updated successfully"
msgstr "La configuración del color se ha actualizado correctamente"

#. %s is the comment excerpt (max 30 chars)
#: app/Hooks/Handlers/ModerationHandler.php:416
#, php-format
msgid "Comment flagged for review: %s"
msgstr ""

#: app/Services/Analytics/Spaces.php:149 app/Services/Analytics/Spaces.php:201
#: app/Services/Analytics/Overview.php:32
#: app/Services/Analytics/Overview.php:42
#: app/Services/Analytics/AnalyticsService.php:35
msgid "Comments"
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:596
#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:25
#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:25
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:24
#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:23
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:24
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:25
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:25
#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:23
#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:21
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:25
msgid "Community"
msgstr "Comunidad"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:172
msgid "Conditional Logics"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:35
msgid "Connect Paymattic with Fluent Community"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:67
msgid "Course Enrollment"
msgstr "Inscripción al Curso"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:459
msgid "CRM Profile"
msgstr "Perfil CRM"

#: app/Http/Controllers/ProAdminController.php:442
#: app/Http/Controllers/ProAdminController.php:473
msgid "CRM Tagging settings have been updated successfully"
msgstr "Los ajustes de etiquetado de CRM se han actualizado correctamente"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:214
msgid "Current Course Title"
msgstr "Título actual del curso"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:215
msgid "Current Course Title with Hyperlink"
msgstr "Título actual del curso con hipervínculo"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:220
msgid "Current Space Title"
msgstr "Título actual del espacio"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:221
msgid "Current Space Title with Hyperlink"
msgstr "Título del espacio actual con hipervínculo"

#: app/Services/PluginManager/LicenseManager.php:43
msgid "Docs"
msgstr "Documentos"

#: app/Modules/DocumentLibrary/DocumentModule.php:214
#: app/Modules/DocumentLibrary/DocumentModule.php:256
msgid "Document not found"
msgstr "Documento no encontrado"

#: app/Modules/DocumentLibrary/Http/DocumentController.php:146
msgid "Document title is required"
msgstr "El título del documento es obligatorio"

#: app/Modules/DocumentLibrary/Http/DocumentController.php:181
msgid "Document updated successfully"
msgstr "Documento actualizado correctamente"

#: app/Modules/DocumentLibrary/DocumentModule.php:56
msgid "Documents"
msgstr "Documentos"

#: app/Modules/Webhooks/WebhookModule.php:47
msgid "Email is required"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:26
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:42
msgid "Enroll to Courses"
msgstr "Inscribirse en cursos"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:205
msgid "Enrolled Course Names (Comma Separated)"
msgstr "Nombres de los cursos matriculados (separados por comas)"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:206
msgid "Enrolled Course Names with links (list)"
msgstr "Nombres de cursos matriculados con enlaces (lista)"

#: app/Services/PluginManager/Updater.php:342
msgid "Error"
msgstr "Error"

#: app/Services/PluginManager/LicenseManager.php:149
msgid ""
"Error when contacting with license server. Please check that your server "
"have curl installed"
msgstr ""
"Error al contactar con el servidor de licencias. Por favor, comprueba que tu "
"servidor tiene instalado curl"

#: app/Services/PluginManager/LicenseManager.php:369
msgid "expired at"
msgstr "expirado el"

#: app/Hooks/Handlers/ModerationHandler.php:143
msgid "First post requires approval"
msgstr ""

#: app/Hooks/Handlers/CoreDepenedencyHandler.php:12
#: app/Hooks/Handlers/CoreDepenedencyHandler.php:13
#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:49
msgid "FluentCommunity"
msgstr "FluentCommunity"

#. Name of the plugin
msgid "FluentCommunity Pro"
msgstr "FluentCommunity Pro"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:105
msgid "For new users"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:106
msgid "For New Users - Data mapping"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:100
msgid "Form Field"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:271
msgid "Friday"
msgstr ""

#: app/Modules/Giphy/Http/Controllers/GiphyController.php:20
msgid ""
"Giphy API key is not set or invalid. Please set a valid API key in the giphy "
"module."
msgstr ""
"La clave API de giphy no está configurada o no es válida. Establece una "
"clave API válida en el módulo giphy."

#: app/Hooks/Handlers/ModerationHandler.php:137
msgid "Global moderation settings require review"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:44
msgid "Help & Support"
msgstr "Ayuda y soporte"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:216
msgid "HTTP Link of the current course"
msgstr "Enlace HTTP del curso actual"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:222
msgid "HTTP Link of the current Space"
msgstr "Enlace HTTP del Espacio actual"

#. URI of the plugin
#. Author URI of the plugin
msgid "https://fluentcommunity.co"
msgstr "https://fluentcommunity.co"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:81
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:71
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:71
msgid "If Contact Already Exist?"
msgstr "¿Si el contacto ya existe?"

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:75
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:71
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:64
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:58
msgid ""
"If you enable this, . The newly created user will get the welcome email send "
"by WordPress to with the login info & password reset link"
msgstr ""
"Si activas esta opción, . El usuario recién creado recibirá el correo "
"electrónico de bienvenida enviado por WordPress con la información de inicio "
"de sesión y el enlace para restablecer la contraseña"

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:61
msgid ""
"If you enable this, existing badges will be replaced from the user profile"
msgstr ""
"Si activas esta opción, las insignias existentes serán reemplazadas desde el "
"perfil de usuario"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:98
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:88
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:88
msgid ""
"If you enable this, it will restart the automation for a contact even if "
"they are already in the automation. Otherwise, it will skip if the contact "
"already exists."
msgstr ""
"Si lo activas, reiniciará la automatización para un contacto aunque ya esté "
"en la automatización. De lo contrario, se omitirá si el contacto ya existe."

#: app/Http/Controllers/ProAdminController.php:323
msgid "Invalid color schema selected"
msgstr "Esquema de color seleccionado no válido"

#: app/Modules/DocumentLibrary/Http/DocumentController.php:266
msgid "Invalid document source"
msgstr "Fuente de documento no válida"

#: app/Modules/DocumentLibrary/DocumentModule.php:91
msgid "Invalid documents found to save. Please try again"
msgstr ""
"Se han encontrado documentos no válidos para guardar. Vuelve a intentarlo"

#: app/Hooks/Handlers/CoreDepenedencyHandler.php:90
msgid "Invalid plugin or file mods are disabled."
msgstr "Los mods de archivos o plugins no válidos están desactivados."

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:89
msgid "Last 30 days"
msgstr "Últimos 30 días"

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:84
msgid "Last 7 days"
msgstr "Últimos 7 días"

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:186
msgid "Leaderboard configuration has been updated."
msgstr "Se ha actualizado la configuración de la tabla de clasificación."

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:82
msgid "Leave blank to run for any courses"
msgstr "Dejar en blanco para cualquier curso"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:92
msgid "Leave blank to run for any level upgrade"
msgstr "Dejar en blanco para ejecutar cualquier actualización de nivel"

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:82
msgid "Leave blank to run for any Spaces"
msgstr "Dejar en blanco para ejecutar cualquier Espacio"

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:25
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:34
msgid "Left from a Space"
msgstr "Izquierda de un espacio"

#: app/Services/PluginManager/LicenseManager.php:369
msgid "license has been"
msgstr "la licencia ha sido"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:474
msgid "Lifetime Value"
msgstr "Valor de por vida"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:486
msgid "Lists"
msgstr "Listas"

#: app/Http/Controllers/ProAdminController.php:394
msgid "Lockscreen settings have been updated successfully."
msgstr ""
"La configuración de la pantalla de bloqueo se ha actualizado correctamente."

#: app/Http/Controllers/ProAdminController.php:78
msgid "Manager has been updated successfully"
msgstr "El Gestor se ha actualizado correctamente"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:96
msgid "Map Fields"
msgstr ""

#: app/Services/Analytics/Spaces.php:153 app/Services/Analytics/Overview.php:30
#: app/Services/Analytics/Overview.php:46
#: app/Services/Analytics/AnalyticsService.php:39
msgid "Members"
msgstr ""

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:200
msgid "Membership Space Names (Comma Separated)"
msgstr "Nombres de los espacios de afiliación (separados por comas)"

#: app/Services/Integrations/FluentCRM/CommunitySmartCodes.php:201
msgid "Membership Space Names with links (list)"
msgstr "Nombres de espacios de afiliación con enlaces (lista)"

#: app/Http/Controllers/ProAdminController.php:602
msgid "Messaging settings have been updated successfully"
msgstr ""

#: app/Http/Controllers/ModerationController.php:244
msgid "Moderation config saved successfully"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:267
msgid "Monday"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:79
msgid "Name"
msgstr ""

#: app/Services/Analytics/Members.php:30
msgid "New Members"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:39
msgid "New Status"
msgstr "Nuevo Estado"

#: app/Services/PluginManager/LicenseManager.php:353
msgid ""
"No Activation Site left: You have activated all the sites that your license "
"offer. Please go to wpmanageninja.com account and review your sites. You may "
"deactivate your unused sites from wpmanageninja account or you can purchase "
"another license."
msgstr ""
"No queda ningún sitio de activación: Has activado todos los sitios que "
"ofrece tu licencia. Por favor, ve a la cuenta wpmanageninja.com y revisa tus "
"sitios. Puedes desactivar tus sitios no utilizados desde la cuenta "
"wpmanageninja o puedes comprar otra licencia."

#: app/Modules/DocumentLibrary/DocumentModule.php:70
#: app/Modules/DocumentLibrary/DocumentModule.php:81
msgid "No documents found to save. Please try again"
msgstr "No se han encontrado documentos para guardar. Vuelve a intentarlo"

#: app/Services/PluginManager/LicenseManager.php:242
msgid "No license key available"
msgstr "No hay clave de licencia disponible"

#: app/Services/PluginManager/LicenseManager.php:169
msgid "No license key found"
msgstr "No se encontró ninguna clave de licencia"

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:49
msgid ""
"Only if contact is already a WordPress user and is in the selected space "
"then this action will run."
msgstr ""
"Sólo se ejecutará esta acción si el contacto ya es usuario de WordPress y se "
"encuentra en el espacio seleccionado."

#: app/Services/Integrations/FluentCRM/SpaceMembershipStatusChangeAction.php:47
msgid "Pending (Require Admin Approval)"
msgstr "Pendiente (requiere aprobación administrativa)"

#: app/Services/Analytics/Members.php:31
msgid "Pending Members"
msgstr ""

#: app/Modules/DocumentLibrary/DocumentModule.php:234
msgid "Permission Denied"
msgstr "Permiso denegado"

#: app/Modules/DocumentLibrary/DocumentModule.php:229
msgid "Please login to view this document."
msgstr "Inicia sesión para ver este documento."

#: app/Modules/DocumentLibrary/DocumentModule.php:230
msgid "Please login."
msgstr "Por favor inicia sesión."

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:82
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:72
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:72
msgid ""
"Please specify what will happen if the subscriber already exists in the "
"database"
msgstr "Especifica qué ocurrirá si el abonado ya existe en la base de datos"

#: app/Http/Controllers/ProAdminController.php:581
msgid "Please update Fluent Messaging plugin to latest version"
msgstr ""

#. %s is the post excerpt (max 30 chars)
#: app/Hooks/Handlers/ModerationHandler.php:493
#, php-format
msgid "Post flagged for review: %s"
msgstr ""

#: app/Services/Analytics/Spaces.php:197
msgid "Post Title"
msgstr ""

#: app/Services/Analytics/Spaces.php:145 app/Services/Analytics/Overview.php:31
#: app/Services/Analytics/Overview.php:50
#: app/Services/Analytics/AnalyticsService.php:43
msgid "Posts"
msgstr ""

#: app/Services/Analytics/Spaces.php:205
msgid "Reactions"
msgstr ""

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:24
msgid "Remove Courses"
msgstr "Eliminar cursos"

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:39
msgid "Remove from Courses"
msgstr "Eliminar de los cursos"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:178
msgid "Remove from selected Course"
msgstr "Eliminar del Curso seleccionado"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:155
msgid "Remove from selected Space"
msgstr "Eliminar del Espacio seleccionado"

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:24
#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:39
msgid "Remove from Space"
msgstr "Eliminar del espacio"

#: app/Modules/Integrations/Paymattic/RemoveFromSpaceAction.php:146
msgid ""
"Remove from space/spaces Skipped because user/space could not be found -"
"fluent-community"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:203
msgid "Remove Selected Badges from users"
msgstr "Eliminar insignias seleccionadas de usuarios"

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:25
msgid "Remove user from the selected courses"
msgstr "Eliminar al usuario de los cursos seleccionados"

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:40
msgid "Remove user from the selected Space"
msgstr "Eliminar usuario del Espacio seleccionado"

#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:25
msgid "Remove user from the Space"
msgstr "Eliminar usuario del Espacio"

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:40
msgid "Remove user to the selected Courses"
msgstr "Eliminar usuario de los Cursos seleccionados"

#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:52
msgid "Remove Verification Sign (Blue Badge)"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:60
msgid "Replace existing badges"
msgstr "Sustituir insignias existentes"

#: app/Http/Controllers/ModerationController.php:225
msgid "Report deleted successfully"
msgstr ""

#: app/Http/Controllers/ModerationController.php:212
msgid "Report updated successfully"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:87
msgid ""
"Restart automation multiple times for a contact for this event. (Enable only "
"if you want to restart automation for the same contact.)"
msgstr ""
"Reinicia la automatización varias veces para un contacto para este evento. "
"(Actívalo sólo si quieres reiniciar la automatización para el mismo contacto)"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:97
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:87
msgid ""
"Restart the automation multiple times for a contact for this event. (Only "
"enable this if you want to restart the automation for the same contact)"
msgstr ""
"Reinicia la automatización varias veces para un contacto para este evento. "
"(Habilítalo sólo si quieres reiniciar la automatización para el mismo "
"contacto)"

#: app/Hooks/Handlers/ModerationHandler.php:570
msgid "Review the comment"
msgstr ""

#: app/Hooks/Handlers/ModerationHandler.php:600
msgid "Review the post"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:272
msgid "Saturday"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:54
msgid "Select Badges"
msgstr "Seleccionar insignias"

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:80
msgid "Select Communities"
msgstr "Selecciona Comunidades"

#: app/Services/Integrations/FluentCRM/RemoveFromCourseAction.php:44
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:47
msgid "Select Courses"
msgstr "Seleccionar Cursos"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:89
msgid "Select for which levels the automation will run for"
msgstr "Selecciona para qué niveles se ejecutará la automatización"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:90
msgid "Select Level"
msgstr "Seleccionar Recompensa"

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:64
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:80
msgid "Select Spaces"
msgstr "Seleccionar espacios"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:86
msgid "Select Spaces or Courses to Enroll"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:43
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:42
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:42
msgid "Select Status"
msgstr "Seleccione estado"

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:79
msgid "Select which courses this automation is for."
msgstr "Selecciona para qué cursos es esta automatización."

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:79
msgid "Select which spcaes this automation funnel is for."
msgstr "Selecciona para qué spcaes es este embudo de automatización."

#: app/Modules/DocumentLibrary/Http/DocumentController.php:218
msgid "Selected document has been deleted"
msgstr "El documento seleccionado ha sido eliminado"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:397
msgid "Selected registered users has been added to the course successfully"
msgstr ""
"Los usuarios registrados seleccionados se han añadido correctamente al curso"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:249
msgid "Selected registered users has been added to the space successfully"
msgstr ""
"Los usuarios registrados seleccionados se han añadido correctamente al "
"espacio"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:425
msgid "Selected registered users has been removed from the course successfully"
msgstr ""
"Los usuarios registrados seleccionados han sido eliminados del curso "
"correctamente"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:369
msgid "Selected users has been removed from the space successfully"
msgstr "Los usuarios seleccionados han sido eliminados del espacio con éxito"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:141
msgid "Send default WordPress welcome email to user after registration"
msgstr ""

#: app/Services/Integrations/FluentCRM/AddToSpaceAction.php:74
#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:70
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:63
#: app/Services/Integrations/FluentCRM/AddToCourseAction.php:57
msgid "Send WordPress Welcome Email for new WP Users"
msgstr "Enviar correo de bienvenida a WordPress para nuevos usuarios de WP"

#: app/Http/Controllers/ProAdminController.php:204
msgid "Slug already exist. Please use a different slug."
msgstr "El slug ya existe. Por favor, utiliza un slug diferente."

#: app/Http/Controllers/ProAdminController.php:274
msgid "Snippets settings have been saved successfully."
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:196
#: app/Services/Integrations/FluentCRM/RemoveFromSpaceAction.php:44
msgid "Space"
msgstr "Espacio"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:213
msgid "Space Advocate"
msgstr "Defensor del Espacio"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:205
msgid "Space Contributor"
msgstr "Contribuidor espacial"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:197
msgid "Space Enthusiast"
msgstr "Entusiasta del espacio"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:237
msgid "Space Hero"
msgstr "Héroe del espacio"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:181
msgid "Space Initiate"
msgstr "Iniciado espacial"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:245
msgid "Space Legend"
msgstr "Leyenda espacial"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:53
msgid "Space Membership"
msgstr "Afiliación espacial"

#: app/Hooks/Handlers/ModerationHandler.php:140
msgid "Space moderation settings require review"
msgstr ""

#: app/Services/Analytics/Spaces.php:141
msgid "Space Name"
msgstr ""

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:189
msgid "Space Pathfinder"
msgstr "Explorador espacial"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:229
msgid "Space Sage"
msgstr "Salvia Espacial"

#: app/Modules/LeaderBoard/Services/LeaderBoardHelper.php:221
msgid "Space Virtuoso"
msgstr "Virtuoso del espacio"

#: app/Services/Analytics/Overview.php:33
#: app/Services/Analytics/Overview.php:54
#: app/Services/Analytics/AnalyticsService.php:47
msgid "Spaces"
msgstr ""

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:42
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:41
#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:41
msgid "Subscription Status"
msgstr "Estado de la suscripción"

#: app/Services/Analytics/AnalyticsService.php:273
msgid "Sunday"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:498
msgid "Tags"
msgstr "Etiquetas"

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:78
msgid "Targeted Courses"
msgstr "Cursos específicos"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:88
msgid "Targeted Levels"
msgstr "Niveles objetivo"

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:78
msgid "Targeted Spaces"
msgstr "Espacios Dirigidos"

#: app/Services/PluginManager/LicenseManager.php:102
#, php-format
msgid "The %1$s license needs to be activated. %2$s"
msgstr "Es necesario activar la licencia %1$s. %2$s"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:290
msgid "The Badge has been added to the selected users"
msgstr "La Insignia se ha añadido a los usuarios seleccionados"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:333
msgid "The Badge has been removed from the selected users"
msgstr "Se ha eliminado la Insignia de los usuarios seleccionados"

#: app/Modules/DocumentLibrary/DocumentModule.php:215
#: app/Modules/DocumentLibrary/DocumentModule.php:257
msgid "The document you are trying to download is not found. Please try again"
msgstr ""
"El documento que intentas descargar no se encuentra. Inténtalo de nuevo"

#: app/Services/PluginManager/LicenseManager.php:355
msgid ""
"The given license key is not valid. Please verify that your license is "
"correct. You may login to wpmanageninja.com account and get your valid "
"license key for your purchase."
msgstr ""
"La clave de licencia dada no es válida. Verifique que su licencia sea "
"correcta. Puede iniciar sesión en wpmanageninja.com cuenta y obtener su "
"clave de licencia válida para su compra."

#. Description of the plugin
msgid "The Pro version of FluentCommunity Plugin"
msgstr "La versión Pro del Plugin FluentCommunity"

#: app/Services/PluginManager/LicenseManager.php:339
msgid ""
"There was an error activating the license, please verify your license is "
"correct and try again or contact support."
msgstr ""
"Hubo un error al activar la licencia, verifique que su licencia sea correcta "
"e inténtelo de nuevo o comuníquese con el soporte."

#: app/Services/PluginManager/LicenseManager.php:189
#: app/Services/PluginManager/LicenseManager.php:204
msgid ""
"There was an error deactivating the license, please try again or login at "
"wpmanageninja.com to manually deactivated the license"
msgstr ""
"Se ha producido un error al desactivar la licencia, inténtelo de nuevo o "
"inicie sesión en wpmanageninja.com para desactivar manualmente la licencia"

#: app/Modules/Integrations/Paymattic/Bootstrap.php:106
msgid ""
"These settings will apply only if the provided email address is not a "
"registered WordPress user"
msgstr ""

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:35
msgid "This automation will be initiated when a user leaves a Space"
msgstr "Esta automatización se iniciará cuando un usuario abandone un Espacio"

#: app/Services/Integrations/FluentCRM/SpaceLeaveTrigger.php:26
msgid "This automation will be initiated when a user leaves a Space."
msgstr "Esta automatización se iniciará cuando un usuario abandone un Espacio."

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:26
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:35
msgid "This automation will be initiated when a user unenroll from a course."
msgstr ""
"Esta automatización se iniciará cuando un usuario se dé de baja de un curso."

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:27
#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:36
msgid ""
"This Funnel will be initiated when a user upgraded to a level in the "
"leaderboard"
msgstr ""
"Este Embudo se iniciará cuando un usuario suba de nivel en la tabla de "
"clasificación"

#: app/Services/Analytics/AnalyticsService.php:270
msgid "Thursday"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:351
msgid "to renew your license"
msgstr "para renovar su licencia"

#: app/Http/Controllers/ProAdminController.php:291
msgid "Topic has been deleted successfully"
msgstr "El tema se ha eliminado correctamente"

#: app/Http/Controllers/ProAdminController.php:250
msgid "Topic has been saved successfully"
msgstr "El tema se ha guardado correctamente"

#: app/Http/Controllers/ProAdminController.php:309
msgid "Topics configuration has been updated successfully"
msgstr "La configuración de los temas se ha actualizado correctamente"

#: app/Services/Analytics/Spaces.php:55
msgid "Total Comments"
msgstr ""

#: app/Services/Analytics/Spaces.php:56 app/Services/Analytics/Members.php:28
msgid "Total Members"
msgstr ""

#: app/Services/Analytics/Spaces.php:54
msgid "Total Posts"
msgstr ""

#: app/Services/Analytics/Spaces.php:53
msgid "Total Spaces"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:268
msgid "Tuesday"
msgstr ""

#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:25
#: app/Services/Integrations/FluentCRM/CourseLeaveTrigger.php:34
msgid "Unenrolled from a course"
msgstr "Te has dado de baja de un curso"

#: app/Services/Integrations/FluentCRM/AddBadgeAction.php:114
#: app/Services/Integrations/FluentCRM/AddOrRemoveVerificationAction.php:105
msgid "User does not have an XProfile"
msgstr "El usuario no tiene un XProfile"

#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:26
#: app/Modules/LeaderBoard/UserLevelUpgradedTrigger.php:35
msgid "User Level (Leaderboard) Upgraded"
msgstr "Nivel de usuario (Leaderboard) Mejorado"

#: app/Modules/Webhooks/WebhookModule.php:57
msgid "User not found"
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:99
msgid "User Profile  Field"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:43
msgid "View FluentBooking documentation"
msgstr "Ver la documentación de FluentBooking"

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:509
msgid "Visible for admin only"
msgstr "Visible sólo para el administrador"

#: app/Services/PluginManager/LicenseManager.php:44
msgid "Visit Support"
msgstr "Visitar soporte"

#: app/Http/Controllers/ProAdminController.php:572
msgid "Webhook has been deleted successfully"
msgstr ""

#: app/Http/Controllers/ProAdminController.php:554
msgid "Webhook has been saved successfully"
msgstr ""

#: app/Modules/Webhooks/WebhookModule.php:103
msgid "webhook has been successfully executied"
msgstr ""

#: app/Modules/Webhooks/WebhookModule.php:37
msgid "Webhook not found"
msgstr ""

#: app/Services/Analytics/AnalyticsService.php:269
msgid "Wednesday"
msgstr ""

#: app/Services/Integrations/FluentCRM/ContactAdvancedFilter.php:149
msgid "Will apply to the contacts who are already a registered Site User"
msgstr "Se aplicará a los contactos que ya sean Usuarios registrados del Sitio"

#. Author of the plugin
msgid "WPManageNinja LLC"
msgstr "WPManageNinja LLC"

#: app/Http/Controllers/ModerationController.php:83
msgid "You cannot report this content posted by a moderator."
msgstr ""

#: app/Modules/DocumentLibrary/Http/DocumentController.php:276
#: app/Modules/DocumentLibrary/Http/DocumentController.php:285
#: app/Modules/DocumentLibrary/Http/DocumentController.php:294
msgid "You do not have permission to edit this document"
msgstr "No tienes permiso para editar este documento"

#: app/Services/PluginManager/Updater.php:342
msgid "You do not have permission to install plugin updates"
msgstr "No tienes permiso para instalar actualizaciones del plugin"

#: app/Modules/DocumentLibrary/Http/DocumentController.php:92
msgid "You do not have permission to upload documents to this lesson."
msgstr "No tienes permiso para subir documentos a esta lección."

#: app/Modules/DocumentLibrary/Http/DocumentController.php:23
msgid "You do not have permission to view documents in this space."
msgstr "No tienes permiso para ver documentos en este espacio."

#: app/Modules/DocumentLibrary/DocumentModule.php:222
msgid "You do not have permission to view this document"
msgstr "No tienes permiso para ver este documento"

#: app/Modules/LeaderBoard/Http/Controllers/LeaderBoardController.php:20
msgid "You don't have permission to view the leaderboard members"
msgstr "No tienes permiso para ver los miembros de la clasificación"

#: app/Http/Controllers/ModerationController.php:100
msgid "You have already reported this content."
msgstr ""

#: app/Modules/Integrations/Paymattic/Bootstrap.php:81
msgid "Your Feed Name"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:351
msgid "Your license has been expired at "
msgstr "Su licencia ha caducado a las "

#: app/Http/Controllers/LicenseController.php:60
msgid "Your license key has been successfully deactivated"
msgstr "Su clave de licencia se ha desactivado correctamente"

#: app/Http/Controllers/LicenseController.php:42
msgid "Your license key has been successfully updated"
msgstr "Su clave de licencia se ha actualizado correctamente"

#: app/Http/Controllers/ModerationController.php:127
msgid ""
"Your report has been successfully submitted. A moderator will review as soon "
"as possible."
msgstr ""
