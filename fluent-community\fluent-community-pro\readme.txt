=== FluentCommunity Pro ===
Contributors: tech<PERSON><PERSON>l, wpmanageninja
Author URI: https://fluentcommunity.co/
Tags: community
Requires at least: 6.0
Tested up to: 6.8
Requires PHP: 7.3
Stable tag: 1.6.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

== Description ==

== Installation ==
Install the fluent-community-pro.zip file to your WordPress plugin upload and then just activate and use it.

== Changelog ==

= 1.6.0 (Date: May 13, 2025) =
- New: Auto SEO Manager
- New: Post Scheduling
- New: Default Feed Sorting Select Option
- New: Post Preview Before Publishing
- Fixed: Digest Email Delivery Issue
- Fixed: User Deletion Bug
- Fixed: Text Domain Warning
- Improved: Multiple UI/UX Enhancements

= 1.5.0 (Date: Apr 29, 2025) =
- Introducing Theme Compilability with all the major themes and page builders 🚀
- New: Custom Landing Page URL for Private Spaces or Courses
- New: Improved UI/UX for Chat Application.
- Style Improvements for the overall feeds
- Bug fixes and improvements

= 1.3.1 (Date: Apr 09, 2024) =
- Introduced Gutenberg Block in Course Lesson Editor
- Added Option to Move Lessons Between Sections
- Added One to One Messaging Settings
- Added Email Notification Settings for Messaging
- Added User Blocking Functionality for Messaging
- Added Poll Ended Date Time
- Added Refresh Feed Option
- Added Option for BB Migration from Scratch
- Added New Tab for Terms and Conditions Link
- Added Ungrouped Spaces to the Space Groups
- Added LightSpeed Caching issues
- Added 100% Public Course!
- Added Hook in Feeds Query
- Added Missing Translations
- Fixed Document Lists
- Fixed Issue with Privacy Url
- Fixed Moderation Email Not Sending Issue
- Fixed Course Not Deleting From Space Groups
- Fixed Notification for Mentioned User in Comment
- Fixed BuddyBoss Avatar and Cover Mismatch Issue
- Improved UI-UX

= 1.2.15 (Date: Mar 18, 2025) =
- Added Add Or Remove Verification Action for FluentCRM Integration
- Added Custom CSS and Javascript Snippet Options
- Added Custom Signup URL option for Auth Module
- Improved BuddyBoss / BuddyPress migration for images
- Fixed comment notification issues
- Fixed object caching for moderator settings

= 1.2.11 (Date: Mar 10, 2025) =
- Improved Moderation Module and Added new features
- BuddyBoss & BuddyPress Migration Support for Media
- Improved Database Queries for Feeds
- Accessibility support for top menu bar

= 1.2.0 (Date: Mar 06, 2025) =
- Introduced Content Moderation Feature
- Integrated BunnyCDN
- Added Incoming Webhook
- Added Social Links Customizations!
- Added Search and Sort Options in Space and Course List
- Added Profile Section To FluentCRM
- Added Last Seen as Privacy Settings!
- Resolved Image Rotation Issues!
- Fixed File Upload Bug
- Fixed Welcome Banner Style Issue
- Fixed Avatar Not Generating for Other Language
- Fixed User Badge Border Color Inconsistency
- Fixed Create Space Group Showing Exiting Group Data
- Fixed Profile Dropdown Icon Style Issue
- Fixed User Document Download Issue
- Fixed Issue with Daily Digest Email

= 1.1.23 (Date: Feb 05, 2025) =
- Added Missing Translation
- Added Space Select Option
- Fixed Leaderboard CSS Glitch
- Fixed Warning Showing for Logged-out Users

= 1.1.22 (Date: Feb 04, 2025) =
- New: Reporting and Analytics
- New: Integration with Paymattic
- New: Allow Users to Leave/Join Group Chat
- Improvement: Multiple Media Upload Support in Post Comment
- Added Pagination for Space and Course Lists
- Added Options to Edit and Delete Space in Space Group Settings
- Duplicate Post Validation Limited to Current Space
- Resolved: Video Embed Issue with Custom HTML
- Resolved Space Remove from Group Issue
- Fixed: Login Button Label Not Updating Issue
- Fixed: Group Space Menu Visibility for Public
- Fixed: Space Permission Handling Issue
- Fixed: Flags Emoji Saving Issue
- Fixed Slug Not Updating Issue

= 1.1.20 (Date: Jan 21, 2025) =
- New: Migration support for BuddyPress
- New: Migration support for BuddyBoss
- New: Added ability to change the space for a post
- New: Terms and Conditions field customization option
- Added exclude users from leaderboard
- Added option to remove users from selected lists
- Added multisite compatibility for users
- Added space permission control
- Added missing translation strings
- Resolved posting without space selection issue
- Fixed login/signup background style not rendering
- Fixed permalink issue in course comments
- Fixed media storage issue with Amazon S3 region
- Fixed course document permission issue
- Fixed profile status update issue in CRM action
- Addressed numeric emoji saving issue
- Resolved invalid media image error
- Improved loading style
- Improve UI/UX

= 1.1.17 (Date: Dec 17, 2024) =
- New: Post Sharing Card when sharing community post on the feed
- Added Missing Translations
- Added Soundcloud support for oEmbed
- Added Fallback slug generation for non-latin characters
- Fixed @veryone tagging email issues
- Fixed Styling issue for color customizations
- Fixed Open Graph Image issue

= 1.1.15 (Date: Dec 16, 2024) =
- NEW: Secure Docoments Feature for Spaces
- NEW: Secure Documents Feature for Course Lesson
- NEW: Added new member list / grid layout
- NEW: Invitation Links for Spaces
- NEW: Rebust Access Management with FluentCRM tags
- NEW: Improved Auth Pages UI & UX & better integration with FluentAuth
- NEW: FluentCRM triggers for Course / Space left
- NEW: Contextual Smartcode for FluentCRM Automation
- Course Editor Mobile UI Issues Fixed
- Fixed Permission Issues with Moderartors / Course Creators
- Fixed Gravatar API Issues
- Fixed: everyone tag issues
- i18n Improvements

= 1.1.12 (Date: Dec 02, 2024) =
- Added Option to disable Gravatar Profile Image
- You can now add Multiple Badges to a User
- Notification Type Icons added
- [FluentCRM Automation] - Add or Remove Badge on FluentCRM Automation Action
- [FluentCRM Bulk Actions] - Add Or Remove Badge on FluentCRM Bulk Actions
- Improved UX for Post Popup
- Disable Comments on courses globally
- Course Progress Bar added to lesson view
- Add Option to Hide Students Count from Course Listings
- You can now create Space without any group
- Improved Slug Generation for non-latin characters
- Space & Group menu management improved
- Bug Fixes and Improvements suggested by community users

= 1.1.11 (Date: Nov 27, 2024) =
- Added Modal View for each post (Configurable)
- Single Post view is now blazing fast
- Courses now can be added without any Menu Group
- Login and Signup Page Customization Improved
- Post Slug issue fixed for non-latins characters
- Space and Course Slug can be edited now
- Fixed course URL issue when using custom slug
- Added Topics as a required field for Posts (Configurable)
- Added Option to edit user email from profile
- Improved UI & UX

= 1.1.0 (Date: Nov 20, 2024) =
- Improved FluentCRM Integration
- Added Direct Integration with Fluent Forms
- Styling & UX Improvements
- Added Login and Signup Page Customization
- Added Remove From Space Option from user profile
- Added MySQL Lite Support

= 1.0.98 (Date: Nov 18, 2024 =
- Added Color Schema Customization
- Improved UI & UX
- Improved FluentCRM Integration
- Added more privacy Settings
- Translation Improvement
- Live Chat Improvement

= 1.0.96 (Date: Nov 14, 2024) =
- Improved Data Cleanup for old logs
- Fixed Topics Edit Issues
- Improved UI & UX in various Pages
- Fixed Badge Delete issues
- Improved Feeds Edit Form - Now you can edit topics and media as well
- Email Digest & Notification Emails improvement
- Added Option to hide members count from spaces
- Improved Roles and Permissions
- Comments sorting is now persistent
- Added option to use custom Login/Signup URL
- Translation and Different String Labels Improvements

= 1.0.95 (Date: Nov 12, 2024) =
- Added Community Privacy Settings. Now you can disable global profile listing and restrict profile listing on specific spaces
- Fixed various Styling issues
- Tagging users is now improved
- Improved UI across the Community Feed Inteface
- Fixed various issues with the Course UI

1.0.91 (Date: 07 Nov 2024)
- Fixed Topics Issues
