(()=>{"use strict";var e,t,o,r,n={143:e=>{e.exports=window.wp.data},153:(e,t,o)=>{o.d(t,{A:()=>n});var r=o(609);const n={name:"fcom_prefixed",className:"editor-autocompleters__fcom",triggerPrefix:"@",options:e=>[{code:"{{user.display_name|fallback}}",title:"User's Name",prefix:"user"},{code:"{{user.user_email|fallback}}",title:"User's Email",prefix:"user"},{code:"{{user.photo_html.50px}}",title:"User's photo HTML",prefix:"user"},{code:"##user.profile_link##",title:"Profile Link",prefix:"user"}],isDebounced:!0,getOptionKeywords:e=>[e.title,e.code],getOptionLabel:e=>[(0,r.createElement)("span",{key:"name",className:"editor-autocompleters__user-name"},e.title)],getOptionCompletion:e=>e.code}},279:e=>{e.exports=window.wp.plugins},332:(e,t,o)=>{o.a(e,(async(e,t)=>{try{var r=o(609),n=o(279),s=o(656),a=o(143),l=o(619),i=o(997),c=(o(723),o(455),o(153));const d=()=>(0,r.createElement)(s.PluginPostStatusInfo,null,(0,r.createElement)("div",{className:"fcom_lesson_info_codes",style:{width:"100%"}},(0,r.createElement)("p",null,"You may use following smartcode in your lesson:"),(0,r.createElement)("ul",null,(0,r.createElement)("li",null,(0,r.createElement)("b",null,"User's Name:")," ",(0,r.createElement)("span",null,"{{user.display_name|fallback}}")),(0,r.createElement)("li",null,(0,r.createElement)("b",null,"User's Email:")," ",(0,r.createElement)("span",null,"{{user.user_email|fallback}}")),(0,r.createElement)("li",null,(0,r.createElement)("b",null,"User's photo HTML:")," ",(0,r.createElement)("span",null,"{{user.photo_html.50px}}")),(0,r.createElement)("li",null,(0,r.createElement)("b",null,"User's Profile Link:")," ",(0,r.createElement)("span",null,"##user.profile_link##")))));(0,n.registerPlugin)("fcom-custom-document-info",{render:d});let u=!1;const p=()=>(u||((0,a.select)("core/edit-post").isFeatureActive("welcomeGuide")&&(0,a.dispatch)("core/edit-post").toggleFeature("welcomeGuide"),(0,a.select)("core/edit-post").isFeatureActive("fullscreenMode")||(0,a.dispatch)("core/edit-post").toggleFeature("fullscreenMode"),"text"===(0,a.select)("core/edit-post").getEditorMode()&&(0,a.dispatch)("core/edit-post").switchEditorMode("visual"),(0,a.dispatch)("core/editor").lockPostSaving(),(0,a.dispatch)("core/editor").lockPostAutosaving(),u=!0),null);async function m(){await new Promise((e=>{const t=(0,a.subscribe)((()=>{((0,a.select)("core/editor").isCleanNewPost()||(0,a.select)("core/block-editor").getBlockCount()>0)&&(t(),e())}))}));const e=document.querySelector('[name="editor-canvas"]');return new Promise((t=>{e.loading||t(e),e.onload=()=>{t(e)}}))}(0,n.registerPlugin)("fluent-com-disable-gutenberg-features",{render:p}),(0,l.addFilter)("editor.DocumentBar","fluentcom-custom-document-bar",(()=>null)),(0,l.addFilter)("editor.Autocomplete.completers","fluent_com/add_autocmplete",(function(e,t){return e=e.filter((e=>"users"!==e.name)),[...e,c.A]})),window.addEventListener("beforeunload",(function(e){e.stopImmediatePropagation()})),await m(),console.log("editor iframe is now ready");let w=!1;window.addEventListener("message",(e=>{if("UPDATE_EDITOR"!==e.data?.type)return;const{content:t,title:o,featured_image_id:r}=e.data.data;t&&(0,a.dispatch)("core/block-editor").resetBlocks((0,i.parse)(t));let n=null;o&&(n={title:o}),r&&(n?n.featured_media=r:n={featured_media:r}),n&&(0,a.dispatch)("core/editor").editPost(n),setTimeout((()=>{w=!0}),1e3)})),window._wpLoadBlockEditor.then((()=>{window.parent.postMessage({content:"ping",action:"EDITOR_READY"},"*");let e="",t="",o=null;(0,a.subscribe)((()=>{if(!w)return;const r=(0,a.select)("core/editor").getEditedPostAttribute("title");r!=t&&(t=r,window.parent.postMessage({content:r,action:"TITLE_UPDATED"},"*"));const n=(0,a.select)("core/editor").getEditedPostAttribute("featured_media");o!=n&&(o=n,window.parent.postMessage({content:n,action:"FEATURED_MEDIA_UPDATED"},"*"));const s=(0,a.select)("core/block-editor").getBlocks(),l=(0,i.serialize)(s);e!=l&&(e=l,window.parent.postMessage({content:l,action:"EDITOR_UPDATED"},"*"))}))})),t()}catch(f){t(f)}}),1)},455:e=>{e.exports=window.wp.apiFetch},609:e=>{e.exports=window.React},619:e=>{e.exports=window.wp.hooks},656:e=>{e.exports=window.wp.editor},723:e=>{e.exports=window.wp.i18n},997:e=>{e.exports=window.wp.blocks}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var o=s[e]={exports:{}};return n[e](o,o.exports,a),o.exports}e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",o="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",r=e=>{e&&e.d<1&&(e.d=1,e.forEach((e=>e.r--)),e.forEach((e=>e.r--?e.r++:e())))},a.a=(n,s,a)=>{var l;a&&((l=[]).d=-1);var i,c,d,u=new Set,p=n.exports,m=new Promise(((e,t)=>{d=t,c=e}));m[t]=p,m[e]=e=>(l&&e(l),u.forEach(e),m.catch((e=>{}))),n.exports=m,s((n=>{var s;i=(n=>n.map((n=>{if(null!==n&&"object"==typeof n){if(n[e])return n;if(n.then){var s=[];s.d=0,n.then((e=>{a[t]=e,r(s)}),(e=>{a[o]=e,r(s)}));var a={};return a[e]=e=>e(s),a}}var l={};return l[e]=e=>{},l[t]=n,l})))(n);var a=()=>i.map((e=>{if(e[o])throw e[o];return e[t]})),c=new Promise((t=>{(s=()=>t(a)).r=0;var o=e=>e!==l&&!u.has(e)&&(u.add(e),e&&!e.d&&(s.r++,e.push(s)));i.map((t=>t[e](o)))}));return s.r?c:a()}),(e=>(e?d(m[o]=e):c(p),r(l)))),l&&l.d<0&&(l.d=0)},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a(332)})();