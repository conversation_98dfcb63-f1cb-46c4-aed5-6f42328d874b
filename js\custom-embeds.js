/**
 * Custom oEmbed handler for Fluent Community
 * 
 * Enhances embedded videos and posts from Rumble, Facebook and Instagram
 */
 
(function($) {
    'use strict';
    
    /**
     * Initialize custom embeds handling
     */
    function initCustomEmbeds() {
        // Handle Rumble embeds
        setupRumbleEmbeds();
        
        // Handle Facebook embeds
        setupFacebookEmbeds();
        
        // Handle Instagram embeds
        setupInstagramEmbeds();
        
        // Add listeners for new content
        addDynamicContentListeners();
    }
    
    /**
     * Setup Rumble video embeds
     */
    function setupRumbleEmbeds() {
        $('.fcom-embed-rumble').each(function() {
            let $container = $(this);
            
            // Ensure proper responsive behavior
            if (!$container.hasClass('fcom-embed-processed')) {
                // Add placeholder if iframe not loaded yet
                if ($container.find('iframe').length === 0) {
                    const $iframe = $container.find('iframe');
                    if (!$iframe.attr('src') && $iframe.data('src')) {
                        $iframe.attr('src', $iframe.data('src'));
                    }
                }
                
                // Mark as processed
                $container.addClass('fcom-embed-processed');
            }
        });
    }
    
    /**
     * Setup Facebook embeds (videos and reels)
     */
    function setupFacebookEmbeds() {
        // Process Facebook embeds
        $('.fcom-embed-facebook, .fcom-embed-facebook-video, .fcom-embed-facebook-reel').each(function() {
            let $container = $(this);
            
            if (!$container.hasClass('fcom-embed-processed')) {
                // FB needs its SDK to render properly
                if (typeof FB !== 'undefined' && FB.XFBML) {
                    FB.XFBML.parse($container[0]);
                }
                
                // Mark as processed
                $container.addClass('fcom-embed-processed');
            }
        });
    }
    
    /**
     * Setup Instagram embeds
     */
    function setupInstagramEmbeds() {
        // Process Instagram embeds
        $('.fcom-embed-instagram, .fcom-embed-instagram-reel').each(function() {
            let $container = $(this);
            
            if (!$container.hasClass('fcom-embed-processed')) {
                // Instagram uses FB SDK or its own SDK
                if (typeof instgrm !== 'undefined' && instgrm.Embeds) {
                    instgrm.Embeds.process($container[0]);
                } else if (typeof FB !== 'undefined' && FB.XFBML) {
                    FB.XFBML.parse($container[0]);
                }
                
                // Mark as processed
                $container.addClass('fcom-embed-processed');
            }
        });
    }
    
    /**
     * Handle dynamically loaded content (like lazy loading or AJAX)
     */
    function addDynamicContentListeners() {
        // Detect new content in the feed
        const feedContainer = document.querySelector('.fc-feed-items');
        
        if (feedContainer) {
            // Use MutationObserver to detect new content
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        // Check if any of the added nodes contain our embeds
                        for (let i = 0; i < mutation.addedNodes.length; i++) {
                            const node = mutation.addedNodes[i];
                            
                            if (node.nodeType === 1) { // Element node
                                // Check for our embed classes
                                if (node.classList && 
                                    (node.classList.contains('fcom-custom-embed') || 
                                     node.querySelector('.fcom-custom-embed'))) {
                                    // Re-initialize embeds
                                    initCustomEmbeds();
                                    break;
                                }
                            }
                        }
                    }
                });
            });
            
            // Start observing the feed container
            observer.observe(feedContainer, { 
                childList: true, 
                subtree: true 
            });
        }
        
        // Listen for custom events that might be triggered when content is added
        $(document).on('fluent_community_feed_updated fc_content_loaded', function() {
            initCustomEmbeds();
        });
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        initCustomEmbeds();
    });
    
})(jQuery);
