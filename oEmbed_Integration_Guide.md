# Integrating Custom oEmbed Support into Fluent Community Core

This guide provides steps to integrate the custom oEmbed functionality into the Fluent Community core files rather than using a standalone plugin.

## Integration Method 1: Keep as a Plugin (Recommended)

The recommended approach is to keep the custom oEmbed functionality as a separate plugin. This provides several advantages:

- Easier to maintain and update
- Doesn't require modifying core files
- Won't be overwritten during Fluent Community updates
- Can be activated/deactivated as needed

Simply install and activate the `fluent-community-custom-oembed.php` plugin as described in the documentation.

## Integration Method 2: Incorporate into Fluent Community Core

If you prefer to integrate the functionality directly into the Fluent Community core, follow these steps:

### Step 1: Add Custom oEmbed Provider Registration

Open `fluent-community/Modules/modules_init.php` and add the following code at the end of the file:

```php
/**
 * Register custom oEmbed providers for Rumble, Facebook, and Instagram
 */
function fc_register_custom_oembed_providers() {
    // Register Rumble
    wp_oembed_add_provider( 
        '#https?://(?:www\.)?rumble\.com/.*#i', 
        'https://rumble.com/api/Media/oembed.json', 
        true 
    );
    
    // Facebook/Instagram credentials
    $fb_app_id = 'YOUR_APP_ID';
    $fb_app_secret = 'YOUR_APP_SECRET';
    
    // Only register Facebook & Instagram if we have credentials
    if ( $fb_app_id && $fb_app_secret && $fb_app_id !== 'YOUR_APP_ID' ) {
        $fb_token = $fb_app_id . '|' . $fb_app_secret;
        
        // Facebook videos
        wp_oembed_add_provider( 
            '#https?://(www\.)?facebook\.com/.*/videos/.*#i', 
            'https://graph.facebook.com/v18.0/oembed_video?access_token=' . $fb_token, 
            true 
        );
        
        // Facebook posts and reels
        wp_oembed_add_provider( 
            '#https?://(www\.)?facebook\.com/.*/posts/.*#i', 
            'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $fb_token, 
            true 
        );
        
        wp_oembed_add_provider( 
            '#https?://(www\.)?facebook\.com/reel/.*#i', 
            'https://graph.facebook.com/v18.0/oembed_post?access_token=' . $fb_token, 
            true 
        );
        
        // Instagram
        wp_oembed_add_provider( 
            '#https?://(www\.)?instagram\.com/p/.*#i', 
            'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $fb_token, 
            true 
        );
        
        wp_oembed_add_provider( 
            '#https?://(www\.)?instagram\.com/reel/.*#i', 
            'https://graph.facebook.com/v18.0/instagram_oembed?access_token=' . $fb_token, 
            true 
        );
    }
}
add_action('init', 'fc_register_custom_oembed_providers');
```

### Step 2: Add Custom Remote URL Parser

Modify `fluent-community/app/Services/RemoteUrlParser.php` by adding the following method:

```php
/**
 * Parse custom oEmbed providers
 * 
 * @param string $url
 * @return array|WP_Error
 */
protected function parseCustomOembed($url)
{
    // Check URL for provider type
    $provider_type = $this->getProviderType($url);
    
    if ($provider_type) {
        // Try to get oEmbed data directly
        $oembed = $this->fetchOembedData($url);
        
        if ($oembed && !is_wp_error($oembed)) {
            return $this->formatOembedData($oembed, $url, $provider_type);
        }
    }
    
    return null;
}

/**
 * Parse URL to get provider type
 */
private function getProviderType($url)
{
    if (strpos($url, 'rumble.com') !== false) {
        return 'rumble';
    } elseif (strpos($url, 'facebook.com') !== false) {
        if (strpos($url, '/videos/') !== false) {
            return 'facebook-video';
        } elseif (strpos($url, '/reel/') !== false) {
            return 'facebook-reel';
        }
        return 'facebook';
    } elseif (strpos($url, 'instagram.com') !== false) {
        if (strpos($url, '/reel/') !== false) {
            return 'instagram-reel';
        }
        return 'instagram';
    }
    
    return false;
}

/**
 * Format oEmbed data for Fluent Community
 */
private function formatOembedData($oembed, $url, $provider_type)
{
    // Basic data structure for Fluent Community
    $data = array(
        'title'        => isset($oembed->title) ? $oembed->title : '',
        'author_name'  => isset($oembed->author_name) ? $oembed->author_name : '',
        'type'         => 'oembed',
        'provider'     => $this->getProviderName($provider_type),
        'content_type' => $this->getContentType($provider_type),
        'url'          => $url,
        'html'         => isset($oembed->html) ? $oembed->html : '',
        'image'        => isset($oembed->thumbnail_url) ? $oembed->thumbnail_url : '',
    );
    
    // Add additional data if available
    if (isset($oembed->width)) {
        $data['width'] = $oembed->width;
    }
    
    if (isset($oembed->height)) {
        $data['height'] = $oembed->height;
    }
    
    if (isset($oembed->description)) {
        $data['description'] = $oembed->description;
    } elseif (isset($oembed->title)) {
        $data['description'] = $oembed->title;
    }
    
    return array_filter($data);
}

/**
 * Get provider display name
 */
private function getProviderName($provider_type)
{
    switch ($provider_type) {
        case 'rumble':
            return 'Rumble';
        case 'facebook-video':
        case 'facebook-reel':
        case 'facebook':
            return 'Facebook';
        case 'instagram-reel':
        case 'instagram':
            return 'Instagram';
        default:
            return ucfirst($provider_type);
    }
}

/**
 * Get content type based on provider
 */
private function getContentType($provider_type)
{
    if (strpos($provider_type, '-video') !== false) {
        return 'video';
    } elseif (strpos($provider_type, '-reel') !== false) {
        return 'reel';
    } elseif ($provider_type === 'rumble') {
        return 'video';
    }
    
    return 'embed';
}

/**
 * Fetch oEmbed data directly
 */
private function fetchOembedData($url)
{
    $oembed_url = false;
    
    // Get credentials
    $fb_app_id = 'YOUR_APP_ID';
    $fb_app_secret = 'YOUR_APP_SECRET';
    
    // Determine oEmbed endpoint based on URL
    if (strpos($url, 'rumble.com') !== false) {
        $oembed_url = add_query_arg(array(
            'url' => urlencode($url)
        ), 'https://rumble.com/api/Media/oembed.json');
    } elseif (strpos($url, 'facebook.com') !== false && $fb_app_id && $fb_app_secret) {
        $fb_token = $fb_app_id . '|' . $fb_app_secret;
        
        if (strpos($url, '/videos/') !== false) {
            $oembed_url = add_query_arg(array(
                'url' => urlencode($url),
                'access_token' => $fb_token
            ), 'https://graph.facebook.com/v18.0/oembed_video');
        } else {
            $oembed_url = add_query_arg(array(
                'url' => urlencode($url),
                'access_token' => $fb_token
            ), 'https://graph.facebook.com/v18.0/oembed_post');
        }
    } elseif (strpos($url, 'instagram.com') !== false && $fb_app_id && $fb_app_secret) {
        $fb_token = $fb_app_id . '|' . $fb_app_secret;
        
        $oembed_url = add_query_arg(array(
            'url' => urlencode($url),
            'access_token' => $fb_token
        ), 'https://graph.facebook.com/v18.0/instagram_oembed');
    }
    
    // If we have an oEmbed URL, fetch the data
    if ($oembed_url) {
        $response = wp_remote_get($oembed_url);
        
        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $body = wp_remote_retrieve_body($response);
            return json_decode($body);
        }
    }
    
    return false;
}
```

Then, modify the `parse()` method in the same file to include a call to your new `parseCustomOembed()` method:

```php
public function parse($url)
{
    try {
        if (empty($url) || !is_string($url)) {
            return new \WP_Error('invalid_url', __('Invalid URL provided', 'fluent-community'));
        }
        
        $url = esc_url_raw($url);
        
        // Try custom oEmbed providers first
        $custom_parsed = $this->parseCustomOembed($url);
        if ($custom_parsed) {
            return $custom_parsed;
        }
        
        // Continue with existing code...
    }
}
```

### Step 3: Add CSS for Responsive Embeds

Add the following CSS to `fluent-community/public/css/fluent-community-public.css`:

```css
/* Custom oEmbed responsive styling */
.fcom-embed-responsive {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    max-width: 100%;
}
.fcom-embed-responsive iframe,
.fcom-embed-responsive object,
.fcom-embed-responsive embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Custom provider styling */
.fcom-custom-embed {
    margin-bottom: 15px;
}
```

### Step 4: Add JavaScript Support

Copy the `custom-embeds.js` file to `fluent-community/public/js/` directory and enqueue it in `fluent-community/includes/class-fluent-community.php` by adding the following code to the `define_public_hooks()` method:

```php
// Custom embeds JavaScript
$this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_custom_embed_scripts');
```

Then, add the following method to `fluent-community/public/class-fluent-community-public.php`:

```php
/**
 * Register custom embed scripts
 */
public function enqueue_custom_embed_scripts() {
    // Only enqueue on pages with Fluent Community
    if (function_exists('is_fluent_community_page') && is_fluent_community_page()) {
        wp_enqueue_script(
            'fcom-custom-embeds',
            plugin_dir_url(__FILE__) . 'js/custom-embeds.js',
            array('jquery'),
            $this->version,
            true
        );
    }
}
```

## Maintenance and Updates

When Fluent Community is updated, you'll need to re-apply these changes if you chose Method 2. This is why Method 1 (using a separate plugin) is recommended.

## Security Considerations

- Never expose your Facebook App Secret in client-side code
- Use proper validation and sanitization for all URLs being processed
- Consider implementing rate limiting for oEmbed requests
- Regularly update your integration to match any API changes from the providers
