# Testing Custom oEmbed Implementation

This document provides test URLs and procedures to verify that the custom oEmbed implementation for Rumble, Facebook, and Instagram is working correctly.

## Before Testing

1. Make sure you've installed and activated the `fluent-community-custom-oembed.php` plugin.
2. If testing Facebook or Instagram embeds, update the plugin with your actual Facebook App ID and App Secret.
3. Ensure the JavaScript file is properly loaded (`js/custom-embeds.js`).

## Test URLs

Use these URLs to test the implementation by pasting them into your Fluent Community feed composer:

### Rumble Videos

- Basic Rumble video: `https://rumble.com/v4u8nl6-your-rumble-video-example.html`
- Channel page: `https://rumble.com/user/ExampleChannel`
- Video with timestamp: `https://rumble.com/v4u8nl6-your-rumble-video-example.html?start=45`

### Facebook Content

- Facebook Video: `https://www.facebook.com/username/videos/123456789012345/`
- Facebook Post with video: `https://www.facebook.com/username/posts/123456789012345/`
- Facebook Reel: `https://www.facebook.com/reel/123456789012345/`

### Instagram Content

- Instagram Post: `https://www.instagram.com/p/abcde12345/`
- Instagram Reel: `https://www.instagram.com/reel/abcde12345/`

## Testing Procedure

1. Log in to your WordPress admin panel.
2. Navigate to the Fluent Community feed.
3. Create a new post.
4. Paste one of the test URLs in the post content.
5. Check if the URL is automatically detected and embedded.
6. Publish the post.
7. Verify that:
   - The embed displays correctly in the feed
   - The embed is responsive
   - The proper thumbnail appears
   - Video/media controls work as expected

## Troubleshooting

If embeds aren't working correctly:

### For All Providers

- Check browser console for JavaScript errors
- Ensure the URL format matches the pattern registered in the plugin
- Verify that the oEmbed endpoint is accessible from your server

### For Facebook and Instagram

- Verify your Facebook App ID and App Secret are entered correctly
- Ensure your Facebook App has the "oEmbed Product" enabled
- Check that your domain is registered in the Facebook App settings

### For Rumble

- Rumble oEmbed uses a public API that doesn't require authentication
- If thumbnails aren't showing, the video may have custom privacy settings

## Logging Additional Information

To log oEmbed responses for debugging, add this code to your plugin:

```php
/**
 * Debug oEmbed responses
 */
function debug_oembed_response($response, $url) {
    if (strpos($url, 'rumble.com') !== false || 
        strpos($url, 'facebook.com') !== false || 
        strpos($url, 'instagram.com') !== false) {
        
        // Log response to file (adjust path as needed)
        $log_file = WP_CONTENT_DIR . '/debug-oembed.log';
        $log_data = date('[Y-m-d H:i:s]') . " URL: $url\n";
        $log_data .= print_r($response, true) . "\n\n";
        file_put_contents($log_file, $log_data, FILE_APPEND);
    }
    return $response;
}
add_filter('oembed_result', 'debug_oembed_response', 999, 2);
```

Remember to remove this code after debugging is complete.
